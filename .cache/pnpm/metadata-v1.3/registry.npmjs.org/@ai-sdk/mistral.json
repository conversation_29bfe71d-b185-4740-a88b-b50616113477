{"name": "@ai-sdk/mistral", "dist-tags": {"snapshot": "0.0.0-85f9a635-20240518005312", "latest": "1.2.8", "canary": "2.0.0-canary.19", "alpha": "2.0.0-alpha.4"}, "versions": {"0.0.0": {"name": "@ai-sdk/mistral", "version": "0.0.0", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.0"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "workspace:*"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "62cec35bf58e6a7194cdc0e544a98ad626a8c9f0", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.0.tgz", "fileCount": 8, "integrity": "sha512-W5fJqwMAkt0kPSC2wT1+u4ic97oE2oAMkERMTNsAipAlRAlT4EutLfDBgMJBKytbEH7UJIVoZ+HZOqJ59LuSSQ==", "signatures": [{"sig": "MEUCIHnKMdr7fTdAGJHJiwTBr6KrGuoCoKFpi+xjaSesqFHHAiEAmpA9OCmUaPyNOAPEP1qShXhNF3m69lHIuk+tn+S/uGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78624}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.1": {"name": "@ai-sdk/mistral", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e96c5405786c260a4193cad5898d44bb99f2027d", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.1.tgz", "fileCount": 3, "integrity": "sha512-4ZC595QcIrEXrnOCKREiJgQHmaVwwyhpiXx6ZDnPKRCvJiYxQMbPIxKur6h+zZm/lEsRawcdK8k0XsjNlUuE2Q==", "signatures": [{"sig": "MEUCIQDi2T6OyppJA8TkZ0lgggMWBwtAIAkwwSfq4KvLWfCfZgIgAy8SNIqTfBQuXLKkC9qG1ySMGSlFzjVccHosmx//ufI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3632}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.2": {"name": "@ai-sdk/mistral", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8fa29d2152e57a1c59026d905261941bbf153d3a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-M2RgtI55U3jFfyLWZsUF5ntUdDLptKqe8VDHHGIjVHXeojnd66Tr0px7A4slA6A9jiAJEstBbO/nwLRvKgSsNg==", "signatures": [{"sig": "MEYCIQDRuffWHeHVi3k36MbEE4bmZQVgweoMqJEk8/ZIdIoKRgIhAOmJkFs9Xx1VdF71Gb8DSzke3dr3uy2uTywr7dmtMgVe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94893}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.3": {"name": "@ai-sdk/mistral", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "88f8cec4a0571f0f2be684261f85f3a76088dc8e", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-oDtGHRpx1hbS/2LG8dwC9vess99CBXdGiljcoB07WGgwUdXzTyFRLen8OwHS4aJvn7fm2zIo8Ks+4vW+RBbsgw==", "signatures": [{"sig": "MEUCIBlZp7QGwgZ3bjJaPMFRWcc7+msjKU+hfdN/IkcQBKdFAiEAyWKoBqNOjB3lTadAVJJPLjt0ntjDSzK9A6WylN9i0yA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100919}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.4": {"name": "@ai-sdk/mistral", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6472ea4c37ee13fdb420b2e9c5180f4734672a12", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-2dVIrwnzt68qpaShWjlxFpfgUIvtj3mEFIjS+siPNzcdYMpJH3PmcZ5tIFoyZjfp2PQdQXVm4RisqzzDWZxXlw==", "signatures": [{"sig": "MEYCIQD/M/XRnG6Vpe0kYRzajtLnr6zG69VTnG+Z+zPHpU4mggIhAJVUP1yR4h946CDZl1CV3sYUQzTJFPxFVD1jH8h+ucPT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101076}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/mistral", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "0.0.1", "@ai-sdk/provider-utils": "0.0.2"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f417aeae4602d7226e39340ff394766cb964bacb", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-wlVG5+Z3JanFqZ/qu1EhTZqErXgB5mdjC1Qo2UdCgSWRKiDZvUpa3ZIuY9fcK+OOIMW5AhzqxlPhrJ1KqH/qdg==", "signatures": [{"sig": "MEQCIFUysoa0eJBpZvSL/3N5u3UIpR0tzXYJhhrn8YVFVtDIAiBCCHIcIRlHcBXcbOTtL2TZ/Hk+HPs9/7F3iSDBThqsnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100953}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/mistral", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.3"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c5e107ed7ed155f730d87717a6d906600d584981", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-BDi5pYO0buZwgt8yRNtozkFspgt8J/RPoqsu07+bUjXAscCD+wXcJT/5Xzx0hoQQv6dDLRsCciv0QSL+n6RJGw==", "signatures": [{"sig": "MEYCIQCKVKsHL0tKl+bxlL09b0p6ko2EgbnWrcgnN/mln5F8BQIhAMBYj4Yh7Usr/eIQeHCNtIutk5c4urJEg4v0KifFG50i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101947}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/mistral", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.4"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "174db91d06878a9ccf02c1209bb294a5c5d00a26", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-7eLzOO0MWgoi28HpyTGXGELiWJc5/4AR2in2Tz5idgbyY1UYepHccTbfycNbUCPeO6Vj0BEr022H7d4vMb0cmw==", "signatures": [{"sig": "MEUCIB/+w8kJbLPnbcLUj1oeVSCwvB/AV1y2hadsT9RmeCQLAiEAuhagWTMGOmbNYyJ1sD6zGl9gsM7MO9/75et2iKLK4vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107344}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/mistral", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.5"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1914fc3ca46687f315eb865fe06a3d604508074b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-BEdnv5KsA+o4ZK4IZxDhj4ZUOKK7crEIbEC93k5vDHfFpVIUwXhEjD+86whiku4u1hLXyd82ggdjNyKy1vOoVw==", "signatures": [{"sig": "MEUCIElYpUWWuaxR2783sUVmm8D/4x/irA++u+o3BPU9K2WmAiEAjE8P1n8hFyaaSEfJQQvLSLri6beNUvfiwXky3ndxc1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107344}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/mistral", "version": "0.0.9", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.6"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6c74706bd048da9a224afbb1010418aeb9f6e95a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-+77VVID+ENXYMADNi/h2+qc+eEaoe0fYodmAE6hcZGM+yTxL+Ko2RlsX1b3popJ85PdzozBZOcXra1MqLHHF6A==", "signatures": [{"sig": "MEUCIFu0MRVN/HYLkwOpgilCpXN9bOFWGcxQ58AEVfb0gavYAiEAnmy0rW3i1WaN2jt8fIoCcYAc+2abQHOAqAn3r4ysVl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107344}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/mistral", "version": "0.0.10", "dependencies": {"@ai-sdk/provider": "0.0.4", "@ai-sdk/provider-utils": "0.0.7"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "041fd5a0b485706554cb600f610190b1bb48edf4", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-aoH00OoBLXb2atUdU1jgF4SDUdvrNqC6V+RIzf9/0v8Bb38QGxDfFcsDTMHR4K20cbk3lbhEAHKrBbiKulm/Pg==", "signatures": [{"sig": "MEQCIGpKaPghN62GzFFDde13t65++kvbV560waKORoIKiTmJAiA6QZw98jeYLb1qiY9eUxLucmihwsZDmjmONbHFKTr2gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107345}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/mistral", "version": "0.0.11", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "604d467302411ff7cd88a48f6fa2d6f1a6db736f", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-Y/2V7BMwyaTnVRfhs6YhI5MGcQgjBrTwo2K6CftUAjJvNIDtSJ+eVc9hCuw02uMvTScfeiTB6lZ8OrzGyCNHoQ==", "signatures": [{"sig": "MEUCIFyU4oKHwCnbe7FPJl2r3N4roe7pDxYGG3+AvYuwBik5AiEAuU0XlXaDF45dU1zkb1eEwYGFg9LJ9NxloK73d23dE7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126527}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.0-85f9a635-20240518005312": {"name": "@ai-sdk/mistral", "version": "0.0.0-85f9a635-20240518005312", "dependencies": {"@ai-sdk/provider": "0.0.0-85f9a635-20240518005312", "@ai-sdk/provider-utils": "0.0.0-85f9a635-20240518005312"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7b5a48bcd41c73f0f9d1b3c6daec22c1d6332115", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.0-85f9a635-20240518005312.tgz", "fileCount": 10, "integrity": "sha512-/kwgAY6pfygqhKId/xvv0OfChLOoQrYx9gajt76sZQ2o76nv2pcCox2sfz4RYpp7sgqRh1GIjD9gtMembN++lw==", "signatures": [{"sig": "MEUCIQCpFQkbAPifYUNzoNW0fp/jC5enG+ECmjMxF32cgMQcCQIgCDnrYg+V28oVeN3iPN+OBb/usFmEH8meMBfH5ph88GU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126566}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/mistral", "version": "0.0.12", "dependencies": {"@ai-sdk/provider": "0.0.6", "@ai-sdk/provider-utils": "0.0.9"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f1ac51428228ae211ac17cdad8a35fc6250187c3", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-mTKE9Q0EWJPtoPdLBCNQGuZzBT7q0gWuuwoF/FMMSgVXK4ubw++gHSAMi8L6zRLpMnxjRZDQ/p2Iw1Jq/sxBAw==", "signatures": [{"sig": "MEQCIHThuh6pvmYgj+/zOTGZMGZmebJMhHgM8kLJQH/0cwypAiBr7QxlfvQgj50d32XFREafWV5hOi6Ekc1N36EXiZ6EEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126495}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/mistral", "version": "0.0.13", "dependencies": {"@ai-sdk/provider": "0.0.7", "@ai-sdk/provider-utils": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8c028a03e81980c49aff3b810e7a064c8729f058", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.13.tgz", "fileCount": 10, "integrity": "sha512-1yzIHwfx7mola2ZFfK15mZMfQR3TpII2zyC6QD4ksP2JRmTaah431SanXl9Os3ICbvCBNd5HS2iv1SEb3Ho12A==", "signatures": [{"sig": "MEUCICyT3c6nt1y6L01BV+o+iqkDr0a2PUW5lsfmBes55JuMAiEAmnQVBmqmi6f7zAy6PqGBjU411XPFGZTj51v4xdVmb+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126421}, "engines": {"node": ">=18"}}, "0.0.14": {"name": "@ai-sdk/mistral", "version": "0.0.14", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0b367fb8b65822e8087e5331799cba61bb8a1276", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.14.tgz", "fileCount": 10, "integrity": "sha512-DH44VJyT7nQSg/few/hyWoLJ8FtXvBK/dciPhV3mAVH3CXuaH0U7XV5XYM9cwj5NPW/E9wgNdZZBqQt67xJxmw==", "signatures": [{"sig": "MEQCIH892GOZYClz4BLOq7tEDO2IHFVSaVJxVXJSfEwzIijTAiA5OdTG0Duen2uNEh7OHWQ7GftXnGnzrM0rcz9LxrET2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130349}, "engines": {"node": ">=18"}}, "0.0.15": {"name": "@ai-sdk/mistral", "version": "0.0.15", "dependencies": {"@ai-sdk/provider": "0.0.9", "@ai-sdk/provider-utils": "0.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bc4b64bd18a2f8b13faa014847e8d005903ae6fb", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.15.tgz", "fileCount": 10, "integrity": "sha512-FT277bxZj5N3i1SP7vtWsg0n2P4y18GHCrofN04pU0TpNryZQqPitsBFQIHMjUf547HyXEAIwlT8rYQz74KvKg==", "signatures": [{"sig": "MEYCIQCxMpqWuALSm5FJyGZMVAZUWgKsdto8So+RWIHO8jfhlQIhAMC+5RpPMuBe8fQ0j1iLr8sX/dv1AUr7UJxDHvz/Pffj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130349}, "engines": {"node": ">=18"}}, "0.0.16": {"name": "@ai-sdk/mistral", "version": "0.0.16", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e5ab5f08affa64066b5d6539b77d9e1f83518910", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.16.tgz", "fileCount": 10, "integrity": "sha512-91/lT3712v6lonJBX6+wzDi9wPrZ/OuUt4uKul33IrfsZ0INvI1yJjd74zkYxPOk7TsSc9Bi6mk4UMr3QamD2A==", "signatures": [{"sig": "MEUCIQDDISzPBlvC59px6hdh6UchYXLgzdaKDIyWRETlV8K0HQIgCIWlJPA80R3jSgGrgF9NvrCUtwZDvWj4QexTEwRLOMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130350}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/mistral", "version": "0.0.17", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eb904181aae226af10783d8aaa9d00acd92040d3", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.17.tgz", "fileCount": 10, "integrity": "sha512-MapQtsTI8o4Efb1m42FQHok669kz6O4UxDNnQ7sM1NJIYa7gmnpmzN2kIyygkOlaLIPDPhrvSfKlnr7v98YI5A==", "signatures": [{"sig": "MEUCIQC4FtLIWmDHHQsBcHIhiSL6VPfuO8Pr1cVbXR5MK5UVSQIgcEPVuuM/JbfH7xj7RY0ZOT4E9pXeSP9qQJHPQLGRSQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134219}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/mistral", "version": "0.0.18", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ccf0969ea2799a5f190bf69cbfa78ac218eea886", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.18.tgz", "fileCount": 10, "integrity": "sha512-aNbdyINZU2Kmv6+uLEEbvQJxHChYf1RofIETYAmCZcOk3wU1gReWSjZK7eP9BzehXg1TkeF1UpT60bnzl0++Pg==", "signatures": [{"sig": "MEQCIA13pnwmSy7d2c5Rj/F3mL+Ltth1ksAnFs4DyyQEjANIAiAHkjVI6SrlTnIV1/YY5FmXTbiUI0NkwGAehNNmNliuMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134219}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@ai-sdk/mistral", "version": "0.0.19", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "475f8a38ccae498a98ac9d4e1c7454187a23403a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.19.tgz", "fileCount": 10, "integrity": "sha512-TB3Gg6mar6IXt0TFYMHG4HmgyiTutjBSL6aGUuE02aRq+1Pn8WCW+N4jTJT73xonf9LiYM0oJ13NB3Gj/Ow4qA==", "signatures": [{"sig": "MEUCIQCUAsyJHgGnLcLcf7CBxsDrmNZlAszdiC4fScirE8A2YgIgEydg5LLrdlMTbAVuiQYnJ6WEDQLst2a/O6knIMb6RFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134219}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@ai-sdk/mistral", "version": "0.0.20", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a2a6ce58f3815860707c959eea0ff8cf17815646", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.20.tgz", "fileCount": 10, "integrity": "sha512-PqRGrBIXN+e1Doxi3eyWNX4XFlIKrku5aqb96+fozHWVpk+TIRGhaXRxZm4quDG5QvdpLlk7nUKbOwsjK6XGYg==", "signatures": [{"sig": "MEMCIDXe7JhEomgLGaTJHZ1sPsXAvBj4LXXalemqZ4entkJMAh8lwp2YX/+IjmNQhNjRB762gBNk85+Sb4bc73gNdWm+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135254}, "engines": {"node": ">=18"}}, "0.0.21": {"name": "@ai-sdk/mistral", "version": "0.0.21", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "663fe8252cedaf704f72650727378b6c96937891", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.21.tgz", "fileCount": 10, "integrity": "sha512-8MD/WV4XuKWdmJtjYuXGitEkPhrOghrNEEEQmIj2yfy6b3DN3SG6rMcvBVqcwdAo4mTZhCyDX7nouMffLUj4+g==", "signatures": [{"sig": "MEYCIQDvMPi2v+R5cgYANGgePaMLJhEfO/+O59IlDC9a1icNUwIhAM9vhiZzTW4wFdvzS5/J2excbFmIcB9lbhZsNOHAtbi7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135254}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@ai-sdk/mistral", "version": "0.0.22", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "63db86a89225c90bbaf59a0d93d85b79d1a4c856", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.22.tgz", "fileCount": 10, "integrity": "sha512-pSWuq3us+X2mMATiRyCfjOKoW7/CIsmKZsb5DrC9ZdxwjdIaQLl9yvmWu17QYJkOqjY77RwcCi9WGfrAEZG/QA==", "signatures": [{"sig": "MEYCIQCZI5xIedqPce7sP4rc+dwfWqxEE7CBXl9SfHcgRaA8rQIhAI9doZvEpZ+Dm+0uVy800W+OYHkeB0Fw6nIBa68Xuc/M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136159}, "engines": {"node": ">=18"}}, "0.0.23": {"name": "@ai-sdk/mistral", "version": "0.0.23", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0db7ec81b431ec22eb1d5d787e97c80121f008ac", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.23.tgz", "fileCount": 10, "integrity": "sha512-4psQIqTZjepzn74cuCA4MWtewP+OhIINzO0wU4H4LbmdVv5VqDUQcAmCkncZImPCmGbVbm63SGcGAKYmE4HuvA==", "signatures": [{"sig": "MEQCIAT86t1uPoc0OlPTOXlpQij26IFphzyAtgNFhhi2wZ86AiA3ZT3zbfB8efOqzxOnd/xmWz6f9u0Lw/zdjfdQ4YoB1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139462}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@ai-sdk/mistral", "version": "0.0.24", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8532716b10cc24aa11bb3f0852c340f5b9af715c", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.24.tgz", "fileCount": 10, "integrity": "sha512-uhiIcj1tOAcUQujYXzM2IVfD/w5RP6EQQVCoo3cfWwmUOEUYV3XU8YA2aa5NPwlTZUBLApzxtxI3Vgyw+iPJbA==", "signatures": [{"sig": "MEQCIAuuw0EhOQc8QbGmS7sTwy5Hf3bCKRrfk9mBM7869Rc7AiBBp0kyy/lRUjWm8Y4eROvfeSA3s3zmrf4MDuy9+yaTkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139462}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@ai-sdk/mistral", "version": "0.0.25", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "019c821c251f83dc1a8189e5fdb121ec4bcc62a0", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.25.tgz", "fileCount": 10, "integrity": "sha512-P628hFUk1Db0kFOs2DNIRPG0T16cQvyC8czCGZqoVTRDq9Nim3LjLc1mQ1RTRiBItJOqSvXVftFreFowJ30d5A==", "signatures": [{"sig": "MEYCIQDby5S9+7mIAY8LmxbTHV9RDws6ejeGHy0HwrM4/p5OCAIhAJByf5hMt74WMQxMMKpkEStDc6LLRoMaE170XNIYnIUX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139506}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@ai-sdk/mistral", "version": "0.0.26", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "027713360d648b1b957cfa971c68cbcd6da331ef", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.26.tgz", "fileCount": 10, "integrity": "sha512-v14/KRcpsbriNAZE1KB68RseG3Z4IBZ9g9oVqnc8i/wiTLfD9MXgJoCrJqezAtkaFb/wpdpshHPNCLgAXyrBwg==", "signatures": [{"sig": "MEQCIFQklc40Bv2RTwiXUNoAjjfMpZfO8u1IStacL+cUZXTYAiAW4nUSWiejFWDAcZ2IVu59L9zPSR/e+LZ8ccKiV4hQbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135571}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@ai-sdk/mistral", "version": "0.0.27", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bcdccb215a81ab235a08f571757d51902c5d6af5", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.27.tgz", "fileCount": 10, "integrity": "sha512-7t4/xJIYzyMWR7uUyYrjH81uHgMOGanKohi0oLIUFfNz8r4RaYipzQeSj+JMb6CDrYoyF3sleyG5LBlmW5JpDQ==", "signatures": [{"sig": "MEUCIFCH3yWgFBrg4bmCeP1sfxdGrGPBMHGhhC/z9Ffu72ubAiEAwhcjgB2Kfp7Bh4iEOaHKtPE5zElmcUs0QifHzSzGIrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135571}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@ai-sdk/mistral", "version": "0.0.28", "dependencies": {"@ai-sdk/provider": "0.0.15", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "331beef555fb5595e24c133b1dd7d03f393543fa", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.28.tgz", "fileCount": 10, "integrity": "sha512-QwNSP4vY6fRWuDb16yTYJKB6uqTPu0MzCRxAGwWItGVNFhUkg2HbF0W+SOZq37LH8avMqz1Wy28GtFL6gwGNmA==", "signatures": [{"sig": "MEYCIQCnm7VtRhIjkEJoW6hMMaxz+aoaOnaBGnoOe1cB2ISK0QIhALyfgAqhliRwUn6oQNERkemYea+RITHKe0qqWCzXSRgv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135571}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@ai-sdk/mistral", "version": "0.0.29", "dependencies": {"@ai-sdk/provider": "0.0.16", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bfd04f0d88df8ce183ef00f985962729b5f2fcdf", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.29.tgz", "fileCount": 10, "integrity": "sha512-ZDuCzRt0+uVvIMbhDU83ieqsePJi9dv9XwV0RmkJirQKCVIJ1hoAvBNOAxxUjRFVIzmuZCcy+JMviDRrccuzSQ==", "signatures": [{"sig": "MEUCIQDhTJZzF+5ukTk6HCHPReoxCdXIQW43Be/bhjM9/0CJvQIgZGJ4xYaUXbOTmMiv9DSUrJW7u6JBnxowhYafpTAHnZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135571}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@ai-sdk/mistral", "version": "0.0.30", "dependencies": {"@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5e91e14726119448775a477dfb77d9eef188545a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.30.tgz", "fileCount": 10, "integrity": "sha512-WeHBVAgBFQRXJhW1XwzvHGKqhZm7FX4OxPBhdDr8V9+4gqI5tAoxM3SKmT6xTYwhMTt826cKu7seNWZee6TpRA==", "signatures": [{"sig": "MEQCIDXmi2wgrz9lk+4+zmxpmORtqAgt4yd3VrR9JvGpeu7LAiAFX+4dIL/Ji8lC/W+jAq0DNiroPVJbhKOsovoem85Dyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135571}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@ai-sdk/mistral", "version": "0.0.31", "dependencies": {"@ai-sdk/provider": "0.0.18", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5467c4191aa5241a971c26e55be0c26c17e0c4fd", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.31.tgz", "fileCount": 10, "integrity": "sha512-CTLlTx3Bva/g61VJ2xVOP+a5xz/76zwoD0ns0D7D2G63imNj0Na5gVPLM3A0wkSFwLxdkxuwvmsqjkC0vSzS5A==", "signatures": [{"sig": "MEUCIQDfd0KgHWKdfZLqZn//eawn12xjPTVQi0GuQ+cjFmWs3gIgE5MpNTglvQe18l+aR1zC6yYok9AxYmDc4opodEpZ9nk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135572}, "engines": {"node": ">=18"}}, "0.0.32": {"name": "@ai-sdk/mistral", "version": "0.0.32", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "26aa1425ea0f6d5f22024289c761ed5bea7c617b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.32.tgz", "fileCount": 10, "integrity": "sha512-zTtIZ/gsF/QubFLnA9rnX6Any6swdxAztCMAgRE2olLSW6uXtfebrHh42oFUcxXwfqZHkkhPfx0HD00H0Qgrhg==", "signatures": [{"sig": "MEUCIQDWpX98D9lDRsmqFPN8ZsptRprAPk13INesKXPqctPW4AIgIDJY+gQVh61Fal+xB9R1henSQtpuP1OWXPfUE4cDzSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135592}, "engines": {"node": ">=18"}}, "0.0.33": {"name": "@ai-sdk/mistral", "version": "0.0.33", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3a9d841d56f67f64171808299803b8e84041f370", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.33.tgz", "fileCount": 10, "integrity": "sha512-EXm+93X3PUiP+N1SJxjTJ257lj0g7zotyZykQ9qiH14ewWHtCsxu009oFEPyPbC5fN1fUOJZIP/djh/FleHjQQ==", "signatures": [{"sig": "MEUCIHTc5HY0K6F+1CTJZmqRKvzL/TLYIC2swU05BYwIlLDjAiEA+yIg9f7ISUdqWa0xa8vyUjJ1qAaZK6y0v66Aowhc4wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135852}, "engines": {"node": ">=18"}}, "0.0.34": {"name": "@ai-sdk/mistral", "version": "0.0.34", "dependencies": {"@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "99a5bd1f8822c7641b8b92e2d925fab754b39cba", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.34.tgz", "fileCount": 10, "integrity": "sha512-u2uelh5PreqnMdplNF3mUJdMkTZbUB8Z+siI+ngAWmwQ1aG9Wb3H9Ee7Y4YriF0NQLG1XyT9s4WzAN3VCGueAw==", "signatures": [{"sig": "MEQCICeytaNv+AIxYp/qUlBilO5IxfOJCjFR454/KKAaBfNOAiAjQm5zd5q0T5RprkXdp3gEZZptZOtgUpzC1avSyL2g3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135852}, "engines": {"node": ">=18"}}, "0.0.35": {"name": "@ai-sdk/mistral", "version": "0.0.35", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9243b4cc11d61b5020123ba2f97afbdb97a6000a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.35.tgz", "fileCount": 10, "integrity": "sha512-MGfZ1lBpQ5WM/BH/aKCF2Bg2BQkVwz5knWbmi46fKpmLaffps8+o8Y2Bw/q/g0kGVlAvZ1LIgk02JQpoRvBsgA==", "signatures": [{"sig": "MEYCIQDafvyL/LOqjurUwfizT+QHBDUt4z4AYAueArnTXPeNMQIhANHko7ubkrONbsibWyVYJfXSHlO2fPAkcUKkk6XmOr3I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135852}, "engines": {"node": ">=18"}}, "0.0.36": {"name": "@ai-sdk/mistral", "version": "0.0.36", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "89f081fab4eda66675ea7e5a76a8a36c639ba6ff", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.36.tgz", "fileCount": 10, "integrity": "sha512-9eYLhBxAo5B2yf/V3LL3AOSZgWM81XW0jGBMucI1UelXxlFxB8uiYz0AZ1bx5N3DdwzfxHr7CW9OrQtxnXxapQ==", "signatures": [{"sig": "MEYCIQCNLgEnvhhniyF2SdjlTGakJMsAbzhCpCEBpsJrtDGPIgIhAIttLRjTiHqWX4r2DTEcVRLAt/n0li4Pa0M3ESi9ah3u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135852}, "engines": {"node": ">=18"}}, "0.0.37": {"name": "@ai-sdk/mistral", "version": "0.0.37", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "52b17e32d86908020c5b27e9ceb9dc50d4e118d7", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.37.tgz", "fileCount": 10, "integrity": "sha512-qenkDQKGgFTgZNdpF7r8AlaO3T1XNc7YCSSMR+A2WVieRWh5XEDlzekx4LFY6bR8vHmAUC9XnofG8p00odZISg==", "signatures": [{"sig": "MEUCIA6iU31AXbHUfdgb1sC3YvHWTPJddV90jD+sCHVtxUfaAiEAumygBf2BI42MoH+RjMCu8NTjtpLkf2o8Mstv9w/0Abw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135852}, "engines": {"node": ">=18"}}, "0.0.38": {"name": "@ai-sdk/mistral", "version": "0.0.38", "dependencies": {"@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a5fffe00dce14ee5679b32041cacd4311ac63038", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.38.tgz", "fileCount": 10, "integrity": "sha512-eKPWcEGJzN0/NaeStWUBtQWxqiT2GBrKmZWfPxVv6EeXfAno3g3Q63xIhOvoqAW0S7Td62hjcePZ7QvzqmXSmg==", "signatures": [{"sig": "MEUCIG+oEHMvrTEecYgVk8oyg7LJjUWDzIBOvvT+U8FVmRHyAiEAlRqwnsTZiQRg2NrFZLAKvjOLxJ3ddAnY9d7AoIXsxgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135540}, "engines": {"node": ">=18"}}, "0.0.39": {"name": "@ai-sdk/mistral", "version": "0.0.39", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "55127120394a24a3148f265abe72ca55075d95a2", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.39.tgz", "fileCount": 11, "integrity": "sha512-cy9s9tS3LtuMtydkPTF5wYGDlLUzHVj1Iet2Q61mPehNiCHYIlA/XdEFclhUsOaRNNsRlxEW9I4qH943T+9CAw==", "signatures": [{"sig": "MEYCIQCFtPr6XTcrLOBCnDaidlArKRtWU7Nc4fX/nK592G1w5QIhAJ5V9OkoXgEj9oYOt/ujqt+Z8QB8cmFTSZfqgPOGhXM/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146843}, "engines": {"node": ">=18"}}, "0.0.40": {"name": "@ai-sdk/mistral", "version": "0.0.40", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f7a0e1be2e3216bb48f33ba2be40d1f398488e27", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.40.tgz", "fileCount": 11, "integrity": "sha512-lGl+YppKJJB+9BbmQqcxE50f6kX1gq1CscT9Uzf19mCLBQlYdeJH/xQxpnf8nRh9p9muF2nSFhc9duW4CNpM6Q==", "signatures": [{"sig": "MEUCIC4GBtid7NyI3565KEulnmcXAgPT2/g+xatfuHm2aQMEAiEA9mGVfDRIBvADbIy19CIUC+0HyoKIYcSxlkHP9g3AkJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146941}, "engines": {"node": ">=18"}}, "0.0.41": {"name": "@ai-sdk/mistral", "version": "0.0.41", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fa444e3e36a6b2e88a08dcd7b60ac01025d13d37", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.41.tgz", "fileCount": 11, "integrity": "sha512-UTVtdC61AF4KQWnM3VAoo6/gi7G1frL3qVlKyW5toiRAUjCdeqLJUF2ho2iO8yqf+qIT6j57jWT3o6pqREy3Wg==", "signatures": [{"sig": "MEUCIQCyhRBykOw2USAPALRP3PU/BK5rU3VdWSSP0vUgBLWoxwIgOwdOdNrfk4jfi6Jt4fNuith+o0BA0bllt/zQjyLXLSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148593}, "engines": {"node": ">=18"}}, "0.0.42": {"name": "@ai-sdk/mistral", "version": "0.0.42", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0b32099f1583eff8ddc3ff49cba4ffec27a9a64f", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.42.tgz", "fileCount": 11, "integrity": "sha512-wvtvfXJcyOAWNuc3ZaWogIcTfKyHLw7sudzzyGYb7jHQgZRRlrythuMeoR9OcC4USEKE2xgYyAQ/JF31TZUPKw==", "signatures": [{"sig": "MEUCIQD2dlmTRAn4+S4SqJLjWqMOX7tfbcgnF77D4rfWp9+XxgIgEaGHU1NMopM+xB2c9XAciCx2i8aEWtwTOWOlvBEDQXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150219}, "engines": {"node": ">=18"}}, "0.0.43": {"name": "@ai-sdk/mistral", "version": "0.0.43", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "900a3740d5559037c9e479a5a94d9bd23f48fb95", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.43.tgz", "fileCount": 11, "integrity": "sha512-YcneVvO57bbmseUmnvQaj6OolMj7/q1W/oeiFj1h+CJZsXIOX8P9i2Cmo2B7HMBbt73NIcvtyPze3GjaczZRqw==", "signatures": [{"sig": "MEQCIFoiW/rmS/+XSv3wcShTtR0Oxn+2ET1mSPPYyHdOgytZAiBZqcpRHU8FSppIq41rKQo/i5jdeO+EC56dS7+7Dv+n4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157286}, "engines": {"node": ">=18"}}, "0.0.44": {"name": "@ai-sdk/mistral", "version": "0.0.44", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c1674700cd0a94384c70d6bb9ce48c35a04bab89", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.44.tgz", "fileCount": 11, "integrity": "sha512-CXDIx3a+TAiFNjjU/yxs4LEyyRTpMY4dkJj07BEchPCBdgDJ67/8GZ0t1g+tZqCuuxmKY2F3bBg5Gj2jRZWt0g==", "signatures": [{"sig": "MEQCICaJ4vaYBJ8+LPo9JuZ30npahisyS3xGpi6O7L3a4imeAiBzQznVUeX7xSdQKf/goBc6X339uK0qlLzCp8VALbdT9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157423}, "engines": {"node": ">=18"}}, "0.0.46": {"name": "@ai-sdk/mistral", "version": "0.0.46", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "56f43b0295bc5fdd630ced6459868d79de5f1fa5", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-0.0.46.tgz", "fileCount": 11, "integrity": "sha512-WP39TqxdUR3WgFnW+k1OgOTmH7FYzNWPkn+x9gJ9/JU/TEZGyEpylk11N4nakN9jLeuJSXhqbG2exooQUX2M/g==", "signatures": [{"sig": "MEUCIQCD70doqRb/9IhrDutCmFmtsenivrDrBa5BCzfCK/wN4AIgI7i6GiBADWlOl+bfD86aTzRNp/jQGTrwQEuKBdz6cYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162713}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/mistral", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ccdde1f783162913b1cd951765b0689489922824", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.0-canary.0.tgz", "fileCount": 11, "integrity": "sha512-DSFUeqQETRerNoIi1F8PDQljKzyafx6Jqyp3d5cI4Yu2AUJ/eaIUqVdhh+dbDUkFFosaHUcBByTbd7XKpOd/Sg==", "signatures": [{"sig": "MEUCIF7zQEKQzE2zN79b7B8xWxmS3ObwLyLSE/8U35Z7W8xPAiEAtQT+U8e2G4ooi6xDdXMbRzl3bg/Y/U3LjrXDa1+nRfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163799}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/mistral", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dc727e83957b7878607abbf9d6ebfd0f35fca968", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.0-canary.1.tgz", "fileCount": 11, "integrity": "sha512-E4bzsBxzSDdERbkOzeYCRE3hYuMnBBP2EhgBeJrDdaJWiH0yq6VVQd2V9SOIYFwXc7+9aopju8W7kMUmw01Z9g==", "signatures": [{"sig": "MEQCIDuMWv+ZGEWOsbIvow4Q+5H7RCWwPqWq8UJTgIs122KkAiAxkDXVDJFV+CKP4hpHG3TGryHpyi6VewjT+C8Y9lVeQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163281}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/mistral", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8736edb38dfc59f0471068d972c3263ba50b65a7", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.0-canary.2.tgz", "fileCount": 11, "integrity": "sha512-fHy1J78naWoC1LQfAOd99o3LobOh8qs00b2ssR2XMEz65Dd5FcP9USCZiHRSYzLOKn5RTovqCfay86ukvIzBEw==", "signatures": [{"sig": "MEYCIQCZ8Wd1LCwNWirujfnHb+7WI7EZNqnqb/zUc1sgGgN31AIhAMdrtSaXayHGFcj97tFckVUQLSJRu6EURTSSjZeqK1f3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153689}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/mistral", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fcde5889d6110d863b3e693ac58e5515703fd645", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.0-canary.3.tgz", "fileCount": 11, "integrity": "sha512-JZ96tdKQCg0MS5drdat+bB0hVrCLQ+zHulyQHeerZ0tjVwQ8GcOF8qgbLkdopwYgPnZv0znPCtZZUHCV2r0L3w==", "signatures": [{"sig": "MEQCIFwsuE8xH3XWVHw8DqVqKLAmdsg+g+og4dQpd1fXFNXWAiB9ti2uNi3CKMorbFHdl2doUbH3oDsf8pb8d9ZV/Djv6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153803}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/mistral", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9608d001f5c3393401d67c4233fc64f5c1651361", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-KNnq7l5Pmko5ZnBPUDRjn/Y9cya6wV4muG++KqT6uF5kseqATYEqC/W20q/VtK1LPxhgCTo4dHZzG+hefBdnIQ==", "signatures": [{"sig": "MEUCIBTrdXVWYWV5zoE/5dCdJxh4KKCeXAmgShzZP/X+WNQSAiEAynDGz5uz43ziAx8ZY4nkcRcYIV+aNB/gDduAYyWU+0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133955}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/mistral", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ea86b54654f65e5f0fea8890f3d744cae0798c9", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-m/3Ip/JvHMu9A/qNmEC5sK1f18XlMrxAVfS+ToxFuDaoqRZk/OqArxDQjzEqfNZrE2+sEr4Q4ZTXtyiWoQo48w==", "signatures": [{"sig": "MEYCIQC1Irhk5Fz4e0s/bny9ubmWx9+HtUHeOjJh2bIKiuOzswIhALrXlEz9QTLf5hwxawyUp5Caxtr6fKS1FMS0SLFULS+o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134096}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/mistral", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "da95e7907aef4ea6f6fdcc365015a000cf4348bc", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-sZx03McYOrSOvEVewC/fjWuhczZ4EWOiMtz855gQfdrUzQwQRhhCpfLSp+9Gyh0v2nsI53jCW+o3R2YMnVY2pA==", "signatures": [{"sig": "MEUCIQCB9zWlnJb3IebTm6GUbzDJqv7eJZeG4iWDu57+waWXlwIgEXINHAf9vTgpXMYL7RsY52iVY8JHWsf96RJxZ1LoxfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134192}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/mistral", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6230e6778e0588993bfd020e234d49a2d4d2a7ac", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-CzwYougArZ0STfVXfg//+BUzW1HHD38slNQYkLMxPjlMVLvNw0H5EqM7Gbk40z5bNibe3o6SWGr0GTX1rUnVxA==", "signatures": [{"sig": "MEQCIHDX4HGx2QZlI2UDqu0f1tDj6j1uaT3h0PB62EZchzJhAiAJD5bZlN1Hfmaufnt3AEJhXeo7/9oL8iWLj6YQTfI5Vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134315}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/mistral", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "296d85d071b1b5d32c48cab829ccc1285a5cecba", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-IL0EJHXrVC1Gu1aa+i07AiKIHViqkHNE4mZ2yNmqKSaeCMM98w3GNzhp1W785cNrdztGttow9/rTAHpTMyh+nw==", "signatures": [{"sig": "MEUCIBES/ToPEc0x/suT2wvbQKm9veXPK2/9muGUhKAnM4plAiEAp6nxIrVvSk58cblUENyq2vEdwugrlFi6F+xrP/fB36U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134411}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/mistral", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "913c6cd239f109a45d0f79616f4fcca1e26c6243", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-CwjmLuLUQVgK6hxsO0Bs4e/azNruDL2SS9+vmJqTO77KgQUe9m7skW5GRTryEVgE/m6pozxau5Q+W9Zk1t7AIQ==", "signatures": [{"sig": "MEUCICv/cICCc83W5XSWU+xdILAGU3If+XPhpQI4CVkZ3dbOAiEA+peHrl1brJ6kTw2f3DdcKn0BauiJ28+JoMsLl56KiXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134534}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/mistral", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "542dcf23ab3fecfbf882e450da6b17c24bd6a7e2", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-MAqMhhYJmXVVxVAg78qsNRmt2eQ7gVhL8ZbCd09fTlapRJ+LyrJ6RgrrU+ZwWb6aaYu9sLbnHZ+thAbPzxaD0Q==", "signatures": [{"sig": "MEUCIDqdeXN6sH5AqM4ZqeW5leu9gD7651QKNDmqhTka7jplAiEAvcOeyues1ZTm8CVfxfHMrna716DFqC+egVAqc1/ERZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134729}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/mistral", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9582f5f22271659de964ef018114fceef4d4ee9d", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-eICJ3xUXRdKY08NyGc0GIf6r7OLGCzmZKZ6oyLJ6FKCWjnMah6VQnZsBZwipFnCF0zmfm0RseudsABc26tmOHA==", "signatures": [{"sig": "MEUCIQDzZlEW9QdXaMzUK7ASx0DAzdBQ6Dn0eDneihjkvphxpgIgRNU/SE5Cu1FbPlSrvvKWSygqSWMpSHUi0eQt0p/eDC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134918}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/mistral", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "87e59d35748d88c03b76e142ca6f3cb006fbe1c9", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.8.tgz", "fileCount": 10, "integrity": "sha512-jWH4HHK4cYvXaac9UprMiSUBwOVb3e0hpbiL1wPb+2bF75pqQQKFQWQyfmoLFrh1oXlMOGn+B6IzwUDSFHLanA==", "signatures": [{"sig": "MEYCIQCpQLRvmkdScMKo5AsM8F8vzl7k6JdQq41e1ao9yoV3QgIhAPvUAzf5zzPS0CnjlGlzEehIWHOsulzQPgYfU7an4z6m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135080}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/mistral", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b9c4441cd42e16f34fa6b3b24b569e7021dc0292", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.0.9.tgz", "fileCount": 10, "integrity": "sha512-PzKbgkRKT63khz7QOlpej40dEuYc04WQrW4RhqPkSoBO/BPXDRlrQtTVwBs6BRLjyKvihIRDrc5NenbO/b8HlQ==", "signatures": [{"sig": "MEUCIAUH6i+a1hbJWeGdY9AeTaU+5V4uck8VrddvhBZbLJ2JAiEAx+S6oHXxD+Nb9D2jRAx1UHrQl4mynMQdokQuaRLo/40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135176}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/mistral", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d7feddcf2e5d3bf235f536c5b2da54efee8cfb10", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-RPOAzk6baQ8R/Cw/N9189tX6CzSOHBNf+r4I5x2H08JCsEx8nycMgwqvVzZ5lhWhaKBvHaD3KoEQSyqyqW82Gg==", "signatures": [{"sig": "MEUCIBrZjlYFCDUXYPfcHeO0X1q7qifquqyYE0gYUEA0U6aBAiEAptKix6r9WC2Tfx29MvM0Kg+IkLNaZe6KyCgMExWngiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135323}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/mistral", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e7e0cd57a5a5d100cc887b9016b98be55858d5f0", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-Z1tCq0/D1DnW311bd0N4stqEkAzuzmcKtnSW7hjkKFWdXAmBb8fJDywRvwywSwgW3P/0zpO7vKMLrZjiH4Jf2g==", "signatures": [{"sig": "MEUCIQDh53wOGEW3DVt3/WvmwkQuFLKIBiAaS64B3QSXHxf+YwIgQh41f3eQ2DgnBluWHjLFzaRAhoQ03HGXBo0uRRalnzI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135479}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/mistral", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd580eab045f245f43c5750eef966c17b86f008a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.2.tgz", "fileCount": 10, "integrity": "sha512-QCxcTCrIZCJfPfs5sAyNbPp4WRDZf5HMHTtviweIQNxDT4RwHcEP2d4O8jelgVCBVHoAQhbDDozsr1IJLQfOEQ==", "signatures": [{"sig": "MEQCIB4FPsUPoB7OieHWJGmndLQLsqtveLrmBvPeqn+6OqFgAiBDcrP9SVB5sgEgwpOzT2TTPmrK+Kl2bIEstZx4323i9A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135635}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/mistral", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "06496baea37155101d2843abb308b07ccfb3895d", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.3.tgz", "fileCount": 10, "integrity": "sha512-x78nUujKfRniC8oh7OZKh+VSyJdxTL4QCrPODIe55xM9jKIas6pkk+sRNmMePZ3la82/EdF4vQaYbDOJ80z0IA==", "signatures": [{"sig": "MEQCICyngyqe7Wf7/Sy4rTXITFt59T3JlJOzkldbZUP/LV1cAiAGqpe2KIpVmyfJ4o964d35Vfk8lB5Q8fvCiTkwbcG+NA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135731}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/mistral", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "61103aeb84a2fe4514512e2c74482ba2bd24e20e", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.4.tgz", "fileCount": 10, "integrity": "sha512-wcbJXQdkJD6n6FoSdDw1k8uePp7vrlOeIyDCC1+TP2v0drfiZQAHtHncEK7btisJ1S2KpBJQ3W5NEVGAcXmVtQ==", "signatures": [{"sig": "MEUCIQCbQ+DmtB0u56WpBONvGHAIxLl79WyemU47dQXSs/2fGQIgdETn663zhCGhdmiTmLH+QR7AmClCFT+2ks/eWN4xox8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135827}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/mistral", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8905e13d274d424769dddf7461b0028f288496f2", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.5.tgz", "fileCount": 10, "integrity": "sha512-Knofe2cAURWaunECx9HP+darFe6MJ7CaZ0sW/JRgqCOxSDzRDi3hr9Xr4GGHJu0JoDb5+bUOV0J+2kPHbVKyxQ==", "signatures": [{"sig": "MEUCIQC7SJBCoJteb9MT3aZ45Zv01koVyJbpfS+HjJX0V8RxKwIgJbKPUB9iDtSq9Z/6ekd1VHw8cK5hAMmiLUMaDI4pJOI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135923}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/mistral", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4066f01842b5d3a6c8132913e6a368b418a87c3c", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.6.tgz", "fileCount": 10, "integrity": "sha512-HrZGZuf1BLcYurpysYepYp4ntmzRMAoWcazXtv+Gv/2cNlqj1yKj9Mso4LSgGp7+99gDB8l5qTcvjzpcNc0Xbg==", "signatures": [{"sig": "MEUCID7NWJUpzK9pRBNk5cVj2FzkIBfuuoaQJJuIMwyZNuxZAiEA98pbEouVTAH5O2K5hQkle9ElfoqpszYzg4LupBmyCxU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 136046}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/mistral", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "431fe6352f1c1c2076257edc7adb949951dfa77d", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.7.tgz", "fileCount": 10, "integrity": "sha512-5YfNGD9zJ1/0bT2hEPLfKMFRZqP/cfaxJfyKILLuZYnduyfhmsxJroOPqOqmnJKxf1620Ja3hrhWO86DKeIZNw==", "signatures": [{"sig": "MEQCICCzyRiU8XNH+kyxD49JiLFhU61QCaJ8HRJTKibt1YkZAiBPdqQwzJzpBSR98v2aYVe2ZtmSZxiSCnf5ePL875qI+A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140660}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/mistral", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "90e970a6dcc06f3e73f27d60e71a6df5ea13eaa8", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.8.tgz", "fileCount": 10, "integrity": "sha512-8KeDekwsjqh6TyqvgieIeOLO2uQvWI/Eynn1oG9qsxwo+B6+IdKSUsaBspAIOw1vQUn38mn9DixSRDog+9dhrQ==", "signatures": [{"sig": "MEYCIQDFPxXFcRlleQr2ZDADzsqgsU96jZJ7Wse/pvaaCAgN6wIhAL9iImOk/U/qIRSG5zsx+70pZ21ztp31H7UpCr6IcRlX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 144072}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/mistral", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f05a69af8e418e7d05388a7df27e313c23abebbf", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.9.tgz", "fileCount": 10, "integrity": "sha512-dNEuvs7PNDV5NOUYq1aDU8N/PTwog1Vx85i1ZLXu1yMzXLIquyosjEZk36x1GxjRkmS3i6Hm+09AVG6GrbGGgA==", "signatures": [{"sig": "MEYCIQDmbj9bdk8HJwNUot744PMT0sNTiD4ShuPQskbU5O9OQAIhAKMua/LGVWsfFLzotZhzo0hgdslojxgN164S8v9//K8f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 144168}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/mistral", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4f15c8fa8632918909fd4c26d2c0e16fa98b2fdc", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.10.tgz", "fileCount": 10, "integrity": "sha512-XSjUCjy8Xh70QcNxYF1SCsAba2nbi67ekWDnocS4/A7HnhahJSFKC2v/3bYyOxOYX1c875SdKDEdgE9yqVax1Q==", "signatures": [{"sig": "MEQCIGniQ8SSStjToI3nFKaBPc0Mfbl6nawDkZgJdVMOVQ2BAiAUiRmqSfUdjI3UD3SWmimMvRk8tmgmV5LeizttmO23jw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 144266}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/mistral", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "85fe6f36bcd7551dd41596e3a1dcc63dbcde6693", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.11.tgz", "fileCount": 10, "integrity": "sha512-PvjmUrtyLZRpmdogPbDXnzuLnFdGlX3I41YT+i2ij5RhZqi+s+BT3L8/01eynT2Z4KIhtXr/GLiw1McTFEjP4A==", "signatures": [{"sig": "MEUCIEKSo3Ro0KaZOglCXzBvMbZDz9ifbpogD69+iiRs9msRAiEAuHCYR9UNDvwZPDByMAyA+0T1VAx5o/OhMeN2xJXvoxk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 144352}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/mistral", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8c5634f2b0c15042b8cdcceddfed16aa6ef6985e", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.12.tgz", "fileCount": 10, "integrity": "sha512-6qoT2gNrwzXPpJ3dJTEYuuYZVXrAGgV9UTqAzzD+ybHuJcoAtdjns3woF7fOuTJ9TcrESpaaD4p7Cg5ZsGgqug==", "signatures": [{"sig": "MEYCIQCjlv3uGQ9IQvcswbMv9zasyvQAYR9/hmRQCZeXbIIW0gIhAKBxIfpmMyifnchJHK+mcD61BQeUv/SmssBe5DuwsgU8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 144476}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/mistral", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a55fc46d6def91d98833b65aebf77fb77cb176a1", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.13.tgz", "fileCount": 10, "integrity": "sha512-yiDfwX8TaNYWEwGk0FFWJVNAU6SqFjaHBHNEwSp6FP6G4YDKo5mLDeRZw3RqWOlqHVkme4PdgqhkYFl+WNt8MA==", "signatures": [{"sig": "MEQCIBuRN/OZXGZGXKCXMvgTECg4UHX58oIcs1QtL7Syr0e7AiACnrTj4s9TabApk6AqfdkTGVUs8KBlP8XAtsN2Iluwuw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 145158}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/mistral", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "818c3879fb00d0ab6646bc940142647af64d6856", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.14.tgz", "fileCount": 10, "integrity": "sha512-lLMKplE8206vyHcWvVguCK8YnsdfOCqlorGbvQRuEE0BqBA23M+ByVicCLzHQyiy8fnC+t/R8W9uNs+QbHn05A==", "signatures": [{"sig": "MEQCICSqSsvPdgN8eVrh89TIVHpMjJ8Eco/FhBVzYiDpx2MwAiAZgjcHa4OJHiEMUp9ShtVadF2zvsA5D1JkOZ/9g6DIpA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 145829}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/mistral", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6f9998013af9d0781d69575482c34ea02b7af85b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.15.tgz", "fileCount": 10, "integrity": "sha512-Mh2Z+AS1CHvaPDcWisDr+KhNVhozne9x5MwvndPUdQYw6ocdjTDdM/3h6Ms/FgrERkwVkQg8X/uryByKMzi7tg==", "signatures": [{"sig": "MEYCIQDBaqb11n4H8OXM0y9FoL+wR/zb/f9TQ+DjSEwdtOcpmwIhAK4ilp2442wIyIBoXrahXTaLoQzJcnVSSotTyiUlV9YE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150576}, "engines": {"node": ">=18"}}, "1.1.16": {"name": "@ai-sdk/mistral", "version": "1.1.16", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "59055133d12a31a2e9114b6caf2205daf22bd4a8", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.16.tgz", "fileCount": 10, "integrity": "sha512-u76AF2Ta9deR4n1itm6Ix6NPc5uB5TV7oJPiT2GhE2UZUn8U1n/RocdzKaxID7Iy7tM1Fv2rTGt/JK8XQzUCjQ==", "signatures": [{"sig": "MEUCIFzXwv52arpmA2kiDzfvYUEDavWY5zuQ0BvkZUCrGjknAiEA3HYANxEmu9m2DS+NonOtp5d1ayxUELQefDJD/6jCTAg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150674}, "engines": {"node": ">=18"}}, "1.1.17": {"name": "@ai-sdk/mistral", "version": "1.1.17", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7cac721e273ef81d1da30ed8ac13101cbed0ed76", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.17.tgz", "fileCount": 10, "integrity": "sha512-v0B6nMU6ZmViwj286HF3SMH1CWkIvRemAYw+0tyYi3pWOYC7td564Llnoqm2+EfKMfUNUCtqA4gj2xBr5cS20w==", "signatures": [{"sig": "MEUCIQDbtgerBg1nNVETLiY2cb9i5YCYEYTmOPc47wBiu2bIXgIgS8tsleF+L85bNUY0DLK6sckFRNGdQ+slvevOhiAYfCI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150800}, "engines": {"node": ">=18"}}, "1.1.18": {"name": "@ai-sdk/mistral", "version": "1.1.18", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3dc764f36dca17ac78d7d3940585f4b42865a666", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.18.tgz", "fileCount": 10, "integrity": "sha512-on5vJzTDTKQ9jzx3ZWBbtPfuvAtD7/r3/dHMl22NZZOcspafT63/9mJUbeOoslUvTN2m/SsDsjT+09Aq49aztA==", "signatures": [{"sig": "MEUCIQD17OZQfLPlqAPXTk5FB05CA1+YyXa+P2EgrmldMda2AgIgd+WGtQqdiapkjCtXr9HzMog43eSqrj95VT6NNSFwq70=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149562}, "engines": {"node": ">=18"}}, "1.1.19": {"name": "@ai-sdk/mistral", "version": "1.1.19", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9c65e77f24b95665f63642b63eef20e073085e17", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.1.19.tgz", "fileCount": 10, "integrity": "sha512-W0BtZhFO38YdYNgPgZ6gbODlWS5U3qRkVxwKzaN2dkyMPby9zwfkLTFm+P20DtR/cTH/q8RautI0AI0hLhec9A==", "signatures": [{"sig": "MEUCIQDLR44uiQ0i9xP8Bx2oWNI5HY+d/h2wtRZc6rlF7PLHLQIgDd0sitaXhjPhHQyLuJRdXDX70OR6A3jEGSuiB8igRgI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149660}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/mistral", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a7502485c2e08202d0cb9ceb5fb5c983f2b1c55e", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-x09jwW6Ok68gbbOu+2ehXhAtR3u3DyE1g9QbNd0iRuDGV4GTf3mcef2JlrLSyr9EEDts6izB6jdl1CQYPnCjlA==", "signatures": [{"sig": "MEUCIQCWLj4sff9hdlxT9AG6udzXtGO+TEfVUo/dSC/x3E7GFwIgIbhu6TNazyyfFebx4keeImLQHKfQM7FeK28nsf46R7g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149827}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/mistral", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "54aadf3c657da305f3bcca4866684f9dc0dbeb23", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.1.tgz", "fileCount": 10, "integrity": "sha512-+7+x/IeDKbG/MJPhSS1v7Do0WOnru5BOBl7ro9p1LuAaogCKHQhTyHPR/KLrTAW4FvmersqCAAQrc/QgvCQKVA==", "signatures": [{"sig": "MEQCIEiJkBU6XVHwojNtB6dF3Olc62htR5vMooOg1d6kzzBrAiBLlauq4yps6kMk/IMLpnpH5Rw5dmgBzCWERbQ7hQVLEg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149923}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/mistral", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a94f6ebaa136db9427126c6bebb45b99bf2d91bd", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.2.tgz", "fileCount": 10, "integrity": "sha512-1j2P7ghnFT/8a18O4VXCW3DPWYBIASjYwV9e+P8IuQwOKsdLryK/1j99cdWx48gc37wO6RjPPnQx/5AHsyWVYA==", "signatures": [{"sig": "MEUCIQC9zd+FZT/cKNEGSHZ4YsgzzNSL2cMaNJzlYm9f0NJBvAIgVOWuBbABeISDKYI9QyBPTj1N7stl2St/8XbWWW9ZPf8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150019}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/mistral", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b70b847b6d98784dcb8c54130c50567ab95612bc", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.3.tgz", "fileCount": 10, "integrity": "sha512-t24fP8Qk7vw0rrp7YbAAbf3OnC/yLBe2cwiDfhDU/pF9zjBKhOKvsCyGENP57q/z3DibibP/AVPNjdCRo+FbBw==", "signatures": [{"sig": "MEUCIQCiMXwZcmzZ7p296X0BSdoxc3szxO6srvm+CI+PmkuPUwIgSg9YCMk9azg5pnlx0U8T1s1uRsWqxguwCvhzjiL6fo8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150115}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2bb3c0a20f2fef61c5e8ad8323114a4c049efc4f", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.0.tgz", "fileCount": 10, "integrity": "sha512-sO27vTfEV4CKSnulPzysV2P9SUXX1pjhX/Mm1hU3cTd5HdOVDoW+u/PIub3DktQiBWMwNdT6KFxV31d4dDW0Hg==", "signatures": [{"sig": "MEYCIQCw2+rPHH8s2QsrbtKuACs6GuPtIwQVfKokqe4ynDUpvwIhALgoGfyfpXQGji8quvfXWkZoc98vzD1NnOanoYGZRbUZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150396}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/mistral", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d3328d7e8de96cf3d4931f30e2d38b2156f54492", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.4.tgz", "fileCount": 10, "integrity": "sha512-gEJSciOKsGWGokoAuxxirnxE1+g9r+ZrDeRqrLJKhucVYz1NelzTDTuSa72ui+kRkrfTYPxi1Kt0QrTZWcKPDA==", "signatures": [{"sig": "MEUCIBH63toCl+TT8A1niTMc/QVmTw0WZO0i4sFi5Aeemxn5AiEA438FzfPxzVT8KoLyfo/2w+Lz8O+AoOeI8DXLSuFxnmM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150275}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "53e2a58a1548c32fd29bb3376e0f56027535a6d8", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-2M4qej3LD9ixwO5hHuwzKjq7PbHu/a86JUOTc286+64oEVCMtsxOTx0DhB9vRdxqELmEbGTJFTUuZ6UPRIHRCA==", "signatures": [{"sig": "MEUCIQDz7EyDMJhzVa+cR5qzftOd2LMs+ZsZ1tQIezUtfOmM4QIgYZZyzsoxlDkJszdO/04380SheFOuWubpOvY1djAZbHg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 151454}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "618cc7c068f69136b9b300c285372fc49fca4cad", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.2.tgz", "fileCount": 10, "integrity": "sha512-9eoJQAq2sYXoIp6a1rCZabcdgErJ0rh2bGJtGNnHtugqXpAQA2E7vFU9zU4XTC5r6PNKWCxmHtMg1rb7qOSJVQ==", "signatures": [{"sig": "MEUCIDMMENQ68VomsxnduJqR/N9VRYNXS613FXjRDJsZZTA+AiEAuHbprn30Y2WwDH8SK3UyNVC0ukyeqpvDQkMVHiixobQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 148635}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/mistral", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2b772198013b949344ffe718812213cc705775f9", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.5.tgz", "fileCount": 10, "integrity": "sha512-698r7pufaoYCmlVeb0rAX6zDeha+C/jZJajZrlxfAOcJq1kNobt7yD9I5Mr7zKo4E7ZsXn30idQOOwUUMpVvQg==", "signatures": [{"sig": "MEQCIGCPk1OfY9s/Q1IcnBo9Ji0v0mZGaDM9FHGJb1P3tSvkAiBlLUPQc7tAfoWmtcz91qReFeIS1iKS6PltaGYNEctAZw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150398}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/mistral", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7be086c06afed9e32d24d8d622d03c705db70af8", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.6.tgz", "fileCount": 10, "integrity": "sha512-Kbwk3l6oHR2BSCJH8TJEqIs3agIHbSMQSq+6+2xi/gZBoviJiWqU09cBcAj8S9AuWhGAsQ/6isdaVXHo09y4LQ==", "signatures": [{"sig": "MEQCICqY+dfwi6PmMAjYqNeZbEeI70KQqpKFyio/RxPMVlmtAiBXRVR3y9AErMn9Mh27pxU6McGUuHRrNrkfEGaVSs+x6Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150521}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd6207f945971f3ede5517c1154912c06bb840c5", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.3.tgz", "fileCount": 10, "integrity": "sha512-593PNj0yPtGMsrLOWNQ0kdWy8oWxAAIq0crg40XmuD1FHGpzEOlJCO6+sSs3VcmGex3eJ2VgT7ttfPyKINtmUw==", "signatures": [{"sig": "MEUCIQD8ufSrvhsIRdSeLYiGk1pB+qTQUBhgPlrHphheNikRHQIgRAgDcELpu06/r3XtJAJhYg6ngwfvEozTl+FoCpFvcM8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 147260}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "827c04a6cb763f49747067b46fea94b4bd789c78", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.4.tgz", "fileCount": 10, "integrity": "sha512-QXROmr7A4JejoyunYkDhg/v5iJYWfqDQW8rK/lwLeyF1vl58cA1t3SPocxxtNKMJ18kHpKjCXT7HVhH926hmbA==", "signatures": [{"sig": "MEYCIQDdle96rXvGjBDV2sZRbYKHroCQBkVVl8td4BJO5QPhQQIhAO6XLWGmkBey/UFRzgGynXulcPoYiUSGGCb4PanJ7LjO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 147366}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cf66c69898ce81ce1b86266a71f16a7234691b1a", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.5.tgz", "fileCount": 10, "integrity": "sha512-DZtT910PYoQRpLAUuIBRcUepFAYQZTjQ6FWFM+ed8tmWeMqvm5vn/99gsiCq5sIBzZ9N/shgRL2AHx526G5NTg==", "signatures": [{"sig": "MEYCIQCE8eu18/t8XnVse59qNsU5M8SQVwSi9g590AZnofXSTQIhALtZGN0ngb9CFdySOQxw9SLSZtoEiQp10j6t19TUTEnB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 146324}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "782b6caf18f48978bc1750f15291f8617038e2a6", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.6.tgz", "fileCount": 10, "integrity": "sha512-VP2n2RHRv+M+goFiz+t+HGE3xqBRfr4JQ30YHHq06hrmXQAcMU57iVryJ3y8g4my79qY+W0QP2sUhZlL0e6U8w==", "signatures": [{"sig": "MEUCIBeOGoGsp3sKG0j2mUAFMpj1Po6ETxez9F9nzUbtJLP6AiEA2+Z/ZXqnyaK6iPJgDWm5F1dwM5e9EfRbRGnPjI/FKQA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 146370}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/mistral", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aef5e11f2393527889c5c170b39a73e884375b97", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.7.tgz", "fileCount": 10, "integrity": "sha512-MbOMGfnHKcsvjbv4d6OT7Oaz+Wp4jD8yityqC4hASoKoW1s7L52woz25ES8RgAgTRlfbEZ3MOxEzLu58I228bQ==", "signatures": [{"sig": "MEUCIQDYCzFAEHkx88zKnnqQzMIHnEishXkmeaErbkVnk/EoxgIgEhgk6j9nyNERD0gkFjBIgbo80ihf2wUXFyAj0H6T+M4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150644}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "80ed5b0bc2a64f90df910655924a4809be41effd", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.7.tgz", "fileCount": 10, "integrity": "sha512-hM0uIFTru9trFuH6GHQTqd1OshUlhqqRyzP6Mxh6U/fG22zSkfFWkfAONCMyMLQED+HZ9gkQ9iUw3sgjlG+cxQ==", "signatures": [{"sig": "MEYCIQDu5vGkLxRfGauisQsNZlitMNT+u1Hw+EN1qbwJ8tGGMQIhALW6Nmw4A4aX+Uq35+qSL6bpKINQx8CPsbQiBD83SCOQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149379}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "40c0231b6b9e642ad93b6b55a15257a6c3dad6af", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.8.tgz", "fileCount": 10, "integrity": "sha512-4turpXeHJoq0swjYSiwSTQ4a9z2juYEcFx1g4jEfEgBkNNC+qaqN6jbhJqpeZTeH34YmFUmdTIbn0+Y9XNAR9w==", "signatures": [{"sig": "MEQCIQCxsONYf3LFBO+3zppXqIEBuxQ7ISBJboztoCwnqybXJAIfNVJOH3ofUj0YdKMQTw/1tSrjyS1ibvNTKjSiythbTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150910}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a3f9e40a102d1b0b9d9f0af23302990e10237cab", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.9.tgz", "fileCount": 10, "integrity": "sha512-yaFqEy0JCsj3x7CoZBnJinh9IaroykxdH0cQnKWGxlDSzn2d7iFUjr2oxjNX/KOPfQu2MTfz3HW0S0uyZYIzVg==", "signatures": [{"sig": "MEYCIQCtL8WN2mYuooYa5GozJaCGtyrZyQQ9MmFS7GkFre2GzAIhAOl7u5XguXDT0v0ppxW4ODasUWc3npDFyMy8jS1S12rt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150960}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e657225233cd857644b74c8cdbba0a29f48a512b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.10.tgz", "fileCount": 10, "integrity": "sha512-7u2AudsxaITrHK/14a8JbU/SQSF+hKnQ2bNn9JDhlo+OrQL0U3HOqNUZII0eupO7XsKU8SBSGmBkCxcX8xX2Ew==", "signatures": [{"sig": "MEUCIQDJPr/xCUDcFk11MIg9I1R/XXOWz8+1TD+jabk3CJETiAIgaK9AchsIN9DMbs5j+VZroTKx3c+/WSGaE1O3w/uQt1U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 151114}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2f2b25f44195fcb8ed917a8ea578dbb3d989d0e4", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.11.tgz", "fileCount": 10, "integrity": "sha512-S8cMY7QyDgFprkXZJykdjkaSxKpQitvfkp/Mba1PzmG5bXz2GHkZxvt8YZRc1JrgKqqhsAH9VFifaooX6XSTwQ==", "signatures": [{"sig": "MEUCIQDc9g3VkHehYPLcfeHBbdlH7RkWuKHJJvrVraR6R1MK4gIgfamCz0MCvPAUhoKmoLt5W3rNADvEEhCBhHKgYP/mVs4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 151476}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f1fd76e3ffdd524f891a2ec48f975223ec99c10", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.12.tgz", "fileCount": 10, "integrity": "sha512-a4oEATC+vmSPXV329PXmJcIDKYdAVpwIXZyS3lVvouHg+JA/8TR1PXwXf+jF/n6zYsVGWmWJF0MsLICcEOTnRA==", "signatures": [{"sig": "MEQCIEELCx2Jp3F98+T60YPRKUYBS+yBIBx1Ulp6Iv/qvWC9AiBME9+wGMQKgt4UIuC9BjvXSIVPhrX8XnONYVliWSy6kA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 148951}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "40b5cdffbee57a9f2150e94216ea1957d6dbdcb0", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.13.tgz", "fileCount": 10, "integrity": "sha512-rn0UBK33gcPxlXPj6eV9dQFRNGwoLkGKahhS2sZrWirO3bvTlT9jonLNs4hPECkrf9CPTx+gsSrngB1Ae/PzHA==", "signatures": [{"sig": "MEYCIQCy6vB3PwKrUwI6xzsl/KmyR3umHU2F0gCRm1uC6Cul6gIhAPsbIGOmCZwA3mhymx7RBfyPmqiu2bPaBU9Le/DhYnxT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 148985}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f68a5fafa0cd451700ceec958c4a1f6901e57c5", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.14.tgz", "fileCount": 10, "integrity": "sha512-mChWzRAysYSxyTk3gIeEqduvScXN2A98gUlK699K7uzM/fgq00Al1TotUTfDqyLkQA1UUgXI74vgNBshZjCBPg==", "signatures": [{"sig": "MEQCIHeK7Sly4NG2EcHs/55Lgar4+x8BsoXldS2g4a51nRM0AiAYJTClSWhmc2KsH2wLFB4nJ7Rhjiw40Su1eWdcXGgLbA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 149171}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "871be8a69bd1b25c1423de072c5ccfce656714a6", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.15.tgz", "fileCount": 10, "integrity": "sha512-Ed5XYf628hk7ozXL22Bu3/qmLyxrcd0Q0vMhpnemjqx9luRBOz69tNyZc8xxZnns/y98GeJQDT1d1GzHo8AvQA==", "signatures": [{"sig": "MEYCIQD2xdQ1D8gyZlA5z91joqoojUSm/vfueJs5i7vvbyxhVwIhAOJVzdYTy/CIyHO6TYixsz3JJlj8PlO/lXnRcEqlN64r", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150096}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/mistral", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bc5d9923c11b5611d3c8a3ed483aa29596f34960", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-1.2.8.tgz", "fileCount": 10, "integrity": "sha512-lv857D9UJqCVxiq2Fcu7mSPTypEHBUqLl1K+lCaP6X/7QAkcaxI36QDONG+tOhGHJOXTsS114u8lrUTaEiGXbg==", "signatures": [{"sig": "MEQCIE2FITWqIBxO/NMNa5h+cbscDSVaDX23IGNqy4A324rWAiA06rivGBQOVltAQCcV4Sth+xnFHJXQdfRMSeJGVvLSDg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150728}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9770f9c84b78dea37514a4a5f073c66944a52cfb", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.16.tgz", "fileCount": 10, "integrity": "sha512-zWHGmO8bvb5eZQbLR0QpDApGhpEawIIbc37qUHUIy3xidh+BhA5ZUIIiQdNiyohLB19mLZ5rbEeUJGNSHdlQjQ==", "signatures": [{"sig": "MEUCIEvMWX9wmSHeqSCbSs+gdrhbJR/WPT+gtq+9aqdnT1FqAiEAyftasauldnFjKl9rGR3t3RFUNLwJz0cn2XzBHpOKW0g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150212}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3ec40254628c5a7b140a52cc17c2255b07afe921", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.17.tgz", "fileCount": 10, "integrity": "sha512-as8OKpgXbMDCCUs3I3CmDaORqOsPT3cTMQoPCh4fNzcYrYLakxDf60jvKpJrLd8jzSfSdIbIaK1MZoO0tVogXA==", "signatures": [{"sig": "MEQCICdVmFsaX3xme1GG90ZG/YDH3+pvVsPgxYmADVrMg12tAiB9IefGnE943oXybqthOr6vQSJckuSF+Vk+1WLYhxNiTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150328}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3a6ad05eb63362d16b6e09ba904a27b1238e6c8b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.18.tgz", "fileCount": 10, "integrity": "sha512-yKbd9NfAofMgHNDCxrCFuGEMghXDTvjC3I3FbrEQcA5uXAHFIlYC4btjQqSlb1IdvNXb0/soFjHeU8ext0ndzA==", "signatures": [{"sig": "MEUCIF8eQyRlX6qjaPC/+mqVmiJiyE5ea/njC0tnpgXqX1p2AiEAn3HE03J7QXtnc/lAp5JMT7TpKv4K9TebsqUJKr120ps=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150444}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/mistral", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "49f694804db408437990d4c0fec86cbb7734d72b", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-canary.19.tgz", "fileCount": 10, "integrity": "sha512-OjGE7CiZDKJzy+h4u+NxJHnhs7psJqiXDpaT/TIjmuu4Mn5yZghyHAfdNidSzXsQS/Cwfvx2HnMO3sudZeccXw==", "signatures": [{"sig": "MEUCIQDWDtOgeuGIq/jYN4xI38ZoZwJYL5Inte4cz/j3kSDlZwIgQ4a6B/36EVuKENrn+nac95qFJois5WCDsANCBjT4DmY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150561}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/mistral", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "afc17a8c30690c104b5ad1d0e1b1695956ff4093", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-GhZjJTVMuyjuKuva7O8zi6O2sHvIlZbj4GttvJcf4+wL/Bey7qS91UiT/zcGliD7kwxeu6SX2ruDdOFstl3aMA==", "signatures": [{"sig": "MEQCIGz935UfQx9FtoarUhO3tHS9MaY/cLwVgxIXDFKwB+6iAiAmbB3yIbu5SEtOAWZo7dbZM88aSajKt+MsgL/R74hsFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150702}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/mistral", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "ff06d7a350cc551ae3fd0edc11a03cc356b3c7da", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-WtSBtlyO6149TsUN5sFQQ8wyTWv6OmFPDHAoth89Fgrj01REZjYIlLRZSn2gdjjim/8TAe924GJht/sXLoB83A==", "signatures": [{"sig": "MEUCIQD1TTI5oKa7/0ZFwxyQsbNj0Hl6EG2Mia9Kfpc9VI2HFwIgL0YcSmKrEazKD43b6tHnJgSNB4wv9B+z8uaPHzHjN14=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150849}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/mistral", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "706adc34d0b1bb982b12115fb03f3a83749ae06d", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-6XdmxcRD08SyTQM7zdZMSGUugwv6DtHATIa2jelXH6MZJV53KBgixo5rkDWlptCwJgrYXz1a7vS9lwpU65DJBg==", "signatures": [{"sig": "MEUCIB69n8mNopBdtlMtaTrpDO5AxW53GYkpJB4Z0QvrthyVAiEAz/XQI7mMemdK18L+xL16PRDdn+/19O4GbGpaGNP6TLo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 150996}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/mistral", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-8u3nF+/3rpVtlV+OPw7RtqqrTjzLJLAJT8I9kTAnQO/MShCc4PnGamDSiTZEmM/YmjzpQwu2jL4wrZ7lPRyH2g==", "shasum": "d2309360e4a4765473e523272f54c076a99cb057", "tarball": "https://registry.npmjs.org/@ai-sdk/mistral/-/mistral-2.0.0-alpha.4.tgz", "fileCount": 10, "unpackedSize": 151143, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHGz+PEhAj0uH0jy0cHRrUfJltYP13TruFnu/tqJFQkcAiBbYFdyqgzB9IU84pHN18LOrirSksjCpeOujXUJrHNV9A=="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:02.680Z", "cachedAt": 1748373701721}