{"name": "@ai-sdk/google", "dist-tags": {"snapshot": "0.0.0-85f9a635-20240518005312", "latest": "1.2.18", "canary": "2.0.0-canary.20", "alpha": "2.0.0-alpha.4"}, "versions": {"0.0.0": {"name": "@ai-sdk/google", "version": "0.0.0", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.0"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "613bb02b30f250b47d62ffd58f4de94ac091c6b6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.0.tgz", "fileCount": 9, "integrity": "sha512-yrdObF5ARasTKZeBIwSDR/Bh9kv88NIho9+hhhaMlzYqaVAM57ePvkBUOjoCa7DWLBwS50zYEQ+YoPxxbl1NCw==", "signatures": [{"sig": "MEUCIQDocOietCtiyTbv9MnpBKKzOryGwMwUxSYRY8kZyBp3jgIgBBiDtdmx8j64hnmQyoV1oDv2mWBvLsKHI1BpPokGm1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84234}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.1": {"name": "@ai-sdk/google", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a551817b972940a25a674ca061e2dc6a687f9b9d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.1.tgz", "fileCount": 3, "integrity": "sha512-F4dNUwWIKNmq8SXpMW4Tp+a53ZMcDerb69uOTlv6pRV0sP8mjkW/GXTP+rAr08dXUxLMnGWxKJseKL/5kTCaZA==", "signatures": [{"sig": "MEUCIAlPYZNZtYtYUZ06P8kGl9wPocejzVWRBaFU1AsXmSgwAiEAlWV1O10lRc6tTFmopw2rAayLi/ewiAFI3uqsEPI77lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3664}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.2": {"name": "@ai-sdk/google", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5625477d8f3c6950b884a49252fd4df5ac45ea41", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-Y2QCWlZtAq9L0bW83E3dT4aYHBkdIo6YZQfkjFvV+3ze/viPMMIVjlDftdI35N2HU83T0krVjQOSmEjAfNHbzQ==", "signatures": [{"sig": "MEUCIQDNPXHjAu7cTzqN+TjoHqkCGCEw7JiRaq/3M/5/SOdnGAIgTanWU9XWrfyfq3ate0PC4mPSIbMxTWf+IUn6D+NmQuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100627}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.3": {"name": "@ai-sdk/google", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a20c45ac1b6fb75d4f122f5bbe4fe9792b97823b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-XAhhC4foc66EUyq+wspTdbLXu+JdsEcOYGjYXRe/7tEz9t0stiAaD+EiflGB8Pstr6tkWIrCtAOjJkysN6Wf7w==", "signatures": [{"sig": "MEUCIAcp+DINpOA3jIcV1hXeBqNcKL3ZClcbFlzrEuMfEFr/AiEA3dirDV+4CjERHlG9GvUX8NQsMvr2KfC4771R3un7API=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101942}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.4": {"name": "@ai-sdk/google", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f771ed669def99be57c093274e7239eb4fec4b8b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-jJyKgJvsma2YgfBnkvzHavL31kWimc0P3T7qrEE/ntMO91MTvZXsv2yox9DpymHfcJB+od681kyJlJyFE1x15w==", "signatures": [{"sig": "MEQCIElAsDoVna69Q4aHa0Yc2H9zk0k4zQhYg9Bps4kPmpmvAiBjkbbwkIBg2w8JKLZHVT9QlyBW9LVocbVyhEKktdxk5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109995}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/google", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3c3b56caadbb7dfb97572930744ab8259765959c", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-NDhFcBart5Vg4Grra5DK4E5gqu/WKQGdTRDpIb0y1GanqluE0umWy1apDaVbQpny2GPAFuqgmDRU5iwEOQ40JA==", "signatures": [{"sig": "MEUCIQDDmTTMg89AUafjzVmYskxbjRaDRR8E5IpUd39ngOYmQwIgaiXfz32DXf6r5n0ByAuDq7CcyzcVPRobNvh9FyGCIOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110330}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/google", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "0.0.1", "@ai-sdk/provider-utils": "0.0.2"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "714cc88086b36fd573d085e39b4cda08074ec269", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-Amp+Y8rwsDJn5Mpas+uHz5E/7fskoHjNpguS4fOzV5x22S1MAz5SzCqxdMjBENCUPSNBH+chJoF28Oyt9ze+pg==", "signatures": [{"sig": "MEUCIAwMbQlO5U0zNd9dQ2ucU84kzrGZpDjs/jhLDt+y+lAHAiEA86mekliHeL24YuVfuun6JpUJ8DtokeZPTxc039Cxhw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110330}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/google", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.3"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1e530c71338918cde2b2167c50be5c3e1867e6af", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-YJnJrkWMdAPhW9wi0Swbfhym30U9n/6KDF2nVob+wAHi96RCUBVNVD0dE02sa16w9QqCHbp6d+OA67LMB9v1yg==", "signatures": [{"sig": "MEQCIEIO/L4RZZpr2sLSFSqXninF8thITDjG98GMpuw1sw6aAiAKkwWbWqzHXIQPKBWpzL5N5k++Qts9uL1H2LWy77mIOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111324}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/google", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.4"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46c578399669b4383009bf485646a7eda2d0d33b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-d91h3Lgk2F5ugxQg4H2QCQnMun9F1u9wWG8PeFFPS1wFhBveeVa+xDHBapwKDWk9Q6S19BIllYtmm4LFyrXwhQ==", "signatures": [{"sig": "MEYCIQCQyBtP+jmgHd7OGS8fNPqznABWZaLhJMIghswmI7cTgAIhAL2hMWd4t16pAktziSg72TsJPipoH9fDpwu420r8iTRi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113481}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/google", "version": "0.0.9", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.5"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "09d26e6b65714f4a4f3edeb0f47d1e1fff7cadc6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-xe00QafJTPWx4frJUb2M1LrNjTrMEUuxFVqQd5P6R4nIgkKTmFUFdXAnHZ+gRB4zUsKvqQ9ZfiAWxvuXQtQXGw==", "signatures": [{"sig": "MEYCIQCXVTqNA/KcS9mTHJ2PThYrcaSndnWujcgZRD/OtnreWwIhAPFbFNc9NwB6C5PnOg4tNLPH2MlSnJD9ayiZgJpL87JI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113481}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/google", "version": "0.0.10", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.6"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "81b17506056741eeb6cbed2d55967039e103e09a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-3TYNGrGnzQpUSwGebSVch3FH477FzDv7wBTw9roBh2sjqr6xGd7lmFeNxm9/wXoZ1ryEM6gHuf2LLot3jiBXLA==", "signatures": [{"sig": "MEUCIBJ4/1hBHc5utdWK1XJj9pBoizu9oDmZo0OHdg1UFseyAiEAqmDM6VtLBRQ+XusJoa5jyYHTsxtRgh4irrbI0NaXOwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114067}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/google", "version": "0.0.11", "dependencies": {"@ai-sdk/provider": "0.0.4", "@ai-sdk/provider-utils": "0.0.7"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2b036b73561e6de1813f31352416e300360ba648", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-oeZLRr/dKETdZ51a/MZ7uopA3h76gtVQLj5hRr+RO2RCLAcyRNNQdu00OWdd9dgsKXNuqUqxQGI+aB1+qKKesg==", "signatures": [{"sig": "MEUCIAOq9HlIoDfZj6gDklzfxX1OlkgKydXRcKNUzIr3I2Z6AiEA78qxxvIi39J+2u6mNX12s5PgElfy1IlZp3WOagMQYOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114067}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/google", "version": "0.0.12", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9c7639ad4e42aff0ce576cb6d83341d092465565", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-j+LhiTXz77jbUL//46Bhm4owUMgv/N8i/y3mhS3/Q6wtYc99si05+CnxTLCq3kkVfd5GZOFzU9eNu3usgRaYUQ==", "signatures": [{"sig": "MEYCIQDEvfuvg4P1B3trBtaHxL0YFpdEB/04mj4disiEGqFTuwIhAKrDpKzCCaHUALtSzanz3EDlrNrNuniu6JPjPISi85NR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114067}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/google", "version": "0.0.13", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eab54cdc35e494afae3c5a71c06f61189defc606", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.13.tgz", "fileCount": 10, "integrity": "sha512-lSLWB4UMM/E/XkvTCZvyK3Gq1CI4fdBWtYFTDaSkxAzqCusOeLv/AIdZgIqhGHO6IofIoRr2fH0uWTl+vl2Fdg==", "signatures": [{"sig": "MEYCIQDJFB/aeLCpCKP1QeHnYGsfTgiZFzsxFVbOcfuCnG+t6AIhAI9KBCZ0oDK62zc7yycgm+8YEhUyg3mIoYWfgnnIb90I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117477}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.14": {"name": "@ai-sdk/google", "version": "0.0.14", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5fe2ad0e11cf1994e6da636b8f30320eba7f6034", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.14.tgz", "fileCount": 10, "integrity": "sha512-4a+g7iZThrTC2nGFf4ud1td2jqJjxr8QGLCP6+xBjRK70DHFf9RZrTxclOz8oXY+IIHYLIo5ybctV9TUH4u1qQ==", "signatures": [{"sig": "MEUCIAacyfWjDGGhBehuFKusYjkXsIIh9cXf4R/woDYk5qYWAiEA5UH5BPLagE7WCb3yuvvMbIzD2Bq8s7oVQVlJJn7wh8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117547}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.0-85f9a635-20240518005312": {"name": "@ai-sdk/google", "version": "0.0.0-85f9a635-20240518005312", "dependencies": {"@ai-sdk/provider": "0.0.0-85f9a635-20240518005312", "@ai-sdk/provider-utils": "0.0.0-85f9a635-20240518005312"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2afbb6d1986fd9d9053d1141f0783332e179256b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.0-85f9a635-20240518005312.tgz", "fileCount": 10, "integrity": "sha512-4lL0Db/9ALVWoXQBkj3UNa8lFyBY/CCMc1YLdjpbiX4JEwmT7XIgG2FjC8BPmPB4P4I4b0EQ7APtdZVURH+EXQ==", "signatures": [{"sig": "MEYCIQCqoHZlIwe2mZLMbK3cZd7upd/5GX2tyRBLg2G9tmF2wAIhAPoeUhA3cb6SQrAQOOTaNdZxx6tfZTqsIotuc1IdU3uU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117586}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.15": {"name": "@ai-sdk/google", "version": "0.0.15", "dependencies": {"@ai-sdk/provider": "0.0.6", "@ai-sdk/provider-utils": "0.0.9"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8502071087d4f8e0f18b285c309c1c300d084bb4", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.15.tgz", "fileCount": 10, "integrity": "sha512-7om08+QvjYk7YyQU/4PolRNLGkDntWoGwKWTtHk4C9AFz1weSA+j1g4EtGyOKX2xCTA3DfQDtzlKbfy3HUDNmw==", "signatures": [{"sig": "MEUCIFW0lOopKO6fCPh92f8oe0i6w1OkGf4fk8R5JmLtPDXXAiEA1aDciq3hT3vtEHJefjImCg3oXkjnAzd0rzFHy8MvAy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117515}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.16": {"name": "@ai-sdk/google", "version": "0.0.16", "dependencies": {"@ai-sdk/provider": "0.0.7", "@ai-sdk/provider-utils": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "96d65ce33e8d5aa11d92d8e821e79418ab511339", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.16.tgz", "fileCount": 10, "integrity": "sha512-hpLbwW0rV5pH0fAqhLOryM13W9vy9hgBTrUmSDzP3p0gj7ihwT/tjZdVsAjaAImO99T0WQQolTxXnl8pLQMWXA==", "signatures": [{"sig": "MEUCIBqKAjEFm6wWjJEs3S8YKMMuI5SbyZ0RnpKZlPfrWlqPAiEA/h2Fw8S13LZEDv67+NdBvDN0h0UasX4SlrZzJeXGILQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117441}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/google", "version": "0.0.17", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "82a0a5d9c855ffdb605ab2144375df4d8e948c6e", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.17.tgz", "fileCount": 10, "integrity": "sha512-waahzRUSHBGPbCnyB5OM1rq+BLkjmLlM2rHrCGAWjFVl3wcGNNUHQ+MJUY0gaeHYpkJ0lga+2AYcQk/Xt3lJ6Q==", "signatures": [{"sig": "MEQCIEq1PRPuXf473VRHMIILgqZXH/sXetzOq++1smklhD9OAiA0pcW20c4LshsEpgwFnM1htz+38yjW36ipKthFHvP4og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123453}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/google", "version": "0.0.18", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0cf2d4828cc14fee3b973d263848385e54830be5", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.18.tgz", "fileCount": 10, "integrity": "sha512-t7S4LS/nRaEmNhFpSGDscAKqjx6lbfYfSVOrITKXbRl0dx7XEQlDJjtNVCHBO1sjxeLyICA2DYWlSxjn5Kp/HA==", "signatures": [{"sig": "MEYCIQDIfw5N9kXU8mBPOUaSmNBjAa7xrDr4Zp920BJwsEv6cwIhAL1gloF/cgTWoc0jwDLL4NdgAKZpbYJMn17lxm1GN7NA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124981}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@ai-sdk/google", "version": "0.0.19", "dependencies": {"@ai-sdk/provider": "0.0.9", "@ai-sdk/provider-utils": "0.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ccf29c400988f6d4827594f11b7f826f9b50bc5", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.19.tgz", "fileCount": 10, "integrity": "sha512-kYTh4zWANYhHJ3bVdgkUXLn2dwlfFmbVD0/IkadH5O31uaEgD3WLp9AuTJzfeI2bSWpLjyYsm90bAu0NyCovXg==", "signatures": [{"sig": "MEQCIHDS73/jbjxk7wQUHVQdHjA8leMT6ILf/LOLAEJW21jhAiA7O2nT5GfZqMSrc4CzDdP0yKOJAd8bb9Zbx5/PmoaDhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126682}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@ai-sdk/google", "version": "0.0.20", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3d06d28a2148ace68974cf1aa41548dddbe894c2", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.20.tgz", "fileCount": 10, "integrity": "sha512-uubp06oJEEluYcuEMr3DfizTwarykINU++dl/vNSZqSqmd7PA0RjgJHSeY33Y5DtdCEs2/vBhdoIL/eaYqQH8g==", "signatures": [{"sig": "MEQCIDrHRrnZBo9INXx8Q2sE7Qjto7UyXgTPWN2a5s1P6KFVAiACJrO3tMLSnLgEgvPrxINxzkEhjPEU1JcCARoWXB5OkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126683}, "engines": {"node": ">=18"}}, "0.0.21": {"name": "@ai-sdk/google", "version": "0.0.21", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "56c084d59cb0bcf537d2235bcbfe86fdb5f01dd5", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.21.tgz", "fileCount": 10, "integrity": "sha512-LsbQ2B91ke36Qcn4yp0g1i5s10BCebh+eBDW0Q+OVh1vk4YqK6JWHrsuoFIXB+THWKRSSouhkgXDdUheOSvWKQ==", "signatures": [{"sig": "MEUCIQDIImGsnH79/+uqKgTgSHSEl4jIhSnrN0VLTlrsre5RLgIgNoYGMzbUQ6cuw1DQyQ6m3irmOeLQ9B6BR0c9axwp0+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133655}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@ai-sdk/google", "version": "0.0.22", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6bff55c1df1c302b5dff8b7b738f857cec5d0ae6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.22.tgz", "fileCount": 10, "integrity": "sha512-Xtj5Z7xAVLQDtXrGteRKrzuByelOxgXe44Td2gn5G/+hyRPw/zZiN2Qxqb4UNrTVyoIoCnY5ARIcrnYeKgKNEg==", "signatures": [{"sig": "MEUCIAsxY5sHhjqxYiBUUPiN3vwbDZeVFwlOxlnaALnSqybXAiEA4EmBhyg/mbFf3JE7TqC0ln6snmL+cq9etxcy4QEz0no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133655}, "engines": {"node": ">=18"}}, "0.0.23": {"name": "@ai-sdk/google", "version": "0.0.23", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "505bf037bd9bb36bfc495d6a7f12834907295ecb", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.23.tgz", "fileCount": 10, "integrity": "sha512-Ariq7Oy6ge02JPqWa900spQewNIdWkSSeZ2Rfe0RLoGa/PDEEU5bQBvUNvRpiTUgGOsmkiUMHY6OJB5L0LDOxQ==", "signatures": [{"sig": "MEYCIQDFPei7BSoSos1PPsMK5Jrh6YEm6wkwSU8nQqwbrlvQegIhANBSxBZU9z6cY0mLxDSr8grpWVnRtYACzf15dp69P+c1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133668}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@ai-sdk/google", "version": "0.0.24", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7e1a6525e061685bcad79406e7b6567551397563", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.24.tgz", "fileCount": 10, "integrity": "sha512-ntF22FiAIj7ZLp3QYYvyCx6nfRByjTAoBcnG/BXKAxDmH8cpJScsIjS+Zz0zGH6Mn/r3Y18t6bj3Bdx4tlTu+Q==", "signatures": [{"sig": "MEUCIHdqJZmZqG6oT7y9k9WcaHN1ui4DoJGDjIJaWo6p6DEUAiEAwaEo39D7+PhqHyaOjT+yZzZPpkIlmZ7UV7NOyiEOKss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134257}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@ai-sdk/google", "version": "0.0.25", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9c19fbe4cf7972890ebd5f6a49a05fc6b07daafb", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.25.tgz", "fileCount": 10, "integrity": "sha512-6av3rgvJJuD1Ud5q5bAsi5Vqx6XfJBwXbC/jrHPMezoGnXMtDahlNLOIH18sYAJ1o10KXs1Y/mXqX4LbFwkb5g==", "signatures": [{"sig": "MEUCIQDdqE6c3E8Lg7rx4+1G4+YeDDMiyO+ACVaqU3ko/DGOZQIgcmzlO6GWP/q8Ugjw2IbFVDNZefzmHTEtc28pfXCpUPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134257}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@ai-sdk/google", "version": "0.0.26", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0831ca3e76211b8890b51df9635770c2d146679c", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.26.tgz", "fileCount": 10, "integrity": "sha512-hR2y2JqByeXnpaqvMMtuJN/WybiltNcIhFxtFEj5Uz0TxznR7aiLH7bfkx6pcIEg113VK93S6u9ragIpDnY2Ag==", "signatures": [{"sig": "MEUCIQCN2TMQIF2waUxXbt1TEk9JoNSazDJ5WhpLLMuD/DvV8gIgB3cFUWIzC8lqVPdfGZzYJIJYf8LTeojpC+5t7FuNMe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134257}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@ai-sdk/google", "version": "0.0.27", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f30c41f2ace51f149edd2a1e7daa6907fb3a6775", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.27.tgz", "fileCount": 10, "integrity": "sha512-HVkTrHq0TA0ynPZk3UBdhIoiAASQSILNS+qIpHbKISlEgxihlbcjO9qneaM8shl4LQL69eH6cM6YTgzAsx22mA==", "signatures": [{"sig": "MEQCIHOEpDtHB4He+TuylJCytNj44b/9UxWUqHn9mv2stQlsAiA10DSMC/AzJg8rrqJv5jxYSev1xWiJCve1AWpZrOCLlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141991}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@ai-sdk/google", "version": "0.0.28", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "72924c411163d9401f6600f93f79aa3158869941", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.28.tgz", "fileCount": 10, "integrity": "sha512-3nRKuMIFTPIqUuvVRHHr3b6kAEHBsBxpMRlwk/EDTFciYh0jKmfhET0anX99hIio5WH00IUtXeg2Br9WX8aXGg==", "signatures": [{"sig": "MEQCIEqLJ4SqEV6O+EAGU4LHC9RfcTT8RvPNY2RGtfeNpVR/AiBEEIC57Mbr5knMTuH8ikmXInhfe30oMvMMbJzTjlf/yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143089}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@ai-sdk/google", "version": "0.0.29", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1408959d08e34e82cf03f47cbb519a2ca9ae0c18", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.29.tgz", "fileCount": 10, "integrity": "sha512-qrXxqx1dZheSefstWxdhEDephj8WdcwlflE7HTVtgOGe+NAQ6JAP/czWL9Os8DzFbpw0EutmIe+bqWUF0cEXAg==", "signatures": [{"sig": "MEUCIGDLkRavuCrC9B9PfNltDxtifUZg4v90Gdigv5HxUx1xAiEA5EBbXY3KGVqL+rhFYmqJz9rYgybWWbHBJnxSfrb/L5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143089}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@ai-sdk/google", "version": "0.0.30", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2ef2349cbce6648a103f29d914be62c1bfbb93af", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.30.tgz", "fileCount": 10, "integrity": "sha512-sGkUc7qyJ5LuIkJ+Lsq+VH8o2521biggU45F1oK3jLi9Y6tMA854ZNye5YhgeWVjv56FOBy/okLYvwxPodHfAw==", "signatures": [{"sig": "MEUCIBUO8r40JA03ThqxXuS3FiTN0ttjih+k5Mj4FBcjEx2tAiEA06A9sNGVMg1WOeo5EG5iEbE8nUs4rUFTJkp8UUsfDds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141652}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@ai-sdk/google", "version": "0.0.31", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "64e41fd8cb47bf272a037b0c2ff4dcc37f9faa1a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.31.tgz", "fileCount": 10, "integrity": "sha512-A<PERSON><PERSON>bzi+7vcSUElqq4DPTPAcfs0BTtWnIAnJdtqmdhSwQtRFj5wDrv60TBFg6r7LM0RswftNkAPxLnfAcPxh8w==", "signatures": [{"sig": "MEYCIQDoSlz22rx01W2VJurCM1ZMfYVxUFrwwK9sg4/PNCMR+wIhAOviH73FP6HqGJf3HFmhmomd4aIah3XJPX1EvVhl0Wbd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141652}, "engines": {"node": ">=18"}}, "0.0.32": {"name": "@ai-sdk/google", "version": "0.0.32", "dependencies": {"@ai-sdk/provider": "0.0.15", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b902c12c738075ead7506e79435ef2e062d07ad8", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.32.tgz", "fileCount": 10, "integrity": "sha512-Zs+J7p68nloOg44P6ZXMPRujuvePw9xByyGAs3QONhX7zIHnbixa2GQNgZ/Tzl2aVGFEZu5mo5DJ82Hk8MkX0w==", "signatures": [{"sig": "MEUCIChyLjv38+cyqkHvETF7dG0Yq7iyrS8Kzr0ErdkYO6aFAiEA+3zKLZDK/g01copuOmwsH0gc1EWfPrwR7XZXLlJrcUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141652}, "engines": {"node": ">=18"}}, "0.0.33": {"name": "@ai-sdk/google", "version": "0.0.33", "dependencies": {"@ai-sdk/provider": "0.0.16", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c3f9fbbc236444b2ab98a003201d04d178271e7f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.33.tgz", "fileCount": 10, "integrity": "sha512-4obgD/8Y9XUoJtbRwjeTK6e+oij7UHX6AIOsWI72hdZZYDA+vm1/y+cICtUL0ePrw0v+cwz+mp8lTRD325A0Cw==", "signatures": [{"sig": "MEUCIGQmfscbGARMj9hfMjKJXSNu7TovGFVRIWSEtLwyn2/+AiEA5ikxDF+bzOK4vvZC42B8oMl7Klq6aFuZTYNqzvgwdmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141652}, "engines": {"node": ">=18"}}, "0.0.34": {"name": "@ai-sdk/google", "version": "0.0.34", "dependencies": {"@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e235e0c32b8ae0c410645f981fb73f856e88fb5b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.34.tgz", "fileCount": 10, "integrity": "sha512-fcsHRV48V1DbXRm6ATpfm2fypSW9ZHoj5YbQgyiPrPckAY7r4GyzTAnu0mLFTmaESDTVgeZ54g4RkeSbum/vyQ==", "signatures": [{"sig": "MEUCIQD3C2rw/aL7h4mqIClwhBorAF8btc8xYPLneDOQAy7YXQIgVZqextWRW9aZ7vEUdi0M0EXRf/fABAs6a3EFd4APlgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141652}, "engines": {"node": ">=18"}}, "0.0.35": {"name": "@ai-sdk/google", "version": "0.0.35", "dependencies": {"@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e3fd0fac309120e4a6ccaac70667516a50066d05", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.35.tgz", "fileCount": 10, "integrity": "sha512-OQRFFJ0Emuw8YEEbBnUEeHEwjyKJTg14ptTbB5h61fL3Stj66jE5o/70iU4d9dBeumyKZjeRVORIzlOsOnfokg==", "signatures": [{"sig": "MEQCIAqZ4ZhNf20HIRO5HOl/r5/jeIErsE3Iii9h04xglH8YAiBCn8AHBeSH6yrX3IciXRxZhclRMoU6VqqaAMhqPV7Zqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142170}, "engines": {"node": ">=18"}}, "0.0.36": {"name": "@ai-sdk/google", "version": "0.0.36", "dependencies": {"@ai-sdk/provider": "0.0.18", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb6c51c123330365a2a4c22e6ace0ea467de19f6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.36.tgz", "fileCount": 10, "integrity": "sha512-/GOcz+WlhqP9vaspg4EPfxxwP/UpYBEoz/Pyg3Z+QcGmx+p/yb58f7y3nc0nL7GspvNVujMHvTMcAHSiYvJ1zQ==", "signatures": [{"sig": "MEYCIQDCifh7TJGM/+4t7oOSTw8u5yj7r7p8BzZ9frAuAPkT0wIhAJoS5DrApZ/XS7AWr9r1DYzWdG70CgcJ6WqSr0uhomx3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142171}, "engines": {"node": ">=18"}}, "0.0.37": {"name": "@ai-sdk/google", "version": "0.0.37", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a2c5bc21f4d2a4ae178ce2f1355bbec136a6de68", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.37.tgz", "fileCount": 10, "integrity": "sha512-eloR6vXIIjYNmqeddAnaLXGQjd2B2fMGj7IffLt7CXSGMv0GwvV7FtPmEYYVEhXjIdv1ZYENZL7fe3kvBDOBrQ==", "signatures": [{"sig": "MEYCIQC98WZlXTTVfPvnHc6JyBU8wGb4TBAAyyD+rDTkUwuScAIhAIAEwBx2d//lvu4/e8u96Cg4fY0h/pM8iG7ZPk1NkR0G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142323}, "engines": {"node": ">=18"}}, "0.0.38": {"name": "@ai-sdk/google", "version": "0.0.38", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a9e776388c6e551abb55cced1348f63094fcfdaf", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.38.tgz", "fileCount": 10, "integrity": "sha512-Ya8qy8xvsXOF9iHX7u0ZvjRbWSoITh844kDsBrEHuuiweW2FfzVGPlSjYboNQWg/yML3qxYv/0NYNi4SAyc+oA==", "signatures": [{"sig": "MEUCIE2lsfozU01Ivdj0eiE2Jvo0AwkBKvcJaHugcbgzl2jWAiEAvLiKXQeGN8X0a9uIZBKqycuY3e1BCB3e7iQu/SblAkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142515}, "engines": {"node": ">=18"}}, "0.0.39": {"name": "@ai-sdk/google", "version": "0.0.39", "dependencies": {"@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2001fbfa77937b0fc339f66c0873c7352ae286a0", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.39.tgz", "fileCount": 10, "integrity": "sha512-++IRoxi37DaqxGv5+zFwYFc4v9UT2r5Ac4d66kxjiQ1Gj5ACYWzaLzPrJJuBOhlO8Y8f20Lx3kNR5bPb0LkU8w==", "signatures": [{"sig": "MEUCIQD7kp9Y01+PwUNk3wPYM6HJhJ6Q+dAXS+DnpiRD05ppjwIgARjyTBao8/t4d29xMFQkBY0QFPxqIprxBtqo4qKDTKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142515}, "engines": {"node": ">=18"}}, "0.0.40": {"name": "@ai-sdk/google", "version": "0.0.40", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd1f1cd3d66eefe11713d39e2d340905cb7234b6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.40.tgz", "fileCount": 10, "integrity": "sha512-NBNXZ3C6QSvVLqtbk/iecQw5Fv+01r4DYzNK2n4vg+AeouVTlEMlzJG4hBp4WluNGVdNLQD2OUobkJPcAyc2Sw==", "signatures": [{"sig": "MEUCIApB0CbvSry5oxZP6VpQfoF2Zd2FzIKJqkYaDGLgdbWEAiEA59MNsyhM0xOkwRgygKvQ9JTcm3cTrR6To1KG0vxL8js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142515}, "engines": {"node": ">=18"}}, "0.0.41": {"name": "@ai-sdk/google", "version": "0.0.41", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "05a60bf6c0a054e2e30841acba459f9e77f57f1f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.41.tgz", "fileCount": 10, "integrity": "sha512-Fi3vxWDcvPoSTEy+C4v40a/x9HNT6d2WkHMNACB7ljAc/763ax1al0hlKHmTZPory4q8pPijEqK26DfDcXCDaA==", "signatures": [{"sig": "MEUCICwAmlRr/LLFZAU6RWNy3tM99chEmBPIi1dMCTTu6ErXAiEApsf698zKCMTAMAn4/rJG4IzOywix+xxma9MmrV6cF4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164455}, "engines": {"node": ">=18"}}, "0.0.42": {"name": "@ai-sdk/google", "version": "0.0.42", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "81b31846cd981c485836796b4496438471ea10c5", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.42.tgz", "fileCount": 10, "integrity": "sha512-ndbejJZG6HxhGXz0zT7CiPp/5nKVmk3KtN4nxdToI3F9QsNsbpTt2T75jiPcVSpeqLlJRslw1km4GT+2woVY0A==", "signatures": [{"sig": "MEUCID90/6vsd/Gd29wPtKUE51p2edswreaV1LTPPkyRVixOAiEA6uYiPnVrcR6Ff0XU0AkBgmsOh6J8B7q8mJL8UidGnOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165047}, "engines": {"node": ">=18"}}, "0.0.43": {"name": "@ai-sdk/google", "version": "0.0.43", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "336d71194a6a89bad6eda8fbbb3dc51ebc3a32e1", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.43.tgz", "fileCount": 10, "integrity": "sha512-6JDZFxWVUqBf5piKmc3u/Z02ltnz/+5LXyCVzOgOKOitOODe21SL6dZ/5ljPA1ZsEM5/C0EOKIEoDouNqFcYqA==", "signatures": [{"sig": "MEYCIQCDUX3mAWyiqk951gsuuhXT79HLUQtnHfR5D06BQv2YnAIhAL8czeMjo0FtiiKK1afuakGEFDD90BF2y2sZmScA9L/k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 165047}, "engines": {"node": ">=18"}}, "0.0.44": {"name": "@ai-sdk/google", "version": "0.0.44", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c481b82492c9723f2e3ad9e2a25cf77fa4a0e6af", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.44.tgz", "fileCount": 10, "integrity": "sha512-lrvjctWwZSsb1+FGWV+eMH5sMdVnzpnifKCLbN1upmip7HuftdgC8WE8HdzqUFlYjLij1HaipcFfEC40IOEy7w==", "signatures": [{"sig": "MEUCIDAuaVd5c+9ebNWQIOqK3zRqJWklprCHburSJ6Nyt9oYAiEAqe1lBseucZlurVMVJI+lGH5mb/x41IOqS5sw6Vz1VDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177135}, "engines": {"node": ">=18"}}, "0.0.45": {"name": "@ai-sdk/google", "version": "0.0.45", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "95489e390f9b4a1c8553a74ef7c707be4a1d5d70", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.45.tgz", "fileCount": 10, "integrity": "sha512-18WPQ5xmNtUhWjhLeY+aZ+rHtmLn+ls9olaRJ2eWNX9V2npbgTCEx6AloQRquZ06Mtle/GlpARrs/KpTZ6yUig==", "signatures": [{"sig": "MEQCIEN27N8Z3zzFPDUnYCgOUXrOGq5G4Vhb0NninrScaSqsAiAwQs4qCNBLNXr8dYONU4sYs9MfHJWS95AJo2040pVemQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177135}, "engines": {"node": ">=18"}}, "0.0.46": {"name": "@ai-sdk/google", "version": "0.0.46", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "07aad526b474cf725037cc530a22ad2e55c4540b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.46.tgz", "fileCount": 10, "integrity": "sha512-rsc3Wh54EfSt3l/7IqPdTeuxA7xvFk2p8/HxxyoHfcwvQYmQ/bpgxmadId862sVsK79L8k3iRxvVwGVkkaEeaA==", "signatures": [{"sig": "MEQCIGMhtZfNY/MqAHi+qZmKmcJ74e/ufuwkBIcH5e7ZXCLCAiAF3xSJ0a2Oum/E4RV3C98mVhPCI5YG5Nbg878LZ56Grw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176808}, "engines": {"node": ">=18"}}, "0.0.47": {"name": "@ai-sdk/google", "version": "0.0.47", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4dbfc9b6d3144b5ed0513c297b3e4fdff1a8dc30", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.47.tgz", "fileCount": 11, "integrity": "sha512-gnKNwi85E4aXjPZ7k2mvr9YST6+xmTq1D+lcLLIa2zwT4IYIoO8vzLwh6NkO1Nmk38D0qe+ON0spzlTaopwA8Q==", "signatures": [{"sig": "MEUCIHICESk8f6cvsjw1Dgw6cx5/UMPb5rSyXpVESK/Cyy90AiEAjUiHEtbmmH3KOkIWbiFtSqsidG8VAvAUY7HBJY8Bi+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858}, "engines": {"node": ">=18"}}, "0.0.48": {"name": "@ai-sdk/google", "version": "0.0.48", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1a367c4da0112d6aca2d58e93d7ac2fe9b51753f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.48.tgz", "fileCount": 11, "integrity": "sha512-8L7a5WVuTZjeU7Q0rPn+mL5VvqkN1zDe5WQgzmu6ppXAJ4KULO4ca5sueJSJj01etJyFhcqnY3g7UNLNn5iP6A==", "signatures": [{"sig": "MEYCIQCv+4y2nTQNdjyP4PmcSv61FidZ6yy2zbHLGYCzKlcooQIhANGamdT6/QEdmHY4M4ZItzqAHgFK+sV7X3Ivj25WhBv3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183956}, "engines": {"node": ">=18"}}, "0.0.49": {"name": "@ai-sdk/google", "version": "0.0.49", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "37b481d7318a15a7c9805c2970c3c56a18ca0454", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.49.tgz", "fileCount": 11, "integrity": "sha512-3/MtjpjowHziNUbo/gcOqJvS734stGwJKLA3LouU5KmfIyLXyTlNCYjA3SIlrGZDleApefbtYAYFwBgwdMuYAQ==", "signatures": [{"sig": "MEUCIQC0aY5h58ylNKCpnMYyZr93S4BUG/OoGjJnOqKgAhmGWgIgYiqOOyVVUqlsVmII1heYi224o/F3sLIaeJqNIEQaSrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184080}, "engines": {"node": ">=18"}}, "0.0.50": {"name": "@ai-sdk/google", "version": "0.0.50", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "735e060cd60b839087e899550f3eb1552b500477", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.50.tgz", "fileCount": 11, "integrity": "sha512-7KAjOdo4NtRpUxklwQXFwjdUnlqjK/RuBGHHTKBhzjJzKuhdw4vhDb8QMGDvvKEdrsAwL5JfBaWANAm6ncWMbw==", "signatures": [{"sig": "MEYCIQCmjND6p+y35FuXGp9i5SSVRvyYHuuTNwg9G7IExrJoDQIhAMUnl//k7Rfvj7fLWmkrYylLCHMBqJbfo+AjnIhUFNcn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184918}, "engines": {"node": ">=18"}}, "0.0.51": {"name": "@ai-sdk/google", "version": "0.0.51", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b0a54f2228c5f3021446d4eabe9860cf63ffa978", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.51.tgz", "fileCount": 11, "integrity": "sha512-MUwN3tFH0grkL5zZdTUFDCDubQdMjykFw0o9bR3dMFtKP8Z2tc/iEqU1fb79+jbz2HzUBbnNGjqMQiUwG3/OrA==", "signatures": [{"sig": "MEUCIQD7J1joeG9WrAVYSZY1yQSKp7CMqrhzkn0wHozoFY5LJgIgTyWP7XRLdKfQqG7fVOPeotU7XwQSsUHiCtZdlCRR64U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187676}, "engines": {"node": ">=18"}}, "0.0.52": {"name": "@ai-sdk/google", "version": "0.0.52", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e1c065f5f62c2fb7c61418c36e91fbebda7ab048", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.52.tgz", "fileCount": 11, "integrity": "sha512-bfsA/1Ae0SQ6NfLwWKs5SU4MBwlzJjVhK6bTVBicYFjUxg9liK/W76P1Tq/qK9OlrODACz3i1STOIWsFPpIOuQ==", "signatures": [{"sig": "MEYCIQCXMmveUFCFGWloaHKBwkLOugjme538P2wZy+Lw1HigHQIhAI97WptayzEf8/TA154yPzWR1JhoPg45o2p2E+h+M1Zs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186020}, "engines": {"node": ">=18"}}, "0.0.54": {"name": "@ai-sdk/google", "version": "0.0.54", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0d2c6b1e6ea8cf7c62a89ad3d5387eaa211dcaf9", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.54.tgz", "fileCount": 11, "integrity": "sha512-PKc9xFZQpxnca6ZXDOmrYyO3eDPwwskFea5RMI2Q860GgB+l7N+QhWOqKAfngqHiZy9jH1WhqhhzH8YZbPrXCw==", "signatures": [{"sig": "MEUCIQD7mgEhBT6/5r5sWSPtMnGkLuNd8Q43SXS54AgwiMPXUAIgKR4EVzhKhoN/fLG/d471jNXMNIKzGgTxBWYe4E8/VTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195681}, "engines": {"node": ">=18"}}, "0.0.55": {"name": "@ai-sdk/google", "version": "0.0.55", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0b9f929e21e7850d038041d33cc759f137a8b20d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-0.0.55.tgz", "fileCount": 11, "integrity": "sha512-dvEMS8Ex2H0OeuFBiT4Q1Kfrxi1ckjooy/PazNLjRQ3w9o9VQq4O24eMQGCuW1Z47qgMdXjhDzsH6qD0HOX6Cw==", "signatures": [{"sig": "MEYCIQCJURPSk+rP1Yz7wxHdCadGTz1V42D56TLNzUpO9zjcmAIhAKURtFjQfi4pH8TBuZQAieDfObp3cRJsVCrbTzJcYGaM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197926}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/google", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "672e2d00ea77932a41f70eb7324678ccdb575b9d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.0.tgz", "fileCount": 11, "integrity": "sha512-S3NaN1oMnVTnUoC/VbzG6mgit/OaAZfgJL7UDzd0AWBLj2qK89s5JvhpAhPd9xv7V91TQFBldsuerpt6mVEHyA==", "signatures": [{"sig": "MEUCIAJtrs7IgsOY7ZTecKvL+2uAQHlSyBdfJxePdsyZ2BrTAiEAoNEaZ9T1YlmgeNdYd3rWK7rEK9cFO1O0K5DOaaDMelU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198240}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/google", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7d757421c50b286828fbc0fe4cf0e2e33393cf10", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.1.tgz", "fileCount": 11, "integrity": "sha512-rXTm/kHpON74ulfHaEc6kV1j8T8e0gvSoo/AiRQr3Q5rMq74bQbieU6yobl4f3q9kEDMnMQj8aRzlvolHKcHdA==", "signatures": [{"sig": "MEUCIQD9mhuJ8DlRzVr2OcmAtW1wjunpV9VbGcTDlOpRuGaRCgIgNEpO2CULDPftK9i+R+rdT0UHab4D2K1GKMJIGLJCFTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197738}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/google", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4466c7eba629d6edc45c77e4e313438560de6087", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.2.tgz", "fileCount": 11, "integrity": "sha512-P4XHdOh6HDnB9bDHcGWegZJNL1emLwZD/mSmqp5cTHu7p450Jc6o3ZQAjHWs8AEa6xRAmIRPby6DOtZ6jXpLJg==", "signatures": [{"sig": "MEUCIB+vVtUOxl3f77rdN3k9ZkvhIn/Tb4Aky6kDkh4o4auEAiEAqV+Yj3hIoWE3xtw+EGcgygdDDdJVWh0JtF4t/3To8zY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197885}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/google", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d15bf018da8f1360d9ca91f9ed760782278ea824", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.3.tgz", "fileCount": 11, "integrity": "sha512-j72r49nKq2C83rml3i2R1AxB8+LmqPUW8D5piC4DYc0LbxNnYSIvRR+i0Vod+zvzxKslTGEXuJijrn6PSi+PlA==", "signatures": [{"sig": "MEYCIQCnQoJm4KXL+g7bypLQTLfq+Q4UswiPK5rgelqYycU4lQIhAMevvPbopEtVFGJMVio7kLThOA8PwtTzMuViBP6dfZET", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197999}, "engines": {"node": ">=18"}}, "1.0.0-canary.4": {"name": "@ai-sdk/google", "version": "1.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73062acb8fc307a7ace6e42bcb4c51618e0de2e6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.4.tgz", "fileCount": 11, "integrity": "sha512-8Q8hobs+/x5taZ2lE/pwMDHgIPxZNGl7yfCZc4cekO0Ra9JEwApZFEPrePT5cAT+n3/e6Gq+CYTE2CvfbKINlg==", "signatures": [{"sig": "MEYCIQDqh/06P2qFP0xW2ObPIaNuzDeizzk/9L+gEkPqO72eqQIhALr3w4DNwaalYr/Zs1nOfQqFrKxqvuwdqTN0ezktkGtt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184169}, "engines": {"node": ">=18"}}, "1.0.0-canary.5": {"name": "@ai-sdk/google", "version": "1.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "08e7bfdfbaf023fd5901ee437331569a0a32c302", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.5.tgz", "fileCount": 10, "integrity": "sha512-CVHpx4uCLRFcvqX/QNfG4b1W9UqGPVU+0XT9DaoKzzXs/OShAx5nyHixZipk/dnFRwvHnnDGHL9dXei/f1vN4g==", "signatures": [{"sig": "MEUCIQDcPdol3NY3CkZm+ym1x4tuvSjlkH48f0zedpUprJZMmgIgb5h+ryoz/+axebSDz2sHthrd26lswS5zU5xRaQ1dR4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160264}, "engines": {"node": ">=18"}}, "1.0.0-canary.6": {"name": "@ai-sdk/google", "version": "1.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d1d23f942966273cec45d8ae31a3d05eb3ca1cc3", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0-canary.6.tgz", "fileCount": 10, "integrity": "sha512-oGKbQnHubD4e/gFyn8wN+C3hj6lrHrC/nOIblCcDlkTOuEr0s4INlaa7A5HuT20S86AjklO6UOWNT/gfxCG69A==", "signatures": [{"sig": "MEUCIBKO9Xqdus91CofgvHTMb2/Ttg0lt23HIEu1AJvXhw7KAiEAvRAEiuKGIkM1qdnBqxiaYENtY37EESGvp3qcKSrDCBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161001}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/google", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e366af6ffa88c4a137e3272271b1199ca09dec06", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-GXt17gZvA9QZdlvNamekLERfM2sWgAOkwHOwKGV2aVPx6lpWs46AMx/IfOi3qxoUjepj3ka0QQg0Ai8mHUPOSg==", "signatures": [{"sig": "MEQCIB7O9jXlN6R7x3DM0uoW4QsoyUJnH4SR3NtSYieaFGenAiBibNsIsPx2/h8PDUdzJEQl4qrZ2ea+wDqJJ/ls1FN3dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161688}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/google", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cf8e1270025d4e863f7817f25c9b4a49c058dfae", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-RnUnx3TjyegrURFXiWWqcpeNiMlfQfotSM198Wdl2XiD+kaqEBzcBB/imHMlqY5VJdnXi0DCleFlcMQldK5SCA==", "signatures": [{"sig": "MEQCIEmKWEArhmYlMvAsoa2brgzpjfb5mJJMYzWk8LryW7mGAiBSDvi0FO/E/QavEQrH3c3FvmsW4ISHoqBXcfGRm55C4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161784}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/google", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "75e7f9dd923d88a39d02d2cab876616656441430", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-sTDfRdJzhWvscEoKzeQSY+uP8soKUDU+u61eUTfuVyOvO2bGVoYrTkpfbwr+M0ngIogsZ0gxxtQpWu+i6rcMIA==", "signatures": [{"sig": "MEQCIHzMOrNidBV/Z6xrs/OdRUErmfujoiIXIuBL0cOynKxPAiAUZ/xHfEtd/gQD6vf4ngaC4GeNHx8YmaXFgztdPrNatQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162286}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/google", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3fafcb1a74a9365be06f17ea13c55cf2449308bf", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-vxHQw2ogNUMgiVtflDUh+8izc1H0DUo0GusT+YHG/2qdcoY4M1GxCD6pB1osNDeIxfjzToA5HfqdatjaRiYR9g==", "signatures": [{"sig": "MEUCIQCYy+gdx0odwliYo6oCm7un+CDAFDcYuJ5y7yj9Rr29ngIgUP616Fbe8J1uYAwdXs7PDcAbCH2g1nanP1wqkHYACGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162409}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/google", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4ae718cca322c9dc116b315a326be34dd40454f3", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-jUFhQjE7nfldU2umQjnDR/M8XlBupQquRkc2S7CwxcEEXVRg5vELYrLrD6xy387PKj7C8+Jg7sJCa3VjxCJ9Ig==", "signatures": [{"sig": "MEQCIGjYf5WIFy6Qx3ayh4W3coqR+6fMwRN9bsTjcimMlhS/AiB0cPQJl95JUXb/HyseQ0vb28OCeguSuG0Yrmnqq2uwmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162594}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/google", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ce9879dedb4b510f0a28eafa5297bcc8b65a075a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.5.tgz", "fileCount": 16, "integrity": "sha512-uq4wNVKIDNGXE18Wozp3grAOtAK2qHNy5wiBPLX34Zp4XHN11MmevrRj/R4+GXvnWy7czIVS3K04swXTB+m/4Q==", "signatures": [{"sig": "MEUCIQDP0eerSFp/TDaHClDj1hQzs6dYwBDenXfUdnDr+trvfwIgeBNmxsz8ceozcNgxr7d2bZlQMAvixA0gVlag0GfklXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288588}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/google", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e9bcb7b4eff52a82f51613999368fce256ff703", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.6.tgz", "fileCount": 16, "integrity": "sha512-/fxMvOfl6r4lxW7Zs7EHISfQEQORdxU+WO2i2gHmwErckCsNWfx7XK+vstRwyvo2oJGXV8HNkysGmj/f8TeapA==", "signatures": [{"sig": "MEQCIAg1ztJe8N/hnyzhgjxcg1V04UAVu+SX1nXXVc0PgVHsAiBThUQ4Ihqpz3LVAPx/ihPZdacRjXWIaSgqG812hVzPGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289138}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/google", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ad3cc2796a5eb0fb50a2d7c5f27fb9534631afcd", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.7.tgz", "fileCount": 16, "integrity": "sha512-D2R/VFA0zpcWYnAoqYGaZn7XqHb8ASt1hZJ86u7BOVoBnQTRPRUYHb4lSXjrMcj/QYMXIJkKojfY4isenkku5Q==", "signatures": [{"sig": "MEUCIAXyFUrzxYusRjzwjCXHsZK9IZJCgfuiIacMtVJF32wSAiEA7SJudgZhpWsqREwEAmLvdQUQb7zP6dBnSKnuDDp3t7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289261}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/google", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "79290ae58ad1f993682c0e5d4061699dca173138", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.8.tgz", "fileCount": 16, "integrity": "sha512-zYI6N29HMccoBZlxRo78iMdzia8xaEIsk4K2pzM0zUZh+6phK+35zroJs3mSU46jkqFyIHoCovm+Nm5bBIRdHQ==", "signatures": [{"sig": "MEUCIBe8TEkHSo7Z8yCSdI7IszQLSIJcWfgpETnY3YSXiXVDAiEAoRNAUyl2KBoP0Txp3ezXIaEI73I7zlb1NGQ3lNiHW5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317035}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/google", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ea0b17b125e4d7162c44108ec4a8d7d6af63d904", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.9.tgz", "fileCount": 16, "integrity": "sha512-fIwKAhMiYCrWDQG3SMUkld3RPKq1yr1yrjFAtjf8Vzp0nKq+DdVtfmnUFFSOawn+rzJLGfhPc42H8ShFX89djQ==", "signatures": [{"sig": "MEYCIQDkWxzKyn/6NGZPVTrsn5RGVkrffCajHPyQoYMvKA214QIhANOBlvfILyZV5AzXrdjtvGGCubjW6avvn8rH0AjpBC7w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317222}, "engines": {"node": ">=18"}}, "1.0.10": {"name": "@ai-sdk/google", "version": "1.0.10", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "50c1bf91247e1210da410a40109691cf2fa83372", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.10.tgz", "fileCount": 16, "integrity": "sha512-8fvwy1luTLHJmuldDp7NZM+1jo20qMhqgzmONkb2ACNwRCnHtpcLEP2j/tJ7FIFLuvl5wHQP83smKHvdyUD9qA==", "signatures": [{"sig": "MEQCIGr6Pu9ebq4Xk/ps4x4rQuRs/vM7/sQTXfiK1igYszHBAiBAm+C8CZVYfjXIE69g0zAekXfUkK69VR7xbwA5lyhe4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348111}, "engines": {"node": ">=18"}}, "1.0.11": {"name": "@ai-sdk/google", "version": "1.0.11", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "712ad23e3005e48a06a8d76f2faae745ced00f05", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.11.tgz", "fileCount": 16, "integrity": "sha512-snp66p4BurhOmy2QUTlkZR8nFizx+F60t9v/2ld/fhxTK4G+QMHBUZpBujkW1gQEfE13fEOd43wCE1SQgP46Tw==", "signatures": [{"sig": "MEYCIQD/hDQkntz1x+5lZCUsZPdQBhVavk4Qzbtjk8R7PLZyIAIhAI2wkaTA//y+gWzWH+rgxrQ4jnNfnAsDAPIx9E5YzeZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350231}, "engines": {"node": ">=18"}}, "1.0.12": {"name": "@ai-sdk/google", "version": "1.0.12", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "43069c159a45c6ea0def38c2f8af13a3a7a4cf70", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.12.tgz", "fileCount": 16, "integrity": "sha512-vZUK8X997tKmycwCa9d26PoGtIyNEILykYb6JscMoA/pfr5Nss8Ox1JtSGn+PRkehpJhclOaLNWV1JQAjp73aA==", "signatures": [{"sig": "MEUCIQCifWV0wphD9436mpslYDMOIaL7gjfN7wYqCLDYvJmOKAIgelC2DFvjTqsdS3P5WntkRXPshvu0AO2+o6LQlIm4el0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350427}, "engines": {"node": ">=18"}}, "1.0.13": {"name": "@ai-sdk/google", "version": "1.0.13", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b1f1b4588f3eb3418c9ce83e329860dc4da5f895", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.13.tgz", "fileCount": 16, "integrity": "sha512-L4ej4rd3JpHp0QVqlgr383EedwQXu9tJ3hmJl793Lt8zMLgY+VKZE816v/bz0R3zCyTtiUrxR+LDhbSMuPV7eQ==", "signatures": [{"sig": "MEYCIQDGZV94Zu0TCOeYxxlW32xxIOW7+QxnTc7iBd8LAQpiqgIhAM3Tl/n+HHZrn4vr5Fs4WRCI0thdl/zSeSgkloDEujub", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350617}, "engines": {"node": ">=18"}}, "1.0.14": {"name": "@ai-sdk/google", "version": "1.0.14", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "661bdc7ddf1a6897ade3126cbba78b03d3dbe89e", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.14.tgz", "fileCount": 16, "integrity": "sha512-Yktuqc6NBByTPoLgeu+ZvMzmX20E4H7BnLHjHCerugy+7LADFRouYJ3U8z7PAk9vENZmfB0+BwOM4tq8AL8Hhw==", "signatures": [{"sig": "MEYCIQCfvIuGDRx/GZtcI4C4icQy4ykdpA9VxAtpollfx1cXRQIhAO3Z0mXGAD5CoSmGU9vc31g49ISKrNKdfmJVnWfzVKuA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350780}, "engines": {"node": ">=18"}}, "1.0.15": {"name": "@ai-sdk/google", "version": "1.0.15", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "50377b38fc32ba81bc235eaf14041d723ea1e371", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.15.tgz", "fileCount": 16, "integrity": "sha512-RiyxvjZ3h9WDj0V5OcmKU/aenP5tVtxut061tNb11KIzGANZKzryfOvp2TMx1T3bSfnjt15bsJAB8y8H6Tas3w==", "signatures": [{"sig": "MEUCIC9aqdwUAt/Xpp01tMU9I/c5r2VwVtnpDNOIdoaA7QY9AiEAnmicPPxf3XMm37bdXVx8CdoR6IiQBKOgyyWtRaP2ToM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 352344}, "engines": {"node": ">=18"}}, "1.0.16": {"name": "@ai-sdk/google", "version": "1.0.16", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f0d49ad37268d1da5a5c45a22984262a783948af", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.16.tgz", "fileCount": 16, "integrity": "sha512-GLTPPQ4P44z+rs9lMWu5bU7Kn3ZCQfuJiGZjSsJPVJiC6G/hCj008RXMAxh+/zXXg8KX5DZVfZ0fR84EFeuLnA==", "signatures": [{"sig": "MEQCIGQI2B3yMhoKlPaWGzqDbpInl+gO92ENMIIxUltvouqWAiB9jbjpabwkrR0No7lsDJK74Cj91cG7ItwAgC0EdNNz6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353648}, "engines": {"node": ">=18"}}, "1.0.17": {"name": "@ai-sdk/google", "version": "1.0.17", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "983947999d57b5a3a97899e953ac3cb26665c8a4", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.0.17.tgz", "fileCount": 16, "integrity": "sha512-MiBtOUdSe+svuPCPF/11xXUuIG8IKdZY2WCoXOTV6/HoPkhJUM/rwpYkNKSlINSie/zQVq0rVqzGoU8Vf9gZ8g==", "signatures": [{"sig": "MEUCIQCFPeBg9QJbfkofpW4jx/OK35WbzldjhoBYpRVDsRr86AIgR+jGIpy+rk2JvXLhiWIJtBG/yqbfZLGRd8RRRy11Re8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353745}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/google", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "855858a2e605d2c5a736f27b94fa5812c57b7040", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.0.tgz", "fileCount": 16, "integrity": "sha512-fOsh87rasHVOxiaMiodMLPGi162fD4dAoNq/k42hUHec7d9/zSvgY7L7xJrFmuRlFNEz1mbsB5VFZBVHsOU3oA==", "signatures": [{"sig": "MEYCIQC2Jy9b1vr5F1g7VYYVktFrxlT2YVDqZfssxIRk37i54wIhAOBkTWV6QBpNEuy9KHi58U8q1Af0moQdo+50AfaKIkNL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 353891}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/google", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "da0dae967136a09c94ab5d0afa77a872d681fcea", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.1.tgz", "fileCount": 16, "integrity": "sha512-DTO7XAcR+H4gJddVynITPEhDrka/lz7LfnSQK3k09zw8XDR45Up93jJbgntKXj0i774JsjZbx1++UJAb2gAzNA==", "signatures": [{"sig": "MEQCIEzcP9Lgb/qFFfC4XaJsrTESLGuIjpGI1ZQODZ585pD7AiAvDl+LfZ0Jt8T+zwtYWNy1dmNoW/2FuOzap6ys3Do5Dg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 354047}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/google", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e867406e39fd89b052373c4292d52d078812df6c", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.2.tgz", "fileCount": 16, "integrity": "sha512-J/T9Ryf2ar5TJN0NW2IFAmjzz5UF15FmRwUaBq6e1a5ZUIVfdIHVjYGc1mxrs4yEPsN291iEj7/kZAVdT2mJzA==", "signatures": [{"sig": "MEYCIQDbDrmI7MOzJ0+1EoZaaAiDsVKxtSDUhrYa+O8FJeAKZQIhAOJIEPwjAvhZYvW9RP3s+fmuE25o2eLOGiEPVo8N5LfC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 354203}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/google", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b343024936e67305b5b9d05ede6a3c467c97ae74", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.3.tgz", "fileCount": 16, "integrity": "sha512-lUuX9RHBfeqpTl9mc3o6F51K16XKIQv2EckEih4z7z3GqFhXWbT/rrncATEhGxR293x44ikWCVfeYkgE1q1rLg==", "signatures": [{"sig": "MEUCIGXmOdJsqISnESd9CMMFakXXRzsffLla1vJ5URBnTX0rAiEA5lZG7KWt2FqIjFitDEragFZ7lXFiWuq8dWaPMe3a624=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 354299}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/google", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5680defff968d233a27d68e9113c7edea8b61e6f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.4.tgz", "fileCount": 16, "integrity": "sha512-vit3iooqMkeVRWd2SbZfHxmMA9/KZYZLv2e+QO5d6a/KItHsK+wuzoCtDjodhiHWr85kfc3HH5STU1d6Q/4ncA==", "signatures": [{"sig": "MEQCIH6xS8JulE+YSjuEvk2scfSZtJbvO7X3W9b96VMq+HWNAiBws+udTXlScZYqm56YaziVNQi8caBoarvP/2h2Og++Uw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 354395}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/google", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "01a58a890f6d7a957414b5a8954826836a313326", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.5.tgz", "fileCount": 16, "integrity": "sha512-hj3v7xi0Gq0KXy1w0hdm+VsNJ9HPdtJ2rEpAjy/sbhY9QTkPdoSBKgRs89Jq5ivQQnnoHumq1Q8RZW/WQq3ZKQ==", "signatures": [{"sig": "MEUCIHs4I8S9KYFOS2VRSATkZNWpu/oV6GtxjB8iWcuJypgXAiEA7GJk6O4LbcLXSplfftGuaISj5KkMnHxRc6ZOmLvwKRk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 354491}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/google", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e260de877d0f420fd110515abcd9da4296db8e2b", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.6.tgz", "fileCount": 16, "integrity": "sha512-W9A2jYbPa8WlqyrLohWncIZ0fGWtyUuxjQNGhhMhlAdA+PZeS2pEy0hFJPr8IRRtxVYfRSbjnTpUrJO/vbcnqA==", "signatures": [{"sig": "MEUCIQDs2e9V+X0nSwBvaHop0gRFDyLmu93+xTSRRyNr1J8puQIgMwteXqpByNsWlB4f8hng4NiGOcaPr9qz/EQLhhJHV1o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 367646}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/google", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d46351a7cbedb71eebbf4fe1ee27453222319eaf", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.7.tgz", "fileCount": 16, "integrity": "sha512-fu6rQzYdWb3n8VJDtW1fEgr/E6Nuf/5NpgDXZXbpNv9zL746NCX6Xdfbnj/Ifxs+3kjcieXE7He1G/X1WQFPGQ==", "signatures": [{"sig": "MEUCIQC2sVqgFEhjDe+fltfAoYes61E5Up2XJ1DnlkJrYceWNQIgFXWmYBlcXajtqoppnx/4ME0Z6tjz5mxSoeYPgazDZts=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 368735}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/google", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "07d9bdcff2724a5cb3a3c663b04f9e9f01410490", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.8.tgz", "fileCount": 16, "integrity": "sha512-8ZOS4p8Qp/7fXWnwNizlFN5aTBu4u3pNlsc7LDFGUtuJXz6j/pAQhqBh3nEaagX3GNuDFT8rxZ1yu82+fYaRpA==", "signatures": [{"sig": "MEYCIQC7thulfXomf+nOSdS1Y5ts5/8e02v98G8U+/ptexYuLgIhAPQ5w5HAgvwo9li5Dwe8N5qRYxWGtJBCyG8jJGdz3pPi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 368858}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/google", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "57fc27a1faa1ef7c66ae0bbea542c32ad2da74e6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.9.tgz", "fileCount": 16, "integrity": "sha512-Ve3D36pkOZ4vhjimMtou/OG01nMdzglZIBesqE3T20NAl20WbPx3IIK7vGywcD3YgASiWtiHYJnySMJwn2R3mA==", "signatures": [{"sig": "MEUCIQDZ7elQTP0cvzznRShNJAhHIM/QHSk3ePLVlrU0voF00AIgHoYXyH2sAjnNxUhPr57T6WlEAhS1K8ulP1QMg7hAjLI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 356046}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/google", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "93da893409dfa55954bd2ff5c89d1bc4653b8f99", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.10.tgz", "fileCount": 16, "integrity": "sha512-g65cKrs2ZjpNMOD9OvE9J/Xt1SxPu00IsWn4npYe56nU4YqVydsPBG4PyUKgDr9KXdrnFEoXYmWxkJeTe/m4hA==", "signatures": [{"sig": "MEYCIQCZtoi8IzxPBK7Lb9dUDFE9rX7WSfYRfEQJrHsePiucdwIhAJfV58KYAgizeEc7oIijDkouBJ8LC2tGhB+HJoI4v7tc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 356282}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/google", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1b3f4527965bf2de5e35506971eb1c8d7e352c0a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.11.tgz", "fileCount": 16, "integrity": "sha512-EcK20MTA3zNJKNOo3r52Y0N960lGL6UxUimt13HFk2RJ4dXPMWl7ZhWFgjwFXwW2QwdSPKqlMHYjne3xvKTBcQ==", "signatures": [{"sig": "MEYCIQC3GlXU2fIg264UDa6mhC+8s23gwgE237+xI5NQsOSZvgIhAPnliyPfhPYx8HZ5vAez2vj5i+gXKG3/oQfmUoonDWln", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 365195}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/google", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46c86ab9add72fad6eec6055ce79c58d463e6bcd", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.12.tgz", "fileCount": 16, "integrity": "sha512-SlS6q6TtgwkSHV/bp11rH+n+mMhTATPpecoqsTg9A/MmcopCkO5BCg3AnVX2peDYpBiboBjr9bg1L5FoMJr7sw==", "signatures": [{"sig": "MEYCIQDR9lN0lzYAyGXdzABRw5xRubrObL/igAEksrH4MyCc5wIhAIeEi7/jVhCDbVahHTi4XBTHRl0IQHu5IlfWqOQLd9Uo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 365232}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/google", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "43a7087375fde68a522541b2187bb8d6273d06b1", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.13.tgz", "fileCount": 16, "integrity": "sha512-jOY8fxawLi6LVk1T6p245jzlY8LyLqpKHmykGLVjgcyq8aMYWw1WuMOvpQ5CIqk8Nz6/M+8h24V+Dzgv0Jl32g==", "signatures": [{"sig": "MEQCIArqVFBBHLeXcdWItWyj1ntguSdnoKxNQhma6JK8MJ57AiByNM5iqiUxQbOJ/sFT3GaHe5PN1FrpYpXCD5i2XISlQA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 365329}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/google", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f9f9173214f0f207b8c4f685a39faa6c7813c23", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.14.tgz", "fileCount": 16, "integrity": "sha512-ZFkSvfjBQP5sBKp9BolHYRxlbSweW0LbjJRE1QiIYRkkM9gifKnI/Atr6W7VGNcs4+GQ1yHsNi1bBDoubviqyw==", "signatures": [{"sig": "MEQCIDjRe2HRWnpLzbvE+mRgmLPAs57SwOxrzl3zJ/PL4qFPAiBQM02bLImnXDRp76TfduLDB9FI900OTfqHMUFc1WVWDw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 373279}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/google", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "69d558ff749d969040b6ee7f526f3d2ddb563e93", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.15.tgz", "fileCount": 16, "integrity": "sha512-7IW5IdygL4AnPy+of7fYIh1FW1B0w67CP8e4S8iSXULMMVYJBepT/oJodwdDpQIOpJDc1UEmzsVfr+XlDr1s7g==", "signatures": [{"sig": "MEUCIDBkbEt0mQ2mxX3Bt4z35ofGtwlnDktpzjEQimWkK1/kAiEAuy+Kq5tMiKX5PGqORPwjldmiHADI1a8WTx+FDDDKvnw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 373403}, "engines": {"node": ">=18"}}, "1.1.16": {"name": "@ai-sdk/google", "version": "1.1.16", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8190c8a9c210cf0005cd35c4a88df71ac7c6e8d8", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.16.tgz", "fileCount": 16, "integrity": "sha512-RxFzs0yq8qH2HzR8UpOCeUuSnKl3L0mDKZHlWFYpvdHnIB7KuR1pyP8uXOHaDRX/djoB/DIkXEkIVDOf/pEB1A==", "signatures": [{"sig": "MEUCIQCoB60oQfAaugLJf9OgV1r4pB5j2kZMvbPFpIaVUtMb6wIgLgcVp+5c9rND3i+Mjp9onkCjuz8uc5Lr2uiXLcSOZGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 374435}, "engines": {"node": ">=18"}}, "1.1.17": {"name": "@ai-sdk/google", "version": "1.1.17", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "832179e6800d0f84964fa8cd803b72ec25e67be0", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.17.tgz", "fileCount": 16, "integrity": "sha512-LFdRO+BMUagDplhZExOSr0cfmnoeV1s/gxpIsqt/AWCYnqY/dYGT74nhjbQ+rILeoE8vwnwUu/7OOZexhccm9A==", "signatures": [{"sig": "MEUCIFi3Q7bK4uGKs5zIktpKgTUeTY07RHFguJE17w1QaSpNAiEA6J4sSCVpKmjcA6TcVQpGdANDKbZBAKwcGrSmz9fYwEc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 374561}, "engines": {"node": ">=18"}}, "1.1.18": {"name": "@ai-sdk/google", "version": "1.1.18", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6538af71355f9cd039c691677961b31a2ed43960", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.18.tgz", "fileCount": 16, "integrity": "sha512-yOqoNhgkyBQPOJt3dPm86Ubn5qJ2N0pwrXE6xhJ42lM0tSszB6Gk5770QO3mTKqfUW2aQ/fbOb6f5u7YHcoy1Q==", "signatures": [{"sig": "MEUCIQDP6D2LrTp6+jd3jERv7/MLUSTdTOduYxZLzU+L/WUYYwIgIRBvSBqA17OdI0PlcFjjLexODwhh7OxkAswri7tiRLU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 373400}, "engines": {"node": ">=18"}}, "1.1.19": {"name": "@ai-sdk/google", "version": "1.1.19", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "258e878457d95bc2cba139c6a517ebdd88e8600f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.19.tgz", "fileCount": 16, "integrity": "sha512-Q4l2iWAADUf1pGbXX60A2nnUqEtPLtLpXsbjr3hVcgI9M3q9BqUmSoGsoJ/AAwvZU3uarEb0IJuv+7zlitvCBw==", "signatures": [{"sig": "MEQCICfmA5NKPz01iUSkjBaBI2gF3aX8TJp+FF6O5oI1DxLUAiBVBkqIcrf52qmBEC6oLKlTSotvPJ6lSe2RYXmXpXn2Zw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 375820}, "engines": {"node": ">=18"}}, "1.1.20": {"name": "@ai-sdk/google", "version": "1.1.20", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c0fb4287596ccfec56cb90ccc1eaddf525f966d7", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.20.tgz", "fileCount": 16, "integrity": "sha512-vsYtmFYy5vQAovWWAqb9XoLZ01lmcfpfxviY8lMV92YwwbLLhYcaPQ4dKaaKo0tSksCWsO2Qehw8bN3eBxmZdA==", "signatures": [{"sig": "MEUCIQDWrZP4YuSiyMA3cMFjWoFIqOTNeSKgOL8dB90I/8STCgIgTZux/BmZfRRd00lFEoAz5+p3cGGLYM1OEx5IB2YnLOo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 376713}, "engines": {"node": ">=18"}}, "1.1.21": {"name": "@ai-sdk/google", "version": "1.1.21", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6568008fa61d37044e1a9a7f708818d1a501fe90", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.21.tgz", "fileCount": 16, "integrity": "sha512-HlxrT0Yfb95aiFc65qBdH5L12A/XEp3Uxd1qcdYBKh04+34j5+yogFh59vPvoLDAgB/AY//CtwkoPrHJrXnMHQ==", "signatures": [{"sig": "MEUCIQD68Lv+ACoFF5WTbklb3Fq6EQVzEc6GJMBBUokE2c6JYgIgQ+DMPX10APA4XxiHIVHau7lovUOyKfscrLNmI2O7c00=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 376811}, "engines": {"node": ">=18"}}, "1.1.22": {"name": "@ai-sdk/google", "version": "1.1.22", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "53e82a10986d4e7a594195a7e7f39407a22c1241", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.22.tgz", "fileCount": 16, "integrity": "sha512-z6k+Z/aJMazxQ5cxJ7nPOw8QsKtaTuNb6aX89g5rrv4FJUs/IT051LbmEw4+uhCLgVllSjQ5SidvN9qw1mdMtg==", "signatures": [{"sig": "MEYCIQD5rH++60flpl/2Q1RAtwx+J46tpH1bN65RGJ6F5dtVlAIhAK83wK2EVOQwaWBH2kqQqyZ/WFyaGvjkpMaoXCJ+quHo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 383843}, "engines": {"node": ">=18"}}, "1.1.23": {"name": "@ai-sdk/google", "version": "1.1.23", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "477dc94c76e4994403ef6dadd561bde59ba50954", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.23.tgz", "fileCount": 16, "integrity": "sha512-sLhgjfVFr5Cu7RIweSkPslxLHj7ud44nB6QrCaEoR+nWkSowCX7c7271qwwSokMbzydu77W7VCbWJuwTrh2CMg==", "signatures": [{"sig": "MEUCIQCnAjXPfrHojhD1c4u+ktOxAqpygktJKiP1xUZs8TzWHgIgBhO/w4dAOkFxVcmuqO4pFFUuiIR/E2lyx5nIKyFbRTA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 384007}, "engines": {"node": ">=18"}}, "1.1.24": {"name": "@ai-sdk/google", "version": "1.1.24", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "72ba509530e0b60cbe8b7553f374079cf65e2ac2", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.24.tgz", "fileCount": 16, "integrity": "sha512-hY9OgzZz61CgZgQYK6NmQbb1IKiJ6NOr43mQIaBzgBlti2EG+l5u7xDFKlGHSi51cMpOV3sGfiFjWZah/p3lXg==", "signatures": [{"sig": "MEQCIEQTGNwi7k0CGaJlyA+iT93qasuJdBdSBjrpeHxusn67AiAgL2UsXLnV9FkcoT1tKRqpghbBf8VJfpyvRt/RozBvkg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 384877}, "engines": {"node": ">=18"}}, "1.1.25": {"name": "@ai-sdk/google", "version": "1.1.25", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5a4ac8e4b01183649aea5dc158a10b613879f472", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.25.tgz", "fileCount": 16, "integrity": "sha512-4eIssVphgVmAAsrDqWbH+YH0LXgHpXXY7ye7yuxK+fobpDifZ7LLLEwsbjtg9mfjwSUJ4dSyI/L5mlZ1eQqu0A==", "signatures": [{"sig": "MEUCIQCxbNuNhWWNYTPVEtoZUSSmHl357Hzm/QF4UpmnY3WPZwIgRsAbwLbISmUd7VFywkn+lRHGolVx62RSJ7QA60cbvKE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 385003}, "engines": {"node": ">=18"}}, "1.1.26": {"name": "@ai-sdk/google", "version": "1.1.26", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cfd1a4ca133933e91a75320a88b09186ed2829cc", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.26.tgz", "fileCount": 16, "integrity": "sha512-VHhwQQZZ2BPWgsw0CKQDyA7ZddDfNKXQ/lcTSRJD16nD86LaSZkDzx3Wx7Ln1lXmokzPEv/tuFrCs5ntqB2jrA==", "signatures": [{"sig": "MEUCIQDSmQqCQesO4Z64TGIzOBApiIHGW7ZohSuCKIWkqm35OAIgMun6Uj1eeigo9VDqvEG4QxDqBfO49QqH7mmkY96hfxM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407255}, "engines": {"node": ">=18"}}, "1.1.27": {"name": "@ai-sdk/google", "version": "1.1.27", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "149ac5561eee3e753a0140dbcb2418074f2c3cff", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.1.27.tgz", "fileCount": 16, "integrity": "sha512-fI0lg7jfqd4Cbtyo932bj1x11Jdhs9s2kNNjOIL1XXxj4EygKepYpHmy0tnKz9gPm6lUShrgnvdKTk5K8GTcRw==", "signatures": [{"sig": "MEUCIQDNBG7aPp0FgH0p3m3aiboccyA0ceIOrgV1kdbT362EmAIgf9TMHJ9/IY7tKbSn20H3pEVJkmdn00+we3lBpGFV13w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402111}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/google", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "45be20038b44fde8cd32add390291134d6d20815", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.0.tgz", "fileCount": 16, "integrity": "sha512-e/sDYsjOc9YdzhnobCt16bkHsEkDEFHs7ZCAOQ1A06zJhRYVll3hyAmWDEgXf25Zdk1R20gu+JEvnIpZ9qgT5w==", "signatures": [{"sig": "MEUCIEwz5RrT2b+0j0bWkkpVQvh/XcnH65fmfUmIk9qmPDkoAiEA6HNY3lHB+8HMTaN51esfXtG1oo/X8YwscWLFp6A+TzM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402278}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/google", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fd6d0b061b2d158ce751bc85566837bd20913f2c", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.1.tgz", "fileCount": 16, "integrity": "sha512-kT3o8pEbks5JJDTIkUdL7OHCNcxrnWYu2s+DQUBdyJt7BTgCBLtqZAwTeDuO8lDAW2fRtVSs7Gk6OVU2yiutiQ==", "signatures": [{"sig": "MEQCIAorjueF1zNB3hvzpoZDjTmbO/iIZP3Ceh2mzxK3YB8dAiAZOl/b/tS9p2u0wkpDzWPSTG/SkJ57S3BRzqo2hYLL2w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402642}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/google", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "45cef7556e868a611410c8924c48c3a6e4e619ab", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.2.tgz", "fileCount": 16, "integrity": "sha512-uHAyn050ZzVYcq9gOhRNnRB40/obh3l7se+zf7FOFi4SIUeRTaIsQmlAJ5QxgGhis/90CZ4U39E5nDf5foW7/A==", "signatures": [{"sig": "MEYCIQClGnOpFtUEISLzM8uQoumsbVg+H2izywCrkhdWsoJKRAIhALAu3tsly5D4LSaHTQEzXIk95xk+MP9EFCYxj/K43933", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402738}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/google", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "83233546d60d96b04b961760caa0408824d45328", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.3.tgz", "fileCount": 16, "integrity": "sha512-zsgwko7T+MFIdEfhg4fIXv6O2dnzTLFr6BOpAA21eo/moOBA5szVzOto1jTwIwoBYsF2ixPGNZBoc+k/fQ2AWw==", "signatures": [{"sig": "MEUCIQCiReyXuHDGLg6P0VxFZUTDeXNaiYjCzilxbZWoZKmJHgIgOG7WCUiw2FfB31D5MxEJM1m16kw+HkPp7pwz3MvPFWM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402962}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/google", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2bd5ab7b218a4c704c73fd2709875d49f5e3cb12", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.4.tgz", "fileCount": 16, "integrity": "sha512-2llZjB8vWxgeVrm1BVOQ7jFpxtoYnXciHBYd98E4z7GGgIfo3wfK+YFn4DF/ycoAjAUcwCjP1+Lqz0okeUFWcQ==", "signatures": [{"sig": "MEQCIFxD58MPtMWW6XD0La8UIIUbodB8W5Q9rOJ3EKBZqokhAiAoi4zV0/qyeqQ+qwJGhQyr3ajfADnEC0icC3yKJU2j1w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 403058}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/google", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4e151f3fb202f1de02659f398964f62fb96474ab", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.5.tgz", "fileCount": 16, "integrity": "sha512-ykSPjYDmaDg7Qblo6Ea6n6O01NpyehZJE0j3+HCYBtUXKXP2RZWesr7XlceIfFBKHd0sumovRtX4ozHrb+1+sw==", "signatures": [{"sig": "MEUCIA+EWXe4SRBrdFOSDhcoWc/wdTDYjqB2uisF2Mpd9423AiEA+Mrbl5b+SGjcPna3Jnq5B7HBMWmmKCME6FX/T3Lad80=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 403154}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/google", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1d70d847b0753d68b36d9ea5412790c549d73b62", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.0.tgz", "fileCount": 16, "integrity": "sha512-mI6xo8vtP9RwE/cFDCxJYRB2bRwvyFUyawL5DqAu3rDO+1l3oWsky8BNRr8ao3lQ1FvE39t1Z3yHSU5xmg4gzg==", "signatures": [{"sig": "MEUCIQCKkrID/66VvwyIoGN0oSxvPiQKnVM42EZ/2HMTjdeiCgIgapW5+seDBi7y7iFLwW32ns7fAn/IKY8ddIvIvMOxRwY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 403435}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/google", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c212948a4f79d998eceeb128fcedc303ac528b6e", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.6.tgz", "fileCount": 16, "integrity": "sha512-e6vl+hmz7xZzWmsZZkLv89TZc19Vjgqj+RgvJNg03npRiuG4f1R/He1PD/JX6f0az//Y55CcozCcaj4vnMz6gQ==", "signatures": [{"sig": "MEYCIQDA+uOwd8Z2XQP8HykyVpC8+S5yuZd11ATx0g7sGXU/lQIhAMCuQOZmTg1J51LZxumaPkYnokhhWtTNJHUBxNeSkC2z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 403314}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/google", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5920585ce920d2b29bd4359986a3d849129fa34a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.7.tgz", "fileCount": 16, "integrity": "sha512-swGJ4nPRB83ZQgR9W5fF7usvZmZsBN61+dm03Hz/dRVVLLLTVZq/0YtpFZ8Yj0utNs9K+NvY0456e7cN3Ff8TQ==", "signatures": [{"sig": "MEUCIDX6kCTrwy7prgzDVAabHTRxAh3zj73sxTK8jjoHZTK3AiEAmrrxR1uf5Pn8k6tYXq0Fp4mJm5AMEOcT5Z+DFlm8E/I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 406563}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/google", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c7aca2d6de6668997d2b792d1b24a2ad33ab9532", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.1.tgz", "fileCount": 16, "integrity": "sha512-G4h+iUjs1aa8t37NAhPUODa/jnPGpH0LL9xOMf1tpLRDtiIArzdiN5E0dgHhrsPFLksOMMgdcweWnxmU5FWURg==", "signatures": [{"sig": "MEQCIEWdGKQxJlPiF6iqmQCgnhjs8giTgT/lADwaVm2iuhSbAiBT3OGU5hKj2/kEd57pJ7ZLEp+jcXB1zhVgfrWZIAQPlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407820}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/google", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "33e76bfec386e11758b19cb0f1a00d769a000927", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.8.tgz", "fileCount": 16, "integrity": "sha512-Q7Y71KGyH5sennNev5xZvt5MhxWlu+crp7tZZtdFbQ9iDkOGrl+TurPssqM0Wv5lYmV+Lc8m14CK9/k7nF2IRA==", "signatures": [{"sig": "MEQCIF8NmKi3r9BXvKt+DZFyN6Z3VMmU3h9uTOWobMWPk5O8AiAMZAXpCU4Ec4N+XViPdm84mTBgYQLt14DW8fKB/2MqXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 406701}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/google", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "49fd01784259a4067e2b8b2449a2a9ccd8da9127", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.2.tgz", "fileCount": 16, "integrity": "sha512-VPpymjzPIQX12CL8mLyqrl+tQcKnG3F6Lnns1seVcwU8pzUgUo7YeO+99X5yut9p/GB2T7aNjtk4kZodeYstcA==", "signatures": [{"sig": "MEUCIBPpdcR0D+qCvZcK0lxWU2T3XFZMlkdoV+YgNkxBwpm8AiEAn5Mo6/PAfS+g+x68ygRk1iSRREhWBYIokVqSTiLcDcs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407967}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/google", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9ed714225d740cf394736a6575ccd88ee7b0e8d9", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.3.tgz", "fileCount": 16, "integrity": "sha512-2Sn7HvqlGOubcv0Ra/SB2sFGtVtohJ5juMs6RZCRjreBcXpxaButtEjuWEHEtUOKnraz5bikqce6VIeTdqZsYQ==", "signatures": [{"sig": "MEUCICZHdkbxl3KTcXoz4gJqPUaw/uAi0U9A9GEMnYm1bJjSAiEAwiT4gt980LBn7BhiRCggyynqTwPtqtxzOJ+Z874GpkM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 393134}, "engines": {"node": ">=18"}}, "1.2.9": {"name": "@ai-sdk/google", "version": "1.2.9", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3a96c1a703ec362ff721a4038dc0cc11ca300c73", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.9.tgz", "fileCount": 16, "integrity": "sha512-594vn1fa+uuPzDjf+1CNCnni7aiMDJAOjTD63A4UscGWwrIAr+FAt6sx4xip/kOsd9OJPfMp/vdRS26EujV4Zg==", "signatures": [{"sig": "MEQCIBu77K70taBHcuoDmIGD3fyMfILxPXDxuwa65Y62wDh+AiBIYMKmcQ+91UieICnjxVeSFqBWgHxkKIuMJ6VaR/AGGg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 406824}, "engines": {"node": ">=18"}}, "1.2.10": {"name": "@ai-sdk/google", "version": "1.2.10", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "826b6691735875279225fa933ab8d5d02c88e5fe", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.10.tgz", "fileCount": 16, "integrity": "sha512-YmZ9DIO6Un0+RU9PtjM9TfoExmUQg2fk8vTlwT+NOaARyhv8eskRCUTne0zf5uUOazPIJuBEv2I6YE9XnS+tUg==", "signatures": [{"sig": "MEQCIBc5ldsNCT6VPgqaKUC/xy78lZS/N6g8gec9/ePM+7DdAiBdQ2CFSxcE3uFv5tN0uKxcVNe3WaFGsifaImMBnO6m4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 406949}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/google", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "34443d7007bef4d5a148c5c80a307812c08b88c7", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.4.tgz", "fileCount": 16, "integrity": "sha512-1H1W24CJ/FT+KT1+TVCNZOmSlkqDWb5X5mfssT0pWi+juoHPy2TtnZBFMZVPN+GQq2cRHTClhjCZn/jrrgqKNQ==", "signatures": [{"sig": "MEUCIQC8Td5ack/M77Xn8Qxc0DzzYhgyShXD0PKWdsqjYt7VugIgF7oVacX+xPG8pGYsM2LxmBNbHICzKeDhw2Ycd3xCgGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 386754}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/google", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "00c18ebefbc47503b29205bc1e0ae093195bea70", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.5.tgz", "fileCount": 16, "integrity": "sha512-K/wCztCQppkHRG+DwyFsOupxRt6AFe1PIH8AUEZTTXgSSBkXMsV6A0wG3cvwfm417BV7wDUKt97AyMgN5K80PA==", "signatures": [{"sig": "MEYCIQDaOsUy22P7yeOLYbmouPFGNLHcxAci1h0ZRn2kc+hOHQIhANO2ai0pXe83KnRD7/oHQcedJ+UEW+lE71SClayClrwW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 387440}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/google", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "253810293314d51bf9c5f06d315db742dca5f704", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.6.tgz", "fileCount": 16, "integrity": "sha512-rxT6Hvdx63N0ld/+2oqusNWJWIoTwhCARwVxxS0RohUqlGw5jcHKNXQQCmXggDsAUo1GMO4VPpcfG/5rXpUePg==", "signatures": [{"sig": "MEUCIQDsyWc4cN23ixwZQYKfo0p+BIHRtLs3VRul3WOLyI6VGgIgIAHaxicfsKLLDIqajmeKI2/IX4bP3LHS1FnyMa6t3xY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 386668}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/google", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "53d9037169cea89e0936145a0b9682b4c904127d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.7.tgz", "fileCount": 16, "integrity": "sha512-8VODK8Rnz9RsImzXit5O3vHIeWVE87fT9ujvYkn3p3cUMyWL5cQffsTG/r8bwpdnJoVg7CegF2ayUv7AEb6csg==", "signatures": [{"sig": "MEUCIFzeT0JrZXqFF2LdOOKm99AioUfmi76DzWekv9owZDExAiEAt2Qd0u6GFPpP0vf6oxWbjPbdb58awB/ozS+QeP5gl64=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 387870}, "engines": {"node": ">=18"}}, "1.2.11": {"name": "@ai-sdk/google", "version": "1.2.11", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "39823b31a12a0230a0b801dd7619c08c94c9956d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.11.tgz", "fileCount": 16, "integrity": "sha512-gjGcxKcRri/Jbkujs9nVwP4qOW5GI4rYQ6vQ17uLAvGMo3qnwr26Q2KUqUWuVHQYtboXVSrxC/Kb6sm3hE5WUQ==", "signatures": [{"sig": "MEQCIHL/PR3HNqz8mbo0y0A7cKdrLVtUlcenEISG0WXbV/KFAiAOhP8OEQOBrbEUYGKKuTvV4O1xsUtGal1R3QVsfH3rlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 407073}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/google", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f5d91da738eb9b76f0a158bfd19f8c3464eb9314", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.8.tgz", "fileCount": 16, "integrity": "sha512-2bdlje+meNc/RPPsFU4wPMKhg+A6F4NqrTq4C6mFqk9CMWblJVBjjgmUfzZq4gYpoGmaZVHvuZwb6CLlyF8cLQ==", "signatures": [{"sig": "MEUCIGXTu5iA3NJajrL3HgCg/YIyqr8G/OnBuWCtT7urLZkIAiEAodAdJM27zFJuOWdG4AtS2Ce+whpwknFL5aMQgSI/CJ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 388925}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/google", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b53ddd31ffda5bfcacec2b83db4d312c36bf53c7", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.9.tgz", "fileCount": 16, "integrity": "sha512-uxbix9AejikZC8QqYg/yd7lBYmoTan8XMawOIaFtv01FHbzM9PpFTKAIQWDgsHO6mf5cSdMtdXnsampcG11g7A==", "signatures": [{"sig": "MEYCIQDvGmHH0uZLXSfgx3NjvPuWFCjzCDqzCcM5GyTJ/6Q5ogIhAP9VO6zE+BckCXy7h7HTBkiStMSJeQMV1tZJSnOtCfrn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 395610}, "engines": {"node": ">=18"}}, "1.2.12": {"name": "@ai-sdk/google", "version": "1.2.12", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d03fe0d7181cbda23751d14dbc89d80c836da550", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.12.tgz", "fileCount": 16, "integrity": "sha512-A8AYqCmBs9SJFiAOP6AX0YEDHWTDrCaUDiRY2cdMSKjJiEknvwnPrAAKf3idgVqYaM2kS0qWz5v9v4pBzXDx+w==", "signatures": [{"sig": "MEYCIQDfIKqV520Xy5x8NjT0IH0hZQfhPDPNoc7CCJ2Uk9ZC4AIhAKC7dTuiF2VrMQ9Dvk1pSGEAsRK1dYjtRZ0ytTkF3dvo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 410960}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/google", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e14cb881f944e067bd731c3f2974f463b3a8b60e", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.10.tgz", "fileCount": 16, "integrity": "sha512-XAaimtPZQ+Zw2G5cH7wcVINiqE5+HIJdLxcLyvv8DqU8rizh3zw7aBXSn1NapiJXlETlgVYftJupGSzRtVppOQ==", "signatures": [{"sig": "MEUCIQDFHcEv34oLxttT4CzOFrwsR6NBI+IQATY43DhC7eaBpgIgJKsIEP2+NNxVk3DLT/HP79QD/Yr/uElzVPc8WzG6V5s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 400324}, "engines": {"node": ">=18"}}, "1.2.13": {"name": "@ai-sdk/google", "version": "1.2.13", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "28e47603d97b99823d05720a78f231c12f53f43d", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.13.tgz", "fileCount": 16, "integrity": "sha512-nnHDzbX1Zst28AjP3718xSWsEqx++qmFuqmnDc2Htelc02HyO6WkWOXMH+YVK3W8zdIyZEKpHL9KKlql7pa10A==", "signatures": [{"sig": "MEUCIQDWjJzGhq1XZtrQsJcbBnmdDsl5w7QSzSSTcfqIhCV+0gIgZ1E+l+w0wSs6XndkdcP/mwk/Ex/rc3pY5t5ZCmKrF0U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 413019}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/google", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f02daa23f05a6e38fb8af234782742cfaca821a3", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.11.tgz", "fileCount": 16, "integrity": "sha512-uhGd5raL8lD0IHX7/07p9WIRfvC7HJOWSHn9gaLdZrg20rF7mhyCH4AudNWOfwz0cRtcRaTTMr+gcZ62mNuNrg==", "signatures": [{"sig": "MEYCIQCPLDKjoCiWNAV+xFnUr4QY8R5/A9/Yr17cG23vROk1VwIhAL2kTC7LhAxU4LKU4FyTIptTZXtJK63D6UGiheHE97UT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 400477}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/google", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e88fa5abadad58ca401ce20fe2db3a2375dc18ab", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.12.tgz", "fileCount": 17, "integrity": "sha512-rXxAJPJk7pSb47PkkrLPfOSB9rqWQV85xUQngRixAHl303Go66lVxU4723rOganaZaAjOyku8sF/w4UGHxxJLg==", "signatures": [{"sig": "MEQCIFSeLhidTGJ+kD5bX+/hH5mx70rzgeYd/kd9t1FuWEDfAiBXAS5SGKo3r7ANUdkjsu7Ii5+mFof9BXAk5jg1vNeG5w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 405222}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/google", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5c99d165b99f55d9ce684b6db3bd6768bf4c96f1", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.13.tgz", "fileCount": 17, "integrity": "sha512-8rAuLUMDOzq1i485fLTRzhVfOiSr9HNgQotXewAYYC6bFm/yb+9AUCAbunfd5GwB3lePzT7BwxoxM7cpjxCSRw==", "signatures": [{"sig": "MEQCIEFppsCyY4DS6Gg9Dv71j8WttRT2pzxyNNHvF9vD+shHAiBvmwZ932e5Au3hYz+OgvZpaF5lWBGx86GftefLnanFuw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 431935}, "engines": {"node": ">=18"}}, "1.2.14": {"name": "@ai-sdk/google", "version": "1.2.14", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd165e3ed673574e46b4d01dfbb86d1509f71312", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.14.tgz", "fileCount": 16, "integrity": "sha512-r3FSyyWl0KVjUlKn5o+vMl+Nk8Z/mV6xrqW+49g7fMoRVr/wkRxJZtHorrdDGRreCJubZyAk8ziSQSLpgv2H6w==", "signatures": [{"sig": "MEQCIE8zEcRnd1ExRgrPpn8WRwE5xXI6TEWKdriENzWyYEKoAiBHKpiBu1Nl8HnWCKurlRpgl7TsERywG8xkeiq1pZB4dA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 413395}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/google", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3327d0543ee2eabdfcecd63916abd0aafa6e50d8", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.14.tgz", "fileCount": 17, "integrity": "sha512-Ik4xXaSVp7d1bP7J/Vr+cvMqGkKYZmMY9h/wCK1/+wLgdQm6b71gd5dO5z6aLlHQwLUccnNvTvk61vd8CMDOEA==", "signatures": [{"sig": "MEYCIQDfvFEx7gDARYs7rWVDEYu4UNf1E7xMrfoyDihjTpk+gwIhAJLTY5pSxCRQrbvdE8IM80O+WpjdfmTa/bdvI6RCeIrC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 432610}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/google", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a5792debd6ff8d1a1b3f487cd52d6dd816972c57", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.15.tgz", "fileCount": 17, "integrity": "sha512-J6Vobzfnn+rULUoj1dP47Suw+U4HjEibk9alypw2nouPLbAbtdasEyK/p3qhUTfPqwHeEDgn2GZR6ArJX6j3/Q==", "signatures": [{"sig": "MEUCIQCV6VTIyabvgDonjH8CYSXI1zGOp/sn8EL5uoR/8OSa6wIgBjg3pSvnz2HkFnkEaskrcVs7XJi4A6ktEZs/Qkg6qrI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 432796}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/google", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3b03e53d60723b390fc228d7e36c4a7cf735a0c6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.16.tgz", "fileCount": 17, "integrity": "sha512-LHRg7EzU+34jtXMWyZwdoDKrWVbtGpbu0bz/gL88NB6xzgM7RS4DQj5K4R6Q1l9iXUVP3jr6KpkBF+DLWb6xqw==", "signatures": [{"sig": "MEUCIQDPpGjPqxi0XjJ2VTRUzR++j8kkZOgRbzBSEgSYQSl1CgIgLD/xufNjRsZ/rUpofF5DkOZ/tXO8b/AnPE0k4zdM0+8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 438408}, "engines": {"node": ">=18"}}, "1.2.15": {"name": "@ai-sdk/google", "version": "1.2.15", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ee3543709ca0f4dc6ad084d803b1dd42cab4a1f6", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.15.tgz", "fileCount": 16, "integrity": "sha512-NYHzLz+HBnf7onEJIZkFFGKLnD3LTIw7REULDjVAUe1z5cXRu2igGV4xv4kb/lSEKLxef+urxhKpDAOfbIASCQ==", "signatures": [{"sig": "MEUCIQDarKouEDZorqw2Z7jcP/anUwdHL2fGTVWIsDT9KhNb2wIgbrtsuMwDZU6F1p+/GeOz6Hw7vC5aQOADzd6obord3gU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 414280}, "engines": {"node": ">=18"}}, "1.2.16": {"name": "@ai-sdk/google", "version": "1.2.16", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9abd8b1d2ebb295d8412848b01703257a2911819", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.16.tgz", "fileCount": 16, "integrity": "sha512-n9neq6s2KjvPvcTvKd8arD4wliIVYda7Iftm3tAad1NthdDoyqG8K75EYmxmCbDeuCn1OLpc97BcndMG3wve/Q==", "signatures": [{"sig": "MEYCIQDuA+T+cCjedeftcNoZ2Fu+4YO17Es7qfkTXFIo3F9RAAIhAIWbEp0aHDCYFixGu7kfAak/HTZeAox/EEYtkW302VDw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 414511}, "engines": {"node": ">=18"}}, "1.2.17": {"name": "@ai-sdk/google", "version": "1.2.17", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e9a3a938ae69ef687ec3bc88469c37afdf204f5a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.17.tgz", "fileCount": 16, "integrity": "sha512-mLFLDMCJaDK+j1nvoqeNszazSZIyeSMPi5X+fs5Wh3xWZljGGE0WmFg32RNkFujRB+UnM63EnhPG70WdqOx/MA==", "signatures": [{"sig": "MEUCIQCKKU6YfOwOS9fk7Aj9JAAyCSBmZtwDVo87BXreSAxqzwIgG52wSbtqj4M8iNCCr8cNuf1tmGw7SBubT6fZfALDQns=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 414608}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/google", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c7c2a4bb5bc7c8828987d897827d0ae5f0e545aa", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.17.tgz", "fileCount": 17, "integrity": "sha512-gcTcrS6R+plmu3ZRO9MS02QKiZY3KIBDIJWFitbhebLJiLpiE8ARzrJdouPGMvzVM8EffgaBRtU4Ix69cZMYhw==", "signatures": [{"sig": "MEUCIQC6a0ZaTgDMAnvIiLnnt1FX7uT82Kj2FO/btjCEotLQGAIgAvX/jnebfjI0KG73dm+2b0JHUPO+CSQKRIgmsxmEQ2o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 438524}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/google", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6cad512679743fdd3ea63bd5c1007e936394b3ed", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.18.tgz", "fileCount": 17, "integrity": "sha512-WMsjcEW5GymLDeLecCqKFD/HNe+pHU0ut+/MMZ6vMAYu1ifeoljbFGGiKlW60uthWYzLCQvUuUZb2dI5ZwuuLQ==", "signatures": [{"sig": "MEQCIGsNdfroQVxc9mnr9S7BMHihwpVXTs66hhiZEDUmOa5lAiAo8gTD6oU5wGIBzXHXg+/YbMFxF4EXqwePzhkyUClAyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 439506}, "engines": {"node": ">=18"}}, "1.2.18": {"name": "@ai-sdk/google", "version": "1.2.18", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f92f9e8d060020851a78ec17597566fc0cb5f93a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-1.2.18.tgz", "fileCount": 16, "integrity": "sha512-8B70+i+uB12Ae6Sn6B9Oc6W0W/XorGgc88Nx0pyUrcxFOdytHBaAVhTPqYsO3LLClfjYN8pQ9GMxd5cpGEnUcA==", "signatures": [{"sig": "MEUCIQDxoZTDBJCoCsD1TxCQp0FvDA/zY4U6UhnOfWcOM+U2jwIgKQ8y6MO6fEksMDZtA0rXfJka0MlwN52aCMIYx+7DMGQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 430934}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/google", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cd9bf56e09758acf1660311fa5986e2dd040fba7", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.19.tgz", "fileCount": 17, "integrity": "sha512-MpGN0/S/0lkvhusOGABxWLrbQSBKZ3T1S1/wo/hHQMG2mCBBBfiqzVY7MoLrbZxC4ALSS3/aTR60S1e0vBMvvw==", "signatures": [{"sig": "MEUCIF1EY6oMlpSzyrwc5v/lpIc6wNLc3m98eiZj+7oN2VZEAiEAqFvS8nDErOLcvDeN/n8ZqMN3GNQkAY6ODy4cGcHKK7A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 439622}, "engines": {"node": ">=18"}}, "2.0.0-canary.20": {"name": "@ai-sdk/google", "version": "2.0.0-canary.20", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "22830856bf3b3366da47092127eb99db10c73a1f", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-canary.20.tgz", "fileCount": 17, "integrity": "sha512-2LqcuiADGzr8mqzHfHU+E7Prjl5Vb4aqFKUibp1GF2pLy9i4dx+4lwRRn5noYBoKQcjJTKtO+PkPW2eErvunKg==", "signatures": [{"sig": "MEUCIQDhXX/r4r+h87zr7yzOTpWqzkiV6SQca5x2pK3ZQEY3OAIgWcaGfZVZr042/Qw8/DT8Ja7MsCBKpblgHcnk3aTpy9U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 439739}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/google", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "5ee0202405809265add7a06a7b30b0c3e72c822a", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-alpha.1.tgz", "fileCount": 17, "integrity": "sha512-YW4O/EjFuPEyg2N6eRIBy7mNJaaP/6tmaP3auI9sEdvdgiVMfVcQzFdA6pwjFb4OC7KDet+jlxlKGBtLIFPZhg==", "signatures": [{"sig": "MEUCIQC4r7AEQi4m6eXVD0GmGRSOpcP7sObuJnfKour+9wdXSwIgZdCdtIb6IMv2FJ+ILZLuHCiTkLod3qR7V2HlBLn40LE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 439880}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/google", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "6f2da61ded891c01930d81790eddf1cc00832ad9", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-alpha.2.tgz", "fileCount": 17, "integrity": "sha512-SmG2ss5TAA0bRYgImh1J86QQ951oFImpnRrtaN/RdmNkw9eNr0fccj4pNzUYkjiyhppxBwG6MajDa+8t5vU/yQ==", "signatures": [{"sig": "MEQCIH8ofNIYnT7uSma6GhGWq9xT+xnhMoUKSqqv0udZA9XPAiB6kyUQiTpfMvlbuDxQuMMViD77Qy/H7sc/koMcA8cCCQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 440027}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/google", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "b1500710b8555bed9128b35e67641f1150897185", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-alpha.3.tgz", "fileCount": 17, "integrity": "sha512-4JM+RhzH6AXFSM82JafmH8pdR0KLDu5xJBe6Klevo0ILsV/B1u9pwYkdacZeA48FgFC7EnlsC/SwlNdOznZATA==", "signatures": [{"sig": "MEUCIQDuFG85gwwYG87DUmHWvY1wdeUH301V1rbXvev9sVa4sgIgQZ7DtaYmS9zk3k9byzbLHPVkdK5/pfh5nwkImFXWhIk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 440174}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/google", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-6cLi1kRNoztqvdWwLrxvYUMjOrWekukHx6vCgTIHRzS+r6c6l8f4EkUJDpMDF6M/PrSa4dKZCQ2nxfoCwAghhA==", "shasum": "64f94b8787662bfe528b6ae1a10012d595144060", "tarball": "https://registry.npmjs.org/@ai-sdk/google/-/google-2.0.0-alpha.4.tgz", "fileCount": 17, "unpackedSize": 440321, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAJ0j0ktGpklQZ+1rc/YogToyp8oZN7821Z4cjBGMxmYAiEA/o/sv1BOD5qaCuM4Ko67N0mB1HO4k4am8mKF/UaH6zo="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:29:57.669Z", "cachedAt": 1748373701694}