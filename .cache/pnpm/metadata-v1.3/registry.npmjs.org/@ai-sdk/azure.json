{"name": "@ai-sdk/azure", "dist-tags": {"latest": "1.3.23", "canary": "2.0.0-canary.21", "alpha": "2.0.0-alpha.4"}, "versions": {"0.0.1": {"name": "@ai-sdk/azure", "version": "0.0.1", "dependencies": {"@ai-sdk/openai": "0.0.25", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ee75e09aa1091456edfe57d34c52cec31678bc1e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.1.tgz", "fileCount": 10, "integrity": "sha512-m/j7g/rIkwfef6x3ffEXQlkbtzTLmLppF0SYk1LhSeRgkl6uQ1RiLmqJcMikTHqJIdClfFK07fIdpl36EBpUnA==", "signatures": [{"sig": "MEUCIDjOrLgGtnE0/MVxLANaPrbDY6FVtLH9V31B5aKCZrXIAiEAkMzxEQpGRDBHy/tjnBdIqQSnwkTRmi/rJ/vFCCp4Wwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17261}, "engines": {"node": ">=18"}}, "0.0.2": {"name": "@ai-sdk/azure", "version": "0.0.2", "dependencies": {"@ai-sdk/openai": "0.0.26", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f152e5a7f9d4b4e6421d221638b41a08f72b3f5d", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-gtF7YjskKfPlWqdZl1/w5v8Q9zMVNgXFULyioXf0QvlhDTlPeSSTAXs8zz1pQRmmspm45ruMan19+tXdm0HJ1g==", "signatures": [{"sig": "MEUCIQDptc/VnvWjKlJJQ1I6V7f3mMIWfEPY4R3NDNzC2+y0gAIgD+TLtjjEWZioFGV7T3waOYXEDTwm18qXhnC0XfJds5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17261}, "engines": {"node": ">=18"}}, "0.0.3": {"name": "@ai-sdk/azure", "version": "0.0.3", "dependencies": {"@ai-sdk/openai": "0.0.27", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cb8bfc321888b07957cea9bd822bbbec229e6154", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-kdqoow5m/YjKu/GdEwCMkgwKOlxTR83ReQDSe9z+F5LUTJVmTRVJKQ1lkVblsMXuTZ8YQbX1dHZpJ649O6jgQg==", "signatures": [{"sig": "MEYCIQCTSFuzD9DjjNLOdRfMwsdbpNVL48E50LVFwbUhn3xRfQIhAP85attXlZATvxKt20rxSeILSM6CbQS/mQr1xqDHD8uj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17261}, "engines": {"node": ">=18"}}, "0.0.4": {"name": "@ai-sdk/azure", "version": "0.0.4", "dependencies": {"@ai-sdk/openai": "0.0.28", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ecf6f953be82d3af49299de69474f116b428573", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-l6el+HW7Tmlv1UqHnCPSKGM6FCpMGNPOHow+WTK3YwlIo7rgLEPfPhMCeCgpdnvqi08OIxtMDG3Kuh/+8D6z0Q==", "signatures": [{"sig": "MEYCIQCAsHEAvpmr1q7pzJiNIitmItugj5BuWqF9kUpFRFnA0wIhALNZplr3eaRkunS3wuHUW4kEQGtQ4Er308O0FVitCT5B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17261}, "engines": {"node": ">=18"}}, "0.0.5": {"name": "@ai-sdk/azure", "version": "0.0.5", "dependencies": {"@ai-sdk/openai": "0.0.29", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ae4b78ce2b04d8ceb7ceb8837160be244772b481", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-NUQHHvz6o9zg2V2wjvj+D9xejakmeMKcjtt6kAMppbZx5TLN64WEaQN5u/R4i09L2FrRD+zcOjtLXMsfLGYJuw==", "signatures": [{"sig": "MEUCIQDfkuccgK3vMJyOt8LjnGv78Z6D59TAADMF9QBXUDez8AIgVEs4JGMxIzCC/CsfReuQLzXpg+cGSLFYGqRozNPrx8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19169}, "engines": {"node": ">=18"}}, "0.0.6": {"name": "@ai-sdk/azure", "version": "0.0.6", "dependencies": {"@ai-sdk/openai": "0.0.30", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "13ff2b2bd469a0ff766ae7dd8f3f358c5eb0de84", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-qc5q0PMMsaTdQZfIx+tuVVsQfKa24dXpzc+3JjFoUZP1SHfP2ikYCX+MzGbJavtBDQUD77G0PAjx75mkdfGHhA==", "signatures": [{"sig": "MEYCIQDWjzDKeW2ScnwtXQz/4XaXItjVQHhFJwd2Pc8rN3QhOQIhAMKg++p6XJ08q7FoClHdC3rP9O1/5QBD6xUN1GZ9phIP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19169}, "engines": {"node": ">=18"}}, "0.0.7": {"name": "@ai-sdk/azure", "version": "0.0.7", "dependencies": {"@ai-sdk/openai": "0.0.31", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eda0128369621f8765f4dae4ee3ef748cf088c7c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-3xWmniBWCo7PbFQeY2yKVkuV2sqU2rWxfNq8NIQsh1ZLUfbUYU1yV2ZIDegpxstFJLUoRrqp98EXggPOpYSJaw==", "signatures": [{"sig": "MEUCIQDOjD8C40vxwm9rcLJKxh+6SZFbQj+9l7vL6ZLQAwxhtAIgV7C1ImexGuSKZ+FgkPC5L0/kkalFJn+FDbyLPM4i8dU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26180}, "engines": {"node": ">=18"}}, "0.0.8": {"name": "@ai-sdk/azure", "version": "0.0.8", "dependencies": {"@ai-sdk/openai": "0.0.32", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f5617373e6cd046ed2b1af5fc04f72ed13ec9ea0", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-Q2BcY7wiXj+SFqi9lQvKVpkuH9tuLX82+gH6qrEHnthVpuKq3xYhMpk8cafvkoQMydcNo5qTNvwS9kcyoMWHKQ==", "signatures": [{"sig": "MEUCIF2PZAhRhNDfTpeRhklSH2SrO6m2HiTzoLbRwSSpKSPoAiEAgLrsUdqCF1FTSI0Wnay0CG8gY+SxYo+7V7ehCP7wbpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26180}, "engines": {"node": ">=18"}}, "0.0.9": {"name": "@ai-sdk/azure", "version": "0.0.9", "dependencies": {"@ai-sdk/openai": "0.0.33", "@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8b5bcc3ab1d9496d47487019c3b1835642ece64f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-3SSt3aUW80vV3lOxDDEmKi62vbPfhzGpEEZfIp5gq7HsaU2Vlfo72UQu061LkcjO1/I0OwiOrvYky9LBZ0JkpA==", "signatures": [{"sig": "MEQCIAcSJWR9/3TA9l+OxIwO9cKHP/iEU9l9p+xMeC7CY45zAiAL0weCJvutdlYWATk1/6Z8D2Rv7T02ncw9zb95B14sQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26180}, "engines": {"node": ">=18"}}, "0.0.10": {"name": "@ai-sdk/azure", "version": "0.0.10", "dependencies": {"@ai-sdk/openai": "0.0.34", "@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4a668bc405b21e02ac9ce41455e81fc24d75bbf9", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-3gMH4CzPZlacHpHtNdIkfMI4CpiuWMSDuK52x2SBPQyUaUmjqlsi14FXv8eQo47ZL96D2jmp2n1PwDRiBTtZTQ==", "signatures": [{"sig": "MEYCIQDssBAMzeTyeQw8+THk3Ym0DgrH6ogxgOuuz+Q95fCi8QIhAOQ8oIpeLIDpkkQ0EpzFcaa+d5q9k1khZA1VSSP6Wvri", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26742}, "engines": {"node": ">=18"}}, "0.0.11": {"name": "@ai-sdk/azure", "version": "0.0.11", "dependencies": {"@ai-sdk/openai": "0.0.35", "@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ae479b8a422ada252c7c4da948a198365a08d5b0", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-cCJb103uwe1CVQlnVCSELJ736FT11Fxy4PuIqrYjWyZxE30x9WhtP0yo/HALnMFccCXkmpU0zsBOYckwx7tfuw==", "signatures": [{"sig": "MEUCIQC9JPlSuHkzgKDv7Fzihx0r6YapTZZ44qKu8ozEo+0z3AIgJ+T+MnA8+NzXssuwSile5eYe6bI+/t30fMcMAnus/Uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26742}, "engines": {"node": ">=18"}}, "0.0.12": {"name": "@ai-sdk/azure", "version": "0.0.12", "dependencies": {"@ai-sdk/openai": "0.0.36", "@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "63dbb05bec1bd82dc3d03a2a642947189af7829c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-oSrM3d9dNKmfH/JsQpGJooYksR3rSH7iTE8LARtOkT/8KM00YIz15DNhnKm4jjQXzq5nFMG2pLQbaBYBfzhxBA==", "signatures": [{"sig": "MEUCIFp3e5kBiKQuBTHhIW++XzLt5dgEiAkMrFLuFFYYF7QJAiEAjPZdo1c5Udzy+NproqTgDmiHH4+o3JqVgbbD88V9uzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26742}, "engines": {"node": ">=18"}}, "0.0.13": {"name": "@ai-sdk/azure", "version": "0.0.13", "dependencies": {"@ai-sdk/openai": "0.0.37", "@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "90c021603d23fafd2e0ff772e8d787cb64eeb234", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.13.tgz", "fileCount": 10, "integrity": "sha512-u1bqFlg1Et/5tO3e1kViMx+HGgmRaP46gF6Y2EcD11a6n/xqyknTf++NS/QbPe31SxCojT6SSO1E6WTXqUfGYg==", "signatures": [{"sig": "MEQCIEFJTFp5Y+TLrNAT4uUHRwjy5ZlE4Ms2JpxAi0D8DlIPAiAFt7f9qy9+tFKHZUGw5JhF8pKk0W6wTBdMlLMWMLjSWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26742}, "engines": {"node": ">=18"}}, "0.0.14": {"name": "@ai-sdk/azure", "version": "0.0.14", "dependencies": {"@ai-sdk/openai": "0.0.37", "@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e5c686ebc0feb3f0b6a67cf70728ed91a9f82f22", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.14.tgz", "fileCount": 10, "integrity": "sha512-rJrD5MOpiK5LTXSR/weqNVtF+686OSSUrcLAHJCk8LpOiunCeINSrvL0LBB6LhDj921A9RwDqI9sm6adehMzmg==", "signatures": [{"sig": "MEYCIQC3sms/vQkYMYnea4nq9OESQmL/Mp+mG40vC2bj14YJGgIhAO6N4H/o1tzvdjPIaUQRSDWAKNDBsaziHCbzDQnjR/UG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29110}, "engines": {"node": ">=18"}}, "0.0.15": {"name": "@ai-sdk/azure", "version": "0.0.15", "dependencies": {"@ai-sdk/openai": "0.0.38", "@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9d470a52df220054212c91734650e404a1a3ac70", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.15.tgz", "fileCount": 10, "integrity": "sha512-h4NKcGuD/Xxoo6VXj03NH24veK4xMu2O9kNdIX2hiGx8pVY9PYvCF9tua8ie2WuNe6O02WC6M4PL6T3A/aVI2g==", "signatures": [{"sig": "MEYCIQCCLEqj8XCJrEHDmGVuQwuWuQVebGjYTbxIVV4lNwpjpwIhAOdlVQ/i+4mdmx9qqPe7eotEZMwoZCOZFsBXMCXuMJff", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29110}, "engines": {"node": ">=18"}}, "0.0.16": {"name": "@ai-sdk/azure", "version": "0.0.16", "dependencies": {"@ai-sdk/openai": "0.0.39", "@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4f6bf08eade24e8d1f2c9e69563e7281acb89e81", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.16.tgz", "fileCount": 10, "integrity": "sha512-PP7AsgGzCodhBNBK4tQg0YIi1GF6wCnB8PzC3ena7VAOoVpGVd3c4myTRg0PXR8x8rJK9BgnY7wDdXLBIkxozw==", "signatures": [{"sig": "MEQCIEYkf/lROaKSdAg0SwA/8qADIQyUdf8ThJGmuioS02hEAiAikAMSMhK1ISvEai1+aw5Hoc33UpuTjHCnt47bBx8CuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29110}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/azure", "version": "0.0.17", "dependencies": {"@ai-sdk/openai": "0.0.40", "@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eb0e0d0a3632d6587761acb972233a9019d0f050", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.17.tgz", "fileCount": 10, "integrity": "sha512-SXp3vJ1KpF5nsJCuKQVzGvk0JiIX/p6ZSNNvBfv0REsamn+Jlo7h8mLaDjbJH7cRQB+ozViP0yP4XekliIuzKw==", "signatures": [{"sig": "MEQCIBI23ft+kPVoaQ5nrj8GeLyfTDyQ25husdaRHSbU1223AiAwav7qT3i7FCq2NPymgPX2Em3ZLnwQBHck97k3tpXiaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29110}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/azure", "version": "0.0.18", "dependencies": {"@ai-sdk/openai": "0.0.40", "@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "507fd7a5ae0f58ea48d99de3e2eae4a9fddfd33f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.18.tgz", "fileCount": 10, "integrity": "sha512-dPJRVjRISJn/v15nUiAo+nfh1KG286iPnaq5cR7FCi1j2P3pY12nFnTYlGJYY91jSsme5Vo4kPKmTphbV8XdCw==", "signatures": [{"sig": "MEQCIGrOI1gRw65hCnJQ7WySZTXHpKpjjzLP/bQV9QJs69g1AiBOofFg6UdCqYCgc06kc9VH3y5fNxske0kYe3zYJflwxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29030}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@ai-sdk/azure", "version": "0.0.19", "dependencies": {"@ai-sdk/openai": "0.0.41", "@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d24c3188b3e245f32fbd4217ee59286a7502a2a0", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.19.tgz", "fileCount": 10, "integrity": "sha512-3/CSKh0m9cLgxQzDs4YGYLtlZCQ6qXSLqr+lI+qHRsXKxavdAEjU7GBQSdIW7vyNl2oztRjM+0FvNnBlUO1KvA==", "signatures": [{"sig": "MEUCIQCTsPREz3fFAQTQheQjRVxvOqZXVRnZq0I8MwDVa44lzgIgTBg1JfZFNoDJaADh/nkjOGCQCbrN+SNDZbsDJ6gfLrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29030}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@ai-sdk/azure", "version": "0.0.20", "dependencies": {"@ai-sdk/openai": "0.0.42", "@ai-sdk/provider": "0.0.15", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "394e6fb21225b119f0f4d5e1ea6505a11a5a3446", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.20.tgz", "fileCount": 10, "integrity": "sha512-g2yLn0YBob+IRE+WNyAapVAwPVStWdpxP/Q9vkCV+NblxaE1hkHpC7CnM33AOiJPnlMoVHTsPW47TlRYi6BsAg==", "signatures": [{"sig": "MEYCIQCPX87peGB7cqJjF4E2VrtrRd/F3A7X0KbSR1dE0JcBJgIhAI9uaj654Ot2JcnvrOPiOsYLVSUboA7pXak6Psdo/Lv0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29030}, "engines": {"node": ">=18"}}, "0.0.21": {"name": "@ai-sdk/azure", "version": "0.0.21", "dependencies": {"@ai-sdk/openai": "0.0.43", "@ai-sdk/provider": "0.0.16", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f930bd65458811cb2ba193086eea9e85fd0e7b7a", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.21.tgz", "fileCount": 10, "integrity": "sha512-4btI5kmzSy1kDP7E6u4RJXYF6z7HUICNDH91vWhqphKmgD4GLxncwXniy9XdswrWSr6RzCIoo8bdDDRQ9bVnuQ==", "signatures": [{"sig": "MEUCIB8rmTU8rXYYMK2q7Qm/F6Ku5c8KSwkr+Dr5J9mXrY9tAiEA+e3dOjLVu78Vtkx/O9PIPdgcI/dI8uQRsbwZtQ215yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29030}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@ai-sdk/azure", "version": "0.0.22", "dependencies": {"@ai-sdk/openai": "0.0.44", "@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a20feeaceb2fc7a77dbaf05c72e1e411be2e650f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.22.tgz", "fileCount": 10, "integrity": "sha512-hMOCX7YvmGvzXdEPYyFrtaxAhbQRBqg5jjIW1VoqSv4OOlk9ohYS6pvRuzX76ew00Q2O34ibdwDD/WVXgWZMsg==", "signatures": [{"sig": "MEUCIFM1WgKNXOLULgtIkVyj11Ru+RzdQjbx/KXuAfSBACJXAiEAxTNebDh0jE1W62Y0x+RuMYdrIuZQvYxR0ZgnHYeH8ek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29030}, "engines": {"node": ">=18"}}, "0.0.23": {"name": "@ai-sdk/azure", "version": "0.0.23", "dependencies": {"@ai-sdk/openai": "0.0.45", "@ai-sdk/provider": "0.0.18", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fa91c8efc5862662878c53a5419c1635ae2fde7f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.23.tgz", "fileCount": 10, "integrity": "sha512-BAAnHiko40BVjHGfQmGUInOP96x7JzXfQNODGm5BIaAfG9qoo8Vd/JEN97iC1JeY2Zyh5PVhmdXSZnsYROk4Wg==", "signatures": [{"sig": "MEUCIAYCi5Yf4aoN/LCAAKUKDGIs7pbM0XhIi8P1v8uimj/fAiEA8INTpcIE/k2QNyE42oP+4SznRup7BoBQHITD7p3p4pM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29031}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@ai-sdk/azure", "version": "0.0.24", "dependencies": {"@ai-sdk/openai": "0.0.46", "@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9a58b51076cdd443c57d72b1f2c608d35f5fe222", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.24.tgz", "fileCount": 10, "integrity": "sha512-LMYiWEt20x9/HKJ2XmDKxeMlqroS5dnMFlB5x6en+5nU4esb2i/keJ8OaUgr5bXD4zSlHROGZhK2ToSNeVIuKA==", "signatures": [{"sig": "MEUCICy+VRs0CvvmZu+lF6Kf/kXpTYzjTXZBAcJWrHCiEAmjAiEA5QA8R1p5yeGkJp3EL6qTxc/ytIVwxdN0dF1nOEraL0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29031}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@ai-sdk/azure", "version": "0.0.25", "dependencies": {"@ai-sdk/openai": "0.0.47", "@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9ec088791ef3ea386716b067294a58f188eca36f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.25.tgz", "fileCount": 10, "integrity": "sha512-HVoVmG+jbSxaHVFtfegBZ976b516eflwKjWIrpOLMw4eOQL6BvSxEPltCY4ciIhKammCpj4rcUR9csPfvVsXzg==", "signatures": [{"sig": "MEYCIQCC0VGOoNSeO04ze5sZ/Q5wcCmztbzzZYkUl9BZpWQhIQIhANAU8UiCRQi7Vx2OTsurTHPCISTzIgqsgnjIqKknL5J0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@ai-sdk/azure", "version": "0.0.26", "dependencies": {"@ai-sdk/openai": "0.0.48", "@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4712035a7b9c2e677be57378fb8c007e7186eca1", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.26.tgz", "fileCount": 10, "integrity": "sha512-+i0RSG53hBnIHrzUEDJxng9mksvg1xLnDiV/7TkCwAUfiXb6XAGrY+zaJipbpBbeHZ16IiHCqQjlfs8OxFSkkQ==", "signatures": [{"sig": "MEUCIQDU6jBfJjTJOg+ZvY2MR+99TzBH0SafOrD++kKTziQdPQIgC6yvch+h5nOZYQsy9PVvuYJtDstcRLspQ1ecV0rSQRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@ai-sdk/azure", "version": "0.0.27", "dependencies": {"@ai-sdk/openai": "0.0.49", "@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "09cc364a891a97ce7be16242d401d7ea4dad5f90", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.27.tgz", "fileCount": 10, "integrity": "sha512-S3rcDYnNXslJPqjFkTNAUXGhkLccl/DpXpZRi0VhhoBwxJvLhgKsp/L40nLv+eWh4Gq/Z3IjsRda2Sx7QyCn1A==", "signatures": [{"sig": "MEYCIQDv8ULNtmd/7Qacu3iErNIoIsx0DYA3QM7VFVS+4jk74QIhAJonSH2p+iJvmadA9hLbn0LVlTvs1iYP8tvBg407Si+f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@ai-sdk/azure", "version": "0.0.28", "dependencies": {"@ai-sdk/openai": "0.0.50", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7fa6c0dfd6e5bb88c33bddd16e4bf319a352fb7c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.28.tgz", "fileCount": 10, "integrity": "sha512-+YRab4gmtWa8i/KAv7stMgWDe9LTx3+cMO2WG04pDR4NXOjM5CAEChsj9IWetPhFS2uVvZs22P0GaHk23GX8tw==", "signatures": [{"sig": "MEUCIBKwoT49BrxMhd5Mf4wCvbqpTMeRiI5S9j/8xSKu3x8jAiEA4MSUQvKJ+iEi91EKYu3iZ82QpDzi+chCHI5gK53flmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@ai-sdk/azure", "version": "0.0.29", "dependencies": {"@ai-sdk/openai": "0.0.51", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1c240252d31bc26a790f810b47688148423a8d87", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.29.tgz", "fileCount": 10, "integrity": "sha512-NDwo3/NnN3LUSQcwP8ekT2/nNSvcMfy1MOq0T0i6l76tqVuAf6fxDFUW3TbXu6HW3knfebMixARtGyeiAlmP1Q==", "signatures": [{"sig": "MEQCIAFAJGfvz5eB7c/r91tXHt2iqz6mGb6RRPMMM1oByxbUAiBZLNLsAkoz2CysgMIS060XfCsuzQBdJpV+7N/pOuPz3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@ai-sdk/azure", "version": "0.0.30", "dependencies": {"@ai-sdk/openai": "0.0.52", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1c49909ef1d8cdfc4b2773f89f60ad84ad8a9abe", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.30.tgz", "fileCount": 10, "integrity": "sha512-mEaVQun/9a6+tm2LfrlAcYzbrkfnuGCYZu9Ofv/kUGIyE9vt6qBdrgu4SitR3xNLguh/s7O3uXannHaQOMN9vw==", "signatures": [{"sig": "MEYCIQCVMw+OYDrnGSlTgxAZ2lPXH3HOoHVErOh/czgCgjDdeQIhAOraL1whtqF05dyJ9yA8S1phloV5JbGgtwuPQYn1IiY3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@ai-sdk/azure", "version": "0.0.31", "dependencies": {"@ai-sdk/openai": "0.0.53", "@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "91d993a377354c0aa3f8566d7e03515f0e5032db", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.31.tgz", "fileCount": 10, "integrity": "sha512-LTiv890qHcw3w87l+OOuYqW1HM9+7olS5mpSOriRY2uZxJWr3MGz8MYqJu2jGNajNKi4j64GsaOuNK69k8KXjw==", "signatures": [{"sig": "MEQCIAhX8SRNYoc6Mlze1WDMKv4f+Zg4hUqxcWkx3JYmBMPDAiBfvmBVjZKARYGim3J3tv1hHXqM2b9Ms0+n63hISPVadw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29178}, "engines": {"node": ">=18"}}, "0.0.32": {"name": "@ai-sdk/azure", "version": "0.0.32", "dependencies": {"@ai-sdk/openai": "0.0.54", "@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "141666072890ece02cb58b9c555e7313d0d96662", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.32.tgz", "fileCount": 10, "integrity": "sha512-4wYFZhKvLoa4CnydQR2anv4NQeKfX0cab/YVk2xvd0bVC5qMWI1QCiJ8+T7M9S17VFOJ3HY03M8QMtr6DD5Zww==", "signatures": [{"sig": "MEUCIDrmr5ylZdCvfRwbSuutrcCWSBeGv+Id2CJW985MOcCCAiEAutIBgxsVK3RpauDbUOh9VjJchkmWPTBJip14tjVxjrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30347}, "engines": {"node": ">=18"}}, "0.0.33": {"name": "@ai-sdk/azure", "version": "0.0.33", "dependencies": {"@ai-sdk/openai": "0.0.55", "@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c4da051717a004e7d8c1907926a221d2e9b7a4ed", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.33.tgz", "fileCount": 11, "integrity": "sha512-MHcBiRGtXftcFOJiQb6tBIWsVydHFoCQBmNuPVu0IKCFs5EP7mYUuI/Xu6Q88l+x5Jgpf+RRAwCg/dIk1I22VQ==", "signatures": [{"sig": "MEUCIQDjKbnJO7VfaStcH5HDYS6NYYrCkR/lak0UdHFbPEm62wIgPPkvMNUlbIV6K+Dd2K97VtUDoR3yNnsf12cnXIeLRAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35501}, "engines": {"node": ">=18"}}, "0.0.34": {"name": "@ai-sdk/azure", "version": "0.0.34", "dependencies": {"@ai-sdk/openai": "0.0.56", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e3c87fcdca50b4b1977dfc54936310e5a4f3be9", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.34.tgz", "fileCount": 11, "integrity": "sha512-kwutGdWHQUrHeQUkyexHnlJNqQL/faPOF1uhtQLOODvuDq7IDzRzEdK18EVy5mj++jLpnD9gEhPIPQGJrCbNHw==", "signatures": [{"sig": "MEUCIQC/JVWAlvnnJ4MmwHYYXObDkdE+QV7q61rQgbSL+dQqywIgFhBn8nnxKfxBXzE6eAGAAtD/ZtwFcQYvsatfeJfw88I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35686}, "engines": {"node": ">=18"}}, "0.0.35": {"name": "@ai-sdk/azure", "version": "0.0.35", "dependencies": {"@ai-sdk/openai": "0.0.57", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "55def5951c249120d53c66a4d7b37d367323d872", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.35.tgz", "fileCount": 11, "integrity": "sha512-UMZzUnTGvMZ4gbK6lgRuMrIP/j52DO6m3+fe8WEe8mY2RFw3GFmJ/eiMxRJBjC2HDXuH8tNMMSekdQlI/ExhxA==", "signatures": [{"sig": "MEUCIDpr7Z6Jrly8ih6W992k7j6TZDQKRVep8i7USQJAaOIgAiEAsdJdohf7al+CNhUlJ5RS0GwSZjZT1ttNadImrksdhvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35776}, "engines": {"node": ">=18"}}, "0.0.36": {"name": "@ai-sdk/azure", "version": "0.0.36", "dependencies": {"@ai-sdk/openai": "0.0.58", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9077486cd4a9f54d7bad18add762e9f7a8bf5e2b", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.36.tgz", "fileCount": 11, "integrity": "sha512-k8wcJDOQAK9qp8KjzB1E5gfGe2UkSat7RaKaObfxWee8Ec5vFpyI9mT3KewkDaS0iNtcpz3uZxkbLEw60WFbEA==", "signatures": [{"sig": "MEYCIQCaPie3/rd/eypd5g5Pp/MliFhkAzFcvp56lDARtbKhAgIhAOpMxk7FK5wphWUBYCTmm/T37URrJqRsHQKukWxQVSiu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35866}, "engines": {"node": ">=18"}}, "0.0.37": {"name": "@ai-sdk/azure", "version": "0.0.37", "dependencies": {"@ai-sdk/openai": "0.0.59", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fc68763ce90655091042f43627264c2418d42268", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.37.tgz", "fileCount": 11, "integrity": "sha512-FVuo8iwNAm+C8OMAr45jEwkxVP4+xsG61zjKIdmgalhvAvt0PKT61ITO5nBPODGD6RmiDv3zFu4qfNzOYWiqAA==", "signatures": [{"sig": "MEUCIQCRz9LiVrBEm9S9xfKHO3PzGI6OQ13yOwyrksUIcXlPdQIgF/fSVm2NSTLPq0mzMJZnMADA6lKQ8LClC8NAIdobYJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35956}, "engines": {"node": ">=18"}}, "0.0.38": {"name": "@ai-sdk/azure", "version": "0.0.38", "dependencies": {"@ai-sdk/openai": "0.0.60", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b34555a450a7f8ec90b569f1be78195305a10e10", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.38.tgz", "fileCount": 11, "integrity": "sha512-8l6aZLmCenLkENTkNUpzGKhiBNa9zB9DW0l0WeIh9400OPko4LLoVhKdwjuatqI7ILf/XzkJawIirI0+/pUWOw==", "signatures": [{"sig": "MEUCIQDG1/Tm7emq3AIlGpLr9zao2l775Hida6lp1VV5MQwNewIgXHTW65F3GsN/gBv7CrnHsJC9ch/ZXE0f1FgA+xrZXz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36080}, "engines": {"node": ">=18"}}, "0.0.39": {"name": "@ai-sdk/azure", "version": "0.0.39", "dependencies": {"@ai-sdk/openai": "0.0.61", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6b4d007f7d801b94ce6ea545df8ecec2cd58c7c2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.39.tgz", "fileCount": 11, "integrity": "sha512-P6CdIwLfkvkhf2hHkbnhqLOkVMQLu7XlksJ2YvzS4yx2Iwl8fsKLVnaCWMX9SBlYfamT/oea+rpyF60JhkdMZg==", "signatures": [{"sig": "MEUCIHtw1L05agzFKRzfyrzeD7mC+t1VLkDF6Yd045n7TjPMAiEAnXgJtsEV5zb80d+JEAJLWTBDFtjfpXm6XyCBNko1IKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36156}, "engines": {"node": ">=18"}}, "0.0.40": {"name": "@ai-sdk/azure", "version": "0.0.40", "dependencies": {"@ai-sdk/openai": "0.0.62", "@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1a72b6f02f3c884b3a1f480c8439381a06a9f88b", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.40.tgz", "fileCount": 11, "integrity": "sha512-ADK98ww3Q/7T4c0bPJc5uVo7pKmMxtRni6XaOMyq5YoYHOTBvf4tv27NdA+tfo1OpOWj8Q8deHoaMpn1calI7A==", "signatures": [{"sig": "MEUCIQCoH9HPdCRhink515DrH4eK9rZJgG9cpVrPLeyy9CsynwIgcHkwdjYPlr9+oISUGu6B4q4ozHjK7N6v82L/pEucIt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36246}, "engines": {"node": ">=18"}}, "0.0.41": {"name": "@ai-sdk/azure", "version": "0.0.41", "dependencies": {"@ai-sdk/openai": "0.0.63", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a9b708161835b51c96e92e145ab079d3bca23ed9", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.41.tgz", "fileCount": 11, "integrity": "sha512-dE2iUD1nzzseLwiP2Eu35BlZU5dPsJrcehk9Dx05o44hm+HhA0YXfN/22Nmd4g9azFefitlJeBUGZaSfsajWsQ==", "signatures": [{"sig": "MEUCIQCI6gVHQl/78cRWytk8G6o6KK3mWZZjnHhcNNP9P0DX8AIgZE0nQLa8QHRlw7NWsNFyT+F4NaOEhhQPu0ss7cENLJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36398}, "engines": {"node": ">=18"}}, "0.0.42": {"name": "@ai-sdk/azure", "version": "0.0.42", "dependencies": {"@ai-sdk/openai": "0.0.63", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1821fac5f7ff5649f359cf163f9984e8133f97a4", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.42.tgz", "fileCount": 11, "integrity": "sha512-M8CGpj8LEqEf1tk4oE10VzzccXUf/azWOjH9/0W5fNvVu+Si//BhoTDcFHIv6OvTvzeEZx0T6U263vhp3sYO7g==", "signatures": [{"sig": "MEUCIQCbbpIM55BkpgU/h1zcMBGh50Qzt8wZnPE6V0dANda8twIgNxMDQj6Nqdt/KWhf7HNmxVr5EnpDG2mEr29tBIqzc3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36598}, "engines": {"node": ">=18"}}, "0.0.43": {"name": "@ai-sdk/azure", "version": "0.0.43", "dependencies": {"@ai-sdk/openai": "0.0.64", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5beafb5ebe456f1fc8573012fb1a873572a85c1e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.43.tgz", "fileCount": 11, "integrity": "sha512-hvqGsoR85oGPR4pFJJ3D3+HSl6A74Ka56jTw8xdo4Ln+XIOil8dgpNN8wJG7cKdBfeiAoETunrJQQ0lHTl4k9A==", "signatures": [{"sig": "MEUCIQCl2XZCS4TNQ3QJIMsoYrbn/3oTFjWIAd5j/W4RW6LzAQIgUcj6/hrdqO44W4htNdXDQsq45/KHamG3f2+QZJE9OCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36688}, "engines": {"node": ">=18"}}, "0.0.44": {"name": "@ai-sdk/azure", "version": "0.0.44", "dependencies": {"@ai-sdk/openai": "0.0.65", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "86271800f25a967b3e4a1b553444cdd3465b9b8f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.44.tgz", "fileCount": 11, "integrity": "sha512-fwyT4vVZFuudWWiPSdnxCxnjxLQHPg9ZEq/eW7APAmWv2EhtwxkaudTFeal+X0beHRohz4DUu83uVS6DmZ3jEA==", "signatures": [{"sig": "MEQCIFKwIfCBBCTjZN8Kv7LyaFR+OXNTXfIN/0121hC/sx7+AiBLyMKsEb0XA4lQLXzlGV7ritz1HUhDH+NqrwMCt1NyPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36778}, "engines": {"node": ">=18"}}, "0.0.45": {"name": "@ai-sdk/azure", "version": "0.0.45", "dependencies": {"@ai-sdk/openai": "0.0.66", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cd2d21e29f63e4eed2b59ad0814c549f14923bc2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.45.tgz", "fileCount": 11, "integrity": "sha512-2ypDYe1UZe5Zgibo6rJ7wtTW9s4Iwsf8dHX5PV24RnzGF3ALMNcRurWg2V06XVoyOZDy1Ucl1clC4sEveXEyCQ==", "signatures": [{"sig": "MEQCIGjAk+qqA2VKJKXGb+ocHcvcIV2cUBXhgSrSP6gZUWk2AiBueyynWxnJQQxtvJvmQoBauh2LCqwZykGOvPajLb4HBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36868}, "engines": {"node": ">=18"}}, "0.0.46": {"name": "@ai-sdk/azure", "version": "0.0.46", "dependencies": {"@ai-sdk/openai": "0.0.67", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3bbfec0d04672f2051ece410be476a50786cf4a7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.46.tgz", "fileCount": 11, "integrity": "sha512-hTISpqSvkhyJk2QS7EgRV4x3QBrhLjrA5aefq0Dw+WoSbzxz0r7Bpyi+NONCDl0fxmj/dJKKmZh2AqvTX9n7Lg==", "signatures": [{"sig": "MEUCICAjdJ9fgAeuCTVbnjB9YPjrwyzwSeSCHjHLuAmkKVP8AiEAp54Ax/4Kq6l4ShD49PTAD+uMfWTFWnBSclnH50km+VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36958}, "engines": {"node": ">=18"}}, "0.0.47": {"name": "@ai-sdk/azure", "version": "0.0.47", "dependencies": {"@ai-sdk/openai": "0.0.67", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8f9c568aac2e646b379a4fa93a7187c5290fd6bc", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.47.tgz", "fileCount": 11, "integrity": "sha512-o3d9afm+58VwvbCEryq4NRTFND5daJg9Qlq0+wMr3w8QCowHuYAOLAE0fEUmj/+peqId0Qtlhu8+N3fhsft/IA==", "signatures": [{"sig": "MEQCIGDS7EmktkVjDbBzP1idvBrajHo23ZpeNNFYJAPj6L10AiAmGu2bY7RfVjy/fhzaU29uVBN4TWgHYufNoudL2P7FqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37029}, "engines": {"node": ">=18"}}, "0.0.48": {"name": "@ai-sdk/azure", "version": "0.0.48", "dependencies": {"@ai-sdk/openai": "0.0.68", "@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "33dea246b054fd43b089b5d18c2c808c6b404267", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.48.tgz", "fileCount": 11, "integrity": "sha512-RZc5WXkhHg/Brsz9L7FvaO0sJiQ+vvV3+DZPVvcXdl7zEaVKwSXa88KNaHCpg+/iBPh6urAqpfrTgRk30Ncnlg==", "signatures": [{"sig": "MEUCIQCzv/V87SvLZvq6FagPxga7n//OYD0Z1DE6Bdn+VsMJ3AIgKPOa0L0ftULNvZDoUGHE3C+NtAkcz2EEuii72Mv7kYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37119}, "engines": {"node": ">=18"}}, "0.0.50": {"name": "@ai-sdk/azure", "version": "0.0.50", "dependencies": {"@ai-sdk/openai": "0.0.70", "@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e80ee5c8ee824fa5a0a92ae1fd7c88ace116ab52", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.50.tgz", "fileCount": 11, "integrity": "sha512-U7NjWo63vL37AokMu9mtir0whEnCk4m5o3FPEcts3lORb0AN7TcMT7EfbL0c5AsWtbw5/98c1HFbg6+yhux55Q==", "signatures": [{"sig": "MEQCIB5BElIf+gq44PWCp5NyuQhmRpwDupJefh1glIlhPZ2XAiA3cWYg0rI5Ah8L1Ghmy4GXjkYqzET6u/VAo+m29drJBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37555}, "engines": {"node": ">=18"}}, "0.0.51": {"name": "@ai-sdk/azure", "version": "0.0.51", "dependencies": {"@ai-sdk/openai": "0.0.71", "@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "980e3f8810e63c39bc493b65aa5a73a05879cee5", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.51.tgz", "fileCount": 11, "integrity": "sha512-DMPuWURdgoweDlSLTj6o3Yx6+hnfr9idvHaTBwgPY+mayvEwlLZp3l08juMn/d/gKgHheQpUEgCQNhCzk6ZJ5w==", "signatures": [{"sig": "MEQCIGiv+s1/UosBm0iV579gOPHE9YhS/W68Ql6LPi/pUxWlAiB6302a8jNA4xE9D3/gtw+Hq9AmKvOJ8gxnQEhtCiC+6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37645}, "engines": {"node": ">=18"}}, "0.0.52": {"name": "@ai-sdk/azure", "version": "0.0.52", "dependencies": {"@ai-sdk/openai": "0.0.72", "@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0e426405297492a1bb56cc0840bc547e24e02152", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-0.0.52.tgz", "fileCount": 11, "integrity": "sha512-l0XHiQymgQbzIe6dilTBD6wI4iom+Lo7yHGQVzEIq2o43/4zHDL+m7k5UCPoF0nrl5lFJk3u5+crOhSGprT7ZA==", "signatures": [{"sig": "MEQCIBrAI9TKoNQ7HGv9yIbGaMqFwtmHCug8kLJPMXUJxohqAiA8QYGWGhYAf3AVbJY696QsXyefvMPxhK2ZEg+0wcDTfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37735}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/azure", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/openai": "1.0.0-canary.0", "@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "65c25d1bb0aa1a69afdb5bcecb06104e64632294", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.0-canary.0.tgz", "fileCount": 11, "integrity": "sha512-Gsw55qsC2ZZDWyRxI1GNK8omDYPBzLXp5P3CBLH5N8VL5DH5tF8IZv1YUG5KwQZ39p+ZvEzWukeHL1ZgqB3sQw==", "signatures": [{"sig": "MEUCICguaHlV3ivSs4y97q+D77jtE0d/8Klek8H7Wie1JdUCAiEAzjgtRT6XdVMByqjRbjX8iQACewwCXJ0M7gEb9MAyN9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38124}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/azure", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/openai": "1.0.0-canary.1", "@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9d4b9d8998699208a4cb5a6593035afc168cc2a7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.0-canary.1.tgz", "fileCount": 11, "integrity": "sha512-NTGce+QgStwngHf1Fm0GqAqer/lkovlBHGPEIgkuhF8/aUMroGnlMr8Lf5CTzt0zQ3vMHGn6DJmRSMncpk7yKA==", "signatures": [{"sig": "MEYCIQDwphFBx15Hk4DUQ09ocmiLhLXHRdptfTE9M072BhwYEwIhAO/6OIy+BWSyWW5vbvcKN8ETeAhBy8Fht3SMSjXtO8ME", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38338}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/azure", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/openai": "1.0.0-canary.2", "@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9a81ec785e8f7bc5cb6000f39f9131dbe3aaf582", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.0-canary.2.tgz", "fileCount": 11, "integrity": "sha512-v/YYqV/55MSG7+RamQMeWb0lCvMcoSEyu3zVZzxOUoV+LLDK3OnVrVWhxHfSpHzNDtnKDQiUefpNKAwpSBsjbw==", "signatures": [{"sig": "MEYCIQCuY7z/kf/9i2OnPKgQbDMGviEMnnly3CiAAyT9C9ondgIhANZheROMMd+Myj4Bz9xAdz0STt90ni92XuTJ3HbVQC7T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38519}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/azure", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/openai": "1.0.0-canary.3", "@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cbd37103fb40030264ddd6998bcb7a895e1f8305", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.0-canary.3.tgz", "fileCount": 11, "integrity": "sha512-tL3Yn+wroIbJ+oB7s97L+WO2npr5dxIxz5jZKM2IZ6a4nZ6Gz9Lxxs5YupcoOiAV0f2c+OZWUlqcizjej3hAAA==", "signatures": [{"sig": "MEUCIFawBQeM/r32kxbHpIrafc3IznlcmUGONfknJA0LFXVgAiEAo5S43m+qNVxMCy3oRz2c/XVvV6wgXTInG3vI8rHmXD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38667}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/azure", "version": "1.0.0", "dependencies": {"@ai-sdk/openai": "1.0.0", "@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "018d19fcf3b68d26f26a858c41fb4a1b10dafc9b", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-bb1vaHIoT0kwiQ/qYYhjYy1XsBH2HeKG63o05FfTte8H3XwPPhwkQfjCjAnboZjdHUdcpqGh8bUjyy6ijEN8PQ==", "signatures": [{"sig": "MEUCIQCAMcmOPZjJcxthw6/XhXmALG+kKmlWCb0R+Chuq6YsSgIgfGSFkr/XvOC2YAHHY4a/QvaCChreNqNWjW8aCMYOzPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35798}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/azure", "version": "1.0.1", "dependencies": {"@ai-sdk/openai": "1.0.1", "@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73dba774a34863bc9419d63b489396bc41a0a1b5", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-k+xpCQW14uvWGbOKGfCjiGCyWIgvnwOtpOHaljt8zR0Rw3WrtjcByDA77SWHafmbiSW6lIOsbn0XwspBsmGa2w==", "signatures": [{"sig": "MEYCIQCbIlxJDPD2PnJGeclgzfmv05rvMnzaF+Zh+dKwZtd5EQIhAP09ZunIO/Rtz7W/RjSMMXlki3BOZUFRt5qerL2o3PvY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35886}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/azure", "version": "1.0.2", "dependencies": {"@ai-sdk/openai": "1.0.1", "@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "33b2b6630ad907fd23f417d02e8c27ba58bca771", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-pvGEAJ0FEIp6zwVP23AL7un1KgBtAfmUvDtYR4NEHrVkbHtvDjTcQL9+G6pz6pwlXZsEil7ANLn2cjk5GOwbsg==", "signatures": [{"sig": "MEYCIQDM1TiqO8qYMg0IlEmuNeMUdEN8cYRTk8Z+csyEL3GfBwIhAJHPtrwQx4471RcX89pT5udU8TyGvuvJYixjjGxcBch9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36005}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/azure", "version": "1.0.3", "dependencies": {"@ai-sdk/openai": "1.0.2", "@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bceee8333ab8024dc369f4af40adc9fc78e4c109", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-Q59XOzBKmJ3k/rN2r89GD+uLlJsaaPD7CYjO5/5v1cVL2Esn6P0jw1kQhOkvd0uX3yVafIM0aJyBW7IRDkIxmg==", "signatures": [{"sig": "MEYCIQCOeiPhsWuT57ZOlSYUcsourfKeb5ta7KcQ9riHVQO3DgIhALBhUNENZah1p40uuYncSoi9Kq2YD8eKZDaYp1RdFBkA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36126}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/azure", "version": "1.0.4", "dependencies": {"@ai-sdk/openai": "1.0.3", "@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3aea48644dc0c59ecf58bb2965913d4477f2353f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-/NKcUKmZO09JCJ1K+vpkGwrYoVEQGao7DgIJCwZaWOnySa9N6OKU3rwZOxVKi+YC3jCBhciT3B13/sd+S6no0Q==", "signatures": [{"sig": "MEYCIQCEvMDyDF66zfEMCCelfyALuYcyMNkMt62T4FKevs47VgIhAKrCM4uzAaS8uVS2UjI80Mk1VUCcal3iFa7vx57/Kz4A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36214}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/azure", "version": "1.0.5", "dependencies": {"@ai-sdk/openai": "1.0.4", "@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0af882c54880a1a6adf90058f4651ba153b54bac", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-z6YL0pkicHs890U3BtXhXWsXcVZbIjkPuOofwT9BmiMVqdiHRAjr+/7IwRYpF4CoOjQMu6dQvQbn89qHho2Xbw==", "signatures": [{"sig": "MEUCIHrlm7p+99z6v6Giu8JbUF+kHQ9duhMbcz84Uch8Ps5UAiEAqQp/iQeEHzxVvAXvP5l6w1iwvjkedWk0tVwVIrskJ74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36362}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/azure", "version": "1.0.6", "dependencies": {"@ai-sdk/openai": "1.0.4", "@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "df45babc0878ee9171a0c21294be88df3664d57c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-KTIJ7QGX2NDCH9ibv4gIUeo5zkTowzB2wRHJMt5XinJZd4uW1/aTZHtB9F5/0DQoiAeX3Xrt+G2Qp3yE4wTSRw==", "signatures": [{"sig": "MEUCIFRrJw5IlLYATwBAY4tTfX2yY0jlHDv9mX5HGzCWqFrqAiEAiiWFT5nyoHHzrW5a6ECf/cwpWVKkyNLfdwjvMh71CTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37278}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/azure", "version": "1.0.7", "dependencies": {"@ai-sdk/openai": "1.0.5", "@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f9abc8baef93b5710b7a84281c2daf75e296c7c4", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-nLOilokE63tM+tLZrPSWBbPxjeXmPk+vwGkQG26zPb/iKF5nOIc7d5FVtlUUfFUH//FU4fy7V+BDiGzgFAOMlw==", "signatures": [{"sig": "MEUCIQCcDNKesPZoR0p/d2nZAYQuebnCfS3jhyXYViHsSXNvnwIgAeRwX2F23o9llErUsuhTh0UsQScaD6Fo7L0kfIPEtJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37366}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/azure", "version": "1.0.8", "dependencies": {"@ai-sdk/openai": "1.0.6", "@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cc93397a9a5715929c97930c12f0b0da437ccae4", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.8.tgz", "fileCount": 10, "integrity": "sha512-/LaG5bb5qz6+WL1WpBdH+i98OJphUymU2TplUr9oh6QX1ljN6YSn8JTKziedsz3Dv6r/oeY8+07njuVbC1cKAA==", "signatures": [{"sig": "MEYCIQCH/UU2DWXMsgtVekjUTYamjCOK9bHGT32xgEjnXBQBAwIhAN163yg98VAmkMoaubbHfpz3O/TuTaO7yLk8rFjx6yMY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37454}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/azure", "version": "1.0.9", "dependencies": {"@ai-sdk/openai": "1.0.7", "@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ce0e192a2cde05688825e092c7d1df67670a9df2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.9.tgz", "fileCount": 10, "integrity": "sha512-A3KCLZ+Cp8+vZjWm0kVYdqUAiiigUjOjDkv6WyYki9+a7woMbhxZ5ZRajV4lhXjT8RCMu1OeoMoiJwhvrGH/0w==", "signatures": [{"sig": "MEQCIBRHNcSMXOGkup4cbsrWnkrO96KhMniur6Hkt3FOy7xFAiA1V+sFO1ZqkPKQbUtZ9EALC1LNrT6UqTJBqSwY/OZgFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37575}, "engines": {"node": ">=18"}}, "1.0.10": {"name": "@ai-sdk/azure", "version": "1.0.10", "dependencies": {"@ai-sdk/openai": "1.0.8", "@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0cc298ef94ed4adcea5424fc502cb35676a697cb", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.10.tgz", "fileCount": 10, "integrity": "sha512-drbmzYS0iPRU/I3xnzphxNsYvSMjYhoq8gK34zShjeJGpPbewZPsXbPrncX6gdrkD4JR021yCahPc9E6RpXr5Q==", "signatures": [{"sig": "MEUCIG97SgaluqCtMh/pDuhrgi83TsFUh0tV12q5DPOytLoOAiEAou1mJasXj0YOsR21/afidYlpygYwyzTnxGTR0SuGgxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37725}, "engines": {"node": ">=18"}}, "1.0.11": {"name": "@ai-sdk/azure", "version": "1.0.11", "dependencies": {"@ai-sdk/openai": "1.0.9", "@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dfb2628d4ad10293ae83190f7ffd989197e382b6", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.11.tgz", "fileCount": 10, "integrity": "sha512-J0oiWlT5Uz0unj63XJ6Ao3xpMlfDowtUt7s9WMknmM9RKL3x5kJuiqghynCPozeNiKwUgyhgCf5pCTforsR26Q==", "signatures": [{"sig": "MEQCIEVoFl+cHf/ueNJtepWxe6VlT5t+zvZ1Ue/xsyGdk3LtAiAjPx2rP+CfoK9hFCpsSA+IUnlVJsA0/vMULjRYxIplqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37880}, "engines": {"node": ">=18"}}, "1.0.12": {"name": "@ai-sdk/azure", "version": "1.0.12", "dependencies": {"@ai-sdk/openai": "1.0.10", "@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a7bcc9b25cc0ad0cfda7c2459970b5b51720b1dd", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.12.tgz", "fileCount": 10, "integrity": "sha512-1G2Imh1yoZFm4C/exic0hG/xV2DS39LWei2/DMOuzQ3Eby+eBtBCVRUdlXBbO2F4oef4XcAf56ajnPKfxYSUhA==", "signatures": [{"sig": "MEUCIEd9VWpkRZ+A88x95n2abSjshmMkGimrHPZBufYQXmz0AiEAog7UkeKa2mxNOfz9cyBKk3Vlb7gYZR8xR8XW2VSwbW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37971}, "engines": {"node": ">=18"}}, "1.0.13": {"name": "@ai-sdk/azure", "version": "1.0.13", "dependencies": {"@ai-sdk/openai": "1.0.11", "@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb1d55ea21ac8ae97ba7ffcf70aa92c2c5a41e2a", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.13.tgz", "fileCount": 10, "integrity": "sha512-yLzjGANoCUAB8Q0KBfcBIYiiU81reSkbzl/yBdYuLH34x2Lob9RUUSXMGIPdk6XW5dyJYYF9jZdER/EbVD3Jhg==", "signatures": [{"sig": "MEYCIQDCihTy+E2zKEFMPKFgY05rF40QCk9N8DjXNErZogbOkgIhAMXCPekB32D3WYfxi/eA1EmjOuvCTlkG6NSxV4V2jIlJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38193}, "engines": {"node": ">=18"}}, "1.0.14": {"name": "@ai-sdk/azure", "version": "1.0.14", "dependencies": {"@ai-sdk/openai": "1.0.12", "@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e04ae6db66de77a0a8348fd23158c958c9f8005e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.14.tgz", "fileCount": 10, "integrity": "sha512-6Wc54Bhi+CVFfesxR1urSiDeQQ0spaTlsT2rrXeZ/kPjSoH2dq2jIDX42J7GdjiF3ERbhXU4czrqZL6pAK0DRQ==", "signatures": [{"sig": "MEUCIQDIzvmSrAdaMDloifm436jkKFklo8BxQ+EeJNSQoLK2iwIgPcK3+hDo1jU697tbOq0SllZ6GfybQAbj043G3fNV7W0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38283}, "engines": {"node": ">=18"}}, "1.0.15": {"name": "@ai-sdk/azure", "version": "1.0.15", "dependencies": {"@ai-sdk/openai": "1.0.13", "@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef1f325ea08a6b4940e6bd0bdd5c03215e46b78a", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.15.tgz", "fileCount": 10, "integrity": "sha512-LTebGVw6qbMJc0vGh0BoTKoaSPCdRmDQCkNanffl+jGquZZJcUpBvAdMPKkoh5qQwXkOTwcwqX81qjklNilXQw==", "signatures": [{"sig": "MEUCIQC3SVZGGBtAtz+152AlVgc6XRxm/TU+BrlBQRcYs4VC1wIgc+ptt+ukxVltQwANlmSj6o0zOJqxQmq99CEzw7VJJ2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38373}, "engines": {"node": ">=18"}}, "1.0.16": {"name": "@ai-sdk/azure", "version": "1.0.16", "dependencies": {"@ai-sdk/openai": "1.0.14", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "362d8f8267fa446cdb0f616c4cd236a93a421bfc", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.16.tgz", "fileCount": 10, "integrity": "sha512-Yj9wqzoxzp11+OAwDsx8MWiEpp5rfkWYbkGk3+QzvbV431KrjK2JZ5KeP9rxPWg2R55W4bgXzky5dNYOJZr8FQ==", "signatures": [{"sig": "MEUCIQCsOQx9aC9k29DkShFq73bU1hjCAD0XiQnDxnIojJN9dwIgM+1qpFEcpYTpiSCC35gGlwj7ULvOcK/PjKENSD8j7Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38622}, "engines": {"node": ">=18"}}, "1.0.17": {"name": "@ai-sdk/azure", "version": "1.0.17", "dependencies": {"@ai-sdk/openai": "1.0.15", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "68cba6b188313f43056b2ff0bc4ed452075bf595", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.17.tgz", "fileCount": 10, "integrity": "sha512-eK83mcyoV9dTOUNySKRxqy3+fTjRtreuBy716omKL53NlL+u3L3/Rd4cEsmqKHzFa4GpS1txsop+4rZmj2FKaw==", "signatures": [{"sig": "MEQCIAwI82/Etbd+KTixW4V91xtFCJojDuhrrZTiH4iyEz3aAiA9yyoRRWTYdIx5xvCWFjvSZM5Ch4avAmw2u2UU+dTaUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38778}, "engines": {"node": ">=18"}}, "1.0.18": {"name": "@ai-sdk/azure", "version": "1.0.18", "dependencies": {"@ai-sdk/openai": "1.0.16", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "009ecb8ccb68db8a5fa267ba33e775257c6dd125", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.18.tgz", "fileCount": 10, "integrity": "sha512-2OgqISAY8NAcgbNInls58onA5G8BQGqlBzohV5vfbqlcSszxnBgQNzDvqptB0cBrVEcYN+y8n2Ex1TdoeiDhdA==", "signatures": [{"sig": "MEQCIG/jFg1PQmZp3XMEhUOC7vC3QwPu1R9ILzPRvgEAYNIlAiBdcxqpjjenBJg3/vEbwFUooaQkJlmkGppKUdEo97v/Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38967}, "engines": {"node": ">=18"}}, "1.0.19": {"name": "@ai-sdk/azure", "version": "1.0.19", "dependencies": {"@ai-sdk/openai": "1.0.17", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "30974c2cef1bfc0b39a74f7ca189bdea3ef941c2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.19.tgz", "fileCount": 10, "integrity": "sha512-uSFqxfgOtiyBDVGdqsoVPwStHcTGSRf6tGqSviNsoqL7NQE9TZue9wzgSUx1RV8XZTI/QLqITJMGUgW3yQKCOA==", "signatures": [{"sig": "MEQCIEIpD0a2jCK9msbACXhARi/NaXKw4b2+T7zBE2g/bJWUAiBjAT0JX8krv3eBjokNWUqcih7i37/M3Bg9e5MI/apc9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39090}, "engines": {"node": ">=18"}}, "1.0.20": {"name": "@ai-sdk/azure", "version": "1.0.20", "dependencies": {"@ai-sdk/openai": "1.0.18", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a66eebf54864425700e1bcd7cea7e8ee3e61dff8", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.20.tgz", "fileCount": 10, "integrity": "sha512-4qZ59vqZotGBIdOhPEDHj64DRhbt4IxCy8qPhSCZBPmY+iBW7Vxx+zko3ea5medML5c0Xv6pf04MDWbM7JeogA==", "signatures": [{"sig": "MEYCIQD4hJoYZ+WkYVVCXvGB8H7ND1lZ1vPaXW0Pob1FCaybCAIhAM3VZpVc7YhhblPI2Gtmk8YhIpjzIafqfkTF6UDGl+kh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39180}, "engines": {"node": ">=18"}}, "1.0.21": {"name": "@ai-sdk/azure", "version": "1.0.21", "dependencies": {"@ai-sdk/openai": "1.0.19", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b3624593fb38752b331e11c16bcdfcb42e1bc164", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.21.tgz", "fileCount": 10, "integrity": "sha512-KkVQb3c4dqNcLMDigdGb8SRU+PM804KBytkh8IjdZMMlE2JXqfgVAdaR5o6QFAJSSPxJN7Rair1vkItLLgp3XQ==", "signatures": [{"sig": "MEUCIE4l17TKZ/8tf7Uz/SJUZpvqPrCEeK30q2vVBrvh45IVAiEAkQpSAwQn85fXWjhvHrmQOwfYTCFFR5RCy/Aykusamm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39270}, "engines": {"node": ">=18"}}, "1.0.22": {"name": "@ai-sdk/azure", "version": "1.0.22", "dependencies": {"@ai-sdk/openai": "1.0.20", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3cd4e65e4ea676ca216e16035984287b82ac0a53", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.0.22.tgz", "fileCount": 10, "integrity": "sha512-AWar4O0tlgoLi/USDdRpx7tBDvdpgQ+VZrPkGRGf1WrAshH5IS2fIhnm/Ybeoemsb2sZ1URUQYyzinbqI2V5Aw==", "signatures": [{"sig": "MEYCIQCdhR9QWztfbY21BIiIPGVtPW+8sZ13BVnypRXOSShwywIhAN+E4haIouSvdXii6LhmytVHSlErQ9bFtf6jpZrWpT5W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39393}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/azure", "version": "1.1.0", "dependencies": {"@ai-sdk/openai": "1.1.0", "@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "04a61ef00fc650e7f611c5993c358ee2bdbe58a3", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-h9ICuSGHHJ/I0D1JYBLxdn1yP2OlHqGNNZ99tA9EsOWsrTDMHEbsJNcDdnshyL+sogaXzSguB5jVFKxf5s+enw==", "signatures": [{"sig": "MEUCIQCcU2p/IyIFVTpyJalETD7ti2+WG+I9p6HiNAOUYqL5ygIgaYixVVE0n0s9h1rxq3T/gKuNH5DSo9WSEgxjEdmK9yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39563}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/azure", "version": "1.1.1", "dependencies": {"@ai-sdk/openai": "1.1.1", "@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4134c6a956ee16dc0bd63f329b48d042e4800049", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-JPA7i8vgsVUPfssg849ikWmxNBJss0kzit+MapU4x5aG0uk9N14+QjHCTOkO3PEtbxxGVrm5yWaVfqJfNNzglg==", "signatures": [{"sig": "MEUCIGZ2TIHRv0Ieb+BLr28XYl2DU3tgRw3fkArHyZJrEGGfAiEAiutisG+Mf8tDfvwNhjPXP49vMqlmtkM+i5qYGgdJ9ys=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39744}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/azure", "version": "1.1.2", "dependencies": {"@ai-sdk/openai": "1.1.2", "@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "56d4f4a2e823f54491b1acb89b9e66ea53ad7709", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.2.tgz", "fileCount": 10, "integrity": "sha512-AqMR0heM3c9f0wKtDwN7E81z6Yj0RJd/ZJWL1B6M6WFKjOHlKvZdU+ZYGAiM6BcSlr5MyhOQMs+xonN4D2Bw0Q==", "signatures": [{"sig": "MEUCIGD902dLI/sO8NuLx+gKKc83Ss4JbHqfkBCrIQeLQ01EAiEAyuHXkHV5Cop4RoOYX166oKZ2iLZfC+XX0C8rnylsXDc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39925}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/azure", "version": "1.1.3", "dependencies": {"@ai-sdk/openai": "1.1.3", "@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aef666fd9ed1f262ca25af9b6e8892ee274231b6", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.3.tgz", "fileCount": 10, "integrity": "sha512-rwqFIhwk5+nk8ikqLauT2Gq1KiRwcF9jIKAWxqK5NvF+H1JNQfg5B4JTvrpmpl/79Zwf3sq4sfG/UtsAp8I7sg==", "signatures": [{"sig": "MEUCID0POUwlJU7mZSIkJJxTZ7a1H6VJGmqH+VDoes6gymG3AiEAlefw52L0oPWthuUzIskba1tZjo9KuvE8cQDRPL8/L1g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40046}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/azure", "version": "1.1.4", "dependencies": {"@ai-sdk/openai": "1.1.4", "@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef5426078979bc4c4564de5c39f62b346bdf0e23", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.4.tgz", "fileCount": 10, "integrity": "sha512-nFa/29MbLb/6X+zSR2o/I67lI145VXPL0tvJU/bNeuPVhBBEiOwAcVvVSk5OXIvrSRrtBB2MaLEz2fuyaYhQ9A==", "signatures": [{"sig": "MEUCIQCNTcLIigU1bINFqP9a0eTVGxW4wgRZ5QtlI4HmdcC1MAIgc4Ss7siOPKrKqn+FEoooYif01GsyP7vsoJaJ4fhz8jI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40167}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/azure", "version": "1.1.5", "dependencies": {"@ai-sdk/openai": "1.1.5", "@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "848fc26cb798174c7f95f2e393d4f68b887a9237", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.5.tgz", "fileCount": 10, "integrity": "sha512-aQEch9BKzWArzBFhEmf1bjIw1SDAcO4foHJR+obKts4bkREAb6cu3jeRCtTlt+Tl2zAnTEGZk16j/ufS01SRMw==", "signatures": [{"sig": "MEQCIBG+/rkf1HmmrrnRAR7HeIVqV9LYaerwUKJoJb8qx4zSAiBI0gqBbcyNct7TRpv+yVBLgafVkPZh2+RgDFAcW9RbQQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40288}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/azure", "version": "1.1.6", "dependencies": {"@ai-sdk/openai": "1.1.6", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c1ee88b5ece005efae6912944f3a2657486f2f6a", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.6.tgz", "fileCount": 10, "integrity": "sha512-5mUdn21ifjD7HW7KDk7OuJtVvApPIi/m8NacAiNEhXfO4vvKN5M4DtP23tH7odWYBvuRpL84CuLxguqVt3MHkw==", "signatures": [{"sig": "MEUCIAajI7V6r54hlDXDjwJqOhjhufOOVUS1TV3QirNOuM0HAiEA6/lARJ6+bxUAEgo7u471lnB5iJZvsdwyQOqJOIJUrII=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40436}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/azure", "version": "1.1.7", "dependencies": {"@ai-sdk/openai": "1.1.7", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "19226b2ab9651988988f8f8c8ceb59595afb88f1", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.7.tgz", "fileCount": 10, "integrity": "sha512-0f1s9TsMtKAqvnyOI0gW+RanWbsJLQcC24EVqcf4tUsqZJjOO3f2a+/osItpbRyLyuYuv/NUJDjJh9+9X0HaXQ==", "signatures": [{"sig": "MEUCIQCIqpP1Ze9tda2VyGyUzf1aXcZHjwYvLVlseeIbdZgh5gIgBsjfp7mv0/U+9eftgZelVoyOWoYE2E1kKZmFS1P4lXI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40524}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/azure", "version": "1.1.8", "dependencies": {"@ai-sdk/openai": "1.1.8", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a723105f82f3c4d7ecaae62d5c6be1b24d62e11d", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.8.tgz", "fileCount": 10, "integrity": "sha512-k0fIzc7ALbQAjz4b8FjtpCplqBAZOYkbx1XBZDtQdyvFp2X/dX25oNe5voBqDhTqAgQi5cPlA2Jd9irfAydLhw==", "signatures": [{"sig": "MEQCICtz9Hc0egCQh6zQn0JJrK9sIEY9cPyPDnz6EnbG2CLrAiAI0xStjWdJJiRkdbUHUbzG1gkF219g4TOe5SDDirblpw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40612}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/azure", "version": "1.1.9", "dependencies": {"@ai-sdk/openai": "1.1.9", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "de3286a124f477176c22d080abcb24f60639dcce", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.9.tgz", "fileCount": 10, "integrity": "sha512-bQL/HGsj8NndoXomgZ7m3LVj/hyRy3alTZNPT8OkUL24Uj3P0mWad4sejaRdSVLr2joBb74YqQ2jlMvRGGG/bw==", "signatures": [{"sig": "MEYCIQCEqtBIqUG8UYydGFLFA9kO+JjiYHFZdM63X2IzG74+MgIhAI2YA2JWcjI1vjQVm4PBcm2fSWn8fq0lNivcXXQHauis", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40700}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/azure", "version": "1.1.10", "dependencies": {"@ai-sdk/openai": "1.1.10", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0f8eccb2df8e8759694e9214b46c8f299b50e54f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.10.tgz", "fileCount": 10, "integrity": "sha512-l8jwPGLUxT5YAk0mQvOs+z5fkSauHKFcTfsZOPlT4LPDjQQejdZiUVwrYpbnbzbZOSRN0deMH6Cy394r2R4kkg==", "signatures": [{"sig": "MEYCIQDEI7JnCWJziDQIChRO0ttzbaoiY5tJAjyUWqZCgIr5GQIhANjq9qTsJi8EPNXzbbYQZrudm8twlV2shNmTmB2YINY7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40779}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/azure", "version": "1.1.11", "dependencies": {"@ai-sdk/openai": "1.1.11", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6351ca037ea6f51374391f3759cb731d35ad822f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.11.tgz", "fileCount": 10, "integrity": "sha512-eLZa025s3vlXHhrwpzxqRYX5nL5Fx/cg4AGDq1XfUDxRh0/BAXtxWFC9i+9W8YMdeplSHjo8cCSVLJuP5EmqGQ==", "signatures": [{"sig": "MEYCIQDy1p9P8f8GPTh530uiIyNwcINyI/siSRcFQZIXHknD1gIhAKCLEPgthUI3P2J27MFVvM4zGOo9voVNmxHY8D6ZWXJ4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40902}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/azure", "version": "1.1.12", "dependencies": {"@ai-sdk/openai": "1.1.12", "@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef34fa03de1f3be4388256fffc0077e0f9184add", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.12.tgz", "fileCount": 10, "integrity": "sha512-FPFbXJ14WWBxAffjk6gwlQwCX7fNRJSJy2hKU1z97VVphWJHpSYTzcJoVrsv9L8fRWNWAB/Zp56l2xM4NXmLzg==", "signatures": [{"sig": "MEUCIF4qnBcOUjGdxOTGX/FmYkW2EbEIAHpkYLd+ONInS+zCAiEAisHETi0K8Xa1LimNfrV46JuB3mm3sPYX0h6+Auxu4FI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40992}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/azure", "version": "1.1.13", "dependencies": {"@ai-sdk/openai": "1.1.13", "@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9e7941dfbde793d1cee16fd23b6f7f91ec1cc6ed", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.13.tgz", "fileCount": 10, "integrity": "sha512-9RuFxVcTFyqBgD185Fuwk3KphNCLJCMVYLGWPAbKcbyPDVMcHg8QBtnqgGS5AWz5c/PBBgKRfymTEONu4FfuQA==", "signatures": [{"sig": "MEUCIQC96B+98mnVj3SyRT8CnIGQBFU3tsg7+Ezx7XsMr0LznAIgbMAMGKba9a5fxpQvOYVBoHy0dTUFa0AzoDxZNBHo3RE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 41142}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/azure", "version": "1.1.14", "dependencies": {"@ai-sdk/openai": "1.1.14", "@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1501bee412c969ace1d65daca18fcd318dd38df9", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.14.tgz", "fileCount": 10, "integrity": "sha512-saCsLXg6VzJkLbJDX7oKx6MRDDt3o1uhQHgrEx4f08MkpxErdkCO1KamTDalFE3Vj2zuIXM49L1WWb2eqWwQ5A==", "signatures": [{"sig": "MEUCIHwRGZu8zD97vpjHCbLXuXilZP8Oy4OlxdrQM2DMKAoYAiEA5ZDBq+DUGxhfkFm/OnMar65sRc/1AxKyC7nQ0giQZs0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 41294}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/azure", "version": "1.1.15", "dependencies": {"@ai-sdk/openai": "1.1.15", "@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "58c4a2dcae486a92db5c7b2868361ec7368ebd89", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.1.15.tgz", "fileCount": 10, "integrity": "sha512-/2XMyl3+BP/zUxunmUKHAxyw3lYOGloEQ5J//yERys42CsMWj2jqut3Qred8ruyQKHsb19rk+HUWlCRg/WUuOA==", "signatures": [{"sig": "MEYCIQCz0H1Tha5bV7akmrvNjeDBpPqwVu6uUi2+fQU6jZ6uwwIhAIfTOd+302KNfl6lLjMrtEok5woDxRfRJYq5O8ipn+ZW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 41384}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/azure", "version": "1.2.0", "dependencies": {"@ai-sdk/openai": "1.2.0", "@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a09f6bed1013ef9e3ddebb03b1555d490d606ba4", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-pSfKSYBHz3/sqR+ulITYoCVPneziTwCc6bRRBGAb0RWQ0w9aU+cgWIo3OPnpx/1JKjIiqaeqdby0Ze56jSqdaw==", "signatures": [{"sig": "MEYCIQDgRYSchNqFbGsApOmIpLoLXlowCqPNddlAjKe+NMa+wgIhAKV2YJp/VmXxvWY0+bK6f2w54xWNCGAB9N6T9Cwvkolh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44906}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/azure", "version": "1.2.1", "dependencies": {"@ai-sdk/openai": "1.2.1", "@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0c7e3d7878378ac6e758beecc726d501f8d178e4", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.1.tgz", "fileCount": 10, "integrity": "sha512-nQU5k5HCAY7W1qr7mNwy0fVYX14ysDGtqEMDeWp5IMjdhbUhG3RU9ZEj0UhdupSa6B+Wuh7obrRrmGOM6dJNww==", "signatures": [{"sig": "MEYCIQDwcyhEt7vsqeJEMconUo9xA2a6RU9PHcFFohCzzeubZgIhAIHDzrMHTXSwpjQTehyXihs0D/stWxoS2bVXIZZLI5TS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45057}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/azure", "version": "1.2.2", "dependencies": {"@ai-sdk/openai": "1.2.2", "@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2e49bf5544600ac290be2236eb949e15a034992c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.2.tgz", "fileCount": 10, "integrity": "sha512-KitD6RUDbcgFChONzuzAZOS0Tc84nabVIl28RykXrW5asskmJ+RSWxSeP2S5MinlAXSHp2O+d0QXhg9bsLbOjw==", "signatures": [{"sig": "MEQCID8lzgYcPSLaJiytPO79zqKK8NaXfqaqmeVe2xx40xREAiAVERhSjJh7YOwLNuAl3HhlHXdLbNbMA1MYz6/lLY6O3A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45145}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/azure", "version": "1.2.3", "dependencies": {"@ai-sdk/openai": "1.2.3", "@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e4675a40b78085acf0fddb9c3c84e74b9634ef9d", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.3.tgz", "fileCount": 10, "integrity": "sha512-TrHX6PTbkUmVfBzSBhVa08yUYQL67jUmF1nWD3HxahVNewJTl3REIBb2Yhd3/ZpPeiJgdzMYropwVDva7kY9lw==", "signatures": [{"sig": "MEYCIQDA1LX+CB5s+Yrgii5ysUTgGcODONMAVfryP625KF4M2wIhAKWX16Qz+NDG3beXzfZjD9XwPbLhhpwKHzaOyKcLoK0p", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45267}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/azure", "version": "1.2.4", "dependencies": {"@ai-sdk/openai": "1.2.4", "@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "94ae2d821034c768efcf5787dd1209d106fd6f89", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.4.tgz", "fileCount": 10, "integrity": "sha512-PrpHaxrTIazZiY2lltcXxNekcQ+xfnRQUQ9RA3VOcrHIonCrxyr6xDaOJ9sWh+F41asvgU3kK5APgpLxEYIWdA==", "signatures": [{"sig": "MEQCIEqhiWVPRizNRYct84w+A2VOh8XJe9UCZV6UFqR25ifgAiABdfXdekPGMdFp+KWis/gqWCF14S/MWxhKE6/Bb/sBxg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45355}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/azure", "version": "1.2.5", "dependencies": {"@ai-sdk/openai": "1.2.5", "@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c27ad14f94521ed1357b140b663f64893f9d98e3", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.5.tgz", "fileCount": 10, "integrity": "sha512-pG9rF1YbOja7hMiwtOhFTXn5OSQibGxe0j+9VMJQTAnuvxUVViu00oMWuUMvxbLKUwvcIsF7BzWSYh2DPidz+A==", "signatures": [{"sig": "MEQCIF0FkZ1fgYF9S920b3txx/StvMRGSHXJOZy+mn3KvYbyAiBEq9xUfjYk+PZ3lg1AhV+ewp5RV0K15FFW1dXw3YtGqA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45505}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/azure", "version": "1.2.6", "dependencies": {"@ai-sdk/openai": "1.2.6", "@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3a4af059ce848e2b2f066bbc717198b300110f30", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.6.tgz", "fileCount": 10, "integrity": "sha512-54JkQ+UIVdJ5D6MdVZEGmz1Tpj8MiATVO2xBRZp4OfbgIvffpNUYOh5aMCataJUnysTlGR5v+rF6Hk1VyzeZBA==", "signatures": [{"sig": "MEYCIQD8RiwDlrJZiybm8n1lncT4slsy7GKIh7WNMwrjBR23hAIhAKz7L7ReE260/kkLZarEwmIftaDL50v/bRa4KbJU4+rS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45655}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/azure", "version": "1.2.7", "dependencies": {"@ai-sdk/openai": "1.2.7", "@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d16171eea927da5db7358b06823464be9f22cac7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.7.tgz", "fileCount": 10, "integrity": "sha512-TfNFkpPLC7vOzACYUONwLCVYrC+evYprNqapgAleOHIWESDabshYRw5IxRn+/xhNZG0A/JcpKGkqQFj6Ejcvxg==", "signatures": [{"sig": "MEYCIQC8ojMYsjQuO9U1CbQJ/FrOT13wg7L03c6GsXfYEzc0bgIhAM2wBjNqf+YALARxn6MO3JDQnqA6Oq85q/nYJ0Liq7Hz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45777}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/azure", "version": "1.2.8", "dependencies": {"@ai-sdk/openai": "1.2.8", "@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "abc95e360e6d642dbd7eff5baddcfc55a207a9d1", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.2.8.tgz", "fileCount": 10, "integrity": "sha512-HfGHaX4zxZCzkEroIFkgVhlYYB3Os3kq+rkqhLLr9SGGJt5NgMBJ/zyTdiANdBkZJpO/Bpj8cKo0G4HSYFpngw==", "signatures": [{"sig": "MEUCIQDdVJ1d2zrE5jz/6fUs45xJOruG18GEd7kd7IxmfYWPjAIgFPiJ5tR8IabAiEvJTLjXbc8u/HNqrNeKTtYd65NIqYo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45870}, "engines": {"node": ">=18"}}, "1.3.0": {"name": "@ai-sdk/azure", "version": "1.3.0", "dependencies": {"@ai-sdk/openai": "1.3.0", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4d585cc11adb29881a70478bb7f9b8db718e58e7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.0.tgz", "fileCount": 10, "integrity": "sha512-2JCWcTt/Hfzk+aeRCggCUuzAe1k7KlfjJqMjP25hRr2dUXn3i4BFYo1yapyd/ShtgXy/tDPeqRLO2ZJ9d8xRLA==", "signatures": [{"sig": "MEYCIQDkg73z423jV3/6/K/pTkIN/aY/YTPr6AD85QWdzodehgIhALoFH3ovp2twVjmT98euP41zICU3+rlb+Mfim7+7VJIQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46058}, "engines": {"node": ">=18"}}, "1.3.1": {"name": "@ai-sdk/azure", "version": "1.3.1", "dependencies": {"@ai-sdk/openai": "1.3.1", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "20641810631783162036904623889f88f73e5250", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.1.tgz", "fileCount": 10, "integrity": "sha512-zcRoW6rX8EXeoBe5jrSFzsREMnfnf8kNyZHh2TKW4udMWqz5rkH+F/F4IV/cSclYIV5m0qrYW+f8QQpmTAExBw==", "signatures": [{"sig": "MEUCIFBU0sDnlUUYgLyoaxyB+2hbuE7zpibQD7wyCCdNKCMEAiEA/4dXsVVLHS/7stQPT6Y/VQTVFarsuOFTSuR+zuBoqyE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46179}, "engines": {"node": ">=18"}}, "1.3.2": {"name": "@ai-sdk/azure", "version": "1.3.2", "dependencies": {"@ai-sdk/openai": "1.3.2", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d8d28d5828a807db1cb807bbe378e7534f88b7d2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.2.tgz", "fileCount": 10, "integrity": "sha512-paolP8DD76u8b+0vRxhcxvz/EyzAhIPpDNWPuxiqfS+fkKNWsFedVJN5W/gf3ax4cgkHijUJ8jLDvPHdLEZ5ig==", "signatures": [{"sig": "MEYCIQDzdvpyjc3ORUrIxtEviAX22K3LiI4n4Yampq62AUtc7wIhAKnTB7CKXgTuVDlHSCB/cB9JKtcNO1YcStA/0lz50zWe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46267}, "engines": {"node": ">=18"}}, "1.3.3": {"name": "@ai-sdk/azure", "version": "1.3.3", "dependencies": {"@ai-sdk/openai": "1.3.3", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0b4d191566d4a398147bfdb36bb9107384a15c5b", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.3.tgz", "fileCount": 10, "integrity": "sha512-ftmhtlDkXig79usqBK9RpRr7JDUBL4JMW+lqjVQZYWoA9iExWD0oT+bQ2OKYHsYkHckNzsQbeVq685QiivDR5g==", "signatures": [{"sig": "MEQCIEAT7jfezl/Zwd9kws0pVX2BxG2OTIA3Z6iedhds1MJeAiAh2IyGB+Jk7FtezsUQuCdNeD1BF22lvUQ/ATFRQhcpnA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46355}, "engines": {"node": ">=18"}}, "1.3.4": {"name": "@ai-sdk/azure", "version": "1.3.4", "dependencies": {"@ai-sdk/openai": "1.3.4", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1ebcc4b016935f8b49ae43ebc80589cdbd2ac877", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.4.tgz", "fileCount": 10, "integrity": "sha512-JC919Vx+GYX7C88SrPYzfjkcjUU9/JRVUIy6qMRYMjGLBr6uWmEffGSJV6zD1JyTZgrarI2SNysrVOiYDtB6Fw==", "signatures": [{"sig": "MEUCIQCJV94Bjr7uPjoDPSbr+N1aAeq34E4yGLvN7wBELpg/cwIgOqBTgOL6NN88+ClBv91jwwjrFT856826EWiSNHP0aSo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46443}, "engines": {"node": ">=18"}}, "1.3.5": {"name": "@ai-sdk/azure", "version": "1.3.5", "dependencies": {"@ai-sdk/openai": "1.3.5", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8f6570f8b0608b22fbd2f2a362fd81e96adc1d83", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.5.tgz", "fileCount": 10, "integrity": "sha512-TEDqlMIxGJKAWwCjBPi6uuyiSJ/saPVmDDxcXr4MduQdKMqxe6gUH+RYwqWYHxdbFEUNVEN1PgJeVVcnuipc5Q==", "signatures": [{"sig": "MEQCIAvRW2u2safHIwbGnp8WkOnUGI5UpUd9GLB/DvF1QhqOAiBpYVStgmGUqB69XNp3zt2BhCEIISUgTmupfP+m/0hJ1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46597}, "engines": {"node": ">=18"}}, "1.3.6": {"name": "@ai-sdk/azure", "version": "1.3.6", "dependencies": {"@ai-sdk/openai": "1.3.6", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c04c03b9566357df935f686fc11ae181ef3e2b2d", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.6.tgz", "fileCount": 10, "integrity": "sha512-w07E1h+QF85/dHyYVmSYmc60mJxMS2r9YZ5mntBHDx7AqA4qqDvcS1XUTy+mjaZfuz3DRjBXGAqCxu+Q6l4WyA==", "signatures": [{"sig": "MEQCIF4zhMhpOtrifaKD1MuDGxNkqsUWScGl0TIjC+0XEs5lAiAGqeZDHUrv1OmJ+omCM3s+Ivp8IpfVl8HlGiMl9HrrrQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46718}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.0", "@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c9f8aa39731d8282c00fa145071909c70bf44fc3", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.0.tgz", "fileCount": 10, "integrity": "sha512-d+6KI297oEvJCwxJKJU4ZMhWecsaYZMcDmtSESv3F0+GXXaBoK4YJKs8YyGVYxeCADMpeYeFym9mBkAL9M1jEw==", "signatures": [{"sig": "MEUCIQD3vfJOQ3KNck+1lDHIiBXQH+jD5uI8UFD7WBdKxi/LPQIgdQqc9aL9DsUTy0Q5uAHnUwFCRrhS0L3dZQg+Q5/rAec=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47042}, "engines": {"node": ">=18"}}, "1.3.7": {"name": "@ai-sdk/azure", "version": "1.3.7", "dependencies": {"@ai-sdk/openai": "1.3.7", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6b2994325d5c3ce3d58983d3c2445dd480680556", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.7.tgz", "fileCount": 10, "integrity": "sha512-QV3ARc5zAC07Je6vQWuWbbQiK4/inkZElCLa0A7KAqaDSuDpuPGP3IGgiWc1evqqoMfGvEXJAuDLIYfDdvbemw==", "signatures": [{"sig": "MEQCIEHTJ1vQyzdx72IJ4/lqOh8RL6bMbm56kJKCVxSsPY2DAiB6cPA/UKjtsKsdSd01pni7YjRltvHr773GDzjlQqCZyA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46903}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.1", "@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6ed9783a8043e7a72bdb2242488cf70f0e9fe1a8", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-EUb37bo7b2JbFO2B/Bp5tjfTXCWHG3LnetOEVzBAyHpq1Z8wlerJNVuo2nIZu8PP0zBmGyj3W65gGty2L4G5Dw==", "signatures": [{"sig": "MEUCIEeN5gISzbq4BzkDMaRlvo/8GLFt62OsdYoRXOzDDqqgAiEA3zkqttjoiO7dmjuSegHsVmPX9NXSkDxIDXd6sVylREs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47256}, "engines": {"node": ">=18"}}, "1.3.8": {"name": "@ai-sdk/azure", "version": "1.3.8", "dependencies": {"@ai-sdk/openai": "1.3.7", "@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0c01a8b0045901e62db60a88a31874c32215720f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.8.tgz", "fileCount": 10, "integrity": "sha512-1B7ht7bGv7gJ0houI8hJAuUFBB8Nmm/HGY12aazns6yaSWBF0/7iBG2tV1I0hUzpuWrDB7G6y0YtRl2dAxzNhg==", "signatures": [{"sig": "MEUCIE5g+YWccQbXGJqx/GFur5c9ix5nJ8H6WT3Mslh0WG/zAiEAx4h5NV2gzZ+KKA1FTWBB6RXuw7fKfr4tzI/LZb93hME=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50326}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.1", "@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5497261692ebe63f8f876fec11f89ea8f37ee6a7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.2.tgz", "fileCount": 10, "integrity": "sha512-DMtX1K8lgnq+oQQVotAHPAEBISpZq/KBeHhe5WHl5nZG5zNWCjCuidqTi2vMs2LMmrb2RD/QQ6F3/pajoPehIg==", "signatures": [{"sig": "MEUCIGLy5f9lF/Y94jkHy43rNVSSXAGCm2mUh7nfcWEAcwkDAiEAu3Jkn49BA5SShJtCl4qRpGK6UcqWEus+SUWU81Qmod4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50688}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.2", "@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2008d274aa62b210d82925628e31079b0a7bc363", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.3.tgz", "fileCount": 10, "integrity": "sha512-mPqnzBS5vrKz15cCBWJuvAfdAEM9uZ/B460M2sMuphPjWUeKCbJaG2TSydbJm1w4wIRQcx9z+m9LZU2LhzZkzg==", "signatures": [{"sig": "MEUCIEeR6vhQxzWSuR4zPSMHpmxj2aaOLzGjsiq+ier0pCOeAiEAsVmO9PBYD7IGun/kWFrGRwLrq9OSYG6NYEad/ZaYznE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50905}, "engines": {"node": ">=18"}}, "1.3.9": {"name": "@ai-sdk/azure", "version": "1.3.9", "dependencies": {"@ai-sdk/openai": "1.3.8", "@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6a89c069d13ff545717262daa22a636153150442", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.9.tgz", "fileCount": 10, "integrity": "sha512-Sow7BkIeksLpdrC4Jynd2afJ8+azgGgfHeQKdzF23qTE74Il8DlSpGhpDsxbqTn3k/KL0Ekv/Q6ZsCOdayZAyg==", "signatures": [{"sig": "MEQCIASxo9yal4DjzhQQ1gfxCf7ob4z8mn1BZr+ErUG/VPJuAiA48kki5iE107bK9ZPb5up1tURB7Ef0PZZKklK2oLr70A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50474}, "engines": {"node": ">=18"}}, "1.3.10": {"name": "@ai-sdk/azure", "version": "1.3.10", "dependencies": {"@ai-sdk/openai": "1.3.9", "@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8e8b27942f19427eab747c62aa57eee5b3f0ed83", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.10.tgz", "fileCount": 10, "integrity": "sha512-kblVdNBV2C3/MdWuzq8i13lQBA73Rw0xiJLPFtwnT+sKnwjYc1S7QKsVf9ixm48VAKgfTKvcFVQND9/093sv7Q==", "signatures": [{"sig": "MEQCIFUlLbLWjKAVi49eRuF1T/YTLM5gg7ZaByuov0iZlZXnAiAlgTNBZ5hC1vNfgkKlf6BjdtkMrp5WVagOqjdiGhDuBA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50657}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.3", "@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c28fdf7bf707a44a05df31d3d5fa9404501d59fd", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.4.tgz", "fileCount": 10, "integrity": "sha512-6nFPd3wU//Rq/XIVD5EDL08Y6Lx6Q9YKHSofVGOCXBCxAtYqFYDv0d797+7znAi77BJG437/P3tZRO67n5SX/w==", "signatures": [{"sig": "MEYCIQDulfVJPbmh7irl/ror/mTrKAzeC/0JBoGWR9gsIFVSOQIhAPr7G510GZw8JoOOfkbuI4uHkLzV569JYz6r1rYqQ8Z8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51254}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.4", "@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ee2149100f0670eacfe62e27209ab1bfba70b1e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.5.tgz", "fileCount": 10, "integrity": "sha512-1FP3oT22YvHwjhfCFR6BhwelnesUV48WzBrB2GIHI766wgrTmuNkAA3IcZMfZR2SuOj9oOyAvp+S9FzerjpsrQ==", "signatures": [{"sig": "MEQCICjtVWKwQ5Rvq87Jkw1bvIuiv5LweYovXf5kb37gefTbAiAU4qbs1EDd7e3BDxLuktmJ7Y5DBovv2KaW27R4vZT7Rw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51438}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.5", "@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb973375de95dc2a351e46d906219bee495f07b3", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.6.tgz", "fileCount": 10, "integrity": "sha512-1JCATCTZsdRUpLziBOyO6rHzDd7hC3QV0KJG1qIDh6MweGDg+Bo7SLoh+pcSj2HtySguH67GhwBFu51sbi7EjQ==", "signatures": [{"sig": "MEYCIQCf+4zc/1x4xQYdbam33TzALvV6qx3s0xc7g6yYYZMykwIhAJFl8qU8eIe8ozRsg+JVVBQ0sgAiZ+AjVOJi8E2WiO5a", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51694}, "engines": {"node": ">=18"}}, "1.3.11": {"name": "@ai-sdk/azure", "version": "1.3.11", "dependencies": {"@ai-sdk/openai": "1.3.10", "@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f23fda6b928c1b140a7de21d518e5411094c3bff", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.11.tgz", "fileCount": 10, "integrity": "sha512-bsvJbGxYFYmjtVAYV6xr3t0cSXi2whgLyay3e7eq9ZywQGA+XN0DK6VxrNvJYxOMgVMy3hyfvVt2BR63NhcJWw==", "signatures": [{"sig": "MEYCIQDN/e2Mx35Aj/oR+aWAyM/fUJXQo<PERSON>keev/q68sEhHMCiQIhALx4P6sun+bQRbfL0nYtjrkqSqZBsT+0jclxesdL9vIc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50781}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.6", "@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1d7f369959f2ec0dab7172a8543ea982307088c1", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.7.tgz", "fileCount": 10, "integrity": "sha512-zbuhw4iuTNDkN7OpxoCwNeYW0mKMb3d7popNDkxPifagmnSSbF6v21PJRLE13foFG2ObrIIqjWShv3gwRFoYYw==", "signatures": [{"sig": "MEYCIQCr23hDuaQkZ9NKOmwsax4sAxHp1zS+IL6ah2EWfpp7kgIhAPR9pzlA5UM0u9UwhhvKjep6DnE0lAd583Amx53tMcCG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52175}, "engines": {"node": ">=18"}}, "1.3.12": {"name": "@ai-sdk/azure", "version": "1.3.12", "dependencies": {"@ai-sdk/openai": "1.3.11", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0e8467eb39070e21f22dfb46665ecb7cc2a3499e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.12.tgz", "fileCount": 10, "integrity": "sha512-Vl69nu8ooZbCzZnB5tMgCwr0FM3SsTxJ93yRQnRaoTiq54U9Zb2gqu/Zp5Y9zZR21JxLMxS5sL6gKkLcMbB6OQ==", "signatures": [{"sig": "MEUCIDD9/7lIx1fj1J5foSnUKgtvLgruvuY/IY6eXhKQq75dAiEAgTAherOQt5y/gbeI4Oxa3eBDlq9DSSNyW+S4Pl3c0Lo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53092}, "engines": {"node": ">=18"}}, "1.3.13": {"name": "@ai-sdk/azure", "version": "1.3.13", "dependencies": {"@ai-sdk/openai": "1.3.12", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2ee38c3927df29262ad772217407c3c953a3ffbd", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.13.tgz", "fileCount": 10, "integrity": "sha512-PS96YEC3MOwpJHSC+mrMY31S7PA+outPmrFZljSbzdYf4/gd0rKQjHnqnV8K7/1GmxjkueoYRH2QfWLT5eaO8Q==", "signatures": [{"sig": "MEUCICz6jfF80a0AHe81fkRHWITsLm6gLCdLTZAcmgcjcSg8AiEAgavaTvs7OLa875dsjXVfR9yn0VMDbzM4qIcOsGbHngM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53182}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.7", "@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8cf6641eda1d1b882535a5404f5280653a377bed", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.8.tgz", "fileCount": 10, "integrity": "sha512-6kfZxvcMJsOgUCj4eNgLN9JJJSSQ237oMlTWoc+ovdT9dyHMxdNShC3f/PayFoL1O+7HkvyFXmFTlzA9T/8WBw==", "signatures": [{"sig": "MEUCIEfsMQ3jsPb4FPZRnJ+0LemmS/xw2PPBTwPMf+rv+SJhAiEAyC5B8xnd88bdkWr9Rln3Ow5M3K35F5+V8FSJj9iHtaY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52722}, "engines": {"node": ">=18"}}, "1.3.14": {"name": "@ai-sdk/azure", "version": "1.3.14", "dependencies": {"@ai-sdk/openai": "1.3.13", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e62692c3ae1b1992986f1e383aaa30576db721a1", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.14.tgz", "fileCount": 10, "integrity": "sha512-XdyIzBbKfAn+TjSYmVtbVlgPBmbxgmc/BbsL2yWCfGawtsQ0aCMTOLYNTtYXxJUPF0mRMbLzsJHTQmnJmdmLMQ==", "signatures": [{"sig": "MEYCIQC3gPDaLY3T+kkLNyqOMA9kOUSnp7unDJ6rfeZ7y2wJfQIhAPNFV3WYjvHavKXaYdOjMW6QXfLrYu6wkkLEMWNCPVaW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53272}, "engines": {"node": ">=18"}}, "1.3.15": {"name": "@ai-sdk/azure", "version": "1.3.15", "dependencies": {"@ai-sdk/openai": "1.3.14", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f248777aca614c976d4a4343fe13d37bafc5d823", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.15.tgz", "fileCount": 10, "integrity": "sha512-oIi/c2LfdndI+7a7SZZS3LWb6pMNEtpyebJf8iAe32jsdL1DkHkWm+K46fXTPceeA89IkNKGxnPqS2Swn09pAw==", "signatures": [{"sig": "MEUCIF2OidgpMk6XDgUhyEgTd9vwNlnQIlyFtbQC5SaZz6znAiEAwuq/ng1psUuPUK7aEQNHIYJiNIx/sYWJYgo6PdtHk0k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53362}, "engines": {"node": ">=18"}}, "1.3.16": {"name": "@ai-sdk/azure", "version": "1.3.16", "dependencies": {"@ai-sdk/openai": "1.3.15", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd77de3f90d9a479a9ed9a10d887122ee2641c6e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.16.tgz", "fileCount": 10, "integrity": "sha512-t40iJ6yep0mdBX8nQUz9EvHFHYmsJEoCbrbAyheOE49dpqPXpiRkIEEbmS8hSclZnYRDylHzyq1nEO2YrhHzMg==", "signatures": [{"sig": "MEUCIA57zgYS6a6pVNDp43jqaa70idzEnxDQ7tasvSK4j2XZAiEA7kXjwwMiZZKL851s6SMWaEgt8ulHbDcjq4UN0Ks5Ct4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53452}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.8", "@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e35488070e631a9dad9b3b2b40ddea8a1afd3330", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.9.tgz", "fileCount": 10, "integrity": "sha512-055lXqANqQvctPhUYyIZ/p0qGM6R7MdjDafXo5OMNCvtDUzryo5OPDsTEVtRe9ipT3nV+3ucV+bZamc6apJVnw==", "signatures": [{"sig": "MEYCIQDhnpreU2tgaVmQfbSyHQx3WyAbanVfsUHKd0h8W64p5AIhAOrDubfXetSpLm/lEqLU05Z5pZymLydb4YMQ98WwBAaz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55166}, "engines": {"node": ">=18"}}, "1.3.17": {"name": "@ai-sdk/azure", "version": "1.3.17", "dependencies": {"@ai-sdk/openai": "1.3.16", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "77bbc766f88a20b12e45a5fd4db8b3b6d5b80e34", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.17.tgz", "fileCount": 10, "integrity": "sha512-uGCQ7q81S3mY1EmH2mrsysc/Qw9czMiNTJDr5fc5ocDnHS89rbiaNUdBbdYpjS471EEa2Rcrx2FTCGiQ0gTPDQ==", "signatures": [{"sig": "MEUCIQCYTOYIr8pWw/exqBN6N4hzroQHVdxOV5ZJkUD4wJqCWwIgB4mirDNMajybOTDyxNFFL9Oztd6Mtucg2cssTehqBV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53542}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.9", "@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "053a23933f27b3a37bff678941f8dc4dd781bffa", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.10.tgz", "fileCount": 10, "integrity": "sha512-bef1U3jq41oZLi62Mc0jrfdJMVxpwywimarSPQg5SKLHmgXhyrs4+q1mQSK81uOdfvml2nDA4BMLUaUE1ez3tQ==", "signatures": [{"sig": "MEUCIQCmXzRUa/U09LZDYR+GqsvcCzvbCPdaOOazhczgPJ3yeAIgGZxZDEtl/1C5wycTo1OMFAbcQvP4jofK5KYAVfSkcBE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55408}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.10", "@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c439c68a7a82390b56199550b5dc2e4ffc941050", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.11.tgz", "fileCount": 10, "integrity": "sha512-Q9XVgJrqXLSliltcfMxqq3/n6DEJsuj4J4PEPB8qEA8mUxEc0ySI+18aN9ls0rWUQ7sGZmAZhPQqCsRpvdDNjw==", "signatures": [{"sig": "MEQCIFpOSZP260EFsv9Ozbz01dV17ol6T7mA9wQOnPANswRDAiBQBl3+YUu+GCGxT/GALW8XqVLWW7gjXq9wAQLbEoOlQw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55661}, "engines": {"node": ">=18"}}, "1.3.18": {"name": "@ai-sdk/azure", "version": "1.3.18", "dependencies": {"@ai-sdk/openai": "1.3.17", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0fe294b4b046c9ce31539d4c2fb24c0e211389f5", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.18.tgz", "fileCount": 10, "integrity": "sha512-kQsddr2xU12RXyWn56I3+OfMNGhOVJGtm5V86kGznndmihMtdsvkto5M8Q0D7emNbp0Rphjnn2KH4n+V4MR0ZQ==", "signatures": [{"sig": "MEQCICoDUHYlrcADA5NgMA5/AMaVs6+gGG0xIA5gf8EtMlYeAiA25ppYXBnz3mQHlpUu9csY/8n9kPIiLojJwfhICPujNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53632}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.11", "@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9346af215e036a152de5215363973834ca8bd5c2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.12.tgz", "fileCount": 10, "integrity": "sha512-WZJwQK5tDFf7VxdzBE2uwzOl/iNV+I2thDEn+upHOtZpRxnJagFJmrZ5sbDPWE+bLA4KQVr6tnzVzz9w41IrHg==", "signatures": [{"sig": "MEQCIFkcL2P2TH27+mKN35uvAtHWTlGxEcyDj6PfXjHjUw7iAiAqyMvT79b95uWroOuX1mzm6AafINPXeA+JUiXQL68Xiw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55882}, "engines": {"node": ">=18"}}, "1.3.19": {"name": "@ai-sdk/azure", "version": "1.3.19", "dependencies": {"@ai-sdk/openai": "1.3.18", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "85bdf31fa263f152fa2518b2d4ebe5513cd4a7fb", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.19.tgz", "fileCount": 10, "integrity": "sha512-XYEa2r7/4UzuXvoTulTRpQ8QWcG5TxO32l0hM7JXz9w4FGXxVGVP4JVKH9p8M9ZLHhDx60JtoL9vg+Yvv8wwPg==", "signatures": [{"sig": "MEUCIQC19yBjE9V8GOmrdoxf1gDAIoFydKWvg1qv3yIP/U7dNgIgZ/MGcRWE5v6iwzKYnBT81lXF5KD98bQuRzHh1y5DrCc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53722}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.12", "@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "68971d5a62162d543c5c81dc703d22335983a38b", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.13.tgz", "fileCount": 10, "integrity": "sha512-tjxQ0LkI4bX6O3b119Lk/47ZNQtjV3gsnDr4FJ5GZB2mO4TxlRalyLyOcd2q3bezkU2Ffsje45mG1olZtpDU3g==", "signatures": [{"sig": "MEUCIQDrog3LWYOwAxPOomsrmktEgRZkeFfujhkcYrGMNBQmfQIgZjuFLyFklBcuLOhmYVdwFLGe1i+ldqacsPyJtDSkeIg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55621}, "engines": {"node": ">=18"}}, "1.3.20": {"name": "@ai-sdk/azure", "version": "1.3.20", "dependencies": {"@ai-sdk/openai": "1.3.19", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d3104567fe264f3a6e7461066062ed34a4c71c46", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.20.tgz", "fileCount": 10, "integrity": "sha512-wt0VnNbnXuUB9UZ91yF6zYK1aiuS+wTis4LK+0d7s4A1Cm8aEzwyXpxH2iIK59CFfwnhYOCSdQ9hzQidpPbDXw==", "signatures": [{"sig": "MEQCIGKpiQbp38s76TyVkAaR2AR66M4ZPQ1cE8aXxqqBasCUAiA1huSMZKlRjQgXN5cwpdvJqUYkChOxT/RSNGNWf616Dg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53812}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.13", "@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0cf72386f0bda71083d3724dd39562eb3272d02e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.14.tgz", "fileCount": 10, "integrity": "sha512-Y910/iiGgPBiJBtXDiStgaOGoTiVvvlD9zVtHVc281kh2Z1ORqkoUi5JzY/Z+zTXbmsJXMnMd7fhe4TkGVK0lA==", "signatures": [{"sig": "MEYCIQDmdijW2JkZI4Bh/zYI8NuQsqbPT70OkfLjH3/cKE4J1QIhAL2MY/inpfHDrnlC5f0wJp0FEtd684n+Vdz8m73zXmNm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54321}, "engines": {"node": ">=18"}}, "1.3.21": {"name": "@ai-sdk/azure", "version": "1.3.21", "dependencies": {"@ai-sdk/openai": "1.3.20", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bbd6be6ba8797641e7f87c19ff4d24f80dd35b3d", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.21.tgz", "fileCount": 10, "integrity": "sha512-GiLnGScVUerruvkS6E3Rd55YXBb1TI15c5y9GxphJEPsU8jzVha5GKpN3+9hWM9OBgIrJlWKumlSfpVpbcNFJA==", "signatures": [{"sig": "MEQCIE2yw6p0D4erhj8o4ccCLyI9QdTvoEbGMo6jZVrGLskZAiAdPP69J89W04Y6qpCQkPsBbQRZV/tV6lB6vDkKbffT2w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53902}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.14", "@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f8409308a450cd15ff6a8d1670a94c564fbafe0c", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.15.tgz", "fileCount": 10, "integrity": "sha512-z3q9H3/KPnXXdWa8o93vAmi9Hs0ZW2NAFnb4h6R/bb9SzwRQkDSft1/w5h/8ScJM2QkhrvmXBOXrPrf8Ssv6nw==", "signatures": [{"sig": "MEQCIEvrwCL7Khy3WPSJK5v+h8AFX6cONCpiNwQC1V7x8cM2AiBl/3+H52zn8Eg2jSTgOnlEI1O65uUZpJ3gBucYdSutJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54575}, "engines": {"node": ">=18"}}, "1.3.22": {"name": "@ai-sdk/azure", "version": "1.3.22", "dependencies": {"@ai-sdk/openai": "1.3.21", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9882a47cf16d8520a7bcda713f0f3932af4f03d9", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.22.tgz", "fileCount": 10, "integrity": "sha512-X3Vlxwh0MSmmZ8Q7LgzCXHvPq0XsNL1dTODIZ3ziC7n8cME8yHvjpwPwMAHLK0a7YbWO7eOW0OsDnZXdong10g==", "signatures": [{"sig": "MEUCIQCew96EXVrf9KAKBEt6tzmE34wX7oizFpxmO1SHMdMmSwIgdRpxuW85XQo18ShqqfmcRY+HAsrDNy7J+Rxpgp/0uIs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53992}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.15", "@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0041a7459a5a12808442e3e5c40f657863187d07", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.16.tgz", "fileCount": 10, "integrity": "sha512-s0K0U1/2vo364KlaVWAhRmZjb8UXZ3aQLOh58SEXp48IxHwVIqTnNdcqZsocbvrfli3tpNCozaF/Vk8H0oKvPQ==", "signatures": [{"sig": "MEYCIQCVBQxBESSs2EcycRci/pmPTi4jA5bHvDGJ2k52Wso96QIhAK7UAXNL2OI4W/2CrS1nY/sranZ15rA7d923OSKY2QAH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54862}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.16", "@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d72d2c6868fcb39933d1f5b57356706f0080230e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.17.tgz", "fileCount": 10, "integrity": "sha512-AKmXz60/LYwClwEwiVeqpnQ/hi01aFlpAhNAUsCaOwCIt1GPB1MN/mA0MMHlLG1iz1iZZlOK4BvubzJnRCrY+w==", "signatures": [{"sig": "MEQCIDhRkcnAjeqkmxuWOX6shxR8rEbVZ8PCRcpg4lPHTAyiAiAO83LhPBHo3rMfkjEJxYdHNmzG1JAr5aOxrKH0IvffvQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55203}, "engines": {"node": ">=18"}}, "1.3.23": {"name": "@ai-sdk/azure", "version": "1.3.23", "dependencies": {"@ai-sdk/openai": "1.3.22", "@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cee6802f7f76404487360a6fba7a39efefbba9fd", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-1.3.23.tgz", "fileCount": 10, "integrity": "sha512-vpsaPtU24RBVk/IMM5UylR/N4RtAuL2NZLWc7LJ3tvMTHu6pI46a7w+1qIwR3F6yO9ehWR8qvfLaBefJNFxaVw==", "signatures": [{"sig": "MEUCIFEbGAakYRPlR4uuiUrFbH9GHCQ78DEMUxIsVMJluII/AiEA2OI7OoSKg07UMoO96FmQ46cIN/pLKsZzxkveJEm4/Q4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54103}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.17", "@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "763d011253ee09fee26448f36681e6dc963c9aa2", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.18.tgz", "fileCount": 10, "integrity": "sha512-vTIpVe0zU1pVrGSBgb3n1DTfD2E5uOlfnVZPHmOHKseO7eumvNU7YrhX8ssAZGRHLzZ7jjN3wKarlLd69eE1OQ==", "signatures": [{"sig": "MEQCIACmxogpy7cztaE+bdRVidkJLRk6hySOD1iptsN4VIDSAiB8Te1IJj/nNojiTTIo0pU0Ks3pyEUwvgc9ZNdrXeMxWA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55167}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.18", "@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e3d53ace6bf4c1ef37c714fe70d97176570fbb0", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.19.tgz", "fileCount": 10, "integrity": "sha512-nvKLj7L/MqKVVr3fw7TVTib39UzpEQetM5J500D1d8tRlYU3imDc7Lt99QN/vzbGRF2OnydjX5f7OWcgBXSQrg==", "signatures": [{"sig": "MEQCIH4M58zj2AHxtzbIRT9xbsMY6XRX8D5n178hnLQNhRVCAiB+kt34Fk5iWJo9OK/1TrUdIeMmOCVoxNxxSDlfZ3p6YA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55393}, "engines": {"node": ">=18"}}, "2.0.0-canary.20": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.20", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.19", "@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cf35859f1b01556a3ccd58e5c940d57e497b55c7", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.20.tgz", "fileCount": 10, "integrity": "sha512-y1elDgifoHd8vLAg1DtVxuIwdiIO0dvRs4QF17ayshVTJUvp5Jy7Yhth50Cpfo7P/87FvYOz1MpuMkSyRXrmyg==", "signatures": [{"sig": "MEUCIQDtlEVbnG8Ovqi29Evh3K1wu0oQxdjcMhTg31ccOYmPxAIgXXQmdigsxoEYK5bRQ0yCJLn5mLOsaD/OuiakEXSPzSU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55544}, "engines": {"node": ">=18"}}, "2.0.0-canary.21": {"name": "@ai-sdk/azure", "version": "2.0.0-canary.21", "dependencies": {"@ai-sdk/openai": "2.0.0-canary.20", "@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "7e4faf3e4f20040077dc60ad678d8248a15f6c73", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-canary.21.tgz", "fileCount": 10, "integrity": "sha512-tnuQrRI1QtA04qzJX8WfC9JjqPBMGd2EI/tCJAusZWuNSPscxs8wmfK/2tHFUmPNlXULGR03HzE72OGpdnZ/ww==", "signatures": [{"sig": "MEYCIQCcJW8qhCibTev+7wF5SM9rnFUlkv7TERJg23JZkbWBMwIhAIOTd4RhoBPlDkVtbcZUDoQb5szZFAfrmrMgPeGKq+MQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55696}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/azure", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/openai": "2.0.0-alpha.1", "@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "8be30609ed0c3ec7b81e33d6aeb4ad8d26aef23e", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-33O2lxi06zIkT4hpweepyvXrZmoYFCrO/iJr9HZNDbsca4pNf4hssfVhSMBNIqSZ4cuGMYLrnT659oOUM3CT8Q==", "signatures": [{"sig": "MEYCIQCvOb7aZ9ONA53K9yWVuQsZpqBVcw5kbAnjI/lwKhWVWAIhAIQ88VJnYDtInxWiYI6pKufl8yNXiYNHfqKGHl6IqPqi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 55868}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/azure", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/openai": "2.0.0-alpha.2", "@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "3e7f1c35cc8ce607e5599266c52c0db2fc0d3c8f", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-ryIcj7modYQqUZKdnZhVAKqz841genZ5FDn5epxhbtYGashl3hK8w6DIv4bnwzPri2X6hjujB6TaHoHzwDUnzQ==", "signatures": [{"sig": "MEUCIQDTEdVCeinM+sLbuZFXYtsNFhm5+8/AqY5MM8QRxsY/KwIgdUb2hNKJNN6bUNXEPT0gOCdxsrNXLO4Ceat9Ii72gfw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56048}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/azure", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/openai": "2.0.0-alpha.3", "@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "ffc82a1fe1204e313734005748e4c5e98aa290eb", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-i37ac8GADKQkyglGX6pw/+ufpDSFobDz8NEL8+/QtNwI2ElQgOMBGIzit1GhT1NciwbMDoDiejffI0cTOtCB1g==", "signatures": [{"sig": "MEQCIGrBIbsGu03iOK9AJ2O0STUQk2qOv6nkAOSuYbPMn2XpAiALBMiT5kZhXHlR02bp1X1aY6JCKtxTex2UTjFBQL+UWw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56228}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/azure", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/openai": "2.0.0-alpha.4", "@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-ksEy4AlbU0D8lRdQC6J1Hx9bfTm0XH56rbX7JSaNx0FfRCPeSlUPvfeamkstwtxoXowYv5P/bcD2DPKs92bo+w==", "shasum": "a115f8ef46a8a1df5681f2f1cbadcedccd41f7d5", "tarball": "https://registry.npmjs.org/@ai-sdk/azure/-/azure-2.0.0-alpha.4.tgz", "fileCount": 10, "unpackedSize": 56408, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCvdJBib91AkIs8NrmDVHStbln/aW8wXN3AMIP3y1LMUwIgeCENv6ZVRzq1yW9Q5u4zUPmiKiBkeTKp6YjkdHozdLg="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:29:52.246Z", "cachedAt": 1748373702221}