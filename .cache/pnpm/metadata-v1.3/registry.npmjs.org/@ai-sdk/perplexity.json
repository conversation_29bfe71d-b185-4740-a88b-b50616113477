{"name": "@ai-sdk/perplexity", "dist-tags": {"latest": "1.1.9", "canary": "2.0.0-canary.19", "alpha": "2.0.0-alpha.4"}, "versions": {"0.0.1": {"name": "@ai-sdk/perplexity", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2", "@ai-sdk/openai-compatible": "0.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "43b7f2917fd174be7b01d642e02c1e474fda6a44", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.1.tgz", "fileCount": 10, "integrity": "sha512-qDhCITgTWvcDzL14s3FKnZTlVtWXbXqPfjqOLcGqCv3C/GYPZSuv/LZBVHp6eBKznAPWpkGmg0JyTQUOpbzMXg==", "signatures": [{"sig": "MEYCIQDWtpwEeWfczDdTb0npmR2PNvNStLGWMtbpgsmi9JtVewIhAJqwkaMBTMAXd7dME463egGGimpn36K3HpzHfLiUkXeb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34679}, "engines": {"node": ">=18"}}, "0.0.2": {"name": "@ai-sdk/perplexity", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3", "@ai-sdk/openai-compatible": "0.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "70ca615bd48b0be9703535e21f872f1404194b25", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-8vRSzyTcRIIUHJBksQmFXC8exRQBojnQvYWZ4T4Cw8EepjcFcrCkTtyC4rnMPqOJ6xkyknF1THKac2nnj8FhAw==", "signatures": [{"sig": "MEUCIGitRnZ3DERRHyqxvhaARStMYnOXnx+VC1kOAvAB4BZ9AiEA2YUufvKXyMoP8zAM+RdCbPzUW15Qiq4XruGkSeQG620=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34811}, "engines": {"node": ">=18"}}, "0.0.3": {"name": "@ai-sdk/perplexity", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4", "@ai-sdk/openai-compatible": "0.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c69926495438784ac1967ca1a24506784edd22b6", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-Lp+3fFlTSiy2OC/Z+d+X3R6+wHiqKlxzpFTL4brd8U0ccqy3uCP1SHWDSe3AaAyhrzlkcN152mUILFiVerF0Pg==", "signatures": [{"sig": "MEYCIQDU4yYOVaIlL0D4gjuuMN8r7Wa/VvR2svIyCAF3D3+4EgIhAOVCrRT8T4hZJ6FQs+hViZgBTUJyJMhO266r2d8kbsDp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34943}, "engines": {"node": ">=18"}}, "0.0.4": {"name": "@ai-sdk/perplexity", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5", "@ai-sdk/openai-compatible": "0.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f5063f9593eb35aa498647eec5f11e1eebeeadec", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-ZWk+Qk0SSxu7a7eZwUTC7PBGyo+ktY96YZYriQ967c58pzvZgnP4rioYri0Wn7jd/VE9JiOIghYI/NxvRUtMZA==", "signatures": [{"sig": "MEYCIQCGwRJZ4M7nZOOR5IckpJYtGFHMeDu9YdLix+2Y606qlQIhAPgAuKJrxCyJ08uahtbcNQcZNIPzzvZargedMgssCPz0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35075}, "engines": {"node": ">=18"}}, "0.0.5": {"name": "@ai-sdk/perplexity", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5", "@ai-sdk/openai-compatible": "0.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "525787d9b2f2d94ca37a5fbc81a9b3d4a73066e2", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-y6kGDEepDD32IgSoRvGzASxu3Cguv6oKmDNUCTaYSGEQgu7oBblchV44PF/X+H+NYwYhRqhEuMIb64H7uCiFBA==", "signatures": [{"sig": "MEYCIQDEoVnwWoEeNNTyRWW+9j8jzSXViqlJLQH6B/Z3JbU2wQIhAJBVkMZaPPSMPhe9HL9hQmGPOo2+sXn6LY4tjkqf4Kk4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35174}, "engines": {"node": ">=18"}}, "0.0.6": {"name": "@ai-sdk/perplexity", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6", "@ai-sdk/openai-compatible": "0.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fcdcfd4bacda4a9a9b74ddd51a568f8106e72e5c", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-GTtLYQZeSy+1z4N44tGlomhypm13uuFydoJC7R67HBrNO4917QhSwklc14rgJGBdh0lgRNbatOwDjxurJmKgHA==", "signatures": [{"sig": "MEUCIQDxIt3VsY68TlKUYLBiH6+XE9RRN9rR49ca7U1rPev8RwIgGJTDe0hv0HdaXOqZ2xGeA45i2Orqi+mmkNFNjvM6Ivk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35333}, "engines": {"node": ">=18"}}, "0.0.7": {"name": "@ai-sdk/perplexity", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6", "@ai-sdk/openai-compatible": "0.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c6b555ce9f2aa55d75f9950783a688495b6d9e6b", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-PmLBRgEIyxVk0vmHDTb2e5AJnSmSBd3CezGTJBq0Wc3dm8VLyXreex2pLYiB1cDzd6j+vZsWbhHcvalpOoSiTg==", "signatures": [{"sig": "MEUCIQDVmYo4gAtyxEBP7SwPg1ENZxB9qDR4Cd0ya8BBZY9sTAIgKWE/iW3ATNx+1ZU/Z7Zufg5OtDUgzyX02xm26PNMrbM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38685}, "engines": {"node": ">=18"}}, "0.0.8": {"name": "@ai-sdk/perplexity", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7", "@ai-sdk/openai-compatible": "0.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a7c3943698a269e16f8454f0ff37416e6a8b9f82", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-k3qFXnz7xcpm/IyqP5+PV3JcPNo1/0LOFxam7Hg9SRgc74JJ3PzqSNWnGh1w42C/NZx3m4KrxySos673LgTbbA==", "signatures": [{"sig": "MEUCIBcXM5nc/DFvUCdtiBuU1f5FGzVyLpF/P7BlezAXhMf7AiEA0LL3N4p3b/ja8ek+gbOYtt2qsO60LUfg0aMX29tZOto=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38773}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/perplexity", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "69a7d97bc04204359df88070f086fa71d8c03ad9", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-TBDkkOCjBelUa54h9Ei+ck8hnyn3eFVIQh1t20tjs1rN0eVTB4uM9xxgv5+ViA9VDvNtQOqrHlY33PcqT46UGw==", "signatures": [{"sig": "MEYCIQDn4zLWYOelZy92rlWA9HIsC0KSxgGaT3Ay/nV+SSJsVAIhAPCDdXgRDKPmrOAxXUmLVp0MdlIi41PJEqrkCUUatmrq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84770}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/perplexity", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f0193b92decf9e82b9ee9211a63e12f220a5156d", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-yEDgEPBGi/gk4EoJ/I6JOU84tvSI6n0+cAO+T6L6O8IVV2WEVrpyfxhNTVnuuQ7BN8hUR0TkmtUe2zt/DDA+0Q==", "signatures": [{"sig": "MEUCIGUS9+Jy+5RtkjkWgg6jkBMzQzJlQnoizbtYimGPTjNBAiEA/8NnDhF4gdCsi96Dp2jfw4pug/Q1egFg/QpLpT2VdYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84893}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/perplexity", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1309e405178be06cef2f0e70aecd84b44329d100", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-KGjJHEuscVCZeIjt0cl+FuBFesdk7d1MiDC3iTVLAnpHwYYmNRwsrNmbfbL5+wiEG11FHJ558Ml6JRA5RonFdg==", "signatures": [{"sig": "MEUCIQDsQWY1aYqYoxa0UWMU4otRMns8V/jZ5gUPTJqtJB91HgIgO0SncIjpFDsQyBXi8b9z/x9fBe2qXY+sfe22jruLoyo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85626}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/perplexity", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bc60eb6722c6ac4b76310b69ca2c5c952786c410", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-HWy/tzwOKZxP62NZnGRO2hUCv4M/D44dvjU7PlGuGu8eGAIUcZ9cpVCNCrVER/0CHtC6Y/KeZf8ufdnyZdownA==", "signatures": [{"sig": "MEQCIE5funRgmarZdP7PUcqCxCkgSHJ1cPkVvfTBmpWlCRsaAiAhHTCPYtH+e4FwBIywfklb2pbQZB6TMDvOLW3Sub7dew==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86176}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/perplexity", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "93261d7fe9cca0889c5ece045492fa2e031937ca", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-uI8dpk1gz8a6+eHgNssy2hoa8dBKb5UJniSXWm4lENmJtu6Db38llQCb2sbU8EZu+qIOuv91auuF9t2+eaVjng==", "signatures": [{"sig": "MEQCIAStB5ONuQWFSqfaizMvSuNB0dNc92F1KGqC0ilbwbK+AiBatp3RR0IrwCv6VcebUU47ITdClKmAClgYOEPLJ4tZWw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87294}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/perplexity", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "de221982baa981646755921f48621a17671353a3", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-Z0RcIPRIDPkfK6hEYpor9kv3kIZ9RMdkapWMipa8uxnISxKJljL78Js0sxHn8lvZ667hZ2ju3jI1x/4Q//Um3w==", "signatures": [{"sig": "MEUCIQDqLM01b5BD4Mqbi2w+fFXBW7Q3Ogsy6AyjOiBSpevQmgIgKmmqbVl5iZo/MnOVfKZQnhaNXZz5Bw4bSoU3wvVI3Cc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87391}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/perplexity", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "db9f498413073c07939aa8533e304951d69ad072", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-4ndOCC0R2dwvTeGwdrWCRrs69mkzNYzzQWycVgml1pJXAJSWXwddD+lz8pAk+PCtxI/hA4ZCxX6debcRAMEt2Q==", "signatures": [{"sig": "MEUCIHl8A5m/JgwNAXqRx6H+QUj9oyQcOKQ3roW9Q3mPqWLuAiEA6lEEKq++6alfqFBMn5QHB/cteQIVWGOLwkohIfktqlE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87516}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/perplexity", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ae78303caae78d28d74a17fe0d6852bfd6ddd836", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-+K86L/X5gnmhu+M89RpaUliZp80updKF2rfH78OO2TBVUxQ1KFGqfc0M0Jm60O4sXNW9Z234l8z5nQbqz+r0AQ==", "signatures": [{"sig": "MEUCIEcRT+G2z4wLSeO4u/4oxobn4xB6e4t/vlkvFIH1SUaZAiEAnzg5oYDNNx0wYICcq5SygGLd6o9HtSFNpTkQY4sQum0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88498}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/perplexity", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bf4433c73bd504229a5798ae39df0040f611630f", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.8.tgz", "fileCount": 10, "integrity": "sha512-pIicVkGMtWOhN/gOxB8XZdVZenqV0OU166r0KbiSoRwTEjpnBIEIPO8TOymVowkcuPIIwgkPFqImukrbfCD7tQ==", "signatures": [{"sig": "MEUCIEYl2kt+z7WmRdX4VUeZEv7VdlsTXcJOLCzm1kvqHQU8AiEAvi87grCjraUe6WVW6e1KEmHf4S8ywCIwiWUyYNkdIyE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88623}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/perplexity", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "872e78d0721085e2f267f3ad75508e19b151e007", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.0.9.tgz", "fileCount": 10, "integrity": "sha512-sMB219qM3ucqiAIWtGWW0mmBV+yFPlZEkNUuNrBzOcqFySoixHRc+x3OQQfZ4H66v5DCjzznT8AkWwn/0MWBkQ==", "signatures": [{"sig": "MEQCIBYAXh00dvO60Er2Xv7+mdvy1HTADC5ffOzQ3/aP6b6lAiA0lU1NlvhxxLa7MXrelMzXsVTxLHVaUsnt9x3h2e8ldg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88720}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/perplexity", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "64c05afc497c03df0ce399bb764c43fe911e4172", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-9vTjCRglhdRfckQOvyLieI70kQqONu69jsnXANOFKiOLH3vxFjzSQ/8yCm7Jt4EdSfhDNxSFSXpozc8KmziNGw==", "signatures": [{"sig": "MEQCIE4F1/3RRXHTLb7pSvjII2Dt5kZ3JqUbwhK8UqkmggA1AiA11YzmtJHEDvwt9OAz2lHx4T0pr4Ro4coFmeUGsEZdHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88888}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/perplexity", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "915e250a22e82efd98cf5db3a13ee7e837ed0b56", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-qERKGa94xsYdFbjIVY09QEuXAfaGoFwbDWQQvOJBel7xEBCixFlneBRc6E5v/qbVSoTo9GoqZFemDBPzq4ilyA==", "signatures": [{"sig": "MEQCIBfziT9jZEFYU2nrSkXpIOgcwr7se0NHv7ypMY8TXwOsAiAkJgDb5gys/VD6oUZQMQAiAfPA4/squHPPopKQvAqP+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88984}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/perplexity", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8a1548e7a63d72fc35cae58316cc788622e1e9fc", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.2.tgz", "fileCount": 10, "integrity": "sha512-Z9Ej77TvF2n4Xbz9T1ruCyyXUCqUYuL26ab8iX7+eVL75Q7wMK1AVebMiuv9ptzaurRxWS9xNJyxsltcaNBxpA==", "signatures": [{"sig": "MEUCIQDZeMFn7upgvUNneVZrV+03CdKbly0pi6T/178ALs5h0wIgbypHGrXjWHCGw9EooV8+CSIOwI36hzJA5BTF0HhTuuU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89080}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/perplexity", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6fab74fd67da4000cb0be02d6d1f9d5b3ace504e", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.3.tgz", "fileCount": 10, "integrity": "sha512-CeWCjYi+z+yxMoodqXXlh9MgtKgQi4/zBgvkDaAGEZXQRyz0lsIKa8oKNXX7znkxeSj+74t9HESwelOv5jqGnA==", "signatures": [{"sig": "MEYCIQCU28OiLuzMaLpAl/Aq/iPXv5ZLb/TQtcgIBdy8xsaUlQIhAMcuM5Pxhl8BXtMRmMOyHF1pBN3nCOKb1zQxNakoyLWV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89176}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d9c442e29538510dd063ea7a3dee93e4f99a6c7c", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.0.tgz", "fileCount": 10, "integrity": "sha512-rQgaVHgUcqPEldTV8qq2QuaNXSMAvK4xYxNLPkvlsACmh8SsC3cyhWl72G0/HAaDuyUehW9d5n88AHZgq16bpw==", "signatures": [{"sig": "MEUCIDCtESlbo6MfX3u3l2U2QafHITRFeEipbW7nFX3Ped0xAiEA2jJwY/kLie8OvSOt7H94TMfwlrARDR36wSZRjajXgO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89393}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/perplexity", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7af3288250b527c5f46522cca7d71f054c77f047", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.4.tgz", "fileCount": 10, "integrity": "sha512-4uGvX4EDHuNcAZO7oeC+el37vf9I67TPf9gPY/6ZKuzTn8YjvCV5ghHU+pIMM0NEMFcbJ4E/5XbbVyeWjJlZrA==", "signatures": [{"sig": "MEUCIDjOv3oX+Zkr465xfzX7FGeJIsIh+rzkwu6VvBtxb9QhAiEAtkdCQHTKrsYzGiIP1WT/GOeA1JvSt5FRj/VKWZglG9U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89272}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4ce549e4984231be2bda1b94a9fe83c00ae7995f", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-cnjfEMPWYJgHql97PYZHa4ThfpExSm1gHgatjM/dKFGmESebyRes+whcQrUj2PlC2/guw7HVWfVCevR6MGcbZg==", "signatures": [{"sig": "MEYCIQCiG6Ttxitz1nc+y6BqwADpumJi83Yq7sWjFEVvY3x+XwIhAN8o8RFH86RMLOLRgFXSMceFXQSvAx1bO4a8KXLTK64n", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90238}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "091d2c80350e09627f9d59cfd1a11d1056eb75a8", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.2.tgz", "fileCount": 10, "integrity": "sha512-Uo9cGut08Ew7cKaAMd6FRC7vpMTch4TI7m2NhKjjQ0+XjmLozBwiRJ1IaALgR1eFH2eysOpZ9X/yjhgh4cBRLQ==", "signatures": [{"sig": "MEUCIQDBamxi4/TIynAgQT40nOXZqJQdFgEMO5yBdc3U7e+RCwIgSfdEcpDeygWj5+203hhEUt1/Lyhz35UnFqEaeRK3i2g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86642}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/perplexity", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "70d650df4488a7ac5e093842d32d336fccdf79db", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.5.tgz", "fileCount": 10, "integrity": "sha512-dhvB5sxZnRbB02CKR4E3xmGRwSSa7tgHRd1HuycVXBnQ92yQohPv+yLiImuoVB9b04la+plXBifQmdaigzIJIA==", "signatures": [{"sig": "MEUCIQDQbx3mIZkRE/KOIMxHlwiFgFldhGvJWEMKvstrFYSYyAIgQ2Yzube9Xx+TIL1wWwvLxhhJZ6eB9bT5Lxm8HR0dZ3I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89395}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/perplexity", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6c4c3fa2da6f02e0870e78dbb8749c97f1019841", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.6.tgz", "fileCount": 10, "integrity": "sha512-OdNyIpJ/0nGH8onBK8ZAHG5XuCAMA5zDk68g0zWPe6Y/EdebJuRCfSvEiFXGrs6e30ft+85iyhworplQ+YSllQ==", "signatures": [{"sig": "MEUCIFA++PqMNPTPiE9Df2cXmfrUQdXgmJpWHM06K0wS86QhAiEAoMtxG6+1V8fq4UXOHXcW1CHdO8YfGry+d0n+jFErrfM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89518}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e5d9722f1c9751a7651a1c6860ce890d1b4b3b0b", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.3.tgz", "fileCount": 10, "integrity": "sha512-hgvOsX/8KjLQBGArtdabhmavBFV1hrlAsEsgbVGv06oxiopBuhEK2MiW7+g5wClOIt5USW6becjh/GzDsX4Otg==", "signatures": [{"sig": "MEUCID/xAtIFBg90tzYLEUgrMFge7t4kopaG5D+RRdHQIqhhAiEApwF3lqwv/++y5+/MJDklYoeiZ/DZXe+obTOP5nwTjmU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 82570}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a1ea5e07fec18412a1f36199098a87c8f19da151", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.4.tgz", "fileCount": 10, "integrity": "sha512-SGBAUg/CTxxahFrbBGcCRYlJl4D3n46E3rc7yUX9iS2JQ0mLayFoduaqdScd9EFd2rdch6nfMfh1Ty6gmKloRA==", "signatures": [{"sig": "MEUCIQCJZ3CFwhEoerGDC9wBjVBEhlznn5wKujluz1H3AgAzFgIgcCEnSJDfy6VNlY/ZIsCjI7XX+PxXB0i4ALwST8o22/g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 82724}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "54b0bb1642df05517595ad0d9da4f05d740dfdc0", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.5.tgz", "fileCount": 10, "integrity": "sha512-jcULcIx/gd/j5+OF1kWgrz3hcLJXduYFsl05pp3duyDHs7s/DX2Q6h4UYydU26DjZ7QcKuU8RiG1+ozbR1QmlA==", "signatures": [{"sig": "MEYCIQCVuS17Ls8DEfuq3MlXUYR/hI2bUKyr1BbtfsK+TcWS+wIhAPFyXiqyB/0b+NgGZtuAk4bDUR9vCJvFN1jpRiEcny5Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 81582}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f1b6ceb279ad0242fd1b59ff7b77f9b0fd27551", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.6.tgz", "fileCount": 10, "integrity": "sha512-rrxg9CHd8R0RMM8vOdmm0pF+7eXNixMH8Wk2lQUN2Varg9ruVtThBC/BCyQ7XdGlEUL+xel77ZbBGPG9XQMi3g==", "signatures": [{"sig": "MEQCIGJvIiDZ9sObIsY02kTBZgINOIxYlfbU/cs8Fcx75H6aAiAR+5wfboUVS3s/cFJqvvEvoqpeSmqbAdOWg57Box5rjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 81420}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/perplexity", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ac5f70166c9b1881871c592485be64dcfffe9e0e", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.7.tgz", "fileCount": 10, "integrity": "sha512-FH2zEADLU/NTuRkQXMbZkUZ0qSsJ5qhufQ+7IsFMuhhKShGt0M8gOZlnkxuolnIjDrOdD3r1r59nZKMsFHuwqw==", "signatures": [{"sig": "MEYCIQC0LEwXXq7U///w42/N+MYp1G1MzYQ8jJnL0H2y48F3iAIhANOcbS9rtUGeXc1qS26wlBjapbRi676A3L2qH4kElDd0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89641}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9fe5390018a8facba89b52cf8ec017abf3e6f479", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.7.tgz", "fileCount": 10, "integrity": "sha512-qfbZOba6cvSA2Fl1IX0mVvwbN9H5lLOyt02qrwvPtpxYRKmNlCnzNmWJ9kwvaymJPw1UAv/VRrHbMKygvCDwhg==", "signatures": [{"sig": "MEYCIQC+Zzxtka5Vu0NXvBZzthfAEZrt/KWUIY7XtVZ9fNo+lgIhAMhngmfcipwhK6OkYdOPyIVooA58Dy+FFEVOn5ju/GfW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 81889}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "041fa076a6487cc23356ba72d6e0044c47562c48", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.8.tgz", "fileCount": 10, "integrity": "sha512-T9GVFzmZf2vATMxPPYWZccycrKgrd7Cmfbatby9QYJ/ksotiv1q3op/y7hOYyCE7JJpTzPJEhdtePKA3jFAJJw==", "signatures": [{"sig": "MEQCIBlLhAXMIgOcNVHqbczGyKu5ItfahUaeBPafL6FazVfFAiABb1h+GvLvPH/RgBv8IkmV1fPrgb8RbvG95Nx3oQje1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 83572}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3ee600cadd002679616ba23f4ceac4f509322b39", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.9.tgz", "fileCount": 10, "integrity": "sha512-bKJTKc6wiJl8QIjL8q2+HlMaSk6Ggk9niZ3szdtvPbe68AOB4cJK7ZNGyMTWjeJgT+kEvtxGEarLFOBHJ+Cf/A==", "signatures": [{"sig": "MEUCIQD+avYJmYX6PbJh/pRBYfCbixTDeTRGNH8fKAkvUkLk2AIgOv4U8DZX/VUJ8eSqSmGxOgDdXs2ULzsHgqAeoBgaLK4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 83700}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d1856e47957cd0b833a6765dcefb6320041a17d1", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.10.tgz", "fileCount": 10, "integrity": "sha512-erBIRa2DjEIjHaC3PdbWVv/vMGLs1dxTdiGnfsmrLMHeyEMKelimsNM9QCk8eaHdh56kcpcOmz1CKV2x5LwsIw==", "signatures": [{"sig": "MEYCIQD5CUTD+Vc+GTsomK/HOXtPQNYgmcRlKFmIny6u0sTGfQIhANwJRbcd4lYqqOuaTZK2CPZEMQ4aBCvcvG5whLXIueFY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 83854}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e34278cbc9424228e1848a9ca4256293bc0d7477", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.11.tgz", "fileCount": 10, "integrity": "sha512-aYGVrzoS+lj5IkI2rt5CeBVhQ1GS/TOEZ7UZ7VRA/sVUV6VMNhDohCfYSMlkloAh/jFOH4/0+oyf4+Xgu3cMLA==", "signatures": [{"sig": "MEUCIAwWfDjdiLCNkOazKxdlZB8KJAzcy/sGfwgBGkUTELBmAiEA8cW1/TKQ0dp0cwV2PzZeCaqQYQUadDmVmWWuzFF1KMI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84070}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e1d37f5966194e6e5d495f8d1cdad23ea418912", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.12.tgz", "fileCount": 10, "integrity": "sha512-tDDRbO8rIoBDcWLHrw8TNwrR8PAIU3OoAN7cxHqr1ZKGydeaqcF6gV/QwCDlZDnN+Jz1yAQrHM3VE5e/jzCX0g==", "signatures": [{"sig": "MEUCICqCKe64K9x6sdzoVTTc/hTX7HkrMPWMRc/0Be9duleIAiEA6Q+XqTeRntVYaE6Be9/N3wwSUaZAsssgmHafRTj7LVo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84289}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/perplexity", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b093c487c05e00c9f900aa2861bbb6ce5cb09acc", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.8.tgz", "fileCount": 10, "integrity": "sha512-hdyMknEKRhUr4AwIswUNsrT5XdQ35tZl9jAiyi1VbhxfW0mCr5dxPt/nYWYN0sc+2m7yfG3L3D5J5V7GIJiqzg==", "signatures": [{"sig": "MEUCIQDwHriMQBS4Pf5enlQTMeIh7kdzDZClwrmfzlcYd5lJvQIgUnxRz9whrfmbWmQxNJw0Xxul//O8yrF3LhXLs/r9E2s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89788}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1a936344293c1c0580ee8a6a4b1be884b2dd345b", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.13.tgz", "fileCount": 10, "integrity": "sha512-<PERSON>szakqUy4HBYeiJ5eJSNdgxg3y5dPJvOL3pUj6Wl8ZB1wDBp4jT8jpwtCaQkNYtzYofiY27xm+cE1p9G9hVWA==", "signatures": [{"sig": "MEYCIQCVm5KB9WQIc9euBs9W0jerSC65XlzGRmoso+4QOcwiCAIhAPRdLCOaOVDjzlDSCfvQJC48+Mplu+cisRN+edwxSqwW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84440}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2a3ff411f4a9ac6bc57fcd0f7e94db81cb25499a", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.14.tgz", "fileCount": 10, "integrity": "sha512-b4KCRO+zytZDB+rgb08lSKjs8nn9HGlYRByPZ8/PSuoweQcXKVKQed/naavQiiLJaSyjqHQsm8rZPkNHGuWpHQ==", "signatures": [{"sig": "MEUCIH07q/qDFTQgF1Sm6b2Aiuta752cCK1jkV+mGE5Ex+7oAiEAmeIrpPTCBNgVtcvCZ/C2R32bS6U+DSG0aqsnhlmDVto=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 84626}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "99b06c148adcc20cb667c02d021e99472092f48a", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.15.tgz", "fileCount": 10, "integrity": "sha512-hgKWEDO2s8FuYtCnZHqewZtWvfoH/NI2IogIqT/Qz6RrCBHLq9f/IRpgk8PQSFIC/tdEDGSJubqT3PTpjT2Kaw==", "signatures": [{"sig": "MEQCIFOT5en8ocYYAZygXoc+0TQflMPaZcusagpF1zooAEAMAiBaC2LBqodguIkS6QHQ3IMpdBeyK4Nrsce5S3WVwEc74Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85703}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/perplexity", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "99542573a4ac240e513c3b7de99359a77a7797a7", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-1.1.9.tgz", "fileCount": 10, "integrity": "sha512-Ytolh/v2XupXbTvjE18EFBrHLoNMH0Ueji3lfSPhCoRUfkwrgZ2D9jlNxvCNCCRiGJG5kfinSHvzrH5vGDklYA==", "signatures": [{"sig": "MEUCIEf5KDiBVzoBZ5MlXGdCjh7UW+ur/kcz8sRHI83lRR1SAiEAx4aavb6aVEEi3jMBN6mi9RwKU8X877y31OF3f0aaf0I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89872}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b5e6aaf68b1178d862127867d89f15e579d6bbe1", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.16.tgz", "fileCount": 10, "integrity": "sha512-eWOCoAOcQl7cdvdy0m2yZyu0FBzjg/jYuIhgVxIn3fOk3RTs40Z3Wbf57Xi+lyKjj6DNbTQ2atDYYa/pFIoHjw==", "signatures": [{"sig": "MEQCID+Nym3fjhqK8xC/0agzvX66eAcCoknvQhLiPoXYgyt3AiA0zDV+oQ/w4riPje+pBYibP/khILWTzRKTukNLKb7ikw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85819}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "89e5915e0db47fa30ba938e378f17cc9c4953c98", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.17.tgz", "fileCount": 10, "integrity": "sha512-1hKnH+gxxx1z3I7TpdlxyFAqm8CxsxrJvdo8zBYbRCLqIqjFRyP8sP3+wvuSBJwjCfhUIZ3Dms6lJsAaomjwRw==", "signatures": [{"sig": "MEUCIHmFrH/cXR53cBju+Z1lsNZxIP7uB+RatkOOabVdkwFAAiEApBvgf0rY+UHLVV34FDxRSIakl2asOjCJyNdFm8ONzrc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 85935}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "94ad1417aee84073150a4fecb4930b086c6298b2", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.18.tgz", "fileCount": 10, "integrity": "sha512-Y98aHR9AQ59mU19d08KkEd3K5oR4LUVTyGF2LJ2NakcY7fw28m8pIr+IzVj1AAE408AHupp1Ip4CcAGMBGtg1g==", "signatures": [{"sig": "MEUCIF/uRm0OlbR/o1MlkCF8AEWfO0C3VjYTm0GAb1QFR6WrAiEAhMeRuDaDk14lwS86HSOSyXK7n3bvyOELTfq/mjJXUlw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86051}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/perplexity", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "31806ab4ca01c74ab2c544ef7fadb83c75183978", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-canary.19.tgz", "fileCount": 10, "integrity": "sha512-aPfVYbNrioxPKiMGomaLi/7uU8aY9/HBdfmw4ub0CPGGQn+wY8pFo0rHJtaAwMdmMdycDQgMrAvMSmxySp+ogw==", "signatures": [{"sig": "MEQCIA7NfVsAobmw7N3vyqNv1QAhHyAAAioje09N3II6fHHsAiBlC+GCk3N4iaNFo/QwW2yiGVxnJrlCnZznwDLAAQEyHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86168}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/perplexity", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "056b212635802b640f7ef47d2244df71b6e53596", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-mJbwEGYEmEybL0WnfcP3la5I/VOIL5u2T83lDp/jYH69Vdfy1j00Cdfu3dRMPTxas/b84Mw1+bznQ6N8IljGJA==", "signatures": [{"sig": "MEUCIQDLb+Nc6oiFZIoY9T2Wcazdroz6CpwqcwSU9tomagG1jgIgLsE9nWroQtBAFyeOsBv4A4G7WY9Pu8SD04R7KSgYe18=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86309}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/perplexity", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "b4cca2ac9ee217b7e52bb35667d1ee2532729c34", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-2ycn2Skn+ewzUf2oo71brkSRhfGhCaSbBY1pxxcBK0U1sifiE+Q4nvwCuGc106W4KHIJeSPDiWAiOgSp/zLSyQ==", "signatures": [{"sig": "MEUCIH2Z3B8Ma/dK288yAlB5tQFKIPWCXN6H8jHtCccFfQ+aAiEAnZxReOsCbGp4gYAabGZ+jNxwPCmqa5TLl7YyCvLDqsQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86456}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/perplexity", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "2d7984d8fab618b7e35702cb72c28e03872b89ab", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-bSqj9Nkby5HPKHJyroHvTTjm6ZM8EJw0HGqWMu5btly+NYiHFh0zj67F1YY5snMLQQ5nqiKX/pa7pHh1/ZGjuA==", "signatures": [{"sig": "MEQCIFGTCMx3BCFuhnoKyQkQvBPKmEIFqkL38gSTP5GFRFZUAiB7WEwqi7ABooDorNZ/5sYgcN0E+LboKEVMUWYLJGKP3g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86603}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/perplexity", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-xO00B6mJFFHGY9juU+uwtd80wZMEvx5Op4XrwZ6NK+W595J4LcvtPtSThSP82o6OxTeZczgzIo6Jc70LqUlKLw==", "shasum": "5ef954f2f7e7185768f551a7a762feaacd1f5388", "tarball": "https://registry.npmjs.org/@ai-sdk/perplexity/-/perplexity-2.0.0-alpha.4.tgz", "fileCount": 10, "unpackedSize": 86750, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCz2P5Si6wmYM/UuHpV+CUUJtfYQ3026CreAyW8DyQJfgIgOjHLaJhm6IRYJ+fqNojxlY6AK0By0wpiik589y6+/gA="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:02.202Z", "cachedAt": 1748373701813}