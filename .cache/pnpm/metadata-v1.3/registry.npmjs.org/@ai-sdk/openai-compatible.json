{"name": "@ai-sdk/openai-compatible", "dist-tags": {"latest": "0.2.14", "canary": "1.0.0-canary.19", "alpha": "1.0.0-alpha.4"}, "versions": {"0.0.1": {"name": "@ai-sdk/openai-compatible", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "848e01859858ec0d6c0c5b731d38dfe638759998", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.1.tgz", "fileCount": 10, "integrity": "sha512-wKoJqogY5qmGBWTImzuP4bVraIOiZikCMMCN6xNQXQvjs3Bur4ahdD7+IlisGlARUyCenDzWFENZvCAih+mjiA==", "signatures": [{"sig": "MEQCIASdjlIDdQbLQeoE4OKC38cF9gepOf/CDl/Ns1sBp/H2AiBB7qYUbZAPP5UqCgOrLryaNIvMHDqHfbAk3Xi81KIa3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222805}, "engines": {"node": ">=18"}}, "0.0.2": {"name": "@ai-sdk/openai-compatible", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a0798fb8b94cf3cfedc138089321dd2a0ab5f924", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-i79B7V8lA2HzwcDMj20zfIBIX4J2uwoF6Vy9lnWIW8kqY9yKBFujCRVCX2Qx7j24p4FQNHwsB7h30EftVprOmA==", "signatures": [{"sig": "MEQCIAiZ1bmMKyHUXZvOwc4cxrUqTMeusCPh4YUYwlPxMcYBAiAovs9sFudjT2r8icEuRac8cty9F3714hd8xr9/gpcqOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 228531}, "engines": {"node": ">=18"}}, "0.0.3": {"name": "@ai-sdk/openai-compatible", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "58362025757ea8159f7b5e738eb9c00e813b61f5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-HzlT1O57llBbLyqgeBHAf7rjRA2uKUmVi7lncKgE8xU1syrvoDdsC36jRAEhaj3sviTKbMhptokDHDlE3t0d8Q==", "signatures": [{"sig": "MEQCIDDmsTDXZ8po1HieAQ0ht2NEodhYu0IEMXjYNFsQ98/pAiBwvn8RYGR09fDXcRmxBvg5Ex8Q8a2D68vS+DpZ279qFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229845}, "engines": {"node": ">=18"}}, "0.0.4": {"name": "@ai-sdk/openai-compatible", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b9b073b8c88d7eb8dd50be9f8a13c01cc3ae340b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-P<PERSON>bLFaKEqMEga75wDXmT4HAYxuR9e4E5QKXkd563t7xK0wkQsUFBFTSGSn0cgA8TuWgZWcCPiZBxtQFSpxkpag==", "signatures": [{"sig": "MEYCIQDCPJdlhG0OvOC6uo2Kv60bThF6nUhtURcabST/BKsO8gIhAItIX1fT803yb9Wf7cscxIB6igGTZYOFaQO7vFYEA/kS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229941}, "engines": {"node": ">=18"}}, "0.0.5": {"name": "@ai-sdk/openai-compatible", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "42640b50064b938b17e1ad75ee8a0e085ddadb3d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-xMx4CANxOAk7HTRFFX/lbsxU2d6l4aiIf018GEi4DM+ugClnsgZEuwIuHBfExFwZGEV4gF2/GPVPtjmf5ZMXtw==", "signatures": [{"sig": "MEQCIGWryeJw0ieHiIYyu/ZvFcyOirdpseKiykZoye20z6YhAiAqTOrb9K3CgXnpVbbrRPUEy6/9gOSIbqwbI/EGuXT4+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229649}, "engines": {"node": ">=18"}}, "0.0.6": {"name": "@ai-sdk/openai-compatible", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "38e4bc908243eb202b70192dd7536f5dbf4ffe73", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-sjS57/Ra3jAbPnhX7ggHBgiPd+KEFOCPkEuI7RhGnAbxJqtwnbtBkHBsIgkM8hCF3ZIjks0o61p0vKP0Lgzd4w==", "signatures": [{"sig": "MEUCICl19eEXfXi1cYcQmhwFtiHPH+3VpYjS46+PH67PNF/NAiEAypf4yFPozFJslzJT1fSbNa9zLPteXdw596EGQui2hy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 229772}, "engines": {"node": ">=18"}}, "0.0.7": {"name": "@ai-sdk/openai-compatible", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d89e5cf83c1e7991b17f14e8fd7c4349a84f44cb", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-rBLWxNKzD8C2q+x8vVYW7mniqNGN7CDf4V0bpfq+8VaoyPWEYgsvI/H+BJjvM2PReVUccDKUKwruZWm3B5id8w==", "signatures": [{"sig": "MEUCIQD1bV5FrW6OJR++fNuPDUjcAfGidEVi121R1+cKVqXSqQIgNmgX7cZaWtEGyGcoI2+0lD4LSOov3hC6tD8fC6vHpFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236565}, "engines": {"node": ">=18"}}, "0.0.8": {"name": "@ai-sdk/openai-compatible", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1f3000f34b50ec417c780e08f9e864b05c364308", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-gEyOSvAdhMRcSyfZeatrx1wv+ju+fS3zB8IbY8UJzs6WBg6YSu4ZqEJnY9UjuZ2TA0fmw4G7Mn3N4rJIrdiTOg==", "signatures": [{"sig": "MEYCIQCKjFwrXdXe/gMNfKgRwHXwRuUAndxsocpZuv7K88HQSQIhAIJUgTp0NOxn2UrQ8B4g2Q7anfTGTAQNMnWLnMAp0Woa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 242995}, "engines": {"node": ">=18"}}, "0.0.9": {"name": "@ai-sdk/openai-compatible", "version": "0.0.9", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aacff62aa3f04d4353cc0a7c088d9974558bee9d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-CPTUtgbuuyEUE/wtilOLIqu9IiIs/uB35wl46a/acngGsX8v3q80+rnIginIUQe7cYVjpMgXxKZJCgxmwJqEUw==", "signatures": [{"sig": "MEUCIQC6IcwUCTtnmeDFb4Q8a5VUmGtd1y4Hr68rWGsLh1mDUwIgfSBMJZ0ZEhMuHV+bQUbbUPPPrOcpcrDBkEydvWFej7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 243215}, "engines": {"node": ">=18"}}, "0.0.10": {"name": "@ai-sdk/openai-compatible", "version": "0.0.10", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "75159f66298763a0662f30f1fb568e8f026ee5d5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-ii31lB/9vucVGrpNxynZ4KHEQFaOXA560iRTgcVpTEXloZ2NundKgWAEQTlb5ZipSkDp47/arOphQX/Z8Ywurg==", "signatures": [{"sig": "MEYCIQCrz54y3rr4B8TbGTdntjaamcGi1N5vrid3A9YtgauRlwIhANCvmQGUP2nEo3LIrCGCDrzGqRduyR3BTBQJQNBUB6mq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245566}, "engines": {"node": ">=18"}}, "0.0.11": {"name": "@ai-sdk/openai-compatible", "version": "0.0.11", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "371010ee6e9053464bf5aa687b28f6c809953a0d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-olUoM+3tr3DSeVsSAbJiUolydb+I48IDnoGi8wBfgJ1mktXbHjZqwVEMCmhuI8lxKdQi7la6bNOOS/Hmtgnn9w==", "signatures": [{"sig": "MEUCIQC8F9FzXqgo5ih0vZm1wEKFzrUwKgtFEC128JFE+Y+8mwIgW/57Qnbu4l5ZigURngLrT1C56SeCAWWWyF+edGtWWNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245850}, "engines": {"node": ">=18"}}, "0.0.12": {"name": "@ai-sdk/openai-compatible", "version": "0.0.12", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e31760f182cfbf4eacc8ec4f5b6247e3ca221dc0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-K0QiZauKC8kNx+0aRYsTwc98NoU3im/bDphmLHFJ1ozsE7Q2wM6YkzFvdWLsV+wRT6HcGckfTOTDbqMBfxwd1w==", "signatures": [{"sig": "MEQCIDMhRfVFDdokyNjxoeN8UwQqfOFqSev9UkR1Vu+Ld7S0AiALqo+ykQ5RJmy/HWa5xlBv3CUi7zqH2JLaOp3UsDNaYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249574}, "engines": {"node": ">=18"}}, "0.0.13": {"name": "@ai-sdk/openai-compatible", "version": "0.0.13", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b281ac456537b8fdd73acb7041d750ac3f1366c5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.13.tgz", "fileCount": 16, "integrity": "sha512-fuauXYKac6PBuf8m52tWcWQW0UCScEkwTaOyr00TcPeK3dd8nPP+ZJzSYE5QhFg7rwi9EH3ahIFqSX1biXhdkQ==", "signatures": [{"sig": "MEQCIEQeF+b32SVa5/tb3QSaowKqVCyxhsQcMvKtMasW/KqcAiBTZ0pPt1yMIdgLymMp1UZz5NRsJ55aHABOUD8V2kmzCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 281900}, "engines": {"node": ">=18"}}, "0.0.14": {"name": "@ai-sdk/openai-compatible", "version": "0.0.14", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "77630d6a97e350571e4d53a0e2a3eca7932ba943", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.14.tgz", "fileCount": 16, "integrity": "sha512-WJzb7u869z3DBtUtbkUVVxwJ+WXK7taPHZ3/xMsGzMgDg9bHC2+R3h0Hj+lHhHK7AS/y56bbEOCSIuWXt3+MpA==", "signatures": [{"sig": "MEUCIBmE50iYsIyZWgSXFeuDpIwYxxsE7KGegVJ2bef5iyrMAiEA9cJAXGi63mINUVCGmSTwPGlFVA9T7Mh6GA9uhWEksyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284672}, "engines": {"node": ">=18"}}, "0.0.15": {"name": "@ai-sdk/openai-compatible", "version": "0.0.15", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5c74486f772d882469151b4fdde0394fc54ab140", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.15.tgz", "fileCount": 16, "integrity": "sha512-kGuaJ2RgMO/ftX5LEpuYnShGTyk0YfH0IFKduFJo0YKuzlI3VgRRfYAnvGgTscpSMmEQTAlTtV4CGt9HM1v74A==", "signatures": [{"sig": "MEUCICOps1gHCq+UeEeiMubzMW7WP4agYpIWpmk8Zg52dEohAiEAmbIRGqbQ4h6rGXUbJ7RjejkEgX+vmkD7WJzG/mtqR5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284835}, "engines": {"node": ">=18"}}, "0.0.16": {"name": "@ai-sdk/openai-compatible", "version": "0.0.16", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bc74c40f4155cd2d753e9a98993e1b9cae9a14d3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.16.tgz", "fileCount": 16, "integrity": "sha512-XBzNy/12DcObUP0ljUmySx+IxZ/4ytf0yCNEl5AFoE6mNJk4SBephavw0jgjcXjP8ffwaHvV8cCmN9Xh0aZZhw==", "signatures": [{"sig": "MEUCIFOrpEKC3KvB6hWJKVIij7xmHT15e6rwD1Q5syVfXKCVAiEA5Q9OrM3wfeiwV9z8AR7nccUusI2RzBAHKFKCZIr+U3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283781}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/openai-compatible", "version": "0.0.17", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d1057de13306f30c423e8d3641d847bba58e1f48", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.17.tgz", "fileCount": 16, "integrity": "sha512-FuaV9gGysF//n6OfadeaCE/tKHG8ruISeWD1zLPsmlEAPpg+eOENICEzkdDgN1JtFwou85zeMF49oyWTWGV8SQ==", "signatures": [{"sig": "MEYCIQCmiylKDNTmROKHTpowao/ZFrUOyBAYXe/tMcC2lXsnCQIhAIxyuTz0mf2E0etvja9ECN4lT8imJOE3J07CL6fbHnAQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285428}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/openai-compatible", "version": "0.0.18", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e6127e39873061ba585a33cce5708bf69c86258e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.0.18.tgz", "fileCount": 16, "integrity": "sha512-4qEDVl8zxKjR07vxJDv9Yk4tGoJMKBe+soAEBPNaxY7bUyeniMYPufSIA32otYNA+c/uq/oKKsA3XsORmsYVeQ==", "signatures": [{"sig": "MEUCIGiGfckKPr8cQTGObsNoVElv526oMGLU1dN/wVM4qSiyAiEAsaD2sjvfMecuiZGKsqFMM4rS8dve6buZJfXQrnZDyYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285525}, "engines": {"node": ">=18"}}, "0.1.0": {"name": "@ai-sdk/openai-compatible", "version": "0.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ec5f4c22a927a1b4499a6c2a4163ec60e7c56fe3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.0.tgz", "fileCount": 16, "integrity": "sha512-sgk8oPdnTwImR77HT+esnjoZ7Uu4V5ptXJY+w06GGRCeM3PMJowAqC7gAdYhD5DBYUo+4Gs+wvKenaPBNV0WVg==", "signatures": [{"sig": "MEYCIQCjHuey/OehHzhx+0mxd9Dt+BPzUavoOZlunkhff5DLhwIhAJLyz5jyeuYlDgVQPLdlmz7GasJDzFYiQsbJn3cBWVii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285671}, "engines": {"node": ">=18"}}, "0.1.1": {"name": "@ai-sdk/openai-compatible", "version": "0.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fdff1e67e6b3b253b0c1cc294c322411adffa571", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.1.tgz", "fileCount": 16, "integrity": "sha512-/RQhsi/7Ww8AS6W7zHf2IqWV6BrSKaBruysZS/ASlnYcI+wHhgBBZfaUQN43zH+3EXkUs9ahxb9RQtQT/DjHQg==", "signatures": [{"sig": "MEYCIQDfCtb2lVHdZh5H4ReodbMwa30sFyZGSB17D+SnDZFcCQIhAOXr+FKI9PzpF+kTIME7HrLwsQY0j+5MpKuxAGv4RaKx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 289278}, "engines": {"node": ">=18"}}, "0.1.2": {"name": "@ai-sdk/openai-compatible", "version": "0.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "849d9cbed04044fb0c3177bb34e9bc8b65928833", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.2.tgz", "fileCount": 16, "integrity": "sha512-5qvWOzJDR30ygNJtnJC7eQghn95krj4KyM61OXan7dN8dtypW69b66oa3XOJVY4Qylbi2aeAvwsNYmTSyM9jbQ==", "signatures": [{"sig": "MEUCIQD9iiYTgMzsqNaLl+SuAFZnJ7Y8BR+Y+D5FKQ/yKm3PUAIgXALDWXcKR5Ng562zVKguV8yvM/RdBxkbFluYcjwSAcY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299575}, "engines": {"node": ">=18"}}, "0.1.3": {"name": "@ai-sdk/openai-compatible", "version": "0.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8bb7926a397cadbccc5aee70623fbd508d70f2d1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.3.tgz", "fileCount": 16, "integrity": "sha512-3dr81jVNTd7Tg4i6JwGKHX47DnQ+jn3zOuxLvu6bM2hFylchtIFn/ut3Et7VfsdMWf4gj9tXp/9rUiQ0JokkrQ==", "signatures": [{"sig": "MEUCIQCuiXcHeykw0P8nfF3Bl2id7fiMfWEPRiGBN+SpyZm68AIgBK0qAU+Ot1SZkiZB+cnNhxcWMS1BdsuExs8vrbJqTsQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 299936}, "engines": {"node": ">=18"}}, "0.1.4": {"name": "@ai-sdk/openai-compatible", "version": "0.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b0e86737f3a56a8bb443a60fa523265c3b3a4999", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.4.tgz", "fileCount": 16, "integrity": "sha512-zt+JEG2y0MptN9bpg9OSJ6G5CM1CwkUivT46r736e/JH9OEJ1EtvP0gTvseQCBnIkV1cZZQB1zMTmVBYFnSmow==", "signatures": [{"sig": "MEYCIQD5lgbWC6yaUUluyTkFa6jDt+U2hTQH/7BCBlRp1v2TjgIhAOvFLtvhEKbaaz0mng3WRCYJpdtsemBiVnjFAhphgHML", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 300032}, "engines": {"node": ">=18"}}, "0.1.5": {"name": "@ai-sdk/openai-compatible", "version": "0.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ff8a338f0ec4e36c7dc60c7de8a35e8b7c7f7c39", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.5.tgz", "fileCount": 16, "integrity": "sha512-Y2bq1N5xW/AVpcKXHzaM30sNt2SnAB4jeNUT3Aj5J0Z68RGn51qx8uvizpmgky88E5ojGGiB+Avu9dgt390HaQ==", "signatures": [{"sig": "MEUCIFlyMk5olplpAJdB6dcq46PPt+BfqaL5q/0LBMVll3xXAiEAwqpZEeBu9rzRMjuTqPTwko/U1WXwRrbBu2oHlrmpg+A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 300128}, "engines": {"node": ">=18"}}, "0.1.6": {"name": "@ai-sdk/openai-compatible", "version": "0.1.6", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f871a6bc5702af9044364c1a9e8f3d32fe48b0da", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.6.tgz", "fileCount": 16, "integrity": "sha512-8byBCRZqdhIP4C6ch4kZ1yjJF6yLUCXik82+71KF3okv5Jl+Gi6qo3P7MJb/LpkdSvXa6lEroNXPr4itThWbxA==", "signatures": [{"sig": "MEUCIGPOa71+Wuo8KWvwwgN//wvOveXrdmPffLudMOS/ks0+AiEA7h87fmVwQABNZEhLKfoJnzLPPejAmRURkdYU27tvxpA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 300224}, "engines": {"node": ">=18"}}, "0.1.7": {"name": "@ai-sdk/openai-compatible", "version": "0.1.7", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "41af4aee60b79cef44e7a96ba21685e4ca2a6f01", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.7.tgz", "fileCount": 16, "integrity": "sha512-hLnV8t4F+JUgAKnSUh3Vw9WQ0N1+qFv0/rz8ckJcRP9cw2zO4zQiaNDwR7eqBiSsNoiFKI6FzPXNpGvIaduLMQ==", "signatures": [{"sig": "MEQCIEOkSvz2Y+z3B1fPQnoJjoTf7k4tdG1T69Lb/+ZPod5HAiBDIK4UwWNAP1y1qs0bUee2Rx8jWBL0aLdtFsIDAqY6jg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 301623}, "engines": {"node": ">=18"}}, "0.1.8": {"name": "@ai-sdk/openai-compatible", "version": "0.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "714c6f0f2877f8f63f490e69940146c79b148895", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.8.tgz", "fileCount": 16, "integrity": "sha512-o2WeZmkOgaaEHAIZfAdlAASotNemhWBzupUp7ql/vBKIrPDctbQS4K7XOvG7EZ1dshn0TxB+Ur7Q8HoWSeWPmA==", "signatures": [{"sig": "MEUCIEi7gABCAFGSaZkhRqWMjPLmQgngT80D6x0mrxBNcAy1AiEAuZnVZNPUcILvay30ioHv5tin67swdvu6px9Mg34PJJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 301746}, "engines": {"node": ">=18"}}, "0.1.9": {"name": "@ai-sdk/openai-compatible", "version": "0.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "070e2af9f986cb52a4204c96a967d206642875cb", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.9.tgz", "fileCount": 16, "integrity": "sha512-uPNpMVgz9pPlhEX2mK7GtVej/5MhvKZoN0ohKBA1hPdTSTdpCqelYlYbuRKLrehwdMFVUyB1SHO8KKKNoa/cHQ==", "signatures": [{"sig": "MEUCIEkoGq3lK6uXuyVwTVwT4wPsU50O4E8q4oldW+Wd4CVHAiEA/RzS4A4vRiYWFG2V5ESfqfOf8OeAIYoBtDDgRKqLbHc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 301842}, "engines": {"node": ">=18"}}, "0.1.10": {"name": "@ai-sdk/openai-compatible", "version": "0.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c979825b0dea469c4a43d54eaea58872e6a229b6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.10.tgz", "fileCount": 16, "integrity": "sha512-AR5Acly7U64wDgVdVs9AqaMHgVps/35TMUMDS4akSZjsB4TdAhKnWdMXmACzCxmbCySHMoMnKY41XgvCyFN1pQ==", "signatures": [{"sig": "MEQCIBqDukSy387NriFj9Yqh0rsKjL1ZMpl3nJhA7UU3qOSXAiATNfJuFSnFYRA/SWmk84wAbFe20Zf1kxrZAEV2uyC4mQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 301940}, "engines": {"node": ">=18"}}, "0.1.11": {"name": "@ai-sdk/openai-compatible", "version": "0.1.11", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "475d7cb0cd3135b14a1fa232aca879099c43eda8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.11.tgz", "fileCount": 16, "integrity": "sha512-r9kIDPdKCM1tbZEEKUauGzXp+w1ADrFPPfSxVeVr7of4QIvKzXqLaH/jR/HtdZ1JR5i9uywHijOAZtw7ZcDvQQ==", "signatures": [{"sig": "MEUCIG2pK696P13XC+Tzq+zuERBi9nOpzFL/mRqA3YS5bf8/AiEAjTi2+2rKJybknh3aPnxJcDaDan84MdTJYqfxp2FECPQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 302064}, "engines": {"node": ">=18"}}, "0.1.12": {"name": "@ai-sdk/openai-compatible", "version": "0.1.12", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7c5d7669122c999d8236d92be54f040c8800ca06", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.12.tgz", "fileCount": 16, "integrity": "sha512-2bMhAEeiRz4lbW5ixjGjbPhwyqjtujkjLVpqqtqWvvUDvtUM3cw1go9pqWFgaNKSBDaXRUfi8mkAVrn1yRuY2A==", "signatures": [{"sig": "MEUCIFSuhn/0EHWvCC8k11cLBLhoGvovPsP3tHAKrziz/HrAAiEAu10vvWS8BXgq6baJezjNfD/FxD3CNkTrYEsZOZsgiLA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 305210}, "engines": {"node": ">=18"}}, "0.1.13": {"name": "@ai-sdk/openai-compatible", "version": "0.1.13", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "99707070d1662ee099ee79480157f2cebefa9cd9", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.13.tgz", "fileCount": 16, "integrity": "sha512-hgj6BdvasVXCTmJwbsiWo+e626GkmEBJKG8PYwpVq7moLWj93wJnfBNlDjxVjhZ32d5KGT32RIMZjqaX8QkClg==", "signatures": [{"sig": "MEUCIQC6r2rRLcqb4MSw1MdLSXaQvV0h8ZZQAHGUePClg1gsJAIgAL00+zqFuBEoyo9VcvrqBocZwbsWrtNsOPqlzQfapMo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 305923}, "engines": {"node": ">=18"}}, "0.1.14": {"name": "@ai-sdk/openai-compatible", "version": "0.1.14", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "905b17bee00feed33343ad9a0e015bfa1edaec5a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.14.tgz", "fileCount": 16, "integrity": "sha512-TGb4gi3/Vnxf+JwDhQQihFvyXQPee+5C9fLvLldeFbvz0PVW1FbqIURD5V1n3WxjXtyGKbSW5zSWCPwWj4cRMg==", "signatures": [{"sig": "MEQCIGSEY48xiX6w3BbXdGVfYfPP3qF97Si7z1OR1PHbkOFZAiBLUSkRVj7iYCZ6hVz7VNmZ2qaOGaC61ZeohYWf3DlL8Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 306021}, "engines": {"node": ">=18"}}, "0.1.15": {"name": "@ai-sdk/openai-compatible", "version": "0.1.15", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a7336d63a400da1c02ac530f42a67d57c9248015", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.15.tgz", "fileCount": 16, "integrity": "sha512-iuylARLylTkaVYXKdgA1GeGU2TwANaJ3RlhJqmN0cVO5uEt3qVKvedrzR3jrQIyy9hu3gngsiXfFBMW2UQ7Ntg==", "signatures": [{"sig": "MEQCIGkiNkgyq5ffKFsn/KT7RdWbREKCp4N16MWanWMsrP63AiAG7ztTJeC4u/+WjalxZ5/n+TofqsGAPBpoXV4nFKv/6Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 306147}, "engines": {"node": ">=18"}}, "0.1.16": {"name": "@ai-sdk/openai-compatible", "version": "0.1.16", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5fb7f42c2cc938b1522747513825436e81dac6c0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.16.tgz", "fileCount": 16, "integrity": "sha512-4+dlSJnXRjadbjTJ/EBDTjYl5f6XEW0vEIxJ8AiHq96Gs+bMmMxmTowwMR6PianLheSEL++BJE4KPie1qH878g==", "signatures": [{"sig": "MEYCIQCyW8+TrHi+JOMfhh5O3nHdRN134kLL+5hdC4R50xJTlgIhAJOTtLYePWTjlt+Edounm7iQ3LbcGSy0t1XuaYww8JGN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 303545}, "engines": {"node": ">=18"}}, "0.1.17": {"name": "@ai-sdk/openai-compatible", "version": "0.1.17", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1d85158879ead78ad5e126d0dc62f2297a18b143", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.1.17.tgz", "fileCount": 16, "integrity": "sha512-e60+yxQ29e8RlsTWBW4kLuQJMpVJzH5+cpOeUXLXU6M9wc8BOQCyYg4jYh2ldnfvYCKXYxb2kYeLW7L9fqhhMw==", "signatures": [{"sig": "MEQCIEUb+IwchC5VzPZfxOi4LmOPyUcytPstaoJ9c4/VOdGMAiB/A4gpe2aJ4XhoyfbSm2tfbjlifQsbXMpA0qjpdw1QlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 303643}, "engines": {"node": ">=18"}}, "0.2.0": {"name": "@ai-sdk/openai-compatible", "version": "0.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "96935200b3d3b227d03f11269aec09cfcecb76ce", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.0.tgz", "fileCount": 16, "integrity": "sha512-8rTOKGf791wlmIM4XKjzN/KqOsO50wUoz9PF9B6ZC4p3UZdLTx9f+pBk+Lo85A68JeK2uRkPAuXjr+8aBpqCuA==", "signatures": [{"sig": "MEYCIQDNkujp/YHMEoWVH2MfGqPsUAajbb8i5rTXh3UVn1E8awIhAPrp2od95qOUfzR8eknhicqE9aOdb2nxB1O2KcznTYs4", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 303810}, "engines": {"node": ">=18"}}, "0.2.1": {"name": "@ai-sdk/openai-compatible", "version": "0.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "95727b711aa6019992478a579e8ef6658a121fb7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.1.tgz", "fileCount": 16, "integrity": "sha512-7vQePiL/+BcbWf3qq5p+Ym+VH3DLY7cuwHucRtcuYSALrxtltKvVBlPr3vU3R9WdxcLyNwykW7UkVE2UpNp5JQ==", "signatures": [{"sig": "MEUCICdao36BGZkUv7VrXdw2xHvra5UeBdUV/J0V1INSLpPcAiEAmxy2nMarffqxT+qcjLNFn3iZ6fume5UsvscPGO1usMg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 303906}, "engines": {"node": ">=18"}}, "0.2.2": {"name": "@ai-sdk/openai-compatible", "version": "0.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e5e7e3f10fb259c8e56f0a9469559ae9af6ccae7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.2.tgz", "fileCount": 16, "integrity": "sha512-pMc21dXF8qWP5AZkNtm+/jvBg1lHlC0HsP5yJRYZ5/6fYuRMl5JYMQZc4Gl8azd19LdWmPPi1HJT+jYE4vM04g==", "signatures": [{"sig": "MEUCIQCC9N6c8ejdaw3iwmZqkci279jFIdu/NaPLkH9/Vu0q7gIgP7D+r3QtIOx5uYQ5aKqFa1PqF5QTyZmVOjdv0l2gi9Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 324835}, "engines": {"node": ">=18"}}, "0.2.3": {"name": "@ai-sdk/openai-compatible", "version": "0.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f8bae07299a4a6ee8119b32dfcc695f1627da0f1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.3.tgz", "fileCount": 16, "integrity": "sha512-q2rSf/Kp5Gsp/t8UKGBPukGFoJUMZOOtjqaqRgRtkUihEmAdV9s6xRufxxe/v+BPfgsGHjOCdqkBrFbtS+VJKw==", "signatures": [{"sig": "MEQCIC49EzsvpTm0cSvImkqnANBLs+BSXrIbOXCyziABqDwOAiAdL9FSZ02VOYql1zVYcl4Q3SUmXEMGpepNs36KV26xBw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 324931}, "engines": {"node": ">=18"}}, "0.2.4": {"name": "@ai-sdk/openai-compatible", "version": "0.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4673a5b8f298feb6578eba31cfbe64ab8746a9b8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.4.tgz", "fileCount": 16, "integrity": "sha512-hLQnBn5e69rUXvXW+9SOkiL+S4yQX62hjtlX3zKXBI/3VnfOTcGKMamK51GoQB7uQCN1h7l9orvWqWpuQXxzRg==", "signatures": [{"sig": "MEUCIQC4J9a+DuFD5bpugsLMLqKlN2X2Bfl8Qy/KHkEnNM860QIgeG0fP6Mr9e5pKPArqaLIydo48JR0+no1YKKQYfUJdXw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 325027}, "engines": {"node": ">=18"}}, "0.2.5": {"name": "@ai-sdk/openai-compatible", "version": "0.2.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f2d86c1ac9d05d733c459c2f14c27d9671cbc98c", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.5.tgz", "fileCount": 16, "integrity": "sha512-Mi83WLIbrmcrQ5b4LQSl8DSs/QHLGTtRu+5+Kb+4lzxCHAGiqzgEGrFww3S6bl+GVfhGtyTCONqU1Nok2r+k5Q==", "signatures": [{"sig": "MEUCIElZi1am8p/rvU9RQGuuP3EFpDWDsI4QHGF9VbIbKibjAiEA9GfO7xgZHFqyhjq5pTfPbnQkwM7nWUZTUgkMZzGYgp8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344891}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "019408e81056f3bfd1cb43dd8741185bf3b31084", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.0.tgz", "fileCount": 16, "integrity": "sha512-Ng5vlmQrygsxKnZEJAkt7uAy/YUNYYdZ6+bUCMGebWzAiIS9vfi4aFppxjo9Ds/9Zw4WOtw2IIutYjkC+PMJyg==", "signatures": [{"sig": "MEUCICPvO7AQFAxpRkVLdoV24Bb46Zwq3vkoyt4jeiZ+cTw7AiEAmAfYpA/V8f8AtDYbM2bO9LBP69ys2CSACOILrYlrs3o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345108}, "engines": {"node": ">=18"}}, "0.2.6": {"name": "@ai-sdk/openai-compatible", "version": "0.2.6", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ffe2361bbc9a4bec4676a795cc30e610b70c8d59", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.6.tgz", "fileCount": 16, "integrity": "sha512-UOPEWIqG3l5K9O+p7gqiCOWzx66JtmG9v9Mab+S4E7WE34EN6u1QS1pX+RDlRDhZ0/8gNJif0r4Xlc+Ti03yNA==", "signatures": [{"sig": "MEUCIHVC7SmcMj5PsbE6cOCtEB+pP8N/WVuVoYqrMnESnyGuAiEA4fM3wsDuXNLUQUcNEKkK3/wDpgQRqFaaCwFXj6PzVgE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344987}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c16bde28df962eb787536c5e25a0e15469408ed9", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.1.tgz", "fileCount": 16, "integrity": "sha512-sPPzqPeivataKFiG0cOjgkGt0glSe3ALBDZCb5yukSHTpQHEcSd+kyA9B5qsnu5Vj4aw+98BofN0V7TwHRkEWQ==", "signatures": [{"sig": "MEUCIQChIJA7ex1e9TffhC/jc6aUdce9OmHTcf4Wj8WCQweL0gIgZodxlM532R5tkSkbm1xiFIbu/CCyEccn+oirgIJVUpg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345196}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "56b193d666e593bc3cf43ca8bb0b63f02d8addf3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.2.tgz", "fileCount": 16, "integrity": "sha512-oLNR5oTjz5IiMYwh4Ykj8czW+ojcRZ6TK9Xuf9T86dcAhGKnpgmUlkHITQTlgYW/wVfZ3SbbIYqLpWPnw2CJeA==", "signatures": [{"sig": "MEYCIQCZFyhQzO0G4daiJ/T4vPM7uPzhYPXA0yGJEkyLjyiY8QIhAJ5BB60pHCO5Ql+ayKroMfuEwjiEI6d4hQ2wRKaGKUY/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 334568}, "engines": {"node": ">=18"}}, "0.2.7": {"name": "@ai-sdk/openai-compatible", "version": "0.2.7", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c6e6d2877003d5357df5629118171952af655583", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.7.tgz", "fileCount": 16, "integrity": "sha512-NZsXOzy/WUtvhEsvfY+UIhtQg1WwvlGeh8CgPxpWRv3TJSPgj46XKPiae4lCe34+EljmcNnW6emBI5APdcY6BQ==", "signatures": [{"sig": "MEUCID89y3VGaQnGQisCT8zH+oLTyJmSJdrbzSyyocBSL6KAAiEAkeR0jO3PljQ0Il75NMKmf1VzSLMdRATj543BtX7CyLU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345110}, "engines": {"node": ">=18"}}, "0.2.8": {"name": "@ai-sdk/openai-compatible", "version": "0.2.8", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "819440a753b3f2a3753168a22c281508629c38d7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.8.tgz", "fileCount": 16, "integrity": "sha512-o1CrhTrXnMj72G44oBqlDDBtunw6iwONysXj7EYN/Fx27rhP7YTcWwVeKs5gMx/u/8TIho9iZ9rkqXXc2xg8Bg==", "signatures": [{"sig": "MEYCIQD/G7sQrPTPypicoUDyXhpfXZvY4rRfxDtGIEL/pGnWLAIhAMtzIUWX7ALVfsgxRP3fHTwzcO5+5+bb0jx1ASCmaBGI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345233}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f54f1efe13e4b175939f2b5f70a3f25eba0213c8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.3.tgz", "fileCount": 16, "integrity": "sha512-kU2wv3n5+69breL4qo9HVySYmQIs2ykxDUHyRe0dc+AT2fyk0zCzbzabQy4QncoizTyNTG5DstOBeBeWOq6Avg==", "signatures": [{"sig": "MEQCIGxCQV/gd2PxTwi4Lvg52QjWzqBT1FoT4ppjzhu1LrGQAiAhyqxzhVzBHcPYEdZvAyO2R3NvMKu8V4T8RNKdoiIMpA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 334536}, "engines": {"node": ">=18"}}, "1.0.0-canary.4": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b1ad95066f0329a0c073759e4998a6a44e850fa2", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.4.tgz", "fileCount": 16, "integrity": "sha512-5IDclz5TytdK9YBb4eQUjSyE5lYNj405JXU91NsyA6curuOtoL4Ejz9RlGLLiI2+K2r5DUFkwhCYYBvbyhsGMw==", "signatures": [{"sig": "MEUCIBu3Yznjp/85ENV7d5/vaK2Xmp20vBad4vlE2Xhjn4KDAiEA3iBKvgg43i6wUNUd/nSg/2vgBoTOU30lyccpxPebvAk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 334814}, "engines": {"node": ">=18"}}, "1.0.0-canary.5": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "810908b45cfcf8efbc2a96ed0d29d13d36456119", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.5.tgz", "fileCount": 16, "integrity": "sha512-3oscHtFLJMKzDYcxaB9M5OgRDVIFOT+c6Cgq9cLYm2zUC3kBheX2jQajP4+LhbMYtgu9axkkqhO3rpdVoC/Nfw==", "signatures": [{"sig": "MEYCIQCnrO45kKp1La4rj3+2iE4hjAbr+EscOI8C71iS0sifVQIhAKdiDmfrHMpopmmJyEocN/UVTTMDP6leCCovEOamqNm/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 333200}, "engines": {"node": ">=18"}}, "0.2.9": {"name": "@ai-sdk/openai-compatible", "version": "0.2.9", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1d23b64ed0f9787c780d913e6373e830c6c20c3f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.9.tgz", "fileCount": 16, "integrity": "sha512-Hy8+dLeQUzZ5ouLRNKEA6Ze1IpIYRetDV9wCsIOHHjh2MsssuUGqvHpB45MUiSFfAiRnBdTHGBNeQM6z8BLtSA==", "signatures": [{"sig": "MEYCIQDpdCe7ZkHvCEtPXtkgGdnb902mSjVwaT/54llG0xQUUwIhAOBUR0lReYCu9FvQLjPI0Jy1qgdn3DTQlnBZnNjDCpQO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345442}, "engines": {"node": ">=18"}}, "1.0.0-canary.6": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0bfe270242e9e89279267312c57cbee84d761d8f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.6.tgz", "fileCount": 16, "integrity": "sha512-hI4SYBbyuar+GkirT/9x0X853jeKaGN1KTUvhFUquHNQlnQkWOpVo93jFZBvXGzrSIOYdIAuHal4Z9VTkWlxLA==", "signatures": [{"sig": "MEQCIDru0bb4uEFHcaQtGYCwZsbN2C4lQL8HSyYw4ShG9pltAiBlnJys1rVOQVKeFwcwnH6VUXdAiCoaiEfsYf+lTVn+YQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 324174}, "engines": {"node": ">=18"}}, "0.2.10": {"name": "@ai-sdk/openai-compatible", "version": "0.2.10", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aea17c86c07fc5ba74ec647f59eb2da4eb6d5066", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.10.tgz", "fileCount": 16, "integrity": "sha512-KG3B3o2Ku7nRwm0YbavAMikhSGCl4kvMP8iHQhTCsrCZ9FipqHoPENk7B06tadUYdxughTyeqF1bucxDSrPH3A==", "signatures": [{"sig": "MEUCIFA+UQm4tE6mr2QORbS41Ugv7fzgtxyO2PvFlwycJVYBAiEAtziz5eB9YdTL6y5E4fv5h0aJcf/fkBf1cI+iloWh1+c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345567}, "engines": {"node": ">=18"}}, "0.2.11": {"name": "@ai-sdk/openai-compatible", "version": "0.2.11", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ab47dd3f7d818acca733bc97750152f22e77fdbd", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.11.tgz", "fileCount": 16, "integrity": "sha512-56U0uNCcFTygA4h6R/uREv8r5sKA3/pGkpIAnMOpRzs5wiARlTYakWW3LZgxg6D4Gpeswo4gwNJczB7nM0K1Qg==", "signatures": [{"sig": "MEUCIDsNleLTPTnKhROfhVT7aC5bcUzFOvAc9UQ+/9hc1yzqAiEAn0CD+XVlgt8q/R22nZxItLkbxRB/nrcmPxTABuQADNg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345679}, "engines": {"node": ">=18"}}, "1.0.0-canary.7": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46e90d12145668758f185fc57c40c68a828208cd", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.7.tgz", "fileCount": 16, "integrity": "sha512-fWTmzYPf1Pu4fyPIAuQBJnlaqumm5Yng/GGdIRa7L2E2yvboUrirOWEnStusvW5hYyDyAx2SOsoari1IDU38HA==", "signatures": [{"sig": "MEYCIQCpwuiGKOFP0scbajUL+d+NQCAwumFVcBDRp+2V445LmwIhAMKVl+rTMnncsDFKfL12zVJfjs/Ofk7xwiIDeDqBMnG5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 339341}, "engines": {"node": ">=18"}}, "1.0.0-canary.8": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f22bc3e589efe343c310bca808302c3c6f8ad8fa", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.8.tgz", "fileCount": 16, "integrity": "sha512-wTrqg07UsHpc33fu2mEcKfon1bb2aJpHeBVpTdv4gMvOa1OXf7d130EprVAK+LyjJVIuXv9KM50XfeWThBUwMQ==", "signatures": [{"sig": "MEQCIGBY4qLe4CRfh/MPtjkN4DaE0FV+pnqeoPPWcZ3fvgKuAiAN3jtms9H6Nye8wId3MnPXf6J9UhvILT0khphOVjNC4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 342104}, "engines": {"node": ">=18"}}, "0.2.12": {"name": "@ai-sdk/openai-compatible", "version": "0.2.12", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6cfabd0750b6e155b35e2014e7607dc7f32c8af3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.12.tgz", "fileCount": 16, "integrity": "sha512-WGk3hTYMOkExgKqBZPWgow/2lp8JLYeJiiO/T/SxBa1NolxNcV+fPaZVvIyqeHGV0/AIXHqNtqI95CFblM47iA==", "signatures": [{"sig": "MEUCIEMCB46Xw6bS74kvSkFueGTCPEv9hEYkceWrCK2CnyV0AiEAyLbIZ9JSrkfEmE/way1mr8dYO68H/sfBU8TtKQr90Q0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 347487}, "engines": {"node": ">=18"}}, "1.0.0-canary.9": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "21a324c1f99c69ab757836a8386a5660191666af", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.9.tgz", "fileCount": 16, "integrity": "sha512-rwBEIZ6geGLsDUh60+ds/fMKcYsVbJUwNiLVyOXsUlVtjv2DWlABo+rQIdDDNVq97Rbu1Rf5WlRRCSPCwjuBCw==", "signatures": [{"sig": "MEUCIQCNCAT4+iRl9EDuZk2AIHD1TKgndjuJQK8xCbizXveDaQIgROAcK8wli1IEYte/uG/4N3/fIoUJdkdclr+MaNlgouI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 341887}, "engines": {"node": ">=18"}}, "1.0.0-canary.10": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2c228782c5d6235375689dd5c4b4f365c21504e0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.10.tgz", "fileCount": 16, "integrity": "sha512-/0PsQtTIADzun56yPp8SdSQ4Q93EkOUyuCzE5Ns8XEcXoACnuxutOOyTCm9gyV2HDcIA4BT0EznCVuc5pRS6Rg==", "signatures": [{"sig": "MEYCIQCG33axN524Qpk+7rdb5hnje8Hb1WkPuiQLZQJzYf2GpgIhAOMc1wAjSShhk3RIKorfFEqMYAHV2V2krl+HBw9pFTJG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 343690}, "engines": {"node": ">=18"}}, "0.2.13": {"name": "@ai-sdk/openai-compatible", "version": "0.2.13", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ca214de7d08434e86d481fb34c6e7346a345af56", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.13.tgz", "fileCount": 16, "integrity": "sha512-tB+lL8Z3j0qDod/mvxwjrPhbLUHp/aQW+NvMoJaqeTtP+Vmv5qR800pncGczxn5WN0pllQm+7aIRDnm69XeSbg==", "signatures": [{"sig": "MEUCIQCp/HWnah3H7kIjo6AFG0cC7h5vO+riebfs6A2aMEJDJQIgbRoJYt8mgtyl772zatbaxa8L+821s2KGKFGhaC+drNg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 348678}, "engines": {"node": ">=18"}}, "1.0.0-canary.11": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0706cf48a49bb4a01562e4da3b829bdee595e785", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.11.tgz", "fileCount": 17, "integrity": "sha512-ED/SucLwWz0s/lKYu7ROHLyHla23Q6K2/Z2mOP/yZX2DAaNWZSfWWuyA3ayT3ViZPIq1N0O9+368/tzwbyarJA==", "signatures": [{"sig": "MEUCIHiYCjdmEgy5iw6yOC9uYLCtWpGPOV0QROcp5nKwaCD1AiEAwi6Lk9/p8f09LgVVtJHl9Acp2r3hAl5BoaLN4w7zW/c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345973}, "engines": {"node": ">=18"}}, "1.0.0-canary.12": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8e95a733b6a6b8dda91913f1c68bb4dc6ea38f64", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.12.tgz", "fileCount": 17, "integrity": "sha512-RZ3DRHsIr9OLOvrD43miwXaqbqBoayUxMwVW3/VpZMWa6s/1tKy3blEyo2Aosa6TMb/uos2BOGGNyM/Fz0EPmQ==", "signatures": [{"sig": "MEYCIQDTyEnbFJ8d5nqiGLwEN64tK+lLyiJCuDWNmHDHeN7jwAIhAMogA/cUAUzKWUQHwp9zVeloRF6M077FS/K22fO/qpR+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344392}, "engines": {"node": ">=18"}}, "1.0.0-canary.13": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7947cf5778f9332ee07d9ce480a73820feb11430", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.13.tgz", "fileCount": 17, "integrity": "sha512-eHhl7vCkp2/jofCOg0ZTxjYGIanxxWM4tZwVRHVNJ94Kk/7CRgj3WR2VvCSQUdgUWM9K+OJc5q/+ef042LmeoQ==", "signatures": [{"sig": "MEUCIBUr0sbrDoT1KXxbGDEBWmp15HqiRI0iS/a0UceQ/EQuAiEAs25005GVVBL7rOE2pKFZD927f4DDg8fewSvDfb9h6Rw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344561}, "engines": {"node": ">=18"}}, "1.0.0-canary.14": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aa73ede55a45514436750a51268b5b10a649c3bf", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.14.tgz", "fileCount": 17, "integrity": "sha512-w+Mb5iK2iln8y21TDzQOGrsVB5J6zZfplOENY12L8EQrJYIejvMFQOoL0ELIMw25IR9d9QDVEPQfK3VF3SRlvA==", "signatures": [{"sig": "MEYCIQCp6h42WWnvyxPmAUmIaTR/Gy7iyFC/YJ3h9dUEWNZf0wIhAMebsyKbybmE1s/Kq6bncHGWkYpf+GVIUators5PIZ3R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344747}, "engines": {"node": ">=18"}}, "1.0.0-canary.15": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "42ee94f563c0fe74b60ceea2acd01342d44af98e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.15.tgz", "fileCount": 17, "integrity": "sha512-IO52fpg1ySY1G5r5/R5bxnN8S/fsX+5ScfUfHSkFKgYNrQQpj0PrOg2cal9AYteKJBB0EF7WZ3EQlASGOqvsuw==", "signatures": [{"sig": "MEYCIQCYoAimHFVXnQRIFdlBOCAe2qyFCtZKpiXNGgZIrFMaAAIhAIXo6VwfHNAD0uFQmKO39heoaYmLMAUmFxsNeop9jG0k", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345485}, "engines": {"node": ">=18"}}, "0.2.14": {"name": "@ai-sdk/openai-compatible", "version": "0.2.14", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f31e3dd1d767f3a44efaef2a0f0b2389d5c2b8a1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-0.2.14.tgz", "fileCount": 16, "integrity": "sha512-icjObfMCHKSIbywijaoLdZ1nSnuRnWgMEMLgwoxPJgxsUHMx0aVORnsLUid4SPtdhHI3X2masrt6iaEQLvOSFw==", "signatures": [{"sig": "MEUCIDc71FH2ChNfc7L73A8qusUEKE28XVCTNvM2Pmqmwvx5AiEAmOvUSJdQ4xwWexEqKM0JZoGr+S5vXU4DQgN3YFh3DSM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 348769}, "engines": {"node": ">=18"}}, "1.0.0-canary.16": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8191a6acdc4f229abdca089ed148e87d392bedc3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.16.tgz", "fileCount": 17, "integrity": "sha512-PZIN40jzXs2eao5aDtsUruFB96/4vnTuLw2B86Ntkbe7J02ioJchycJqfDinWcJ1eaqdMaC0K+RqGYqkG6faPQ==", "signatures": [{"sig": "MEQCICTrMNmISkZiEms//Ng61Z5e5ttNkGsVJ/ygmpx7gVXqAiAXVpEfrLkVkWNBE282dFNwXcPnhNyFlh1sLSnNDBXAQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345601}, "engines": {"node": ">=18"}}, "1.0.0-canary.17": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "66b67f1316238cb571ee50b1e16171a4665e773d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.17.tgz", "fileCount": 17, "integrity": "sha512-Ofp0mShR4+hpyhzOY+Np96Y+li/3xJTjxR2eBIi8fT5oJdDRT8vg5eb3BUWi2XfoZkJyf9yjL1pNEEncsuhmbQ==", "signatures": [{"sig": "MEQCIBqvIblnI7Z0anYWI1Lc7kL4TBFkg3vRgE+6P7OeTezFAiAp43E0cxvh8yKtSJ0n/gM8cB7Sd/nw2NuMQu+3f+/xWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 343939}, "engines": {"node": ">=18"}}, "1.0.0-canary.18": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1892a8793ce919100b6cf74363f7d7ea44f78535", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.18.tgz", "fileCount": 17, "integrity": "sha512-4JCMNoFWSXxuf4ZnuGwKmsMqP1BgzVk/gRB+gXfJPZ3S/2j2tFPEOpFrV+m7IwBPpttmlzK67p01bCKfNuXvDQ==", "signatures": [{"sig": "MEUCICp8mPJ2WPK4IaGwy91Mz7Owm8k9pnEn8tjaDapcGLcOAiEAghJcsJAH6FCj3Gure1ugvHCxkStUPci/ywnNellSeQ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344055}, "engines": {"node": ">=18"}}, "1.0.0-canary.19": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "036bc407499b5f20d64391eec50ce0b20bf90a51", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-canary.19.tgz", "fileCount": 17, "integrity": "sha512-Iv0KXnYlzXZ13iTZkSpeQfoFzgCFpOzKVHy9240IJ3A3JysDgl3Z8IzI2QhzeU3le5zNlmJsUMOr/I/IByNM1Q==", "signatures": [{"sig": "MEYCIQD9Qld3cszPA/0OBhKsvOSeNmiEdmoepfkDtMTWArB5QgIhAN1MAyzlTqwjr/8iTvMSQog1+jIgmxr6swSqjKHRnj3Y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344172}, "engines": {"node": ">=18"}}, "1.0.0-alpha.1": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "8e41020326a2b29a0c9eff2b1231dc854a72c2b5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-alpha.1.tgz", "fileCount": 17, "integrity": "sha512-vwM+Ietp+xDCB2/SHoBVi1PLTGQ+Mg/nOEybQxaL4eCRXl5HNWOzi41w9IV8S7ZTz866yI+NSp8vCbBcUX0hwg==", "signatures": [{"sig": "MEQCICvP0x/8RJlvnlBANoq+SfMEpqU9Pc4HMZV3/Mq0qHZIAiBxgfevbjolEfBPjHOMgyQQsuZ9CZ5Z6nhDR7HPsvqzPw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344313}, "engines": {"node": ">=18"}}, "1.0.0-alpha.2": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "4a80034f9dab2ac259acb00a0410205ad072fa9f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-alpha.2.tgz", "fileCount": 17, "integrity": "sha512-TpmL1Se/fB+i5oJeficwo3BZ+lxjcf1FWN4/DlUNXB682ten1+CBBHeqCkLOmnLVsPxJBLFbz+0BB9PhGmj+Lg==", "signatures": [{"sig": "MEQCIAy5G+Dkq/odRmLoHg8KuLLpnGjq8NlF+uGDHS1QggaJAiAlExN7S+Kbt8h5pa+mk5dPC7PLNYBtj7I9uqSztzuWig==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344460}, "engines": {"node": ">=18"}}, "1.0.0-alpha.3": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "349ce38bd6835745469206efa5c85d6df5723d13", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-alpha.3.tgz", "fileCount": 17, "integrity": "sha512-c/ItvrNTpo9n3KDsiFCfgHJvA70IBTqhpIvfUZFDATFRET40WrhClok7XtG6IkN2iabLSLhBBZ2jLJra80OUPw==", "signatures": [{"sig": "MEUCIHwiq6b05gsV0UIIejRjFjvufN2EUSSxJbrmejFkVV4UAiEArK+lG18ASpDsGXLeRP3zjVS2CkjiT30D4vFoyL3r+Nc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344607}, "engines": {"node": ">=18"}}, "1.0.0-alpha.4": {"name": "@ai-sdk/openai-compatible", "version": "1.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-+2G5IPkTqr74cbJpx+NuMWgZ3yzcF16oTD8FHfdmfp3mnIQj4Bf1QLPOy9G7Bg8xfMYn5vtQCAtO6thnjLbBuw==", "shasum": "69a028321336f74327211e381c46fa3dcb183fcf", "tarball": "https://registry.npmjs.org/@ai-sdk/openai-compatible/-/openai-compatible-1.0.0-alpha.4.tgz", "fileCount": 17, "unpackedSize": 344754, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGuNbECl7nfcUZvLMw7GqJiUH/6nggcsPfHq8Yyp5RLQAiBt0so1QWLJCaZ3YIo4FbVmxUph0rum6e5vq5mw3rCgqw=="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:02.252Z", "cachedAt": 1748373704300}