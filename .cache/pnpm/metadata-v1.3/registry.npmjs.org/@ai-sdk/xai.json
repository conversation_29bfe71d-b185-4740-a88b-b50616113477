{"name": "@ai-sdk/xai", "dist-tags": {"latest": "1.2.16", "canary": "2.0.0-canary.19", "alpha": "2.0.0-alpha.4"}, "versions": {"1.0.0-canary.1": {"name": "@ai-sdk/xai", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "21905be9d99fb438494ec8d5411a61ec48fb2cf8", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-P8sv4IIXTJ/yJb7tG5VhcwtVBztIx5wyO6lmpKIyqyvU4ZmAigWvlN3Rio1tgGN2hYX5fQhmHbvh9PN2O6XhyQ==", "signatures": [{"sig": "MEQCIDQRdhx0R9l/oIcoqwCEUMXrXgnBkkkKmzyb1GvYz15QAiALMVePwRR0bA8BDUtrPKXEawlpwVrixB5XocUx8GLo5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123671}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/xai", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "765d9424cce2d3b9d186c95f116e7951f197916d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-cLyheRE5gn6wUVZ9ejUFDafvYhHT6bqKPzSCkKa2v7kQDps4EFNtrqLSTjyb1sW+wRqVzas9cdUGS7ozqV4GXg==", "signatures": [{"sig": "MEQCIDDPM0zNhuKaPvYyET3hjflAY4+LT4QJTOejOtIuwJEvAiA3fjSP3XJ/eN7x+2x01mQcj5uG4S4helwIrBewsAsTnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124020}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/xai", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "429e1d45bb96020233592cfb7c253f67207accaf", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-kshNRnVX2j9SzLAilP0HqUqCvEWMpft2JFNW2pO1Ncf/w9sK6Nst2LnB9/TUfnaEMd9OTybROJqWzsm4+NGJ7g==", "signatures": [{"sig": "MEYCIQDZp/zN+iHvKLNkfhEnSdcTd2CPGvxmIIz7u0EKiVPVFQIhAI+weOUItM/p4b6PBcaPSTrjQRC+lfNJwZgJS/+2GIAp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125544}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/xai", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f6d4a931b5f75823b67134641c7180d354cff66e", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-Dv9z24XavnJUJmyDrr+c0KUnxr12butxxwHW/Z46BNC+LkzlgyMktuGqvPjB39yvlKGsLLRBSmPis17ptpu4nw==", "signatures": [{"sig": "MEYCIQCiE3OyMuhGBX6n0t3IH4owom3fpPtCdKPP8+TBda1vIgIhAPGJzP3wyE0elmSlCZ0DeUXs79cZjhABkTqTdV3APwCg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125640}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/xai", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0da5c0b8a9bae864a388450ee00aed1e0cb42fbe", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-Z3ovBU21Wp87EPwkLoP0K4SNkyIzwQk+YAFuBPnRLCSVtBESeMarcI5zDVvBJ0lmQalRX1ZBAs8U1FvQ4T9mqw==", "signatures": [{"sig": "MEUCIQDjdpYkVK074dlNzN98zOa0K03+QrX6mfJC/53MzbL8DQIgNKVXXMv+kPf25xD6oRL1peHT+EqNOoVArtN70QPz9+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125531}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/xai", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ba2abdb78d13cc046aa023899245b853f919f7e3", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.4.tgz", "fileCount": 10, "integrity": "sha512-Xp/J8v0SGuJRdo00cYISNoTw0gOZwYMexJvH+AeM0vR/SDxDqNiNhMVFMpCYqbkfSHaO7hZDm4U7waHEphswzA==", "signatures": [{"sig": "MEYCIQChfBw778yNE0ZQJofR+gKEyM6VpazBEtbuML3eLvmxZwIhAMg7kK6xstu8GTT9t9DTt7SyeGJEPyDz/hPpcOF7jhJk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125617}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/xai", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f8dc06649f9a145df35ec5bed5f09b335ca45c0d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.5.tgz", "fileCount": 10, "integrity": "sha512-XNl2K5Oeme4/CgvTRAHWOWfYRk4ZVaMsabUHdI3h/MY9+8n/2pKxWw5HGk8rkj1i5uUuEksPT44txbdSvkfUWQ==", "signatures": [{"sig": "MEUCIFVVU0FzMa+fKiHjYZ/t9WnnIUFUJr0d/WYd65yycLPDAiEAruV2C1xXQacHbVZBPhSohKeJe6lxobGXY4UcmquTqD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125713}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/xai", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "80c1f3779cd8bd77f4a727b69d523b76f4dacb97", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.6.tgz", "fileCount": 10, "integrity": "sha512-bNEAJMSyjMNJIUx9bEr0o3PvP+s4tTU+GzuzG9OVhNc8Zx28kHGXogl4SjDkOgCKHOLixE9RIKuVojZFOACdww==", "signatures": [{"sig": "MEQCIH9Dv+qbIBfZFUsDoE2+faupAoAtERm9KXKrB/aodlhIAiAL0okFr8nsjDL/22RUCNEcZIoczWZdoig6a53K5V6t1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125836}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/xai", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4", "@ai-sdk/openai-compatible": "0.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b31650503d7e396d357b3cebcffa97b499321ebd", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.7.tgz", "fileCount": 10, "integrity": "sha512-ap8lM02hH+RwxwSG8meuW3SSUYTlEEH4H/fJD7KTEQVHIemwGw7UZutCtnVkii3zjm9BdOS+G41A5AfVT5/3WQ==", "signatures": [{"sig": "MEUCIC1/ct2Pr+9N6eUJlRa0tyk0OeYy/+yndVi+2h7ngy80AiEAtQILkxTc9HDoDKTzH65wH+9pF0MrQ0kqbvmaNWGQE90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20389}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/xai", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4", "@ai-sdk/openai-compatible": "0.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a13f81a337af80af432306d01ab367f0ea97fdb3", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.8.tgz", "fileCount": 10, "integrity": "sha512-it6X4MWopWGqZMy+g+VRFR/d/AlPjyM5K6CXKupKuTNjBZhTrTehOXIvWFzFWRwGzJdZSqWbotnm7Vcs51gvMw==", "signatures": [{"sig": "MEUCIQCmHKIXWmrsctiapnWLkTZdAIqbcf0Hvby7xFu4S/oSrgIgcMHCUMbg/72OjRIrvjWHX1Qg0mK9We/4drceDwut2Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20487}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/xai", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4", "@ai-sdk/openai-compatible": "0.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "61d06d8bd0a42d8d18d1525e50f4a77e21bb84f7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.9.tgz", "fileCount": 10, "integrity": "sha512-YjbHBW8t22R8ya8cEOqlkK2csSqNGTyZahV9AnKLA+squG/FMjzBPPW3OMLWedp4M3plF3uoLtvuzfkfrm2wQw==", "signatures": [{"sig": "MEUCIDyk/87h4V5AVckmdx3/WXgFb5Lu0vQXOzECVZXolcgGAiEAlwKyCKkqAsa8NGP3ZsX+ZGeeOT1pgH5ISD4DmlInNik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20586}, "engines": {"node": ">=18"}}, "1.0.10": {"name": "@ai-sdk/xai", "version": "1.0.10", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4", "@ai-sdk/openai-compatible": "0.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8ca76d2137820e8a2517681aaaa7fe78dd09e073", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.10.tgz", "fileCount": 10, "integrity": "sha512-mDxizZhhCgaFaCHc6rf0vfXucdPC0h6EgWwJTNNz4a6cvJrExa3A9AaCFrYrrfDptMpe0CDEtRT5wTOHgbdDag==", "signatures": [{"sig": "MEUCIQDDij/eMEwIpW6FNCcxbSCxl0b/L/2LMK0RVFgXyaBL6QIgRnXNUf143K0JIRmbRCfOmbNN9vcv5t+ry9Rj6+fZzX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20687}, "engines": {"node": ">=18"}}, "1.0.11": {"name": "@ai-sdk/xai", "version": "1.0.11", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4", "@ai-sdk/openai-compatible": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6b447acd1c14f7df88dfd6830a0aae2742dc142f", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.11.tgz", "fileCount": 10, "integrity": "sha512-Cl+Z/qZ6SnwY067oPYnt8hHq9PSwBCF7dfsO6iporThPalogFxOGBRar58W8fLTsIJAnBB9P/vAG2uYrWYfeQA==", "signatures": [{"sig": "MEQCIB7NiAe709J3MvnTAVRDp7/nASYQFfKbHScCV1/npelpAiBrL04g9lyNQgv90VZWKFaVwmT20N3hXkwA9XN1M7DBXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20789}, "engines": {"node": ">=18"}}, "1.0.12": {"name": "@ai-sdk/xai", "version": "1.0.12", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5", "@ai-sdk/openai-compatible": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "23d4280e5933ed4f6c07e0904a9fd13dcffec349", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.12.tgz", "fileCount": 10, "integrity": "sha512-hGjmSH3WZ0i2JU+0QDfSF4a4M7xBG5FGvspxawW+Fi2K2/hV1PVxX8+fggp2+XykOMdSBSi31hk80rcK8Fd/aQ==", "signatures": [{"sig": "MEQCIEgatzKuQwy/WfE2W/OJKpPWlnhHnV/hxxdxoDlnANMQAiAaFMFXv6E6FXdbh67H8ef3dTIvKsiYNW8RKPBpmYW/zQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21055}, "engines": {"node": ">=18"}}, "1.0.13": {"name": "@ai-sdk/xai", "version": "1.0.13", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5", "@ai-sdk/openai-compatible": "0.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7b9f23bbe0546a2b7035fcb0b4a81d3c4d024621", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.13.tgz", "fileCount": 10, "integrity": "sha512-l8WEcShmkrU05rQXjBJpX6osCNxLVyDbtEcLB28kwILL4P2Hpn6z+X5mjPjvbacwG7+pGpV/ppsdo9rp0aPcBQ==", "signatures": [{"sig": "MEUCIFVG/cBlVxRhOUliNO80qYDPPKMIJMRWaO92Eey8AGfYAiEAhZGbV2NBSVVHCs2U63eDzjbodRleEa+E7n4g1s6/YrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21156}, "engines": {"node": ">=18"}}, "1.0.14": {"name": "@ai-sdk/xai", "version": "1.0.14", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5", "@ai-sdk/openai-compatible": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "db7546537cb0ebf9ba072394763fed023e9fd96c", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.14.tgz", "fileCount": 10, "integrity": "sha512-mZnbiDZjNNT2kzgeZDv7s36ZfqFbRLjZhX9nG4ky4btEsCW4KHauz4hR+V2MCOXEosfrwzXMLzjlIPQY2WnCIw==", "signatures": [{"sig": "MEUCIQD/Ri/ZIXVkbd/aGA4bO+UQaTWe1OpFrdFwXHxeEYrHNAIgbFAY2UEz1uembjhQmOseFJ38Wn3GBJ8kEVa5cgh/nC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21257}, "engines": {"node": ">=18"}}, "1.0.15": {"name": "@ai-sdk/xai", "version": "1.0.15", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6", "@ai-sdk/openai-compatible": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e9496d3a0328d33c4ac5c9d4222024380d0281a7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.15.tgz", "fileCount": 10, "integrity": "sha512-CIfOksEzWYKZC7blfFPRjwtxtCbY97SeSTfLwWvkeechrzwnOm1wkB0nR963Kr0wMfy2XQwX+ycjF/FfLz0y4Q==", "signatures": [{"sig": "MEUCIEF54InV6AuWsGU8he+M8CsuuQOOuD0Y0/jj8EnFNTvNAiEA6FaN6y5gnNCG6PzMS8zH/vNmR0V7NXTWVzRGwKEg3+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21517}, "engines": {"node": ">=18"}}, "1.0.16": {"name": "@ai-sdk/xai", "version": "1.0.16", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7", "@ai-sdk/openai-compatible": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d33ff0a8cafb46ca82564f4a439cb70faba7eb62", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.16.tgz", "fileCount": 10, "integrity": "sha512-Nmi9uoLe0pnr0xcExwKkGwOtDwwBPhyizJxhdnQ2gXmZ/xUq3g52Awwpa75xLOSts/UZKTmXljeSUpu9SHWZ+g==", "signatures": [{"sig": "MEUCIQDS+7N4SH4wmpH6I6AYuQfPCKG7EBTLDxL3+5Tq2vJO4QIgWx6OkACEA6mdofxxN/mll2bVL+Ca7OtdMfVArLENjdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21717}, "engines": {"node": ">=18"}}, "1.0.17": {"name": "@ai-sdk/xai", "version": "1.0.17", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7", "@ai-sdk/openai-compatible": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9691c34050f1f8ba11b787b7f541603c9ae6026d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.17.tgz", "fileCount": 10, "integrity": "sha512-5mCzqbvwmaQTvq+TpgNZRrIs7OKOrYKvZoVzgipHMYUkTaHQhoE3lmHkSlE+TAyvOviovT6MDJ7c+AOm/BUS6w==", "signatures": [{"sig": "MEQCIEAeP14/Cd5aRNomXwg5xmteEWmaNrpyFw/FI5H/uXUcAiBZxJRI9ZuKwFxd8hob4YuPwCkkpsCQyXFM5SKpqVGeww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24195}, "engines": {"node": ">=18"}}, "1.0.18": {"name": "@ai-sdk/xai", "version": "1.0.18", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7", "@ai-sdk/openai-compatible": "0.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a587c3715da18373609aa0b373b035ab32196fa8", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.18.tgz", "fileCount": 10, "integrity": "sha512-0xMYKx29Bt+MIUqRYIVa2e/CAMOt+gaq8njYlSVNF8yq/weAjm+1Fe3E9Y0BHJQV0Sc4XwASm+Duuw0FjQFCmQ==", "signatures": [{"sig": "MEQCIC05oJUO9ORTmLL2dHK/IOsZupuf9Y/XNfezHrrvQHpNAiAJnjUB21uBr5mGGfAEJYQyq18kn21ZxEkgsIF8pNIzVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24296}, "engines": {"node": ">=18"}}, "1.0.19": {"name": "@ai-sdk/xai", "version": "1.0.19", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8", "@ai-sdk/openai-compatible": "0.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3af9953ce518829849498c474dbda31e8da7219a", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.0.19.tgz", "fileCount": 10, "integrity": "sha512-dC2fe2RIRJ9YVQTkA2LZC/SMctqgjIRNS39/2iM0xHaYbwzT7b74Vw9NksE8BluSQme0B+iNsmAs8fNQ5uMSUw==", "signatures": [{"sig": "MEUCIQDWiZZZR7tJdNQZS6s9RoaFOWjr/c4R/f03MuHdTky4HAIgReT8PZuy/e9WC1AUlu1v3fX98/8nohuwd3LXQCXusWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24430}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/xai", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0", "@ai-sdk/openai-compatible": "0.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7c9a84dee643c5cb7ae5e3aa28cbf4bc8b77fed9", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-Cl/0G8b/JG6AKVUL6KCWS2ePWH/S3zq0u22v1Q+JmjvBClT5zI3CLR5cEPAnHwn8g/k2BTKQePfKoi7921bwIA==", "signatures": [{"sig": "MEQCIB+KGvl6SPldwPM7J+CrHGwGrQdvgwfVnwpzSiPrSlRbAiAxzoGBGie2pn3US1Su/ehu01zUbgv9wiu1SoIoLL7WCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24611}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/xai", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1", "@ai-sdk/openai-compatible": "0.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e03cef413e29281e0d248c04d971b581aaee3db7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-HDBE6znm7V3bvhX3M8BftHc4I/RDhspZTzai5UL8wAw1b6Vrf672vSv7g+TOY2/E8hw03IN1MKlql58M2A5ujA==", "signatures": [{"sig": "MEUCIGNg3EZCrO1acQRH9aywdfy3Dkq14RFZ0dqJezA+UKs1AiEAktAEZxRC34MqYT0Hsw2cH0wE8PcZMdWqQ8k4UixLamY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24803}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/xai", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2", "@ai-sdk/openai-compatible": "0.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9045140ac0cae206d27f6db55e2c728514b496ec", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.2.tgz", "fileCount": 10, "integrity": "sha512-h7HD0IpGdpJFVKYMvNskRkPJu2LnnW8RvaJqxKTB7f1Zr50UkMQrzAwj7D6OIrI3x2yhP1+1zCm5mgCfcmS5KA==", "signatures": [{"sig": "MEUCIG1Y0qGVRNkWj0Q6z3g/GmDjqRNY1BCoZXhuIDgIPgzlAiEAxESYBPCgxO2ydDX0bEZ6nxJBh/wU2pdRHc8LI3DDm8c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24995}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/xai", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2", "@ai-sdk/openai-compatible": "0.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fce9c4a965c3de1502742edd6152ac01dad8ab83", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.3.tgz", "fileCount": 10, "integrity": "sha512-BUAGc5MmLrhMZnHuS0v7QYA+cG4bT1wvOvKTwCn/RGCcPEep1Ym9f0u08OXMnNUieZuayQRivH7xq151rnxmTg==", "signatures": [{"sig": "MEYCIQCBSzVcU6LylSuHXHXAKYIEHkT5muLpGUcP4QC/pEy79QIhALfGKi7oNI1D9L0E2J51+OqkO2fvKUDAMr9zqy/DjLS3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25094}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/xai", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3", "@ai-sdk/openai-compatible": "0.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "578623f7039b6aff65b8158b9cd98e82b132c522", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.4.tgz", "fileCount": 10, "integrity": "sha512-XYN2GKHoE+3bcXdMkSRoz7WyRN6V5haIAiamfSdqkXd2Jym2CB/7rA1hf4J1ryo/GisES3mQFMSN2gb5nf0OsA==", "signatures": [{"sig": "MEQCIAJmmJitb2SqtbthtQpj9d9t+629uV7Q7tFPmJyttfc3AiB5CroC5d3ACOgCr/SvFCTK8Oj4lfL1FYAy6K8cI8jPcg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25226}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/xai", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4", "@ai-sdk/openai-compatible": "0.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7cb5dd2d0403909fc62faac3b773203b42a51ac1", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.5.tgz", "fileCount": 10, "integrity": "sha512-AIxkrrfcIQmDJnP4Xq4fHHQDvPvd9sWlIxd/U3KybnOL5wy7d0X2ejhgnSfRjRWoVt+Ixd3+yNGnZR0hojaKIw==", "signatures": [{"sig": "MEQCIDxS6VrBWtgtnxCOn8fFOxgJvPe0XJHeZm1M/6CVQgOWAiAvGsSttwbg3BbLSazrQLiFo7TV73/53wHRtO1f7LCpJQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25358}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/xai", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5", "@ai-sdk/openai-compatible": "0.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "347b892ed91c75cdf38de6032b8361347187398e", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.6.tgz", "fileCount": 10, "integrity": "sha512-KP60K0mIgsRc6h9CNzm9LI4AGinkFpNOFBcIUqDmCKV4oAIbF7uUBDnOzyfDCrXVY5FrZGEFRhiAfe5S+ka/Ag==", "signatures": [{"sig": "MEUCIC45XtsMF9flFhcTedlafx9d9xJYxOWybbsosIN7OVHrAiEAsx0SnSR/mBFp2zOaxrNviDCjyEVpsGiNoL+iOB7+rAw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25490}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/xai", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5", "@ai-sdk/openai-compatible": "0.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "43605f2fe88b0b4e698f3b54f24d1480ecf823ac", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.7.tgz", "fileCount": 10, "integrity": "sha512-XMeiZ6XgeRI68WRATcDM+90T1jpaAWeqINR02NU9xksKzaAPBdAUFdR4x4ICWpXTEid/t9X29GI2aJgbwZmb8g==", "signatures": [{"sig": "MEUCIFEvoZIbvnyEKhCdGqz39lOBsBapTmgPaBBWL7LHkXuKAiEAtBdRn2xWaC2BghaqYmPUhYRVrDlOY8VIToRKvNo1pgU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25589}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/xai", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6", "@ai-sdk/openai-compatible": "0.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7c4d2bd28ac5aab5615ff850deee4e9389b4d4f5", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.8.tgz", "fileCount": 10, "integrity": "sha512-2ezkPMBSfqZySYbXZDa1dCq3c1RV5ZXcwkWIB18qpmwGaRCxdjzBIyZHPOH2FeOLQplZnGC0VfxZcOAbvWhV0Q==", "signatures": [{"sig": "MEQCIDYM+lJ+rpCqBO5wNkef8XottdaiaKXPzQ37sURMjyhQAiBSVWmigmgDu/m8FeO3X/SLXTmicwC3cW9+ZoOjJjCwDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25748}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/xai", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7", "@ai-sdk/openai-compatible": "0.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "38a80bd73060ab28aecd7a507c7b614ed944b89f", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.9.tgz", "fileCount": 10, "integrity": "sha512-IqnMMCY4t8ZQcGuXnGx3oL4bm9D8Vrqoo/3R1oAVk0ivd79w0tcw9+hZ9f+VkKO5vmlZiRKloyp3b3HB/wF8+w==", "signatures": [{"sig": "MEQCIFIsECwR6iQAFTvxNKHsCNgHLWKv5XV1jVpl7k2cT7bsAiAChbq0hZ/RvC1W8oSgaFg8Op0o+p5AfDUofWVJZyhhuQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25850}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/xai", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8", "@ai-sdk/openai-compatible": "0.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5fb23cb73af49adc0af917c0a293aab23dc9a436", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.10.tgz", "fileCount": 10, "integrity": "sha512-rB19K0HeuOYh19eNzJrYlIa0sf/nrs7BA854qVMegcuUutAMvHWwLIJrEn2uUBieIdwTOLnTMPcViZq3W51I/Q==", "signatures": [{"sig": "MEUCIQDdMMqeJ84dY2cr4LkR2poDeoEsY52k0jn57fFbmPg/ZQIgLxwElJYU7410ZxeC7WN28HE7zHVfHFW1AYR1oBfWtTg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 25986}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/xai", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9", "@ai-sdk/openai-compatible": "0.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "08a40d16f6c2f810cac458b6c85a4ee50b3c0acd", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.11.tgz", "fileCount": 10, "integrity": "sha512-eag7OmwoDXvqXX9p/WQIYHrmX3Q3ZdTRWX01BiMCY5qri/+/FK/2CkGuxxMI3PHy2bqvWaK85A02j3AALafVIw==", "signatures": [{"sig": "MEYCIQCS3X4X00lB3KK6DG9cop8rHX0hLzHuH4zbn4gzbrFtHwIhALqnm8dzkIPOYksjcGEHoGFXYkp+ayL3r/sZuDVW0emn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26147}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/xai", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10", "@ai-sdk/openai-compatible": "0.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d14f52c1e3528a15fb3812b73db6a27b55a0f7c0", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.12.tgz", "fileCount": 10, "integrity": "sha512-e60KtMDOR7vGV7hPpsar4jY6sw6sUSI6zpCVDQEkVv6B0MUzD1s5DQnCvo6+hnqVjZJHgktIFvc5QwnpVZkXPw==", "signatures": [{"sig": "MEYCIQCrUVW4zBbzBA8ZcECnR6aox+pHYNHqHn/qwyln8zHe+gIhAJkjb+Miqyg5mzLCCDol/IazNdcxsAAzTmNfc6uCj4+0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26310}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/xai", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11", "@ai-sdk/openai-compatible": "0.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8869fe9f4fd94425ddcee17c15b8dde5e9ff027b", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.13.tgz", "fileCount": 10, "integrity": "sha512-3v4ATVLrmSMbah9FJpHyWRKOLBAyQR9Hm7SNav5E6hmCJquwYgJy6EMGCtkjRtcemA/qBHpAmgNcqlb2uBLFBQ==", "signatures": [{"sig": "MEQCIF4cL4U/MD+eBAm1SrlJ0cXG9ds0WIVQ3Lc5qEoDZ5DNAiBOmoxRBVBjkdrZ9Y4K1RCHwysmEcPdTqaMttsr9mc91w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26474}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/xai", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12", "@ai-sdk/openai-compatible": "0.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "abf203a5954d894757d4435ef936b1d263a1a510", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.14.tgz", "fileCount": 10, "integrity": "sha512-6ltD+Sd3VeMaIqGfFImei/Y9VT8cUU1mq3FNuuTdv18bms6/W9Q/MHB1YJPaqzVjORqV2EChfAa4GZBG1gwEpw==", "signatures": [{"sig": "MEUCIQClNTEWFlp3g8TeTszK+ppIa+IuZ78ehmsCnRQ/BzTQLQIgGKaXgu1MGP+dRna23suF1Voishp6vNwyvtf7HN6gJ14=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26609}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/xai", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13", "@ai-sdk/openai-compatible": "0.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5adf631ddf27d7c7dc4714c2e15b14691c08ade0", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.15.tgz", "fileCount": 10, "integrity": "sha512-Uuc6NC4xsNF5KyYivf/9egWEBNNZ0s3nnQ2klWLj0k9gu5h9fuGAto5thhp2Rd50cPMWzQVQ7R5zvYHmfiunWA==", "signatures": [{"sig": "MEUCIQDtWtgjhIrJrbk7OfMc3JE55rnx+xsr8npy68zb54Xt6gIgXTMD9iBlF+dFZ612JhJNbuMxmNIdg6Mx+uYqmBe9cjg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26772}, "engines": {"node": ">=18"}}, "1.1.16": {"name": "@ai-sdk/xai", "version": "1.1.16", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14", "@ai-sdk/openai-compatible": "0.1.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d663c90d740365f58f53a7d394c26406e856050f", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.16.tgz", "fileCount": 10, "integrity": "sha512-pOy/M7H10et02A3i07hcjH0aFoHjp8X+mt/b9+sM2PScVjQXkV4sCzVnhucZIqLZ8Z/TDFNepuVeATpn/Mrbqw==", "signatures": [{"sig": "MEYCIQCJE/mxnq7uGJuMAmxeYLw1xbcJRQVvZSFEtsejG4JbIQIhAMKY3cU/Dn8f1me+9/FzbolymlKpZUR0+lrlrvpqcUeD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26935}, "engines": {"node": ">=18"}}, "1.1.17": {"name": "@ai-sdk/xai", "version": "1.1.17", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15", "@ai-sdk/openai-compatible": "0.1.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ba369694aaade8285ac641f94e9dee1e9d8fe90d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.17.tgz", "fileCount": 10, "integrity": "sha512-UIMlmibU6Xjpes5+GrE2teP52bHaH+ImkIyCD0S1zscVHz5zKllJ34SS75kZ6NhI7ogDW66IqJlhUhakeh9dMg==", "signatures": [{"sig": "MEQCIAlYEAPpuChhpSVxGmDTyf7JiNCw44OSiyvgmryvRfGFAiAxaldebEDYolO7R69gyHvqcitH9rhX+8hVGqxg+h+49w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27070}, "engines": {"node": ">=18"}}, "1.1.18": {"name": "@ai-sdk/xai", "version": "1.1.18", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15", "@ai-sdk/openai-compatible": "0.1.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cbeb51a896412d5970d92465a76599a1cd3e6eb9", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.1.18.tgz", "fileCount": 10, "integrity": "sha512-iXrf+8oJd4sk06EXtvJdC40fnrXpJ4SNe643/KVKbl//MPiz4ePReiZxBOzVACa/tCyJscKEPSuFhab25zT90w==", "signatures": [{"sig": "MEUCIG4MkM5M3BDOsXkzVeAxygl0C+4/wWkI3dd3E+rR0p/pAiEAgl534XZg5KGXgENGLjbu9UthBzUvZuQ9kn+4R++v/Gk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45714}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/xai", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0", "@ai-sdk/openai-compatible": "0.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ad2e8ff0ca523091e16e892c7cc6454d42349557", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.0.tgz", "fileCount": 10, "integrity": "sha512-ZxKbHl2CIDAHGO8Xvd09mOvb73C+hlHyDFOaAj+1qFqZN782eKPSRI9dP32zjfOMPYckUHXvwTUyWyN5oz1U7Q==", "signatures": [{"sig": "MEYCIQDL5bg3pb/AAekfE8RG5UDjOBCO8aecn+m1cbH+OSSDlwIhAOSb0WCoWSm2FXWAss0AWD0HqhFoS14F3k2uyLyueXat", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 45911}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/xai", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0", "@ai-sdk/openai-compatible": "0.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "57441055b1b95d0d7a3488b83ff5ed6167bd1cc7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.1.tgz", "fileCount": 10, "integrity": "sha512-aVrucjm9ikb9blpkO0oo8nVPRRoA1SD+GvfRU69YgjbM3XVNK07nk/mEpq7xuEU2iD+7/T2Fkl2rjayS+Ee4Jg==", "signatures": [{"sig": "MEUCIQDcFK8pO02TZKdMGAIxASYHgPSyvyb7hGvWhHZkAs0WRQIgCyZt38Ei4E85Hqh9CJ0x2TS/iXASYZVDyR4AUMwh3Ns=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46396}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/xai", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1", "@ai-sdk/openai-compatible": "0.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4f2aacfedd1ddf32fd9bc7f04206c444f5b1518b", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.2.tgz", "fileCount": 10, "integrity": "sha512-VGG2DzY12hLyq5RjCWR/WjbMLd4RApD2u2z4oy474ivNlA8mia98qs75cgEJQvf1w4hVPtWdOXGi2bk+9K7KnA==", "signatures": [{"sig": "MEUCICVQQh/Cu65cfdactDEFr768Vo1TAAzwor7vXFVkDtX4AiEAz3uT/RtuIQBjLH5hzCC7mfHzlXbTaMUs/OlWkxqKI5I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 46528}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/xai", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1", "@ai-sdk/openai-compatible": "0.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "702583c600cd9bf45f2b09943db7528f37a118e4", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.3.tgz", "fileCount": 10, "integrity": "sha512-rlDQcMKwmjPQqVexjyJvIOQ1yIKbBEo4f0MFlhKwidMxfc6ZvZBKs0hFo8F/OgotK5+nr0G2MnpbqPid2weK5w==", "signatures": [{"sig": "MEUCIADRxBkbMb1KGg1gVAekGkwcwPxW98hgP0yq4PDx1eWAAiEA6AEbrczjzmXmEMfe0/TiwEVmf7v9fqGa3iOeJQ7fAX4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32210}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/xai", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2", "@ai-sdk/openai-compatible": "0.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "236ec078d83971f25b04e0efb956692474ad365b", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.4.tgz", "fileCount": 10, "integrity": "sha512-mWb1J1eXSUcwGatZFANG0Mfqv3xSlzM1HICsTE1QOnXeH6TnysQjX/3zIgiUu/UjANrxMLCINC2LRDOCDaY4Mg==", "signatures": [{"sig": "MEYCIQCaiglMqEpC7Uh061ARfliiu7zxxqHrqwRz837vt2/11gIhAOJUCuX6OVE5LXUV+mAJcw1y27Y/QfHl2cM3wo1VaAzf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32342}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/xai", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3", "@ai-sdk/openai-compatible": "0.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "78b804130a97be09265e91a4405f7eb5bff2c4c9", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.5.tgz", "fileCount": 10, "integrity": "sha512-7EntcfF26u6zgayIGqNJl2eSMf5Q7uLO88Fb4l94f4WSzsiEni9E7mdRpAzoSfNJtF7GrQzLS8zDdobGY6MTlw==", "signatures": [{"sig": "MEUCIEm/MdR62/2QVms/wnobDZ90eLpcMlIzp5Tj04x9K5cSAiEAoGxrtVoS8UpVZ07n237dCR4/VVLRNo+Ao/uj8K4PFRc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32474}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/xai", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3", "@ai-sdk/openai-compatible": "0.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f73b9427c89df0f2af67fae3a1cb7ed61c6fc04", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.6.tgz", "fileCount": 10, "integrity": "sha512-I5D1uH8kCAx+SDsYdkNQETWMOLMoNGE4BUSqV2qzxnc3+/0RHa0W5pkhyGAgArs+3GDWA3UyRO5OFFdTTCNeVg==", "signatures": [{"sig": "MEYCIQCBrQVLCRL9KlBz3Yb9IKO0v6etWLYVuNU8i+DPxK+31gIhAMwjNpMsAr+9s4QLdPt5g5qLgxIgXU9opCzoLwy9KKBH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32573}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0", "@ai-sdk/openai-compatible": "1.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "22cd707b3603f9b20a387efe4800e41454bd6c49", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.0.tgz", "fileCount": 10, "integrity": "sha512-tc2FQBOB2FWbgBWDZ/3VDQHY726mZMQ4GWgICrl/mc0VIAp0kgg43DUpFzh5OlpKc0EPMgi/AnvBMDnYQfCsWw==", "signatures": [{"sig": "MEUCIDH4gKlHmx8e/zLaj8UVBuWyf+r42+/y4k0HLNAAC2cBAiEAuYaHEPytEahm46A1+ZE1ZE5grWHEWGMJYK/8v7tIXyA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32844}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/xai", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4", "@ai-sdk/openai-compatible": "0.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "adf4cc80a35ec0dd82f51c46baeb17806a89a0bb", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.7.tgz", "fileCount": 10, "integrity": "sha512-FoOX3JwcRCbFHqxtZ+5K1hmhZtWSFptQ6gl/uGhlfnF7Na9g6bN6yQljMki/pfU/0gDT976xgmjp7NUb/ItNgw==", "signatures": [{"sig": "MEQCIGzQlODU2eGU1Wava55mPw9IX04ESWs7O1sfVcklYCE1AiASsfUyupPhR6ALdfJGYbFybBko8L4Cs9EQ9Qf4/8xBxA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32705}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1", "@ai-sdk/openai-compatible": "1.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b1b1824265babe4ef141828cb673248a9b68f29a", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-X4MFXxCHrlrRSeF8ZRip8ij1uhu2XfRZCjzIsfCn5R9z01B0n4VNZqLiPd3kQWdCeHPn2crElPFy2gtgeg3MZg==", "signatures": [{"sig": "MEYCIQCgaArqhIHS2uQZ/oD14QXKhvt6wXhXa9nc+ra6+F4L3gIhANSn3Ws6FVUisK/A94aJd2Pvk07PqKlBYXXM7Zr+LC70", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33069}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2", "@ai-sdk/openai-compatible": "1.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1f82bd6f56563e8df50ce2d789be77ba43181559", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.2.tgz", "fileCount": 10, "integrity": "sha512-3fK3NiZf3eGuknLQmGx/pAwWlIqv4HLQxwSfVz89qbf6tdywxjmnl2yR8V7fYRjN6iwkGy/KJ4SYrxhb0iesXA==", "signatures": [{"sig": "MEUCIDnysY9agtgfmJPAn46RO9ujlWyg7srLw+r5uSg71JsAAiEAt7U0GxuGST10ZPGRvROQAr3KjTDTz4iVGT22w3nafb8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33297}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/xai", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5", "@ai-sdk/openai-compatible": "0.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1e3aa3e506a17c9b4ccec258fb9df311b5ff9e8f", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.8.tgz", "fileCount": 10, "integrity": "sha512-tQVUMih2XnRnhd9s7QiTTJAUJnt4I9cTKWiqxoUwNBYBDXCgEFQPKd3DuF84IUcOX/EEPe7fBBrq1tnoJXwbNA==", "signatures": [{"sig": "MEUCIQDOgaCoibNGj61s73ebOKAN6Je1iV+ROdqFzOcoTY3D/QIgMRBS3LCDo8x74+u0nnMqdYDxBZ6P0BxViwtKaxIwpJw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 32864}, "engines": {"node": ">=18"}}, "1.2.9": {"name": "@ai-sdk/xai", "version": "1.2.9", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6", "@ai-sdk/openai-compatible": "0.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "78fdb6ff80703cfb2dedfcf54579fc3f9cd7f87d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.9.tgz", "fileCount": 10, "integrity": "sha512-65uzZw0vFC+RobsPw4AfAf6wkVfDj7n4Dqk8Fz97ciiENylTxtvPhs+1vTF2ip+kEpSwcMeWLJbA33EQ2OgMGw==", "signatures": [{"sig": "MEUCIQCl4L3KCkN1Z6zChzpVbMpsTg9LJhlgApOSk6SSuaRdxQIgNtqpYeiSiZKlwSozKp2dL99lQY+mE7pX6Jv6xY/7evE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33023}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3", "@ai-sdk/openai-compatible": "1.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "44ce83cb0543d9b2e93d68794e36248ca6006906", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.3.tgz", "fileCount": 10, "integrity": "sha512-K+0YWJ6Gz/at8B4TpbVVL5kswR/3FSi7LTPx4kYLt5yNdq4fZgiY9RnQQU57IRDimjS6BCgZPGJCBErfVgXnjg==", "signatures": [{"sig": "MEYCIQCBTXgvShYDXiRpl97jxDttw/ba7WaNLdNASf4QJ7lpYgIhANDIKmQ+8tWP4vFalZEAXIP44snBKTyQLHOOAHXp0MKo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33624}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4", "@ai-sdk/openai-compatible": "1.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "66cf5d6a1875995f068f8f23c416f7007ff91cce", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.4.tgz", "fileCount": 10, "integrity": "sha512-GxMTSiNr3Sz+NVoeL3ByB91v6rqaVteyXNc9PFZ79Z3ZzMDk2Nwo4Emb10B3jX5BpFLvYkpNCz/NwgDRHNqJbw==", "signatures": [{"sig": "MEYCIQCnkjuY0Ur2AVBx9DJNgUT/M+l5ek1M4Jakg38pS//97gIhAM1etEo/t7tSniLTHD0B6O4AsgFULyEE17N2K+cb6EkQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33819}, "engines": {"node": ">=18"}}, "1.2.10": {"name": "@ai-sdk/xai", "version": "1.2.10", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6", "@ai-sdk/openai-compatible": "0.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "abe89904b1972accab0e815d412987b4ff37cea7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.10.tgz", "fileCount": 10, "integrity": "sha512-jPaOq7HHJ9A7FF3i/zngcUd6jczFMJF1x9Ayle4IxCZ2gIhtLgv0hiUb1X95keh/gCHjtelBQdzLFNNuiPBadQ==", "signatures": [{"sig": "MEUCIG+dQN3nyGmPc9PGOBEYHb/yJj1Fb5XsYeLq/xPUIgFEAiEA3XYsS9lewS7jL4TF79RcZ6XeSB3z9m6ZS12Ub8614wA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33468}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5", "@ai-sdk/openai-compatible": "1.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c9b5cc3063f986f2557367a22942e013ce6fec5c", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.5.tgz", "fileCount": 10, "integrity": "sha512-TAmnnzEJtoAbbwrJ7Og+A2tYb102lEAJsG2l0PZqXcfH+ey9V2B3/l1ZfGspzsiuaYm1ABtL61cGI8Es/KcNuA==", "signatures": [{"sig": "MEQCIEsjoNCczQodyfwXfcmgMmDALESQxrJrT3WJoR7v8hHlAiBitq3YHLFypA0KY1fkCYtd3a0dZ9bm/Ny7KFHnTLm//Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34499}, "engines": {"node": ">=18"}}, "1.2.11": {"name": "@ai-sdk/xai", "version": "1.2.11", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6", "@ai-sdk/openai-compatible": "0.2.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4c0c5e4415e9fb6cd3d4e3eeaa48c492644149de", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.11.tgz", "fileCount": 10, "integrity": "sha512-nxn1TYQE0mSMlhA+SexMgVZiLxKE3kcBlnfGdeFALplLlRnl/abL+zm5fviQ7CfEpbSPlrWGu2qMNneF6Ij0WQ==", "signatures": [{"sig": "MEUCIE7Xu3CZRdCaHeA+DGFDJHF8ujj0l5IJ5t2cgO6F27nQAiEAvn/dQN+FAHSvccOH9ku8qTZXRzsuglSE/a4or44FUZE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 33568}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6", "@ai-sdk/openai-compatible": "1.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1c76f1c46c53c8a4d5ebd944fe61ec74fa382e08", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.6.tgz", "fileCount": 10, "integrity": "sha512-gTwMDKR7RjCXdkxPABbhR+HN+TI53Ezq2QLwls4z5G0fIeVZhBZwYH1tc1Bgzi5LACjfIJBp3bILLYpv4lkz6w==", "signatures": [{"sig": "MEUCICpFRkpME8n1mlkBKVsKorMsFyDmPGBuqyfKvaDpAVhsAiEAhKz0OQ55EixHKLcs4BzxBpFTNrrPHfZ6911FiurUgXo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34859}, "engines": {"node": ">=18"}}, "1.2.12": {"name": "@ai-sdk/xai", "version": "1.2.12", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7", "@ai-sdk/openai-compatible": "0.2.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6f6bf1369f3a4afb86559ce86379f5097da3c023", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.12.tgz", "fileCount": 10, "integrity": "sha512-bsOElERbaimQnzVwHMTXup32VTCGXgddJwKs49u11Biu87kZnYRymzYjfTY5uK5pqE3xjtcKofDMQiYoZNLvtA==", "signatures": [{"sig": "MEYCIQCgRl1DdvQsqrHTuYhXAstgap7k/vgUK/Kyr4P/r41ovQIhALcWpqGGWz0AL6FOJUiwN3QXE5Uz+KA7nyVeoIjcUcT1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36337}, "engines": {"node": ">=18"}}, "1.2.13": {"name": "@ai-sdk/xai", "version": "1.2.13", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7", "@ai-sdk/openai-compatible": "0.2.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d11dbe0bc685deea591abf8835f99c4a20bf7c51", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.13.tgz", "fileCount": 10, "integrity": "sha512-vJnzpnRVIVuGgDHrHgfIc3ImjVp6YN+salVX99r+HWd2itiGQy+vAmQKen0Ml8BK/avnLyQneeYRfdlgDBkhgQ==", "signatures": [{"sig": "MEUCICX3DqFy2EJ65wwC77YxkwW0UKhh8K/9s1pfDFyHWo50AiEA8NwI2hfjbNTJk1d8nPy2wIfBOoRTGcqhlfXQafKsIaM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36438}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7", "@ai-sdk/openai-compatible": "1.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f05ac997e1cbd5bc88bf33e1145aa8537d8e6015", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.7.tgz", "fileCount": 10, "integrity": "sha512-oHoOtbGKNftxKzqQKBjL3U2wrhE+ViOP+pPrTNP3ExHxxcGYmUUhtyGH4kLgKd9w0A0RmI6ruUvN7ovCH8cVzA==", "signatures": [{"sig": "MEYCIQCKO31cqsp+uKmrS8c9SiyObCzwb8ENF5m/Fhbm+YO+LgIhANLFbXvk0jym9xAN0SHdtKGXdhYF5M5nH9dkjGDzdofl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36728}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8", "@ai-sdk/openai-compatible": "1.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bedd10d48996c26d2af8cca7984e8460df12e17e", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.8.tgz", "fileCount": 10, "integrity": "sha512-lrQ/GLLY8wh/A4B7EO1FC0795NQPuZqHw8QNx80hxyor27tf2K9zImIjnywzp08Ihu3kUCc9Ly7Tp81Wf/0/Bw==", "signatures": [{"sig": "MEQCIBR5GE8woi36dEg5oX6BkRGNOG2L1VMotdHvMbEKPyI5AiBC82l7fHGOC76dqoNc/2cdF8PNYumCF7J187IkbsdJww==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37055}, "engines": {"node": ">=18"}}, "1.2.14": {"name": "@ai-sdk/xai", "version": "1.2.14", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7", "@ai-sdk/openai-compatible": "0.2.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fb4f1b576a21985a1d555849c7a057f0200302d4", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.14.tgz", "fileCount": 10, "integrity": "sha512-ZxGjI/D1heIOX6XqyAwrqyBSkICP2mge2La7qHmr5RRcqw9+vWU+2cAg2y1FqCw8FpN/pGFZI81Dm473cYwlpg==", "signatures": [{"sig": "MEUCIQCnLa/hLg+FxPLXAnDZGRhzxYlimIoyaLrWVo/wHZTo6wIgUn2QFgEpIX3qb4qL9jYTBQ5X/4jFgxNBQSE6RrSK14o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36758}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9", "@ai-sdk/openai-compatible": "1.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0e6172bcb536eb88cb8a433982ba546dadcfcd7d", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.9.tgz", "fileCount": 10, "integrity": "sha512-rvdlNKJ2YcBC6EtQu+dUOxkmQVAMELawhauclzz8FWx64u1eBgk76uubE0XBVdYYcIv4oyQb2cZzsQJ0QE8MjA==", "signatures": [{"sig": "MEYCIQCdQyr0zSAnzTqgQV3VdfBDp2840A33GQ77/TFxTckDNQIhAP5gE0077aegsKDMWXuEUksfIYQl6hfl0eosc6pa+pns", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36952}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10", "@ai-sdk/openai-compatible": "1.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "067bfb4c85a9b5944c94f2d29a18a11b00a82665", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.10.tgz", "fileCount": 10, "integrity": "sha512-qBSdu3wFvZkl5e05A6aqaBFDVGaXXu8EsriDPKIkI9vXPASbEO+fiWgW68pVyeid+guWTbLASM39SQoILweKzA==", "signatures": [{"sig": "MEUCIQDUlrOOwt0rPon3pK6J7p8iolCyvoGHmr3Kvy8cXnfKMwIgTA2sUtzSE9SJfs2xyuc216MnE1mtrR7CYk4PnouPsDU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37405}, "engines": {"node": ">=18"}}, "1.2.15": {"name": "@ai-sdk/xai", "version": "1.2.15", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7", "@ai-sdk/openai-compatible": "0.2.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ac67ee865ca8fd272bdd7926408f49431157c15a", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.15.tgz", "fileCount": 10, "integrity": "sha512-18qEYyVHIqTiOMePE00bfx4kJrTHM4dV3D3Rpe+eBISlY80X1FnzZRnRTJo3Q6MOSmW5+ZKVaX9jtryhoFpn0A==", "signatures": [{"sig": "MEQCIAj72+zLgk+PsiNx4Upfc0mr/NutyF2VOxSo7mVzZEIyAiAbHz5kg5WmPjALgwMwk7yTNusorr3hJXI46nVNKcq2Jw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36859}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11", "@ai-sdk/openai-compatible": "1.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4fb7897bbfd965b471d467c0003dca20ca1b99aa", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.11.tgz", "fileCount": 10, "integrity": "sha512-t+7ogvviogZXW99XjANIEJgw65efdoVmToS2V8Nn+2ZiqHlhnqEKb/bkt7arU9zzjqudsKwzbS4wgE/d4a+a6g==", "signatures": [{"sig": "MEYCIQDEHPc9SzzE7eF9b+kGzJAr8fL6BCKo41PxcPsygwEQPwIhAO/erYxoXr4gut8J+GEtzWODSsuMW6UIbhf3I3ZNcpHr", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37806}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12", "@ai-sdk/openai-compatible": "1.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5a00d732b54e4aaeaad8728a215e372798e84a18", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.12.tgz", "fileCount": 10, "integrity": "sha512-K9hFE+Q8tLwEHBg53xLoAbei80CzSeqXEUfYbNhsuoQrZhn7+irmIsqcvZttMvRA/3yLgZ3D8IrO/ELaPj105w==", "signatures": [{"sig": "MEQCH3A53pU3nqQTIpjnHJbD9Uu5weqmf+MLDQArVpppP0oCIQCSDxaZnfNVl6MMXVEAymyMNsgGq+UEDAcqKdqcyIqgbw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38071}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13", "@ai-sdk/openai-compatible": "1.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "755dcb93fcecc2fffcf21c3d8b0ed868973c0643", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.13.tgz", "fileCount": 10, "integrity": "sha512-YRMFYtfEwJVbLfLzTsvuNot7iTwXVHb58UQffmKxpxotwEaFAaY+DooTIt7/OoTK5J9sZG2MO+zjgEyFLfPHZA==", "signatures": [{"sig": "MEUCIQDIDppwLt9tECIw1b4CfxL7C+Pz323wsK4M2LjllaBDxAIgNBobuKu5OOfX1V3t9wn3ivQ7sadM75DNGZNa56xNXOA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38336}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14", "@ai-sdk/openai-compatible": "1.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fea5a6a3b181340ec74ed44804ca787adeaeeb55", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.14.tgz", "fileCount": 10, "integrity": "sha512-j4WpKoi2hk6naMkDBwNESBOCp/ahNrr2bs4Kmx0wEGFI+1IEPVcN/ScBrcFWN8MBQI6f/4FhPmd/c18L7EaGXQ==", "signatures": [{"sig": "MEQCIE6JXcOS7xKZpQ34zx0m2E8WtDrUWbsYJJOdF5F/Du8OAiB0M6+TYoONv5o3+cXNICkceKLXNBjUsEeHvCKpIYNmAw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38568}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15", "@ai-sdk/openai-compatible": "1.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fdb4b54f84665642a4bb6d2ffa9208ccf78a9948", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.15.tgz", "fileCount": 10, "integrity": "sha512-+cvhk7Prx6T2REab+K0QKol1tMabYHGs5DaN4AdwueeFFP9h2KrKvN9l2ctMkhnmg+v2s4Vl9mBRxik/jXu/Fg==", "signatures": [{"sig": "MEUCIQDQsHuVFMeeAU1QvweEFvKEF5tI+wP2dIevJCB0KGJxbAIgFUB91Z4i4FRR/Co9QFSN6y+tiQNJVEhBz4MKdElZ4EE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38854}, "engines": {"node": ">=18"}}, "1.2.16": {"name": "@ai-sdk/xai", "version": "1.2.16", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8", "@ai-sdk/openai-compatible": "0.2.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "85ebd5d37d022422915e0259619ef43a525c79e7", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-1.2.16.tgz", "fileCount": 10, "integrity": "sha512-UOZT8td9PWwMi2dF9a0U44t/Oltmf6QmIJdSvrOcLG4mvpRc1UJn6YJaR0HtXs3YnW6SvY1zRdIDrW4GFpv4NA==", "signatures": [{"sig": "MEUCIDHck2VUnpf0Flgzmp4+bLCtIBas8fGKSuBgnoioFnp6AiEA/+a6aVDB/FucV5WJe4MXamD6m1FdOUUjCeKQjMX8rao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36981}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16", "@ai-sdk/openai-compatible": "1.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "225e42c3041894736f5a4c1ff25bbed53b6d94ad", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.16.tgz", "fileCount": 10, "integrity": "sha512-AXAdG9ii9bCWf5i1iOe3iPzudkGmTZXVDq8OC+Fs4JfomdsT8eXUSrCnbqeVRpoE14FbvthJHHDHWOFYD7b2vg==", "signatures": [{"sig": "MEUCIQCVn7TEHQr2docsSYJL9hJJFuBmXg6dpQL4HMX5A6xd6AIgLYbi4g1mOBneySUjLvYYlIT2iqXRKmrwLE1GljVwOao=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39016}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17", "@ai-sdk/openai-compatible": "1.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "166adc9f5f000be10629fee884863c3fd272d454", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.17.tgz", "fileCount": 10, "integrity": "sha512-42bz+cQAl7QRB9BjtJNOKW2mtFryosm0T1uOL4UnP4EEUeH+L7+WOWW2c9wLZfCiKIciipbmIS1naeWGE2IjDA==", "signatures": [{"sig": "MEYCIQC1LWlSHAe0adsUFfNZqu4MFChMN6rcO91dSdSB7lTqLwIhAIxVy8kKF5hmPWzvFaGMNAa+XFSa6o5eo5QSlMnJQKEv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39295}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18", "@ai-sdk/openai-compatible": "1.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0f12ffe4d96f4060a7a0c9a853951c517e147336", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.18.tgz", "fileCount": 10, "integrity": "sha512-xF1Udb5tSe8FdJCmkfQ160r52gzM8M7gd+IM4BiCfqN5BMIXu4Y1Q/D468nmPX9ylPDEq5fuAYOeek1MoDoJIw==", "signatures": [{"sig": "MEYCIQDNsUfAb3Ny52P02j5EUo3+Rn2a55HCRH1rxDOVilIOKwIhAN6diKY847i4nWPX3dVShUrVVXV8M7/wo3KCjDf5WqiE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39457}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/xai", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19", "@ai-sdk/openai-compatible": "1.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "a9f7f56021ca1faaa5ca08c857e49df0427fcff6", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-canary.19.tgz", "fileCount": 10, "integrity": "sha512-Aj5qUYL2vsTSIpDYAH1Y1lCX3BRRG+tgSO3jgUgxvNTWwu+K4F3TahK1BBxI46UafL6KQpAf8dUkX2pKcILI2w==", "signatures": [{"sig": "MEUCIFVk2G+HV9ylQSk3i+C/HfNvZ3wqaxGnOAo7+W8LJvHHAiEAst9PoPEcaavZIhLmAvgMYSIeu+NxDlIOBbseJHCyOnU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39620}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/xai", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1", "@ai-sdk/openai-compatible": "1.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "4e34bace434630cf10c92fa6acc286e6fd27ec3c", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-osb0wKEvzmbl/gBSkUyz74Q20FVkTcYfvg29Ysg+W8Ksi/CMzfzyYMrJE0t26JMTNogYo9yrPtdaRhsFoH9uww==", "signatures": [{"sig": "MEQCIF+aW/k9VDnXx9YDv4lVuUj83ndNoWx1hs2bZyKyawBlAiBeuo12VhG1u+GCz2DnNNzHSWPSstJiZPja3eor4mDhYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39803}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/xai", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2", "@ai-sdk/openai-compatible": "1.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "8d1a8237019ddc4fd3d79c4c9059a470ab02128e", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-g7wFQ9rF15n/oo5cpelw5LKYr+CKJoV276Vq0ITyZXPCU7VgsTbMlvkPIZfFZqONeFEDV46uZKMhigkEEMKCIw==", "signatures": [{"sig": "MEUCIQDV9d6rNx4wqRmrQ1G54oyr7jVeekaUM7VVqcjM4WxclQIgfnpvgsmdCgB6aFMX6NEGIJq3apJ8tvh4nTPTB6Qjntk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 39994}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/xai", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3", "@ai-sdk/openai-compatible": "1.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "11b81442d74da54e798674ecdcfd89d3f5fe9176", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-9i1/65aN/f8jqrj3DarUInK/+Keq8/D5eBN5AJ7Letdwlf6h7qi/AKzpxwWW5Kp7kA4yfflK2t9YbOlh5DUOxg==", "signatures": [{"sig": "MEUCIQC33dj79zCCH0kdlmvMletYEgnRqeA/uAeSeHx9Aah1CwIgE76pdEeAjnjIT6LRtM91dC7slrnjFZbNCOsOrrVT8ZU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 40185}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/xai", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/openai-compatible": "1.0.0-alpha.4", "@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-5VTR+bUT0N+m9XvWgEtOymH8wArDy1Z7vGWoMiaN5//SOM8BJIhlpbjE41tAHgEhZPjZ1NTnaS9SFe85a9GacA==", "shasum": "a24addb18663717d7263e8fad8f33e6dce2a4c1a", "tarball": "https://registry.npmjs.org/@ai-sdk/xai/-/xai-2.0.0-alpha.4.tgz", "fileCount": 10, "unpackedSize": 40376, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBBIdjtTbAKsxC4LbGWyXw30kIRiJCzkbSDSlsn2GKI6AiEAsATAWbXeUg0FX/jcivJ6PIbqaj7Gmo+buAA0oZCJ/S8="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:06.060Z", "cachedAt": 1748373702362}