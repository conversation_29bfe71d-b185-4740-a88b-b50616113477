{"name": "@ai-sdk/ui-utils", "dist-tags": {"snapshot": "0.0.0-9477ebb9-20250403064906", "canary": "2.0.0-canary.3", "latest": "1.2.11"}, "versions": {"0.0.1": {"name": "@ai-sdk/ui-utils", "version": "0.0.1", "dependencies": {"@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "dist": {"shasum": "53a43b05a2274cc78d9da9bff5dd6033b2d36d43", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.1.tgz", "fileCount": 20, "integrity": "sha512-zOr1zIw/EH4fEQvDKsqYG3wY7GW32h8Wrx0lQpSAP59UCA4zgHBH6ogF5oj7+LUuWjT6be9S0G3l/tEPyRyxEw==", "signatures": [{"sig": "MEUCIBT6P7FczuQTjARUcfveivwBa6r6iywMsLzTfkcPgxY2AiEA2eOfdFSqejzOlQU0H15v3iyqbllrkSzLYQJSq7v6upo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3898592}, "engines": {"node": ">=18"}}, "0.0.2": {"name": "@ai-sdk/ui-utils", "version": "0.0.2", "dependencies": {"@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "dist": {"shasum": "8f5207f3cc687e11f85d84908a659798b08b18bf", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.2.tgz", "fileCount": 20, "integrity": "sha512-CpWKvYO99UC72oHUts5MrJGPHEoOWrxhN4R8KjeHanZn7cosJ3bal++seSGJrwDz2ImqrpfAdRHmQtdbsCCOyQ==", "signatures": [{"sig": "MEUCIQCm5CxndffqasuQDMqhgUMT5Rfyx7IptOZlOTwnXiTAUAIgCGDQCHJqDnvhnIgbV/8M39HEXWZkSfqcJureHBJdP38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3905683}, "engines": {"node": ">=18"}}, "0.0.3": {"name": "@ai-sdk/ui-utils", "version": "0.0.3", "dependencies": {"@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "dist": {"shasum": "71a28af08058ac3b7a37f86cd80dcbf14c9d93cf", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.3.tgz", "fileCount": 20, "integrity": "sha512-V3tF7zh1h4wEORlh8hRE8MI31KiCE/RliXrVNQT9aW3+lAlDNd9nd/sEtGsdioxhJcddqQmPxycVrSOSAFEEzw==", "signatures": [{"sig": "MEQCIDffcKB7af3/00jLb5tN5uW+cdJIphjvZIVtQGUr9jaIAiBifl+BL7gDEZqwDJqAivz5QBgL0VVYgOuiYqy/gKimyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3905683}, "engines": {"node": ">=18"}}, "0.0.4": {"name": "@ai-sdk/ui-utils", "version": "0.0.4", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "98d9868c9f780e6912dd0f75bfb8270c314205ff", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.4.tgz", "fileCount": 20, "integrity": "sha512-vUfuqVOZV3MyFokAduQyJsnDP00qzyZut6mizFscXlCOmiiW3FAnu/XEnMEwCmf7yUG7O4v7Xa2zd4X1tsN5pg==", "signatures": [{"sig": "MEUCIQCZab5z95mdbKcZNbkYR6FzKFGQTW/ks7pC3NQwW2pzvgIgNCHH31LWyi8NOj+gWBtFbDSxdTOacbDX3ym41Ejnfsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3974948}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/ui-utils", "version": "0.0.5", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e0cf161895a96e777d4fdb74ad62679cd3ecf797", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.5.tgz", "fileCount": 20, "integrity": "sha512-Ug2qsKVLLxzZtJMu8Omw7wA1p8RqX82M4OeAZ2/oCPlZSAVAte+VnuXl6q6lUsAUfprVCDpzDDm9GJOOOYZg2Q==", "signatures": [{"sig": "MEQCIHU46sOJ4LlsgyJzDME4P+Gc1f8v+DhByzCMJdDoNEDDAiASemIJ+SffPtP+3E+yu9xxotU8GchCvTQHu8tbUtVpVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3974948}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/ui-utils", "version": "0.0.6", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2adc645f32e733c2705f5a40b0c4bce199948fdc", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.6.tgz", "fileCount": 20, "integrity": "sha512-CpenkIWaA3nkY/cUJ0/paC/mT9hD7znCc91ZbDK1jH/MwLobLl1IPAACbjxYhnTo72od7S9/dGrpiI+RqWIwXg==", "signatures": [{"sig": "MEQCIB4A/jN6NStm8x/LSuB8yTrG+Mvo/7HbarL0ZVuGAe1iAiAt7fC58WWqhEW4RDw/Su1juZbL+h0z6swaGK6ZZw8rQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3977093}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/ui-utils", "version": "0.0.7", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "223c76064ff616e5877e90a6fcf2522506b611b9", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.7.tgz", "fileCount": 20, "integrity": "sha512-1QM9p7Rso0IgfIxBpFxn3MfrqUDY9q5IuzR/bQ47LQG/aJDESy5mVPUOT9quSPEuHps9wLM4zkVssEQiaz+VmA==", "signatures": [{"sig": "MEUCIQD8VeRjmRdavUd/edkY1cm0A970N1Y6US9G6xLNOMezEgIgau6tFX28kjzzeZItxKnBudG3dRoAas7PieZpxWDPSWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3977175}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/ui-utils", "version": "0.0.8", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2a41984b20f11cfb821df8bd099f58318d4a7e0a", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.8.tgz", "fileCount": 20, "integrity": "sha512-eTMZBwpU1v1y/cvq5oFkKI8RgUs13jbLNoWo2gYEkS6ojudLMGhQIXvIWdPm4r3yd+EzCj1d1yHComwjA8vwKA==", "signatures": [{"sig": "MEUCICFR7OES2UkdLR1mSCSgK5UHOx0tV9i064wv2WXQIhDvAiEA5IokCnof7Zh9aiDPg1giQv1NwFXRAy8RsON3gjc7NTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3980957}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/ui-utils", "version": "0.0.9", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "67cb81fc4bb3818282ccaeeb2cebaca81fddd348", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.9.tgz", "fileCount": 20, "integrity": "sha512-RdC68yG1abpFQgpm3Tcn4hMbRzpRj0BXbphhwSpMwHqPQu4c/n82tYYJvhGB+rRXs/qLftLBS1NtrhqEYSVZTg==", "signatures": [{"sig": "MEUCIGhTSfAOgjuwtLd+immZSUEy5missaSb4Gaeqos9DJvPAiEAxHWMeAs9m5gDUM+iIV1rlww2bCQ+eve1NJToAHeZyJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3981249}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/ui-utils", "version": "0.0.10", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fa1bf3f31787770fd9951e0ba24b7f1ba41bfaca", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.10.tgz", "fileCount": 20, "integrity": "sha512-XHqfqtx9ryVXZ/ZFyVEX44r2JJslKh3DBSIPVdSsiNoxUpzzM7mrIVT2XOMSrCSxoCqScOk8GVRsw/dwG1xw7Q==", "signatures": [{"sig": "MEUCIQC8hO9XfNosUsUJfllx9AjL2aJv+RwwS/xGSInRUPwwKgIgWBYLKIVH6nA9KdjoESYSe/5iTF1+qMk0ScSAuQsQME4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3981250}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/ui-utils", "version": "0.0.11", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e5cd32ef419455050702e4b8c3bc3cf6fa4840df", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.11.tgz", "fileCount": 20, "integrity": "sha512-1Ag67zp0xue0i4g6TUF8L7YPkmYPlX3iiDUY9tsMqVjViqnvG6458fc2Mnh2yGgDjt0URv0rpjORPquxO/Llgw==", "signatures": [{"sig": "MEUCIQDHCF4RPj58M/dVbuzejA7HPuEBgVlgcXmF6yYFXzOfhAIgbvcoH5nzIElhLFDbQQxGLdEAZsUVVt+NPZi9IfVULuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3981250}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/ui-utils", "version": "0.0.12", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "03b65f6f33f1ef793c5d226320035628aafd8f0e", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.12.tgz", "fileCount": 20, "integrity": "sha512-ivveEuneZPOUKqcIqZRCr2NUD+LJC8mYfL7jJRWaCr+JZqdYZ+5uR/nc8GKCly2TcC9/qoF3zxQuZEn0c5805g==", "signatures": [{"sig": "MEUCIQCD8FknOVrpOVTgWeTeRJq/CCFxqOHcConp4RQOdQFW6wIgLzwU1zNWtBbKuGy+a07X07vu8+mcAXMCaooaxN+sxPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3982708}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/ui-utils", "version": "0.0.13", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6f2ae961702b34a09a455a176a3902ee4716309d", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.13.tgz", "fileCount": 20, "integrity": "sha512-BUPlYgWGns2X0JFNpwywhgcrXwmW+mW3NYIMx9JREAsT6PwE1dtgeykSIZOra/+pLkHqMlZs1HRDXbD7Da6DBg==", "signatures": [{"sig": "MEQCIHHTIHqsNZuyNXKZrB4gAK46sZGFZdwmZMxR/4Zohw90AiAIBqCq4N5A56GJvCYhM9mvI8S8iS4Tl4aUznRTJICHOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3983376}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.14": {"name": "@ai-sdk/ui-utils", "version": "0.0.14", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "51af11b19e6bf44b10e2acadaba3db245115a854", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.14.tgz", "fileCount": 20, "integrity": "sha512-mm9jAkdZW+UTrSLMGwRcT7o8pHGO7Z5FmmLC0B1NH7BRzLfh1/t0ZuF1U6T7GQbmRQXwQUWjdjnlpY83kc62zw==", "signatures": [{"sig": "MEQCIAQca7tpbcXshopeneLVvMdJ5ZC2j7FIaqq1XYgn86JGAiBoGWcJCNwU+7Syr1QEFL7TOiB7tGxLt38FTIxUxNxqEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3988487}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.15": {"name": "@ai-sdk/ui-utils", "version": "0.0.15", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eea3d3a049c020f7a3876d98b869c2b8d72a1d58", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.15.tgz", "fileCount": 20, "integrity": "sha512-NzdAkFPTq+qJ6D1u4VQpyDg2TIF2XeTPTCBZiU8pqP0Mkpj8hMeVKjbRACq0lOXyFNMCvUiLoF05fKuuC0Ak/A==", "signatures": [{"sig": "MEYCIQCVqKipmcKNl/0wsLnefIIjn2qmeVsnZ2W/we9BDfPTDwIhAK8zWZGdZj3Y0KmsYg2+6+XeOotINSKbiqrmWE0cczSP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4010049}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.16": {"name": "@ai-sdk/ui-utils", "version": "0.0.16", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "416f43942781561ce84088e99bf1c75bc5574328", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.16.tgz", "fileCount": 20, "integrity": "sha512-DWPj2gPtY+MfEwxPeUZ/pLlKzfvL2u0p2fZLCYDJhJJgFWkvjvKUejRYH9M5uj8zTkZlwi92Gy+vZ20jto5zwQ==", "signatures": [{"sig": "MEUCID0Pp2f7Q3IULJ8lF2+n9a4V8A67m1K7qEXHyOOK2HKHAiEA4c8UZ8ey2XCNErWI1EkZwqDc/eVm3OgYfVDrav05cHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4011467}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.17": {"name": "@ai-sdk/ui-utils", "version": "0.0.17", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fc0d3b90ec3ad67e7f4210282c878f9331037ffc", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.17.tgz", "fileCount": 20, "integrity": "sha512-+KJHoYvCUW+0kwzOYSEcQTixRMobm2WiSBre31K7t1cPbau9epp8QZHyMahnhZtKtXmM9k3wqZN7kxQXptFCiw==", "signatures": [{"sig": "MEUCIBSYs7Z59ijwq34fIje5nZbypZUTYkHiLgpO1j/U6XfEAiEAoeYXuFBi1pt8B+CD5JYUmL61h5KmVtCUnvKDjI73QFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4011689}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.18": {"name": "@ai-sdk/ui-utils", "version": "0.0.18", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7c4354be223bdb9c9e648ffc2cdd8aec738c7023", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.18.tgz", "fileCount": 20, "integrity": "sha512-POyvsJv0Sja2B/gwK4xKiXg5GvjAzW7XRFtchfdtmU6zX+iUyVo8yDcVupPaHvdeIS3gJySDngtKi76VmccARA==", "signatures": [{"sig": "MEUCIEscFNiaWTpEX3MAjeTLdWgA1psdRbGdSVIVj1aEe2V1AiEA/RDDpesRPruyoD6KJE8sa7HWx5BPOZzMWR6dOiZx9to=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4011689}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.19": {"name": "@ai-sdk/ui-utils", "version": "0.0.19", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f207cb29cae0fdb2195c05f7954715f8c48b894a", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.19.tgz", "fileCount": 20, "integrity": "sha512-nLCUNoQ4hY+v/5pMLz7mIe8ZUW6tR644Zaqh9gg8ScKVfc3EMvyxJ/AoAjcgD00iXOmbT1/xTsF6TkXRGExZ1Q==", "signatures": [{"sig": "MEYCIQC6z2oy56LNOYo7WEcSCA0JE3gQAYp2KLI4veYYXEF7EgIhAIaYYgPHdtrmqzhtpzl2+dPfusEyCxLmy7S8FhOdA9+z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4011689}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.20": {"name": "@ai-sdk/ui-utils", "version": "0.0.20", "dependencies": {"secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c68968185a7cc33f7d98d13999731e1c7b672cbb", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.20.tgz", "fileCount": 20, "integrity": "sha512-6MRWigzXfuxUcAYEFMLP6cLbALJkg12Iz1Sl+wuPMpB6aw7di2ePiTuNakFUYjgP7TNsW4UxzpypBqqJ1KNB0A==", "signatures": [{"sig": "MEQCIEeS+UigsmvI6XD8uFRUsb53CQkAHlmO3SzpLus5GveCAiBsg2cbey41fvuU6k9+AdcJMxoW3yzAecvI3+yaWKiwCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4011689}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.21": {"name": "@ai-sdk/ui-utils", "version": "0.0.21", "dependencies": {"@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "98a55885a56ba7e5a4de14d0d0d23b0703d595ff", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.21.tgz", "fileCount": 20, "integrity": "sha512-qZ4ScEhNVpVqfg/2KcKYKKQcUpmfVyMD3P06OVDqXpzMaWvw95lUSYkJO/TDI49MhyVyK4eAI35pRU5B7bhrTQ==", "signatures": [{"sig": "MEUCIC8qw7zmqLWvwI+m5rPzJPfiQQDXfwGQLRxhONGcuvREAiEAttSiHv4CMyGqa3W2HU2OqZAuGsOl6m2GPw5Q5zGh+tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4026151}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.22": {"name": "@ai-sdk/ui-utils", "version": "0.0.22", "dependencies": {"@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "de7116d04ee322f5032325366f5506ffab718a5f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.22.tgz", "fileCount": 20, "integrity": "sha512-iBjuCxBf7N7ZaU9dlvMSU+AdasmcmzhJUBfOAXuR+5jyRTD6ukP46Kdz+6GaUBU7PCgNLldDlyggx4MV3NeLZw==", "signatures": [{"sig": "MEUCIEppWu/w2ef5hhPq0dkS7dDBXIhJ0IcOOAEqHXD1DExxAiEA7kdeycPCnJcnTBuAGa0Ud1+rrWLGnfpGWpolTxYPJRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4027001}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.23": {"name": "@ai-sdk/ui-utils", "version": "0.0.23", "dependencies": {"@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7c46da248e10236af23b469d9e4a414c7617ad08", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.23.tgz", "fileCount": 20, "integrity": "sha512-9KONrxwnoV9VyW9I3m9+cEi5IANvADeLuCe+oB3JzOobNKASwYwcQZ4X7no28DckfiJUmHk4gmPnsC3yfRoU5Q==", "signatures": [{"sig": "MEYCIQD47vscE7EH9sVB3PGpeVdwSOdU6vXq8QubztqFFEwiswIhAJ3GJN8RbwHgeT+oYEg9dVSZIGohRJLGa22OFz+ZSMh3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4025895}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.24": {"name": "@ai-sdk/ui-utils", "version": "0.0.24", "dependencies": {"@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "09fb99244140e2bfde4c4d4ac0aebb5b24eddfd7", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.24.tgz", "fileCount": 20, "integrity": "sha512-NBhhICWJ5vAkN4BJnP/MyT+fOB6rTlGlsKGQwvjlhNxdrY1/jXqe2ikNkCbCSEz20IDk82bmg2JJBM96g1O3Ig==", "signatures": [{"sig": "MEYCIQDQ52KEuGMYCBgGfx6AOq93a+4v0q6oscyLHa3nDkBiowIhALvZlgcXvCfuW020NbhHJfbE7PvrRUddqoWy4/4v2WKR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4025639}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.25": {"name": "@ai-sdk/ui-utils", "version": "0.0.25", "dependencies": {"@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a2438fef3234ffe7d30b141dbae1d39c3a220256", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.25.tgz", "fileCount": 20, "integrity": "sha512-IpBDdwcPg6Xvhz9gZNcQdXwQDWvTl3nsUxyA8lUQwDpOSeBhqdta0nkOJYtVDtQFUE/Nk1xmNqxAEJqGmwQ56Q==", "signatures": [{"sig": "MEUCIBgXmxF5CWD6OMOFXKLG2SmaE0Xr3x0MYCZhp1n1VeapAiEApzFknkbAgTlfEe7jzY0d6XHzuXTGQ6UpD3gzMd6FChw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4039347}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.26": {"name": "@ai-sdk/ui-utils", "version": "0.0.26", "dependencies": {"@ai-sdk/provider": "0.0.15", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f6d2d86589bbf53de832cbaeca427af32fe0c276", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.26.tgz", "fileCount": 20, "integrity": "sha512-d+16/dcKJM4m4KFgaz8AtJFvJhUNEdqGVFczY6jtFyyBqXz42pQtAASRATtgOatdSuOeEzOSkTzBAr49/qN3Wg==", "signatures": [{"sig": "MEUCIQCN/eWV6ZZuXj2toCxTWZ0r0WqOtsJy6ArmBJPqFqO19gIgRmJl5WnxzmKZ09xuD1ooeFzd393/Ge1QTED8LLiHVSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4039347}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.27": {"name": "@ai-sdk/ui-utils", "version": "0.0.27", "dependencies": {"@ai-sdk/provider": "0.0.16", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2360eca7816d1a8bf04224f9fa31976b398dfa7d", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.27.tgz", "fileCount": 20, "integrity": "sha512-XQaKMLg/KAml7Rt0zdny7AJOXZWZsGp/vQ70F2c9eD/ABkGzHNjWRVVpfYJgHoQxc9KRK/mrGEJEZFFqm2yyHw==", "signatures": [{"sig": "MEUCIHlWzKHhqRRxyn5YjDJpbHdo8MUaRybrVqOmjb2nzD4xAiEAhihgQJ9xXxg2jxeQ1gbVmahYCBrZrxDOocY0LjVgakk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4039347}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.28": {"name": "@ai-sdk/ui-utils", "version": "0.0.28", "dependencies": {"@ai-sdk/provider": "0.0.17", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "503068d78f71da3e80175e6278db336f21fa84dd", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.28.tgz", "fileCount": 20, "integrity": "sha512-yc+0EgC/Gz36ltoHsiBmuEv7iPjV85ihuj8W1vSqYe3+CblGYHG9h8MC5RdIl51GtrOtnH9aqnDjkX5Qy6Q9KQ==", "signatures": [{"sig": "MEYCIQCnqv/aRyKqQANWSQXu3PFIskzsvjxGS9Tbj1joQMRkOwIhALkebrRjDwxKsFFTjBckHcg1QFbd/DmIuNAiKtTNpw2S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4039347}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.29": {"name": "@ai-sdk/ui-utils", "version": "0.0.29", "dependencies": {"@ai-sdk/provider": "0.0.17", "secure-json-parse": "2.7.0", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "811f278cce4f5be811a0f1b32211c07cd0f23691", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.29.tgz", "fileCount": 20, "integrity": "sha512-1d+8ogA/xm8xiVdKQt2uJ5HDbkowMYUDS4a30kYkaGNXk7JHy65feEtDHQnnUZKqHdqAruyPB8sLNA75gi6OHQ==", "signatures": [{"sig": "MEQCIF5eE6+3mvMyQL/9cf+SJecBapNpbIAV+7hvykaHQmz9AiAM3xkrFWlQJdV4Y4EdqTUQOytVv9ucO9VZeU2V20wHxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4039791}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.30": {"name": "@ai-sdk/ui-utils", "version": "0.0.30", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.18", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "836befd084049cc7e8908a505723406f6accc31e", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.30.tgz", "fileCount": 20, "integrity": "sha512-ifmYSQtSVdeY1XlOFirAdbgWVnSg5hYPhG8bEiNI9TvR1HFdMN/zZhxscjLyKXXAelf/ACYvUjwNQmCnWSurZQ==", "signatures": [{"sig": "MEUCID1zNw7GyzfmtUqf32ganjhL2YiBn6fqbFK+2TryybS6AiEA6WLCWpSGTvHgVlHHFGidypnvBkQmGEfE7b5VaP4KQys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052295}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.31": {"name": "@ai-sdk/ui-utils", "version": "0.0.31", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.19", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a3d92e9ba3fd2d3a4de8b43fc50b97d22fbed6f6", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.31.tgz", "fileCount": 20, "integrity": "sha512-PA1mI+WC69Bc8JCTDOXwhLv9OAfocex/d+MRtQjfuWE6jTBjkBMa6davw+JjN7Vcp6zP0JLQG6gd7n6MnkFepQ==", "signatures": [{"sig": "MEUCIGVhfMDB7LfolpPUquSiLG5eYVABnc1wpVrm6ge4TH9RAiEAlxGjOR484iScO/SLtK4o99ljJ9hDmRKV21y2cmDiMiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052295}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.32": {"name": "@ai-sdk/ui-utils", "version": "0.0.32", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.19", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "28babfce7a0970ac90d6c7543b69d24844837709", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.32.tgz", "fileCount": 20, "integrity": "sha512-ag9DVJlSy06qa6WGg7c1ElFNOxqF0HhwPNqDpAoJyNmSCMiN5/KFMG/mlhQq0bsjPRPvZGAazImFb/VtJZ1DIQ==", "signatures": [{"sig": "MEUCIAedrUxEVO3Up3Igk16Q1PMpRFqq0rxGC4/lg6ktUH0xAiEAy7PHv7yW2uKVbC2nnVDYiOHHBO9NTdI0D1CGRfM5Wsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.33": {"name": "@ai-sdk/ui-utils", "version": "0.0.33", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.20", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "01c74f4721591451dad064beae1187d712f737f1", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.33.tgz", "fileCount": 20, "integrity": "sha512-2oZjZzZG3AGQihO1d3mWqgFuywTtjBtkUEeE7d8nicw3QQv9m1MwrbQqRhhKbbBetBke6V9o5FQ5wngmb/+3iw==", "signatures": [{"sig": "MEUCIBRIIfzwaBZaV5zrdcsjwaNgFbVs8Co3vwDD+l49dvSGAiEAuiOS+N+CK6wZ+doV8YTKIa0QVS/V5r3x7hrkMofNPKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.34": {"name": "@ai-sdk/ui-utils", "version": "0.0.34", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a9b2cedd9ebcfb582127f791e27d15d62cf9b5b1", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.34.tgz", "fileCount": 20, "integrity": "sha512-8nTBsQklLrp6r/AJyeWxD8D4pvhQhlGfrDaBAfo7OEdBLwF6bNdnh6CJFSwUMiXUtUPOBqPV5tFgY2pJmGQikg==", "signatures": [{"sig": "MEUCIQDN8egvceg5tBA71XaSGY8x5AVyn+85FmwdAsD86c5w2AIgdzU4GXe6kkXhe5cbX3Qxy3d2IZg9AZhglsltR4aUnNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.35": {"name": "@ai-sdk/ui-utils", "version": "0.0.35", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.22.5", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e2984a262ff4265c234a6700b7fa535b3571d4f2", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.35.tgz", "fileCount": 20, "integrity": "sha512-JZWp5gbH9K0/qmmqv0JFrH97JNMB9dU1xtrR2a8uzRE0wYtNmd3KsM9x3KW/f9OGjxUHzAkrboMvxKv/3uz24w==", "signatures": [{"sig": "MEUCIEmlFmESZZFeVIOrzpUh7C7ggHGg2xyUmlpHUs8YPeajAiEAvOhn3fDZ6XAJgVFDhJux1Q+3W69k7V+ruNhktI6NbYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.36": {"name": "@ai-sdk/ui-utils", "version": "0.0.36", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "91c0b35f83cc375feaa0fd788cbcff7675dc687f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.36.tgz", "fileCount": 20, "integrity": "sha512-aaVQFFp2jzmTezIf+1r1Oj0F6IXMYwT1Bx2w7nLTEeoQDxPriLL/I+0nJJWUMPztAJhmZEx5WRaPMVC4Y5tm2Q==", "signatures": [{"sig": "MEYCIQCnamuTcO6dX+zE5U3YDw3yUQtlJBNGHle0Lh96eTYq5gIhALhTLYQfGM3wHWIIvi8sbwZpsVwur8Hq9dyXmxWXDwFA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.37": {"name": "@ai-sdk/ui-utils", "version": "0.0.37", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "035e4edb0c5b5c32ebd436d94c1952ad0e44e865", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.37.tgz", "fileCount": 20, "integrity": "sha512-iMf+ksOjFPlqWVuW1/ljGtsKXtNTlAfRuxvQbMEImrRaSSOH0nKI5H34H2E0Vsa5SCyH9Bk1Y0zvZamb9Z/bYQ==", "signatures": [{"sig": "MEUCIDIQ29rvbsK2FGOUj5lqD1LuS53OW//MZguC1VwJcKwKAiEAk0obG0/N0L82QU67wprjrCEoUdylHbRsKfx+oaGKcrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4052069}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.38": {"name": "@ai-sdk/ui-utils", "version": "0.0.38", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9015eee4d7ba2633ea088e6a94f4cf141738b1e4", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.38.tgz", "fileCount": 20, "integrity": "sha512-SyyfqBu7xnsfUuq3kSxzP+fxGCTMqaSL5WYGiBJpr/yLWySjBJCg/k7WueO440AqVpZBzCd3nWoCpPmjfMK8Yg==", "signatures": [{"sig": "MEUCIDSqIK3P569w1q/nh4BZWiz/86BmXplVdzzMexIWzU7FAiEArVOibLEBSImfbJYFGF8UrryqNTqFf4IfiJJubP0VUdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4054216}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.39": {"name": "@ai-sdk/ui-utils", "version": "0.0.39", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.22", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7f99936e1a9b90b480ae9ba50deed291d777f0c0", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.39.tgz", "fileCount": 20, "integrity": "sha512-yxlJBFEiWR7rf/oS7MFX9O5Hr7VYV0ipMBrvds66N3+m52/nCbBB5C/eBefzeR+hoGc/r5xGo7Yd1cncGYHHTw==", "signatures": [{"sig": "MEUCIBk2tPBNg0p3In/Rbzhe6j8SQiJuZOlVg2ZDaHVeV146AiEAlBgRCvREhinF6rSVfuVY/lsWVG3OJ3ENrclpljs0haY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4054216}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.40": {"name": "@ai-sdk/ui-utils", "version": "0.0.40", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.22", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0d9d66684e6fa868193be403dcd74201eaacbb5f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.40.tgz", "fileCount": 21, "integrity": "sha512-f0eonPUBO13pIO8jA9IGux7IKMeqpvWK22GBr3tOoSRnO5Wg5GEpXZU1V0Po+unpeZHyEPahrWbj5JfXcyWCqw==", "signatures": [{"sig": "MEUCIBPKGzdCZd5pI2iMuZ+e555wBIkgO+uqyX4fUF8gNtxgAiEA4CY7kvcRn7FUx9m9i9M1YgVy2i3It7Nimr2ujHpSv7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4070383}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.41": {"name": "@ai-sdk/ui-utils", "version": "0.0.41", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "411d6764bb154689834cf13487917a37df729e2d", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.41.tgz", "fileCount": 21, "integrity": "sha512-I0trJKWxVG8hXeG0MvKqLG54fZjdeGjXvcVZocaSnWMBhl9lpTQxrqAR6ZsQMFDXs5DbvXoKtQs488qu2Bzaiw==", "signatures": [{"sig": "MEYCIQDBLDyYK6yCQjBSnidV9L0rDOYfdwNuYsGpLX0C5jRKSgIhAMYZneGq4vJHyvOl+uiO4ip1Rb+zYeyMMq/ERU/QlP41", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4070542}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.42": {"name": "@ai-sdk/ui-utils", "version": "0.0.42", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2e82923bdc67e518f9f26c10b53fe9d28b2572bc", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.42.tgz", "fileCount": 21, "integrity": "sha512-NO84tR/hJWsK9siLYkPS9YCOm2aMfkijElNc8y8NT6WG2FwLddp05CxvCEHWKsKmOD+GWXEy/LczMtDh3xYEew==", "signatures": [{"sig": "MEQCIB7y58iWvAV88Kqku6Jxwo8tFltCS5mKf37pCYdJwF3wAiAv2LoEEu0bJ/GOQ0iJk4342r1z4e0CUVi3jOJtbCnEKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4072389}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.43": {"name": "@ai-sdk/ui-utils", "version": "0.0.43", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b365d64fed5133e7a76f51d6ae51e41d8be2d633", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.43.tgz", "fileCount": 21, "integrity": "sha512-58tHbE+TI19hDKbQDIosxQIhm3NYDe6ZBsG7DuQ+zDHpqLx4rxgLX0uvbfqLqiLxybhfapDEx+wIfMBdkgnVdA==", "signatures": [{"sig": "MEUCIG4hkG8A2E44N1SgleOQxTN76GPmX13yUFsDpoY3bQdoAiEA6+BwBQ+pof7UkK+KVtWSTi4tkI/onNjFlOLz2YJFwDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4072136}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.44": {"name": "@ai-sdk/ui-utils", "version": "0.0.44", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "04f46f5cdbd6b678e8b2b557df67c74f39ec630c", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.44.tgz", "fileCount": 21, "integrity": "sha512-0qiyun/n5zqJzQs/WfQT86dZE5DiDhSHJc7b7ZGLYvNMztHkRQmak2zUCZP4IyGVZEicyEPQK6NEEpBgkmd3Dg==", "signatures": [{"sig": "MEUCIDL0ZWtDzxJAOJuEW/hX/GSjvXyKZNricfoh75kccRPjAiEAxprPcdcg7UTLssBbGlsbLTWCzXn6Y6X+Xp6MYAVArbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4072234}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.45": {"name": "@ai-sdk/ui-utils", "version": "0.0.45", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e25f5be7bfea12a89da4736a093e05f91a943cd", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.45.tgz", "fileCount": 21, "integrity": "sha512-FDGT8kjAfOZogb8FE2+xv8ti/UibIbaEmViqXa1y1Epk97ES2LvCVWRs4gBd4IZylq8pLS2LTQtHj8ItlXL20w==", "signatures": [{"sig": "MEYCIQDURhsp13zaP+c0GMWAh2q4La3BOt8WZLmXN7uzaE7TJAIhAOzdUzomvGRys7SmnnD6Tb9T0ZJTC1ciniPyxmuDneuq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4073724}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.46": {"name": "@ai-sdk/ui-utils", "version": "0.0.46", "dependencies": {"json-schema": "0.4.0", "@ai-sdk/provider": "0.0.24", "secure-json-parse": "2.7.0", "zod-to-json-schema": "3.23.2", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "72311a1917a370074089cc6dd8c982d272f6b836", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.46.tgz", "fileCount": 21, "integrity": "sha512-ZG/wneyJG+6w5Nm/hy1AKMuRgjPQToAxBsTk61c9sVPUTaxo+NNjM2MhXQMtmsja2N5evs8NmHie+ExEgpL3cA==", "signatures": [{"sig": "MEUCIDddOdq47lh3J5JHS7SG8y8KPNt6YclBRFTsfrqrI2wwAiEA5OsZVq8MfP9DA+wz4N0fkzNMIyPqxLJLCvB70Xva4tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4073850}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.48": {"name": "@ai-sdk/ui-utils", "version": "0.0.48", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b06aaaad8a9e71d3e0bf862ffe12657a480a7908", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.48.tgz", "fileCount": 21, "integrity": "sha512-kKol0xTG4OFDqu77WqVVpPvpABXIXs67Ivlqg6qxD69pD/cFZ2zQVliBcDxPBJoz08Oyf5BxU5Ht9i8J99QKcA==", "signatures": [{"sig": "MEQCIE2m65JPtdsCMuvgGwyqR2vlowoml7VCOMeQODDzcQ4BAiAUYXHwSD/d4SQF0AIkVxmeIeyku30lg2REy/qSiRD3Sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4072029}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.49": {"name": "@ai-sdk/ui-utils", "version": "0.0.49", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "da29e61ef547da292ce81a22b1c7ee81f1968d3b", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.49.tgz", "fileCount": 21, "integrity": "sha512-urg0KYrfJmfEBSva9d132YRxAVmdU12ISGVlOV7yJkL86NPaU15qcRRWpOJqmMl4SJYkyZGyL1Rw9/GtLVurKw==", "signatures": [{"sig": "MEYCIQDI1WD6YzmySGUq56I1pbfBGmr9bd6xY0pQMtXBtm7q+wIhALW6XGZssw6BDzQWm8FSvnpPBSu0wcG2ZqmJp+xo3Bir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4074292}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.50": {"name": "@ai-sdk/ui-utils", "version": "0.0.50", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f396d24b5ac1e7a8090684a6d8de47282d0bad96", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.50.tgz", "fileCount": 21, "integrity": "sha512-Z5QYJVW+5XpSaJ4jYCCAVG7zIAuKOOdikhgpksneNmKvx61ACFaf98pmOd+xnjahl0pIlc/QIe6O4yVaJ1sEaw==", "signatures": [{"sig": "MEUCIQD7sfIU9Q0Lza2tub2U4Tzrn0YOgU56xP4g0bVFDG1ZCAIgLDo+N3HlhIqcIqD1pTiRGacGYs95dNJ6tvDE5XPwH7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4074682}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.0": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.0", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f9f8bccd44529ebf044faeb3640f07b6bab3666f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.0.tgz", "fileCount": 21, "integrity": "sha512-lWvbmIaR50UA0MEvUcRfIP8X2SNDRAXL26Xh38e6sClHL1mb5fV1cTqTNpaGAW1ICXO38YNmN7lin3PWO3BVIQ==", "signatures": [{"sig": "MEUCICZ89VQmppDXyHrQpTJ8k1aCvSnMn5JU6BoWCEQArOTIAiEAudXM68F0Fmn7aFs38Ueq3XthHTNtMm/AAV9iY41dJ2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4074796}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.1": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.1", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "74d95705e91b237d040caf087b86f05591c7f1f0", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.1.tgz", "fileCount": 21, "integrity": "sha512-a4S/DqrrWOZr3XbxXPCyLwax/9lQ0YSw5z1zyWLJxR7Y2zAFDyLS2On4NAIY6z3Lw31fSU/d62Pqwdpm1a8klQ==", "signatures": [{"sig": "MEQCIBjGfkl0M2wszsYjjjUUCnwxaTsz6+8EYqDViTfyufNIAiArNWvEGLLkuRj9D27Eyhj1Lt2CuWAyqqYN9/EN/4NXTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4003524}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.2": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.2", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "93fa22107cefb559a828f33ab9b507eaa7481fe0", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.2.tgz", "fileCount": 21, "integrity": "sha512-srd5828Qu+llNYcUuc+/+D0eKbbOuZaYTn3eLR3W0WWmK8RvsGWGR7ToBpTfGHueXPXXKeYBj++ThfrGwDYB/w==", "signatures": [{"sig": "MEUCIC7LB1nsRtv4R9Unu8TvKDzwFfIN0DbZsQHs5gav1tdKAiEAhk98N8q7LoS/IQ8PUmdPiWLCRCjAFchTt0QbNRIMnbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4004234}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.3": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.3", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "afd2c726dc5983318c413f689eebd378e68e7c48", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.3.tgz", "fileCount": 21, "integrity": "sha512-NUlqbF8QyOiP5Wmrbh7bEGhDpVVKqtZTjavev6xeFuf/GF7rt1Zr/TxSaRbxlMuWVwQLgmay/TRmhygnSY2Hqw==", "signatures": [{"sig": "MEUCIQCL/k+Nfi/N+CtjZgBJM3S2Pbr94zkgf+Q+e+nXTDD/gAIgSDz8nPCW7jZ/8pZVtcg+05jaIRhYkY3igw/98chhEqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4004348}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.4": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.4", "dependencies": {"json-schema": "^0.4.0", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "28ee8c5172e42ab8a6f7e5c8b4fab78bfb1e9b45", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.4.tgz", "fileCount": 21, "integrity": "sha512-04vuCBZ7OO01eLqzVTq/u2Vfj88CZuLz49Izz833OR1TNt8tbDDfbzu1CnSNf9ivNjmiJ9YoLHzIFohrapztpw==", "signatures": [{"sig": "MEUCIE4QKX+ieBODuV+iSaZWqQHhSqdTQsG8rMcSzNZvKWUmAiEAhYhg9li38ETWjqYYCctjXJMNegmrpr4Jtv6ezbt7BNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4004495}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.5": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e6e4064330ea7daab20f8734833d135afc43b40f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.5.tgz", "fileCount": 21, "integrity": "sha512-/+uxOLH9q6QCG/jKFwdWCnRKKM0eC+dt6/RAwOhe6yPSq5QXYi3gfFolKcItre+6IVKkWv52BnbrcqDHS4Fgvg==", "signatures": [{"sig": "MEUCIBkPsXtvN4XPa+UtHrVS3Ka4TzCN2jXlNbhLUmRWi6RaAiEAzl27s3O0GXCHTHwxW5eEAACR0Wo6HsR2o55pF959014=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4004460}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.6": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1599343042396345c995fcc148c867819bf59d00", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.6.tgz", "fileCount": 17, "integrity": "sha512-+Lp3gqR/o2F+uMqROk8DUUFUaNlNHnmj6Pomfxabu7ruL8KW+yvG9JFxDeKzdtn4FbLpXfEyZccPHVcdaTx8YA==", "signatures": [{"sig": "MEQCIFfAA1PHnmUyu2sUV/9F1HXTKPd1v4Zh+Yfwyu3jY8bSAiAnnPefkBFrabTY9qOZPFdVDDzxu2NcMHrTkImqbcnbHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332703}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.7": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e3af6812d16d80c1c46010ccd2f4782853f5e231", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.7.tgz", "fileCount": 16, "integrity": "sha512-jfn/NTR1sXYKntXiZ/7Cvy22deen0hisqc1hOL444rQBwWTD82QZ2zUCeB1fcDc+uMuGOyB1w4JFH+YMqW32og==", "signatures": [{"sig": "MEUCIBqKUki7wUfXKOnCZM7eaOOGcQ2TDiKCG+ZGpRnWGQvuAiEA1bMpHamlPiIONbP2wroVM8XZfyASWZu04SQEN1opFQE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 296179}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.8": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0215e5c79cbeac195b1dc77cd20ab6e8692cf40c", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.8.tgz", "fileCount": 16, "integrity": "sha512-yxVLJHxzWk8xXaLIqB6gF0WJLie0IaNqvZ6B1XHLjQ1pAC5BUJuckIcOsUMvQCd/m65tG2PymK5yII3hUDtW7g==", "signatures": [{"sig": "MEUCIQDBU9IYmnmaDmVV08BiZeTzQ2dhw+AtnIvgiLvW2BOQMgIgHzsWwzec0EUa8Hog2Uk0NeDm6pP5L/2NIY31DK/57hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 315542}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0-canary.9": {"name": "@ai-sdk/ui-utils", "version": "1.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "zod-to-json-schema": "^3.23.3", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "eda7c346ffc37c7c4e59b44999f91d95e0f33de2", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0-canary.9.tgz", "fileCount": 16, "integrity": "sha512-qbnY48EDbn+TBAI20pvMEYYFWXR7ip2grjwcneICIBHGdb2TEMLodgpF6SEv/FDTQ+Zd3Z2jOCkxcwjLL9lLpQ==", "signatures": [{"sig": "MEUCIFcvGXY4FS4MHAtFpTx+KzCncyBY2kqo4V2xbGZmmLreAiEA5UwSd0dEjenCrsxWeLRtMShELy9v5m+5Zbidn7s8HoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322078}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0": {"name": "@ai-sdk/ui-utils", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0718955195d81a986470b3ba40e39ba9ec163901", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-oXBDIM/0niWeTWyw77RVl505dNxBUDLLple7bTsqo2d3i1UKwGlzBUX8XqZsh7GbY7I6V05nlG0Y8iGlWxv1Aw==", "signatures": [{"sig": "MEUCIQDzbfNWZDTEAVYnFvp65+JGomP4ZcfW5Iyws/vdzYTW5wIgSEG8mSjOtJBBNPhPgAK9GvVTrIbUmo6dxaN/sPkxcNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322908}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.1": {"name": "@ai-sdk/ui-utils", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "238aac75928be764e09ebedc2234028c7d633e0b", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.1.tgz", "fileCount": 16, "integrity": "sha512-zK7yNixtCve8ng/8+9jUFyLvI+1dPzSHuyIM56p3EeXwJECRt6e8xyk9AZJNskhmDN6jP+qucP2rWlkX3ZQ2gA==", "signatures": [{"sig": "MEUCIEsi6FQ1Ou3DPfiOAB1zZcii1xtKafHA1+t8Gm/qQeUGAiEA9kD6PKXU5cn3UcJhZG5YtEUlJ0K2jsDMyvOTlXsXxA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323004}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.2": {"name": "@ai-sdk/ui-utils", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.1", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2b5ad527f821b055663ddc60f2c45a82956091a0", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.2.tgz", "fileCount": 16, "integrity": "sha512-hHrUdeThGHu/rsGZBWQ9PjrAU9Htxgbo9MFyR5B/aWoNbBeXn1HLMY1+uMEnXL5pRPlmyVRjgIavWg7UgeNDOw==", "signatures": [{"sig": "MEYCIQCa7WFONSs70yAPfsT8iXyPKEg72q7zfXCyr25a7NJ5eAIhAJK2eMZfKd77l5LsaPxsAZWG+/1jMg4AvCumJIiqsLP5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325214}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.3": {"name": "@ai-sdk/ui-utils", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb61c0fd2c46da27099c3190d43fb181f1d829b2", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.3.tgz", "fileCount": 16, "integrity": "sha512-UWU7j0gj9ickDm0RW06kUBVjdWVhYIo3xKCCLlrgXMFjao3WHSFWTILLI64ZyU72Xc9y1epW07m6PROnHkkApA==", "signatures": [{"sig": "MEUCIQDkdvv6BlhcPw8Zh5gjxu3c/6dMhvsCKS9th+ia60z78wIgQKyDaWlZ+TyjXm6+jRBBf3exBJuLp0o7NFU7KT2fh+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331808}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.4": {"name": "@ai-sdk/ui-utils", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7e4eaabb84e21828c468c3097999071e1a7c6d2a", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.4.tgz", "fileCount": 16, "integrity": "sha512-P2vDvASaGsD+lmbsQ5WYjELxJBQgse3CpxyLSA+usZiZxspwYbLFsSWiYz3zhIemcnS0T6/OwQdU6UlMB4N5BQ==", "signatures": [{"sig": "MEYCIQDwbiHD2OJJ4adbwBCV9haD1MjFhhj7LPhEZhPJM89LGgIhAP9ohyVZBN5Q6meiAZnFVygDP/I35SWckn4kU12WoE73", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331904}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.5": {"name": "@ai-sdk/ui-utils", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.2", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "096db17d070131851e5dc27dfe2131d658e00906", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.5.tgz", "fileCount": 16, "integrity": "sha512-DGJSbDf+vJyWmFNexSPUsS1AAy7gtsmFmoSyNbNbJjwl9hRIf2dknfA1V0ahx6pg3NNklNYFm53L8Nphjovfvg==", "signatures": [{"sig": "MEQCIB9HU1kv4ENiyJn+6MhMfqOjrTiaKFiDD/zebC2804QcAiBdFj6WD97ummGspkNdU5TSbZW/LYERImTyjQj1BOsweA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332027}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.6": {"name": "@ai-sdk/ui-utils", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.3", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9962b4db71675c79f7817f65813cdeebd4b6a23d", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.6.tgz", "fileCount": 16, "integrity": "sha512-ZP6Vjj+VCnSPBIAvWAdKj2olQONJ/f4aZpkVCGkzprdhv8TjHwB6CTlXFS3zypuEGy4asg84dc1dvXKooQXFvg==", "signatures": [{"sig": "MEYCIQCfE2moAUTZQn5npejjCHxzwed1PKm98KaTvBvitvjiaAIhAMx2wxgZkb7HZQ2NMSg8W1Bn1+eKfdmA8E7nO/mn/2FH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332150}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.7": {"name": "@ai-sdk/ui-utils", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ce23fab7f22affc47d4be4c8da87ace73fcd8415", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.7.tgz", "fileCount": 16, "integrity": "sha512-W9y<PERSON>lrnSF00zo2qDSsrooPEcKsAGIuGzHNRfkivbwjHj7ZuHl1tLNpvmAWT59sN8aeN0sM4iwa68OjT2MygqSA==", "signatures": [{"sig": "MEYCIQCv8bbWj3Wyp8T0C6Dk79bKcKupsMLIbp5ULxZS4EsM3QIhAO+j5NMW1K5OWbwhSEkFK8S9XWcrY7DDiDFpr/0yDYl3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332339}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.8": {"name": "@ai-sdk/ui-utils", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.23.5", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "570d3d697f1f73666738b084457b9f4412f1e7f0", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.8.tgz", "fileCount": 16, "integrity": "sha512-7ya/t28oMaFauHxSj4WGQCEV/iicZj9qP+O+tCakMIDq7oDCZMUNBLCQomoWs16CcYY4l0wo1S9hA4PAdFcOvA==", "signatures": [{"sig": "MEUCIQCC1pnX8eNq4qKefzV/l4lBOOduKpqYhYNwIcGeOtWvZwIgTLu/VVRes9I+t/gN6W3jyBx2NXlUOPkaK5mPjnzBlbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332501}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.9": {"name": "@ai-sdk/ui-utils", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2d39e4574b53839b8a2b868b2b7976811cb5c2d8", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.9.tgz", "fileCount": 16, "integrity": "sha512-ULJ+TTCVk+iW5asdKjG33vEfMmalaE9rJOlddVE5t6729Fjow1a1COXRTwE5y1r1qU2Rt+YiXQcTTA5ovx0SMw==", "signatures": [{"sig": "MEQCIAHsFbqxQrvx+CIgV/arCmBSkVP/30OcdGQfF9Llb5TFAiAsFykn6IsZlvYf1SyggUQP/Rveop3uMyrj2iAHFXaocg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333676}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.10": {"name": "@ai-sdk/ui-utils", "version": "1.0.10", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5fe2ea6bac0d72ac82a18c83b839607192027ed9", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.10.tgz", "fileCount": 16, "integrity": "sha512-wZfZNH2IloTx5b1O8CU7/R/icm8EsmURElPckYwNYj2YZrKk9X5XeYSDBF/1/J83obzsn0i7VKkIf40qhRzVVA==", "signatures": [{"sig": "MEQCIGnkA1cN4jp1YyceWCG5cnLbCBq96dsLAkKOawGY8qntAiAekKAEIr4n+k8rmEdpzaDykvMTKTxD4QKW1Rp+7+BdzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333800}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.11": {"name": "@ai-sdk/ui-utils", "version": "1.0.11", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6a0ce88bd96c7b23c79dc2100b68c99a16bc2bd6", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.11.tgz", "fileCount": 16, "integrity": "sha512-hbC3eSw42zbl+sRUxuvxGr9Tzx4MY7Ln3s2FTU2t4s4rcl546Xdq36pzM7k8ZQUDFMzfVfjLYqtM4Gfnno2MlQ==", "signatures": [{"sig": "MEYCIQC1VsSXTfgoxlFMT0hl4UXAUoNA32y/iIeWkCpEeVGykQIhAM+evI55yx3Wk0jV+W9Y2AjYBXeKTq7JE/wB0EPGTdve", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339832}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.12": {"name": "@ai-sdk/ui-utils", "version": "1.0.12", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "580c77feccc4c659261b473137f37923e7b3912e", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.0.12.tgz", "fileCount": 16, "integrity": "sha512-cZyiyMZb9+AIaV9+rFyJsu4064TCi553y42sLdknCPf3TVkRKKQs4nngA72zlxXeOO1gFusbsFjVgschcVJL7w==", "signatures": [{"sig": "MEYCIQDECnGr+XtUPtCCRkEUk+0CKShWv0cwckodymTu6Or4iwIhANRRneDn0QBNaopxwnY7tVWgpuEjI/Wky1UoLfsgPAvi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340062}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.0": {"name": "@ai-sdk/ui-utils", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4c31029bae0d06fd2c2530dd214eff434c5a3787", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.0.tgz", "fileCount": 16, "integrity": "sha512-ETXwdHaHwzC7NIehbthDFGwsTFk+gNtRL/lm85nR4WDFvvYQptoM/7wTANs0p0H7zumB3Ep5hKzv0Encu8vSRw==", "signatures": [{"sig": "MEUCIDX2Exql6HmvswWq6bVubPuSzdf0INCtcR/uPzuRG951AiEA1wgc5PrD7sqvq1anJT2TpVKNmUqTRRTGIg9ExSmJOGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340208}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.1": {"name": "@ai-sdk/ui-utils", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c50db0e1c05eb3f0410448171aa8a435d2df42a8", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.1.tgz", "fileCount": 16, "integrity": "sha512-lkTxGoebnEgs8HtKeWut0AglXN7zpWQwYmun4yuhpiup7DxPWTmt3vGiYvqQTBOFAmyoea3uzIKjHwRHuayr2w==", "signatures": [{"sig": "MEQCIACTAzDnUnplkcfLC3Z8BPorctX3oZ8o+iWAeg5HrwczAiAKRnuPeZRHDl/OxywuZ5CDf76YblxH+X8MIydWhIcs/Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344317}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.2": {"name": "@ai-sdk/ui-utils", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b082f59ef18bdfd779fc7dab85328c49853bd412", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.2.tgz", "fileCount": 16, "integrity": "sha512-+0kfBF4Y9jmlg1KlbNKIxchmXx9PzuReSpgRNWhpU10vfl1eeer4xK/XL2qHnzAWhsMFe/SVZXJIQObk44zNEQ==", "signatures": [{"sig": "MEYCIQCzFhPMhRpxySJlHrnjkHQx6MlpCgqlh7KJe2OyjDRtPwIhAJqlcGaHw0mZmARJHnJPCP2cQAaXbH+5sE8PpBsITnks", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 344473}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.3": {"name": "@ai-sdk/ui-utils", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "76a39ff4b99185785638868ff178785f73812cd2", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.3.tgz", "fileCount": 16, "integrity": "sha512-DczePOv7SxXNY7IkHo4dbN6DT68vmDLCm5uJNUQy0eVkF6cfTbGmScgU36UQ63v7ggUTGffexOo0aB3eJOdFcw==", "signatures": [{"sig": "MEMCIAPmVllruVUffRqZplqI5KYbxWhfPaTbjPeuByxX/qDfAh9SCs2/prIk+guD3Z/RdCeO8qFNcDdAt5EX29Iv3Jr/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 345949}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.4": {"name": "@ai-sdk/ui-utils", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "712c8ee22f30ec204d0d26989072dfc000d697aa", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.4.tgz", "fileCount": 16, "integrity": "sha512-bqWg0d5Hcw1sZbWBrrm9XBbCuuiHDtBE6hWaOYBuOy90tGLFE0n21ku110dvoVNjKW2rmpNGEipgwi8f7BCVeA==", "signatures": [{"sig": "MEQCIBiLvFz882Xlhmx2PGgLTKv+HDzK2ORr/eyNLRThslCSAiA5A1uV5rBeyPFhfQEJXpgxnva4MXYllX970bWH3HJVXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 346045}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.5": {"name": "@ai-sdk/ui-utils", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e9c992a3b116c91757946ced30eeb3e4844fa5cf", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.5.tgz", "fileCount": 16, "integrity": "sha512-N4/YyDxJ7STcUeE6qv48Rgihly33yMIBeXtADuPEtuObofUcVAEjHSeWWSNuY780m2nocrjOV34XELwMDmZ73w==", "signatures": [{"sig": "MEUCIQCQ+BErxcC5QyuuzDeWmRvcXRtwktXHYpZT+lUTwdZckQIgZaU6u08hoaOLxMTjbYKV+jTfrQ+NVyJD6kuRs8Cl+Xo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 346141}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.6": {"name": "@ai-sdk/ui-utils", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "285f59f6e9c6d3f10c0ab9c5a5860a1c03c9514c", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.6.tgz", "fileCount": 16, "integrity": "sha512-YAwZhFwpIcvWERIjkET2o2MAwMFfJG18WdtcIjtxxMW7hA0bt5cliOV78DVcwRrxqJ2IKBlxaFmwUjW6M4SdOQ==", "signatures": [{"sig": "MEUCIDiZJ5aZuBKcGLw9PdiaPZlipfEgFHEtMbpOdzxahRHBAiEAiCH2bbjF4cFm0MdSYLiYG7d6KskbhbwXX5QFAGT/pG8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 346238}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.7": {"name": "@ai-sdk/ui-utils", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.6", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7ea16f806491cd2d6f991813d410c4989c527a79", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.7.tgz", "fileCount": 16, "integrity": "sha512-S9Q9rCvMPUe/RP5gc1kSn6yLe/4CFrvLP7H47Dcq5c0z6NEYDySL+ycecB6V2eRyxqxsVneL2zOvX6aubDhz1w==", "signatures": [{"sig": "MEYCIQDvVDAQmjEeSnVQPXApwxjilTNiT7vxsgo04pMYDh2OkQIhAItmHQrMvITQAXwMGwiQeMzSpM8sQc7KChpuJP3rMHpt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 347548}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.8": {"name": "@ai-sdk/ui-utils", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8937ff092f48ce5421e27d1d4ebe09ced69b0ef4", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.8.tgz", "fileCount": 16, "integrity": "sha512-nbok53K1EalO2sZjBLFB33cqs+8SxiL6pe7ekZ7+5f2MJTwdvpShl6d9U4O8fO3DnZ9pYLzaVC0XNMxnJt030Q==", "signatures": [{"sig": "MEYCIQDQMcx+Oya9YSTPiYTeRjCsf7ZhbXIeIO0EzLqdNciEbQIhALKxBwOIXU12ECiUDCVwYbqyb1B7RbnpgjvQbDkwn7CJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 347671}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.9": {"name": "@ai-sdk/ui-utils", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "37d9125c359c5de85026a9c5cb17b42e81d9dcd6", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.9.tgz", "fileCount": 16, "integrity": "sha512-o0tDopdtHqgr9FAx0qSkdwPUDSdX+4l42YOn70zvs6+O+PILeTpf2YYV5Xr32TbNfSUq1DWLLhU1O7/3Dsxm1Q==", "signatures": [{"sig": "MEYCIQDfSfieNsempHQ3cDG2NFS4pgrXSiMIJmHKUVN7Kk0biwIhAIG5K0yw+HAGVXeTk8cpUFO0hfaFVyqsJfnxTKWvTckL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 349404}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.10": {"name": "@ai-sdk/ui-utils", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "694689e371c1f79005d8a0651d4e49d2b7586004", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.10.tgz", "fileCount": 16, "integrity": "sha512-x+A1Nfy8RTSatdCe+7nRpHAZVzPFB6H+r+2JKoapSvrwsu9mw2pAbmFgV8Zaj94TsmUdTlO0/j97e63f+yYuWg==", "signatures": [{"sig": "MEQCIAr0q8JP/ymZcimtbrLtiXPTUuk9pskoVgqZIOzsia2MAiAlxs/THR1RvOfDrTTL2XkbiyhFzYgJLWYlQ+1h+J2PUg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 387099}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.11": {"name": "@ai-sdk/ui-utils", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef49a85d4c9c8c8962f3e804fe440616af22377b", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.11.tgz", "fileCount": 16, "integrity": "sha512-1SC9W4VZLcJtxHRv4Y0aX20EFeaEP6gUvVqoKLBBtMLOgtcZrv/F/HQRjGavGugiwlS3dsVza4X+E78fiwtlTA==", "signatures": [{"sig": "MEUCICFLM+Jnw7yVFP0v28EwknsqNbeCf6VmBco2Hc9wCGPNAiEA139ikUtm/PtVJDM1d4ZCAIECpjwCXLxAaCAxQiVxaYo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 387338}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.12": {"name": "@ai-sdk/ui-utils", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "22473e5d467ad7a0b84d3b97994b275981a1587f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.12.tgz", "fileCount": 16, "integrity": "sha512-2whKOSuCYg0PJG5Twuw5QIpikzv+0AH9k7gxjFg4syn2KB4qBrsxgGQFy4eTuxuHc1NrSQ0dObMh4K1nC4oF4A==", "signatures": [{"sig": "MEUCIQDK+jJ97cObB+Q81MeAEJsNyhyFktkTSj3PU/a5nDnHkAIgO5NSeXxt4Og26UGNWa4WxrHYvn/jxfJW694bPAf804k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 380508}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.13": {"name": "@ai-sdk/ui-utils", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1cd027dadc2f728165408fe5dd308ede1740dc46", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.13.tgz", "fileCount": 16, "integrity": "sha512-79NmeAugHibjKxZDOTwkkmqsC5AgPDfsxEP5pcXF4dnyPIPdPaR3uESIyJ6W3a919/DCasnJBvYwV0h5rhc5fA==", "signatures": [{"sig": "MEYCIQCcfmoBj1o84yZPVuNlgD9be7wKs2M9K7ir45Akw2OwTgIhAILBo7d7eNbdVf5UTOl4c0TlFJ4JsDNOvkXjQwkzYWl5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 380605}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.14": {"name": "@ai-sdk/ui-utils", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.7", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "64f26d94a413b1fa2b8684cc5aada5ba8cb5ea15", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.14.tgz", "fileCount": 16, "integrity": "sha512-JQXcnPRnDfeH1l503s/8+SxJdmgyUKC3QvKjOpTV6Z/LyRWJZrruBoZnVB1OrL9o/WHEguC+rD+p9udv281KzQ==", "signatures": [{"sig": "MEUCIEOGVkpTIMOt9lNhgm8Fc8vfepf5ylKlrBOOdzaJ87XTAiEAkmvUm4k1bbF1DJbzVbJorMdFoGKiSnlt0B1XhBse/oQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 380702}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.15": {"name": "@ai-sdk/ui-utils", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.8", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "13ce822c008237abd83a65362b13b04fe39d311d", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.15.tgz", "fileCount": 16, "integrity": "sha512-NsV/3CMmjc4m53snzRdtZM6teTQUXIKi8u0Kf7GBruSzaMSuZ4DWaAAlUshhR3p2FpZgtsogW+vYG1/rXsGu+Q==", "signatures": [{"sig": "MEYCIQDg5JQdIcOIOv7DgmXl7Qg8AcTSimJxe0DhUuEXk+5zdQIhANNInXmFcgMYLhtkodYICoOdPygrs7/7E8sfOttI7//o", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 380826}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.16": {"name": "@ai-sdk/ui-utils", "version": "1.1.16", "dependencies": {"@ai-sdk/provider": "1.0.9", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0159425d41b7737f406103174e20c0843f140d2f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.16.tgz", "fileCount": 16, "integrity": "sha512-jfblR2yZVISmNK2zyNzJZFtkgX57WDAUQXcmn3XUBJyo8LFsADu+/vYMn5AOyBi9qJT0RBk11PEtIxIqvByw3Q==", "signatures": [{"sig": "MEQCID+wcKULDKToJi2TzpsM6OhgxT8LXKDjoTu61Sq69vFvAiBwljz29wa/MoK77Qi2Z65iAZETeieBF0iukkbuXkvIOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 395930}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.17": {"name": "@ai-sdk/ui-utils", "version": "1.1.17", "dependencies": {"@ai-sdk/provider": "1.0.10", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "937788d0da2079e1f9dde83ac2be994897210458", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.17.tgz", "fileCount": 16, "integrity": "sha512-fCnp/wntZGqPf6tiCmhuQoSDLSBhXoI5DU2JX4As96EO870+jliE6ozvYUwYOZC6Ta2OKAjjWPcSP7HeHX0b+g==", "signatures": [{"sig": "MEQCIGntBqgnWrRFDeUqUQGgi9Y36CF/FYpLA3kTM/cUruVRAiAai4+MUjCHiaGIy6eKvwCqty9cEuwygmMQZjohaShmIQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 396195}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.18": {"name": "@ai-sdk/ui-utils", "version": "1.1.18", "dependencies": {"@ai-sdk/provider": "1.0.10", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b9df2a3cabe67c5c66b0919920388f375a48a140", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.18.tgz", "fileCount": 16, "integrity": "sha512-PCDXKKKHqA8Oqm5LsXl3Byxmit0r0Gg3nMPI3bdEriDIExoQikULX2T6/IS5u1qNeoC3UK5F2acpCyl5Q+aIuQ==", "signatures": [{"sig": "MEQCIB4Skk//gu/A8CHwwRd/k4pvrb7spZphgIUbIQ8T6BW8AiB5aoW6oEAG6rdjH+d5oC3Rwccd8bx7+FwUrJTetzUW0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 396293}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.19": {"name": "@ai-sdk/ui-utils", "version": "1.1.19", "dependencies": {"@ai-sdk/provider": "1.0.11", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "66356a2d18f78ecfcfd792c3bb07bda493b1b346", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.19.tgz", "fileCount": 16, "integrity": "sha512-rDHy2uxlPMt3jjS9L6mBrsfhEInZ5BVoWevmD13fsAt2s/XWy2OwwKmgmUQkdLlY4mn/eyeYAfDGK8+5CbOAgg==", "signatures": [{"sig": "MEQCIFiYxRe1bo0PqZwSbsDQ+KJj45LlZnnjFH+WYC1avSlQAiAQMiZm7Ps/B33jFhtytQS79m/ikyIrfULvjzFyPz3KVg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 396419}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.20": {"name": "@ai-sdk/ui-utils", "version": "1.1.20", "dependencies": {"@ai-sdk/provider": "1.0.12", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "040966e2e0894e9b893682c2b3fabb932b86c2eb", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.20.tgz", "fileCount": 16, "integrity": "sha512-k+qQh1YA8/kH12d8KQFWjr0UxyS9nLibzbLr2OY6fzpfZg8xIAKlZeH952JIp2XwAp72tesgbsAmA15cDldtyw==", "signatures": [{"sig": "MEYCIQDJp12RbyYP9Z6UI7DCZphFUjgL6TkDYpGKeNsdNR/hngIhAJCi57sg+i6uk9qdknru5tClcrKaDGReVxpv/mzh/2Ns", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 401877}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.1.21": {"name": "@ai-sdk/ui-utils", "version": "1.1.21", "dependencies": {"@ai-sdk/provider": "1.0.12", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b63b1253502a65f9fa5ad066582a4c520820faef", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.1.21.tgz", "fileCount": 16, "integrity": "sha512-z88UBEioQvJM6JsBoLmG6MOholc5pDkq1BBeb53NZ7JmMeWX4btCbrGmM4qs+gYLDnZokV/HB8C6zpS1jaJbAw==", "signatures": [{"sig": "MEYCIQCbCyW4E3KDAO7d89E0PUi74XS/rbsW40lFerS4FsTOkgIhANjMu9WERdMFxm6z/O2TlrgLTVsjr8wJ6rqJDJqtKf+1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 401975}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.2.0": {"name": "@ai-sdk/ui-utils", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "1b95b80aa2f837fffe0311537cc4b56799626f06", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.0.tgz", "fileCount": 16, "integrity": "sha512-0IZwCqe7E+GkCASTDPAbzMr+POm9GDzWvFd37FvzpOeKNeibmge/LZEkTDbGSa+3b928H8wPwOLsOXBWPLUPDQ==", "signatures": [{"sig": "MEYCIQCgB6XAssR4WGmj39Rsk2yYdQQPVFgldQIxl2k8LQSJlQIhAJyEUouMUXXBVIIrFlEE/y+NhFJU0G3rRKz4Wx8lVXoy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402068}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/ui-utils", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "5d3719ad19ce6f993cd1df6c0976f10c4352706e", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.1.tgz", "fileCount": 16, "integrity": "sha512-BzvMbYm7LHBlbWuLlcG1jQh4eu14MGpz7L+wrGO1+F4oQ+O0fAjgUSNwPWGlZpKmg4NrcVq/QLmxiVJrx2R4Ew==", "signatures": [{"sig": "MEQCIFrbEe8SLAD6XorfG7Sqh3eXG5yVV99BALGbauvZPlo1AiAT54z5LwuGsI7HHgT8mvQy+vN/KlzatGdUcxQtwymPnQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402164}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/ui-utils", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "9db4aa46a9f90a856a1f2e51ea26a48308aca5f9", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.2.tgz", "fileCount": 16, "integrity": "sha512-6rCx2jSEPuiF6fytfMNscSOinHQZp52aFCHyPVpPPkcWnOur1jPWhol+0TFCUruDl7dCfcSIfTexQUq2ioLwaA==", "signatures": [{"sig": "MEQCIBWHIhlsIIT2JBE1bEiLmHCi7UYI+chXhzjo+3tzvHIoAiAaJqifsakuuYfwH7c3qtqGAag16hgA+BWTTKuKImulyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402304}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/ui-utils", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "54c4cf2840912e97326d648e039166ee409aeb8f", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.3.tgz", "fileCount": 16, "integrity": "sha512-vUr+A344eEPeCZhBxuTE83On8bijQYdwbo1nfo9GvCPxfeCo5gy1XCl150je9XUe/WPw/Qvqe1OKjnijj30BBQ==", "signatures": [{"sig": "MEUCICAXXnyZLS/Bq2O3hJLTYHZkZxVC0Rv+Dr/OXtw4nFAvAiEAkzGXPKBkVv7B5HIJvc6exz04fbDfaClSTZ7zg6xAbkI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402400}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/ui-utils", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "2b5b580d4f878edb53c69148b5bfce6a979b30a2", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.4.tgz", "fileCount": 16, "integrity": "sha512-wLTxEZrKZRyBmlVZv8nGXgLBg5tASlqXwbuhoDu0MhZa467ZFREEnosH/OC/novyEHTQXko2zC606xoVbMrUcA==", "signatures": [{"sig": "MEUCIB+VaJnbUTSe1tWI2TID4NtDP0ebxQOlGW+LeWBxmH3fAiEAgT3Oqa3OrXU8P9PNwH+I9gNA+fx2CxpoYYmqLTf2twU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402496}, "engines": {"node": ">=18"}}, "0.0.0-9477ebb9-20250403064906": {"name": "@ai-sdk/ui-utils", "version": "0.0.0-9477ebb9-20250403064906", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "19513bd1b2f9b428951bc4546b1a286a74ba80a5", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-0.0.0-9477ebb9-20250403064906.tgz", "fileCount": 16, "integrity": "sha512-c03ES844/2oIXuLbCU2up/5op73DcvusyuVsXOBkBlARler/SCbTigAG7Bm82HX8R0hVzRjTXGOgsOxuaeuS/g==", "signatures": [{"sig": "MEUCIGviWcRubbQWELGCPaRRw/uUsQ4riSQ4IPI99cYtX/sKAiEA05dT0ZBbFXOrXHhNiwWe6yZErI59+v0JE2BES3SK+vE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 353727}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/ui-utils", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "d1dc0e150d96e9abf35c2c5db9b5857e0952a642", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-2.0.0-canary.0.tgz", "fileCount": 16, "integrity": "sha512-LSY8+zTIE+N6zgWLTZW6EXa384RKbM5nsU/R7v1MAShlSUnXnzVq6oBVJVku2NIyzTpk/kRZMnT2RdDTzNu1mQ==", "signatures": [{"sig": "MEUCIQD2tOz6zcbsOb2a8IX1gb0u5C2vhPyLr6LMx/lpyKb7cQIgTOAj0GpYV5nHMGsuKbq94Xrlfm+T4ftF7jNy5LLrFBo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 353866}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/ui-utils", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "472a11d06863895d39af36036edcbec6ab5f8fe7", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.5.tgz", "fileCount": 16, "integrity": "sha512-XDgqnJcaCkDez7qolvk+PDbs/ceJvgkNkxkOlc9uDWqxfDJxtvCZ+14MP/1qr4IBwGIgKVHzMDYDXvqVhSWLzg==", "signatures": [{"sig": "MEYCIQC+bf8CXvvL5Q88Gkt+fmH4Vbfr+lMRlAzG8YUiamEW9wIhAKoQDdC9LrNFR+4VwtaKP66HIJJohlsZaJu+IZCHm8VC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402592}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/ui-utils", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "b80bff233a1ae94a7cd7ddd3b3492c7c3c67daec", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-2.0.0-canary.1.tgz", "fileCount": 10, "integrity": "sha512-kOfxKtML6s7AQSfO7b/2v3QcdSJrEeQfcKpHPzzWUXxe+AmS02xtkCD42sgvo9k85dcUSZmL8Dg8Jv8mz4XSXQ==", "signatures": [{"sig": "MEQCIDXbAXDvt4nL1FpDBI2NkOB5D2jeA7clMJFUzmodVYIiAiAFHdkEoX0h+LTJNhZBCF7u/Z+ez0UMQes71xhLRR5+DA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 336116}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/ui-utils", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "c324fa0c0e9407273b1ef3b73a20841779e816cd", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-2.0.0-canary.2.tgz", "fileCount": 10, "integrity": "sha512-UFOmBCvAoMPn89S1M8JkbKFbjDQp6mMLZfnUdxPYJKbCjFOl81vEogXcVpDSBCgH0v3g7AI4b4MDPPNq5MN+eg==", "signatures": [{"sig": "MEUCIAPFywGaao5Wqior6RhCMLh68DBrR8Jd+/K+ZTsFG4DDAiEAueVsf4Gg1j/13V7K7znxGME4xq0iWV0jFddAEKQaAQg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 336299}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/ui-utils", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.1.1", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "39c7ce286aa84bb2c7f9f03523db2de433ae0aed", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.6.tgz", "fileCount": 16, "integrity": "sha512-jO1tr6wtQKzHUEHq49ev1Hf66rGHFpEAbGLTJfLPkDH3a7O052oFGTvNloRo26gzsr0906kgGNelTcenFMH87A==", "signatures": [{"sig": "MEYCIQDCG+0bydK8CPzBBIXYvNRurQEFSuJWE6rylAe/spzQrgIhAOupp8NxlNEreI0S6dXV823d1EAzAwx1qAp2/AyTX34L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402715}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/ui-utils", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.1.2", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "899d2ea2f6fc16bf8b4bd8329d9946e4f6eeea54", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.7.tgz", "fileCount": 16, "integrity": "sha512-OVRxa4SDj0wVsMZ8tGr/whT89oqNtNoXBKmqWC2BRv5ZG6azL2LYZ5ZK35u3lb4l1IE7cWGsLlmq0py0ttsL7A==", "signatures": [{"sig": "MEYCIQCEDteSSdwFLpJu0VG1x6K36JRC6CaRJhj0bPGNVfvo0gIhAMX5oAnIT4IWcjEJcXenBUULi/LXOi+9I+hsePNXf5p7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402838}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/ui-utils", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "5152a7942cf655350076bb5aa5bca81ff73a9068", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-2.0.0-canary.3.tgz", "fileCount": 10, "integrity": "sha512-8jhUWAhqrg9uxDF2cH/vP6ImOjcoBhcsH6i+Ra5meZ2ucwg36tiNy0HPkXEyNseu1eDjAQdiqYidlVoF4rXuDA==", "signatures": [{"sig": "MEUCIQDOTU/A9Em+PRfMvjXlgt4smYK2AzCr/Y6tk6JcXB2lkwIgT4QFS7olnG8rTUCbtDeMjnuxkg/0yVI1ZUtbhmfF12k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 336959}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/ui-utils", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.1.3", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "2e65e18a8d23bd361885aa1b26abe9a1e30f4e15", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.8.tgz", "fileCount": 16, "integrity": "sha512-nls/IJCY+ks3Uj6G/agNhXqQeLVqhNfoJbuNgCny+nX2veY5ADB91EcZUqVeQ/ionul2SeUswPY6Q/DxteY29Q==", "signatures": [{"sig": "MEQCIHXolreb1yYZ+fUXQy7K3RPDyg98iybkrTKUhp9/UcVnAiAs6vzqKifLnQkEgA8o0JhxuYgDa/YBdcPgJ2Di0j/0WA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402961}, "engines": {"node": ">=18"}}, "1.2.9": {"name": "@ai-sdk/ui-utils", "version": "1.2.9", "dependencies": {"@ai-sdk/provider": "1.1.3", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "35bc97759f86ad45fb849c05090432aed58ed732", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.9.tgz", "fileCount": 16, "integrity": "sha512-cbiLzgXDv3+460f61UVSykn3XdKOS+SHs/EANw+pdOQKwn8JN7rZJL/ggPyMuZ7D9lO3oWOfOJ1QS+9uClfVug==", "signatures": [{"sig": "MEUCIQCNTRUIJlohaVo7w6SMMcn+l4kkd3r2PCg9nDZ5s6vMKAIgfkfSI65l6XTAKqyt7u3S1JZGKrDNbfEXNGgwY19jbMo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 403550}, "engines": {"node": ">=18"}}, "1.2.10": {"name": "@ai-sdk/ui-utils", "version": "1.2.10", "dependencies": {"@ai-sdk/provider": "1.1.3", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@types/react": "^18", "@types/json-schema": "7.0.15", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "445806731a7189c7593d8d4e984e44fc6395426a", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.10.tgz", "fileCount": 16, "integrity": "sha512-GUj+LBoAlRQF1dL/M49jtufGqtLOMApxTpCmVjoRpIPt/dFALVL9RfqfvxwztyIwbK+IxGzcYjSGRsrWrj+86g==", "signatures": [{"sig": "MEYCIQCqXRHIzj8QFfMlbaTeSX3bHLT++/0IrWY7IW8JRf0AnwIhAN6I1QLtYkBBWtq8+1ZxUF+xjKQ6KDvB06QTxAmErpSo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 405775}, "engines": {"node": ">=18"}}, "1.2.11": {"name": "@ai-sdk/ui-utils", "version": "1.2.11", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@types/json-schema": "7.0.15", "@types/node": "20.17.24", "@types/react": "^18", "tsup": "^8", "typescript": "5.6.3", "zod": "3.23.8", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"integrity": "sha512-3zcwCc8ezzFlwp3ZD15wAPjf2Au4s3vAbKsXQVyhxODHcmu0iyPO2Eua6D/vicq/AUm/BAo60r97O6HU+EI0+w==", "shasum": "4f815589d08d8fef7292ade54ee5db5d09652603", "tarball": "https://registry.npmjs.org/@ai-sdk/ui-utils/-/ui-utils-1.2.11.tgz", "fileCount": 16, "unpackedSize": 405869, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIF5ybPpIYf/k+Ai45s1sQQ49TqxFlOaU51ScQhotBvFSAiEA8CLVC8o5mmBJTtVssTeG1+aipteUNzBcrOTKBBqJrDA="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-07T12:19:25.394Z", "cachedAt": 1748373703074}