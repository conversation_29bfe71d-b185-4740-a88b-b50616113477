{"name": "@ai-sdk/anthropic", "dist-tags": {"snapshot": "0.0.0-fbda7b18-20240815003233", "canary": "2.0.0-canary.19", "alpha": "2.0.0-alpha.4", "latest": "1.2.12"}, "versions": {"0.0.0": {"name": "@ai-sdk/anthropic", "version": "0.0.0", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.0"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "workspace:*"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "109aef09355971ba9907010d593e0f87e5f3480e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.0.tgz", "fileCount": 8, "integrity": "sha512-PGKKZkGaRa6TOLU/mgVVa/RUklyWI/aBcdI+BiFvqH/r+Ui7qEhmlQrkAUmUOStvYmwU6aKhabhIz2jn7kQrLQ==", "signatures": [{"sig": "MEUCIQDUynKpVs28DhQMU7aCkEGbDmKJhcfXcHqzLEOXwIX+dgIgFUIlxU/PEdxTt8LiZ3CxcS8lxmAz3ruptdFYMHkIXsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81285}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.1": {"name": "@ai-sdk/anthropic", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "91afe670057793f2310ae028bcd76d85f5e72377", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.1.tgz", "fileCount": 3, "integrity": "sha512-8qbfGM9OaTvqFS/R1pmsZYiONBXSZvr2ZXsaA7iqHu/n7xHjSs/NL8GErUewj0sxEOD3SdL2ftBgIrn3V+gqUQ==", "signatures": [{"sig": "MEUCIGE1aatU5h6xaoUAJT7zmBTIno+WjZmXTaGBF4Q8s8WXAiEAka/S76++bhm7IJfBTT/lD0V6XKz9YseGhQ+na+FBsmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3763}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.2": {"name": "@ai-sdk/anthropic", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5ea6a024521067418ae8a0f9e718e26c664e47a4", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-BAYS8BDnELghTUFByz9nb25jfEguY7SY6eGXFEm80Lf72nHNFiZ3KzIfA+l1HPHHr9trZuZhOXnfWA1pcOnuFA==", "signatures": [{"sig": "MEUCIQD75jTgHvUdNVOW8Mbh3XfPYM3UnI5vBd3/qaOfjo/AawIgZWe8hGYnz0BUmLc/ENyJVDz3qLLhP2lK5F+TGdDAsww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98139}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.3": {"name": "@ai-sdk/anthropic", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6e155199e2ba9ffc5a998087a74be5f04bb7d48d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-cAXuMjQ37t0uAD3Yp82jk1OvXHSTNMNAzYrT/OeJXVZkL80sILI9qnhKflMbLXbYddvL+3XC0HBZ9IhyOf7JoQ==", "signatures": [{"sig": "MEQCIE//iIs3DdpWNvIJ+BT5rUiXdRl59wIs01dx1gwsR0/0AiBMCHeWZQ5NcE6v9fyHeadylj2rpQ6mwjRGgKzzGn+5nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99456}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.4": {"name": "@ai-sdk/anthropic", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "807ea26310b9e5754a8d67cd85b1ab079ad32fde", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-QE2IAik6j5uEcyr4RUOehavK5pefIGeLwY8L9GP9F2R2oFpKUK9BO1QLa7J6Nib/e1Ts3fROCy66cSsSMsRGjw==", "signatures": [{"sig": "MEYCIQDroQsLJUG5SJ+Sic89wSWRQUQWb4AC7NiaarA7RuWj9QIhAN+iH4OZN1oJHId+N9n3WkZEvR60XZfDXLRj1jbVp9E5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106958}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/anthropic", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3bd92ab68c44554868c76a56d46426de223f2b96", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-anQNRJdY/FKZhtSY1v1X4mcj2M6Tg0x9mxasO+Y52eyfLjBzP2kaUXSMU8bEMbaj+vyqRfQcFFEdqmY2zXDOIQ==", "signatures": [{"sig": "MEYCIQDeBNCU2fiJRjYJKxklx1bFZSekq9eWALPWUWsOtWrC5wIhALJGEFAoYmZWDnOlpV30O8RtIEDcaC2TYO8EUFLLGGTh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107119}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/anthropic", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "0.0.1", "@ai-sdk/provider-utils": "0.0.2"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "91380517cf7feda1993677dbd9b7826b2e91dffd", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-6Lru0XssvlLqVZ2NtLOHzhj0JF0NWXAN9491nlxt7XPnRma9F/y+ihLqtqBbd2bSXVJ7G0FLMG0Bjm5fIIzxbQ==", "signatures": [{"sig": "MEQCIG1eGdoVrkDw9rGKFMPI7OrxFaTFGoBi0FeikWfxb2RDAiBg/TAyeqjyXaDODpFGHUKrSU85cFIGiXm8+T8FI3JZQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106996}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/anthropic", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.3"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ff807a0e87c64c182bd135175c8154e3d0692674", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-/7VHAVo9d8jkJY5tCi60vFMU5mc5aQ0KsVT2QUaJCCLF6GeCabIvsShJ3w9n2pXHv9glXeXS3kl2E7SWXjcz5Q==", "signatures": [{"sig": "MEYCIQCXwThFKjPXVkBTV1IQp/buouy2AzHyovMqMmw9Dd3XFwIhAPtxbomKhLwi++JGAFQjCaQ1aKpmS5Jb+LsGicLAd3KI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107990}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/anthropic", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.4"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "acb579e8a260ddb8b6af9c483cc823c88e1291cf", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-T1BXrvbE3kPFwUApIjTyWOoEBMlAw1WHPJ3KPsG9y6qxfsc0rtc1DbIbw/ODOPukfpkUnXmj1+2Sj1oqqpprhQ==", "signatures": [{"sig": "MEUCIQDF705J0DTYHUCsU87VX4yg9m1WUgwUDdAjRliCWPQLpwIgJWZYWWG0FXyR+wO86gZxkHFrvBmFvNHdzhUrx9B01gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109373}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/anthropic", "version": "0.0.9", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.5"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "662167ad8bf7b3396d3f655cf0af2824d07df140", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-pRudgyxsxLTnQEDnvYzh/lTSSijxa2eifAjbJO/lNp9jkFGl85Sa2pNhNVWKT9I/E6B/Zv4LyyIH5WDEJkwRPA==", "signatures": [{"sig": "MEYCIQCuHtuEORQB0+qw5h/khUe4FWtM/FRN9y15OeQX4aEWyQIhAJ3rq+SSMembpn8GULWKLRrhCif5urHqqMFVvaVm/cqN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109373}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/anthropic", "version": "0.0.10", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.6"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "71d71577ee20e5dacb71636f69f852fe86c9bfda", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-ya53IOWG7xtGEW/d+r55qeUIlEP81LkH5XCot3fbqshnhDVBsqSLxoBzp2vyN3PzftpoL7awglOWR6sHvF9U+g==", "signatures": [{"sig": "MEYCIQD9m0o/SrTrVz84S+yHZxAe8hBYt4kvcyNVaOkF+SpFdwIhAMNlY2f5hWryhKtSmi8TG+7usMWQx6mFp0fmAGDA0btv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109374}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/anthropic", "version": "0.0.11", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.6"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "02076bd8c77ff7b8d7e57abed894b3286b60399f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-9Tx+7InXrWTtsbAIfYv2gEfzItuY7gXCCwj8EsZEOhpVmtY1/NeC9t6taOFgfXnM5erCCmYz+ONvjPykR9xx6w==", "signatures": [{"sig": "MEUCIB+iUZbpZE97HVwijlzpB94vcBo7UanPjtf9yUOG45iDAiEA7mLakLi9pfbRUs4WRiqRdikVG5w+GbNivx6vamq80yE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110459}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/anthropic", "version": "0.0.12", "dependencies": {"@ai-sdk/provider": "0.0.4", "@ai-sdk/provider-utils": "0.0.7"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d80805393c7360a6b78d89df59a210d9ff193ad2", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-7lEC2llHxEZizZfSt30ab5zRs1BkheEdGRrns+04GTsT33ySDLKkrCJ1eFYkHwAGd/Cjqi6sOPxxhBNHJLlVdw==", "signatures": [{"sig": "MEYCIQCPb+Hs93pDH7I5Kg4atolPkC8iPiLzXvQfVxAXZ9CBdgIhAKGiTunzmfsuNf4RvqQ36jla6cS5Q5GkjNF2Sf24QWhE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110459}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/anthropic", "version": "0.0.13", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "647bd58216be086f01a4a48690d0313fcd56f989", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.13.tgz", "fileCount": 10, "integrity": "sha512-zTL39o/o69qdSPQBxOu6PFt3oxlZwZm1vremGNWs+7GOs78qmuYRnuJVEXXJQCfGSp6DCopctsSvMjKLWgEsug==", "signatures": [{"sig": "MEUCIBZLZu4R/zmzl2QSOq7UBjhnuehDrPW6aaOKh5iWbmCmAiEAmtba5UDYRZLKmJoV4Cv7gMrED0ytwN9VrM2WbmtPAA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110459}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.14": {"name": "@ai-sdk/anthropic", "version": "0.0.14", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46d94e5f23317dd7694ea0b90fdb82febd66c061", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.14.tgz", "fileCount": 10, "integrity": "sha512-yQbmngQ08h6/6hkfEZuNg5fzXM/aHDoCSyD7L9c7gEYBhwlbVZaQclORdmK0jrOCvuqJc3dlWTimc8niaeyeFw==", "signatures": [{"sig": "MEUCIDFW3A0iwz5bQR0dLfyebnMk/H/l+zmR8q6d/U3bZc62AiEAoUZWTGZ2Uznslvq5YPKGgpudyyiyUsWiJxhDFtS4Pfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132099}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.0-85f9a635-20240518005312": {"name": "@ai-sdk/anthropic", "version": "0.0.0-85f9a635-20240518005312", "dependencies": {"@ai-sdk/provider": "0.0.0-85f9a635-20240518005312", "@ai-sdk/provider-utils": "0.0.0-85f9a635-20240518005312"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "157de7811ef8a03f5281319a75a18bb53037824f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.0-85f9a635-20240518005312.tgz", "fileCount": 10, "integrity": "sha512-cvxbYVAStHJtWTpUThtJSn6UYdhw4VTlT93eXEpp3H06Lt9TTaYm714CQXUjtgwz1HeFtL3CvMw48+739vHxSQ==", "signatures": [{"sig": "MEUCIQD2ITPXttvAokuywktVkI/3bpVoier7zCDUh0B8ZQaKxwIgHVG/C5x544tF2yAU6fB0IehkrGDe1Jx66U80T839UUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132170}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.15": {"name": "@ai-sdk/anthropic", "version": "0.0.15", "dependencies": {"@ai-sdk/provider": "0.0.6", "@ai-sdk/provider-utils": "0.0.9"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "20d48239cd9401a63ce3dfe0fa604c859b88683b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.15.tgz", "fileCount": 10, "integrity": "sha512-5QqfH2x4wFqqwfo4CHOr7FlHOiherpQZ6iezaXWw7DIonMS1hTbfz1y+LGVAztNHsh1WWHoBIJpMWKrtHj2bUg==", "signatures": [{"sig": "MEQCIDsinPg56nZkdHysKoQqieZVZ2JvRrE7JT9LkR13wMtvAiBiMn5QbpWBgJZ9Oxg8rfPtx4O13HpIojR9djUTK+mCcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132099}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.16": {"name": "@ai-sdk/anthropic", "version": "0.0.16", "dependencies": {"@ai-sdk/provider": "0.0.7", "@ai-sdk/provider-utils": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "687a8bdd0c46ff1e2b4c911d90fc8fa0f8813a40", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.16.tgz", "fileCount": 10, "integrity": "sha512-5ZH307aUf38TO/YnmGZCIi8s8t28jMxTtI3wUvdCgBYja83iy5DYvfgLg/AgZGxqIAfVknSNwOH+q8c3WyJVWg==", "signatures": [{"sig": "MEYCIQD0dk+tjxlcxkDhVDAXmwNVapJV81ZuRfbLSe5H6tIHBAIhANNCVUAFxeubvGyWuQX4xLIPpO3HS10Ccmvo6s47h2/I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132025}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/anthropic", "version": "0.0.17", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cc19f441e84439402e9228e0b25364a337a8ada4", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.17.tgz", "fileCount": 10, "integrity": "sha512-GH96m+mdz7XA1z4153g+SAGcib6CFdtjbwMXfWWE0q4iIAObeW6eC2N8VuucQdQNSbJ3h62KqdjWMho6k6L8Aw==", "signatures": [{"sig": "MEUCIQDq/TAHOy2uzhwk3g7/CvV4nDjf4e6/bRHInkP0Pc1JRQIgSSPE+9kNFnw68HXr8YIw4m/QgB8+5pf3bYgR/u6/d1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136123}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/anthropic", "version": "0.0.18", "dependencies": {"@ai-sdk/provider": "0.0.9", "@ai-sdk/provider-utils": "0.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9adcb20549346a2b8e2b8a8c3aa273292926f650", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.18.tgz", "fileCount": 10, "integrity": "sha512-1eQ0BPkfQTsFldBNZDe8suz0owhWJxtLQQJQ75Q5eBdIRKEmHOhRw7MT0ny4f8ZUSRLsRewJd0KgPT4TgTRM/Q==", "signatures": [{"sig": "MEQCIB6u2coMwF+3OV3wK7wrvoQgEXcovYMhoVncKT7eD1KrAiBH73+fmcocSUN2KMe/FE+5qwUT8GppLXywIQzJSEVlnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138536}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@ai-sdk/anthropic", "version": "0.0.19", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e4968052ac2d4a60354539ae70a74aaa34cc3e05", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.19.tgz", "fileCount": 10, "integrity": "sha512-6Sd1gng/7NOwLSGXjEZClSPtl291zt5JAv0TreH5y0pJgRS5ZcXPKPMNtS590bIMPR+Ioh/No5nq39tVakgqeA==", "signatures": [{"sig": "MEQCIGafm0j+WBIozpSPf6l2bR8cFOCBZqisssBB8Z8o3XOwAiAIrM5K8vIRP1xbqGV5n6jW+w/rOXvsbUywQvoS3y9KIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138537}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@ai-sdk/anthropic", "version": "0.0.20", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "449222e5c8020816cbf49ade459105e56f43a38f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.20.tgz", "fileCount": 10, "integrity": "sha512-oSY3FpYtmh3L/6FWzqJlgTJICYRvehrNNKXNwT34GnBq6yZm36vP6ekKo+15LOAtXds5LYyM1d5BW5dGRuACZQ==", "signatures": [{"sig": "MEUCIQCHDe+YzZS+CnRxZN2wFdYi2JSDGu6MU2UZHwL///W2KwIgYzDJqIRCIjnYGrIgYdz9rzDIkCfuhYVYg7mjC8uplFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140997}, "engines": {"node": ">=18"}}, "0.0.21": {"name": "@ai-sdk/anthropic", "version": "0.0.21", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0d6d0cf77785aec022339a347176c74fd65549d4", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.21.tgz", "fileCount": 10, "integrity": "sha512-QjVnTbfbAmfMjDqLbcZFC4pKBvp4RqzrZJQF3mzulSkeXWqNZo9G9oV7W1PDbMA3o+DJdxRFyTK43aKUBCP31Q==", "signatures": [{"sig": "MEUCIQDhXwUA8L312Jc1SlevDXJp2kgSozs6PiBshl9LKatyTQIgNiHApL5RjBYkxN2a65K0LkqZ4Z8MFfF47yIw/x8hjw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140997}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@ai-sdk/anthropic", "version": "0.0.22", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "201d43a24ef430fdda59e974efa170a6dec710e3", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.22.tgz", "fileCount": 10, "integrity": "sha512-kpIM5uOWPUWu2X9L2+TJTpf4U8PSpsu+7bjxHrsngDImL2cCqMQv9ecOoegguK4m/eBIJy0RZXlzlM0+VP7icg==", "signatures": [{"sig": "MEQCIGOjgVfFprGxhmfuHIVF9nQGJrFFntU6/z4/g6UtxegFAiBw2/UxSAR62zzemCq47GJxDVN+VHJs6i0zwpMRXn7cDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141059}, "engines": {"node": ">=18"}}, "0.0.23": {"name": "@ai-sdk/anthropic", "version": "0.0.23", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8a84e6f5575ca7294f43e28b6bd4c3289a13af01", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.23.tgz", "fileCount": 10, "integrity": "sha512-vhcG5umNN21tLgMdsmCoyiJBG3QPu40RPkHKAO8HHwie40T8Gv5AynlswZsFNXLxumNbR0KvIzDpjVNPCqT4wA==", "signatures": [{"sig": "MEYCIQCdUbSjdBs9mjs592x3wPKWOZNaIVzqyzj92c/02vHfBAIhAJLMzc+ZzqcluAOXOqE8XbCAXdBCrekCMoYZ1L8/3+ET", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141059}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@ai-sdk/anthropic", "version": "0.0.24", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8e54d1380ad84553d1c1a4a0f7fdc293bd8a2e33", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.24.tgz", "fileCount": 10, "integrity": "sha512-jJnjdeqRoOyy+aXeWqAViqD8M3SnN/RyoQbvNUX0oJ8m7M5dgam/wHrkFHr85cX2hHhLSq2e2eJgtKCjZ2mWLw==", "signatures": [{"sig": "MEUCIQD2gR9w6Yd/IUNC2IZ+Gj6MGq4vFt6sve/+q5dAbOKzDAIgTSzZFUpEqmpX1RbwZosm5bi42Gxm16+IzLln4W+84EQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157009}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@ai-sdk/anthropic", "version": "0.0.25", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4ce811182d800ca0db712c2444357af7c9a7f979", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.25.tgz", "fileCount": 10, "integrity": "sha512-EFo4o4IBAsOsx592ZJuby3Ej1C2cDvQcFskm/MO6gR/165AySv2ZZE51R8WsyW4C73daP8WdcYhpwTh0XrE0gQ==", "signatures": [{"sig": "MEUCIGknVJyLQNtRp1zKIa/FPhtet0xTnXuBYQN3pvToGHbLAiEAwljB9BOSEGGv/tNCFVfka9nNBYIx79OiMHvXa/+R8zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156761}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@ai-sdk/anthropic", "version": "0.0.26", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "76b0d1310fc97fe66261f7eebb12b09741ebb307", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.26.tgz", "fileCount": 10, "integrity": "sha512-9xhUBLYnSJkH8hQMnb4iGdaGB/C/ak3BkNNcstgI9aYZWdKoBfm0ZWE5BKyShb7xE/Xy8S+g1RBGd3rI6ZGzTw==", "signatures": [{"sig": "MEUCIE/utG+gbp1Vf6qJjoiR24OuAG0NbZcWWGhzZOlFFa8gAiEA1jlm4sgOaKOcicODB1BwAy3rOzyF/96mshCSgEpZCac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157350}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@ai-sdk/anthropic", "version": "0.0.27", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "734ea8928a40bbb26bee69f348f394ad4173cdcc", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.27.tgz", "fileCount": 10, "integrity": "sha512-SmWf4YnyAmPag6XMXK8rhvmwvzuD6+KW9jeyvvIHAQDDu3HqOKTuAEQ19UOSLkThyEqV6PyO4GAwbrTeOKpecA==", "signatures": [{"sig": "MEUCIEKoZ8e/G8V0BeR9Oi5fQ1+hP1rbUgTTBSoVUBCfaRexAiEApPqmMs9UAe6ZXj0UuM22wxOXv0Tkv0RRTMu5EIEWOMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159307}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@ai-sdk/anthropic", "version": "0.0.28", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a92b9119af21fdc0e0f888d26ce0a1f58ab78d4b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.28.tgz", "fileCount": 10, "integrity": "sha512-g2Troc6/zh1jlYOXQY4zA4dzqJo0/n8lvQxEz7CDL682uEzspzYIcvrwz59/JlreOeU11LwbFYv9qw0VFSRiCA==", "signatures": [{"sig": "MEUCIQCtm9RRMwjoq7Tjf2IAk0VBSZS/BVbKxsYSrVmpDf8aegIgcxAXZt6y7bDcat9Hl3ti9gkCQzmx8gXF6iV46byOgBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159307}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@ai-sdk/anthropic", "version": "0.0.29", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "67b75c8fed69b4cb822989a0d04751fbd11403b7", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.29.tgz", "fileCount": 10, "integrity": "sha512-qGOyf3vER7r9liTg8Vvrvl586pltwKwWHd//H0lrFbuwn7kttKUcgsEEYIg1JDQ/CN4zTv6dTV/qTwdhvVrjHg==", "signatures": [{"sig": "MEUCIFd70TLsQ7/ZxsN+5Om0+IJP5pHEXo7IqRCKLrqYfFzzAiEA9oqRIiBMhCx1ilsdJ7m9jfocmu5gEqTAZOODi1+eCQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159307}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@ai-sdk/anthropic", "version": "0.0.30", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5d8fd21ca20ae7efbde004ce22731802da38b04f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.30.tgz", "fileCount": 10, "integrity": "sha512-iPJjKtIH8yk2cf5BNXLN6sn6TTghOh8puWothX4pPVBM/OKC4RWVjYTEELwUv2VDPIw918KBg2j/T0RfTgu+bw==", "signatures": [{"sig": "MEUCIE7IaSKDS5B7sa38W+qOIQ/B7nK3vRcgor/EikSDsYkyAiEA/EnlFk79tQNEWJIob0ZFX1EDfxLPcQ00eWVUdTmje/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158529}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@ai-sdk/anthropic", "version": "0.0.31", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ccffcade64fa94f7312963944482ac801d30c60", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.31.tgz", "fileCount": 10, "integrity": "sha512-5eW1tu3T1Rjio2kNWPdgZrmYpakkJGsD+GkRaZJEejC0xIYMs0gYjf4XBkY2+nm3wjSVJUPZH0J6bIrEOCb9CQ==", "signatures": [{"sig": "MEQCIFdKtXjo7tm5/lTblOOK5uZ8mOWLQ8G7gZkkAVjYxKR0AiA2NFQbkctxDuOGfpB3YUegLgvGwRNQlsI+zTvMpormaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159706}, "engines": {"node": ">=18"}}, "0.0.32": {"name": "@ai-sdk/anthropic", "version": "0.0.32", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3576dc398def2b7a482e48a74fd2a848524f0630", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.32.tgz", "fileCount": 10, "integrity": "sha512-1gtfxcZAyAW8IndHJ33BhFAnxxOykWYYbffzGLVPltcpe9sVjQ3CdkQKu981QgupoEEXIejM4vQ1dzmlnG8WKg==", "signatures": [{"sig": "MEQCIAD3o1IHrFuCiiYWJV+3kXgRUZxnJlHff6XLmvLgdtlzAiA3QiN61+eFNNDKW8ywCqcHt2etcCe/0MCGqC5BpxiHGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159706}, "engines": {"node": ">=18"}}, "0.0.33": {"name": "@ai-sdk/anthropic", "version": "0.0.33", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ab0d690e844965e0f54e6bbc85b91f0a90a4153d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.33.tgz", "fileCount": 10, "integrity": "sha512-xCgerb04tpVOYLL3CmaXUWXa+U8Dt8vflkat4m/0PKQdYGq06JLx/+vaRO8dEz+zU12sQl+3HTPrX53v/wVSxQ==", "signatures": [{"sig": "MEQCIF9bqwit/iYrv66YzNdHk3l3wcywlJqMEN5RmnpaxXc9AiBR27c1aoU/Lv0cmtiLcXNq38kUuyoVGNytsO6WN9O/SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158067}, "engines": {"node": ">=18"}}, "0.0.34": {"name": "@ai-sdk/anthropic", "version": "0.0.34", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "add2b4abfe6ce0b8914ec957fe014073d503889b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.34.tgz", "fileCount": 10, "integrity": "sha512-U3E2N+Y9WeqwMWyfyxwRBE5tw7RpLxkqxBWN+wFrpz8WDISUcLim0knqE52DWBcVH9W5MPxnoQzPN/nzBtDLXA==", "signatures": [{"sig": "MEUCIFyMLkdIwYSkmwauqu6rHxrPW1OPmrL1/1Lxaqho2DIMAiEA7n/KzaLlBstLJ84/wXbJB94PLz6JHQJfDTG1jmWZhFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160080}, "engines": {"node": ">=18"}}, "0.0.35": {"name": "@ai-sdk/anthropic", "version": "0.0.35", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "daa005c579b5c9513f62964f1b1f7ca309019461", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.35.tgz", "fileCount": 10, "integrity": "sha512-bCyonZyMQZdYTfSazLFRzQ2teuVc8icejbG6JhIZJvoQpcA5zzmruLD335vAf1hhGAFrHjrLNB+u4vTL52pfbQ==", "signatures": [{"sig": "MEQCIBOUfLXBCXhWahwEQQlguY9U0P7lLxDgP2sk432Xu16BAiAaGlYYgd7rUQSQVJJ2h5GL81dTlLy+HhCNnMmJ7UqiLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160953}, "engines": {"node": ">=18"}}, "0.0.36": {"name": "@ai-sdk/anthropic", "version": "0.0.36", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f349fa4f8714728371d43d86a35b14c8f3c6ba60", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.36.tgz", "fileCount": 10, "integrity": "sha512-HePvgBavshKmCVg0eiDrYSEbjKpemwL3+0LnrI4w9JpiQpjq+VZGWJQqYA90dFPaRIp4f1OyFUPvp1OzpSlG7A==", "signatures": [{"sig": "MEYCIQCA9bSI2kdAVJxTE/wDpnSJKqlxfJXrlFS5Ve9YrgQrlwIhAPD3RA4A8Krsp755MFLZehkNa8YfXnx69uYADBiMkokj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160953}, "engines": {"node": ">=18"}}, "0.0.37": {"name": "@ai-sdk/anthropic", "version": "0.0.37", "dependencies": {"@ai-sdk/provider": "0.0.15", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0bc75a1ba79d1107b337b82f9a438f523e5af4ae", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.37.tgz", "fileCount": 10, "integrity": "sha512-RkFtA+dg5BSptuK3T08JDmFWCDaXehtnRjK/gy8VFWuWT5T44wqoP0Ca9AJksmsEDdYdTXUrLQOS5QmAgifc8w==", "signatures": [{"sig": "MEQCICk3GezIV3BxNLJOJfJCdt1pbrMVJ8GnOYMzQz7dn1TWAiBbd7P0hqQ8+za+/I4WIu62y9CrOfXEyE2q+jusMEvKxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160953}, "engines": {"node": ">=18"}}, "0.0.38": {"name": "@ai-sdk/anthropic", "version": "0.0.38", "dependencies": {"@ai-sdk/provider": "0.0.16", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "74fc9294874cec583d8116d7664a3f2a02ee3327", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.38.tgz", "fileCount": 10, "integrity": "sha512-XQWhQH9/x5CWCS7g8K67+WPZUI7vVXJ5Z2a3yXCu627QmiatlJGrhx107Qhq+yPZutjKQQgSP1aYDZCs0LEgqg==", "signatures": [{"sig": "MEYCIQCVZ/R8EMFTaFLJ24pRNI+pjnsXwSMT1xgLEUJ8294iqgIhAI4mjKOTu2hE+/Oq9q37k4PauyubcJzLGHq9pi/B4vnP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160953}, "engines": {"node": ">=18"}}, "0.0.39": {"name": "@ai-sdk/anthropic", "version": "0.0.39", "dependencies": {"@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "90cd66cbd9c92e82b97d57773da28bb14f3d7de1", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.39.tgz", "fileCount": 10, "integrity": "sha512-Ouku41O9ebyRi0EUW7pB8+lk4sI74SfJKydzK7FjynhNmCSvi42+U4WPlEjP64NluXUzpkYLvBa6BAd36VY4/g==", "signatures": [{"sig": "MEUCIQDCGihQv1EMRTAcTM4BkpVs1Qcu2Aygu+RjC1oRlVv6HwIgP7D/JGAkwEqR8aFL3wEwCr2272dUnZKZdZahYBDxYVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160953}, "engines": {"node": ">=18"}}, "0.0.40": {"name": "@ai-sdk/anthropic", "version": "0.0.40", "dependencies": {"@ai-sdk/provider": "0.0.18", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1a9381042dccdd46b3745055843422c17b45fb88", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.40.tgz", "fileCount": 10, "integrity": "sha512-qu1laoAQk17FMF1PYwEpXU43KEMzR2tTXxmbM1FiEo9/L/VwZJCDYHRKl4r91uOHBOm3lRmesNumvCWORjB1Ew==", "signatures": [{"sig": "MEQCIDj0MhWFd/p/kGvyOUWdMBLhdN+TKV+WVAEZP1GuP+nLAiBwdPfmfOYEV1shNyKh9xqq2k8Oq+LTXZjQRBNs1hxvOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160954}, "engines": {"node": ">=18"}}, "0.0.41": {"name": "@ai-sdk/anthropic", "version": "0.0.41", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7e0af6ce60868c7a64c3e59435be68e2c2d37f23", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.41.tgz", "fileCount": 10, "integrity": "sha512-ZGH0Xah9II4jEzDm/z+9G6qf0jC2vWRBURBDTWdQlVUI4COOMd8fmTrqwZrQuAl/y72vYakG5k2AKpJSnY6MeA==", "signatures": [{"sig": "MEUCIQCe2h9U9owluU8qd42rtwbVMHocXislzA3mB+8ArGLi2wIgNIapIM24fdKrsMifvssVoGh7c2pVhwjlSaQbqquDNio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 160974}, "engines": {"node": ">=18"}}, "0.0.0-fbda7b18-20240815003233": {"name": "@ai-sdk/anthropic", "version": "0.0.0-fbda7b18-20240815003233", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2294257588414e50f1d6b7415f175afbd9a41451", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.0-fbda7b18-20240815003233.tgz", "fileCount": 10, "integrity": "sha512-1Z6DpyRTNLADMFMGxmj4Fzin2nPc8+N11TAonxBWQhjUJDqzweAp79TIwMspR3kWuvTNIdQYqttBl5AH3JZ06Q==", "signatures": [{"sig": "MEYCIQCIgkxIplQT4TALvbL0bAHJ0zLJLvjvTpNeubtAYA7tsgIhAJ1uW8og33M0yFvAGId9FPF5wt2O1U1hAm6j6Pv/PbhV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163199}, "engines": {"node": ">=18"}}, "0.0.42": {"name": "@ai-sdk/anthropic", "version": "0.0.42", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8dedb6ba0772ed82e69882fa016e8dffe0a51d66", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.42.tgz", "fileCount": 10, "integrity": "sha512-eXgh/9ePf2VitUmU0cNmbPp5JqQWz85zW0HHoeXSffRmJgxCpE3d2RjymZUeS+llu3A9O1Eb7UTy3VZqPN0A0Q==", "signatures": [{"sig": "MEQCIAclsWrNG7LnFuqpTFIU9A7TiSnRJVxRjUu62k4zEZlVAiB2eg2syQkYfSNMZwTXz5Uu5TOo8UfEF4ZXTkN6gB72VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161200}, "engines": {"node": ">=18"}}, "0.0.43": {"name": "@ai-sdk/anthropic", "version": "0.0.43", "dependencies": {"@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "314b0be0f9adebe2743d8a246cdd230fe9ba4f5a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.43.tgz", "fileCount": 10, "integrity": "sha512-X9GBINVeaynnpSETD6rBgkmkR5VynceyVe8FxDCM+5jQF9fNNPCpaXX/syE9G8HH941HGFdEcFuGuLR6MvNdeA==", "signatures": [{"sig": "MEYCIQCsD9PcLlOHzILZNbl/4CvJjCpIpymxkES4OwFmjtRhgAIhAI/NwdygxY2KYlkLUrF+Ud1VNAI4vueQnovvFtcv7oL3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180929}, "engines": {"node": ">=18"}}, "0.0.44": {"name": "@ai-sdk/anthropic", "version": "0.0.44", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "83f4f55dc35b707ddd05a45a4ac58fbf78fefc86", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.44.tgz", "fileCount": 10, "integrity": "sha512-h2GvM8hyqlTFEpKfo/USh4vDYmkwBg/FF90UNC22w6g+hihmejNp/q1p1DhZZ6k94uVnf6SUl/r2ya61yJ8U+w==", "signatures": [{"sig": "MEUCIQCT7k3nELbTKyrocdmJhmRAZuw7udLmIPMAsj3euhVYHwIgK7oKXpnlgeASr7/JAKuLjupY/vhvE0dw7nfe3mzOkzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180929}, "engines": {"node": ">=18"}}, "0.0.45": {"name": "@ai-sdk/anthropic", "version": "0.0.45", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "468948d46b279c1cbb80b510d3d09a5f4fb82b70", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.45.tgz", "fileCount": 10, "integrity": "sha512-47AgOn0E37ImpNfnI3RGJSREZ+rbJszOlpC+iqwP9XY+i5JLrbEUrnAun0vNop91m2V7BnZE+roENI7tqnvttQ==", "signatures": [{"sig": "MEUCID+I52aHvOhaeyA87860RsZSb+PieEwOn4WM08HDRk+MAiEA8az/0RP8C9GqXJuKbH+iKaY2QWNsDlBMGW9f9WmDJGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180661}, "engines": {"node": ">=18"}}, "0.0.46": {"name": "@ai-sdk/anthropic", "version": "0.0.46", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a76d38c9208ab2102829ff03b7e36b6c53433f3e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.46.tgz", "fileCount": 10, "integrity": "sha512-44tU2iXrMQmEv+UNZ7Yj9Vl8BM+emRPpDxC2ae94TEEZxyJiCzum6rZ1alc8n5Yq1t22S5JxhlGwpOT5+wV4zQ==", "signatures": [{"sig": "MEQCIE3H7vzt+7HfoyfqIcKfu9DQi5MPYRD6SC2FlNvoOJNiAiBjQtd0PKptLSlhXinYZadbMjwD3gHyaFcWMwhPCsA+ZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180661}, "engines": {"node": ">=18"}}, "0.0.47": {"name": "@ai-sdk/anthropic", "version": "0.0.47", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "572c8a28a94a286b1ed0acaa8dcd82cd69ae631c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.47.tgz", "fileCount": 10, "integrity": "sha512-CYwo4Bu5lwZ/KdLQ2ORJ1g1QPywBjcb1//Y6858nUAVK7A76o1qJAbYfF0RT6OQOhrbPxvbmqAbfGSs8RJlxWQ==", "signatures": [{"sig": "MEUCIDWuCINxnuuq53ys7Jz+6DDXPnmigYQsnp6OPhjB5mWsAiEAx7bKrhZy6uCcNKAr19FJdQb+GpMt30xm8FhkTPy1Xhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180661}, "engines": {"node": ">=18"}}, "0.0.48": {"name": "@ai-sdk/anthropic", "version": "0.0.48", "dependencies": {"@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9ea8464f1c0cece7575af39d7979c27f561c4fbe", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.48.tgz", "fileCount": 10, "integrity": "sha512-o4DhUwLXsWJw+6LFWfJgg2m7xJu342/m373zzRWZXXKCrsc5oi9fOMfHtkuggUgC6nSY56Awq+cXkUiwqa8a+w==", "signatures": [{"sig": "MEUCIQCQI/YgYZsJUFxyAL4hj3asFpYIkd9g6jUA4w2W6fUmUgIgOgr5ohoR4fK+4ecHLthRIytOo34XeY313cnyx4wORxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181988}, "engines": {"node": ">=18"}}, "0.0.49": {"name": "@ai-sdk/anthropic", "version": "0.0.49", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5fc0b0a3855e9d8fe3e82e17701ca7295eaba98e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.49.tgz", "fileCount": 11, "integrity": "sha512-RBMRJqnsfl1mkuNsdWSfuz64knKUCocF9wB6GySfprZZYyKtLsHK1aIqJBOjcQP8TX70wxwCC3FUrpALKAEokg==", "signatures": [{"sig": "MEUCIHMrEeed5jDGJBMt1A9nyGw2AKjZ8+V1DpZs9djhG4xtAiEA7dEwZT1xqabc1U7OMa1QBVqv2PI+saHoAvLJMRQY/eA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194821}, "engines": {"node": ">=18"}}, "0.0.50": {"name": "@ai-sdk/anthropic", "version": "0.0.50", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "95fc1cc49e0c9289a4d3a39ac42528fef25b5d02", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.50.tgz", "fileCount": 11, "integrity": "sha512-++mqmFcUoQgjoCchAU6eVG3QfKdwkeJVNdMZ+jUiNdawn8diA6BlARlu7xFT4F7W3bcStfYv4hK1jwRyzAQtCg==", "signatures": [{"sig": "MEUCIDPBRwGcezWWBjsjDbg/pXBEFT/Jv8BjsGemPT82svBnAiEA7EYF3zi/slcQQlHgXIArZl6Ajo7HSuwFPmhJyUld1k8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194919}, "engines": {"node": ">=18"}}, "0.0.51": {"name": "@ai-sdk/anthropic", "version": "0.0.51", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f8f01d182110d774ac275bb68dccb8f15b7aeab9", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.51.tgz", "fileCount": 11, "integrity": "sha512-XPLBvdwdMlNAvGMyfsDgrCDXN2Wz7M+wfCJthqiwdiKHmq2jDLGdt0ZCAozgxxW28HVzMfJlFjuyECiA5Le3YA==", "signatures": [{"sig": "MEYCIQCNXWUGPTUpMQcfnkdCkIIb9p2XUn7G8KnpQbbCzp+toQIhAMAMTVf40rH0f8FDyOTzUrgbYpOAutibECKVSjXk45if", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195031}, "engines": {"node": ">=18"}}, "0.0.53": {"name": "@ai-sdk/anthropic", "version": "0.0.53", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a8448c8b7f1c656a039e5ba1eac5a315ad287fca", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.53.tgz", "fileCount": 11, "integrity": "sha512-33w5pmQINRRYwppgMhXY/y5ZIW6cbIhbuKbZQmy8SKZvtLBI2gM7H0QN/cH3nv0OmR4YsUw8L3DYUNlQs5hfEA==", "signatures": [{"sig": "MEQCICaP68mv9g97TP37PvlI1Wa28EqHWbMlJnk7HJecH7EvAiAofoBr6ia6lq8ODw71b4HgDh+3ArZ5e9zx5JlS++JQDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255532}, "engines": {"node": ">=18"}}, "0.0.54": {"name": "@ai-sdk/anthropic", "version": "0.0.54", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e609cd05dd7daecec1de588ee1c8e8801f22fd4b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.54.tgz", "fileCount": 11, "integrity": "sha512-N2Ol6Tp1VvUOSDcEOmlW6qwaPDffm7kocn5KoDUaTGtIj4XQyQ9uBkw0l9DkZBq6/jvmgIkK6zxL++t5+i80Ow==", "signatures": [{"sig": "MEUCIF0WJ/4x2iwFSPeb7AwBqoqWShe8sTACxK/LGCrvlwPmAiEApERxFiXg3hqXNntp6W01dN0ASwX3bhjhYbPydRrF2q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266236}, "engines": {"node": ">=18"}}, "0.0.55": {"name": "@ai-sdk/anthropic", "version": "0.0.55", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ea12cd0a417f948126fa74e1beec3a216ec03ffb", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.55.tgz", "fileCount": 11, "integrity": "sha512-SIPGu8on4PKl+PIdbjOniT5/AiE82Yw8HOE/W0GEb2bNGq2KhfkjVt5MBhppg+ZRme5w2iexB4bl66eRa2o89g==", "signatures": [{"sig": "MEUCICGvOSGvZiabrzEOf63Nl1ptF2iXNxw2DsO+vqCfhxwZAiEA2wjl78YYhSC6fcyQjG0ugOYK04YSODD1RPq3+Xs7ymY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267404}, "engines": {"node": ">=18"}}, "0.0.56": {"name": "@ai-sdk/anthropic", "version": "0.0.56", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6d3629f08a4025578e0daf073d13ca31a33bef71", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-0.0.56.tgz", "fileCount": 11, "integrity": "sha512-FC/XbeFANFp8rHH+zEZF34cvRu9T42rQxw9QnUzJ1LXTi1cWjxYOx2Zo4vfg0iofxxqgOe4fT94IdT2ERQ89bA==", "signatures": [{"sig": "MEYCIQDUeJtF+rO9yMPeni/VIUQ8/BUpQkk416G2ni6uKXboBgIhAMIy15/f7pR21+lucFhYDe+ksEYLPVwYmzU13SgB6dk9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267613}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/anthropic", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "28608ad96cec7ee3fa09691131e098c3995550e9", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0-canary.0.tgz", "fileCount": 11, "integrity": "sha512-F2HVOsZEgtz1JXSLFWheHtzq1aURhUIf/gp5rTD5AroLlviLUzZ+sAJEIXmkwUMXbU45k6hTCya6Y3JiDDKDeg==", "signatures": [{"sig": "MEQCIBxbcha5pqXzVcMqIuL4GGQZvL9NR0bFKe48OAKLo27NAiAOd+PzaRYHLjomuD4v2cvmXazYTjsjiBNFa1Cs3Dd1jA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267927}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/anthropic", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1ad480e6d75cb6c51b0069e0c7b0449e3f336922", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0-canary.1.tgz", "fileCount": 11, "integrity": "sha512-AgL+DQFOxlboc1WjvawRHPuUKJnMUsozraCkRgScG2pSSIPueMosfeXLDodxi51zLay+CcAMxpFvUYTnk3m9Bw==", "signatures": [{"sig": "MEQCIDFNnobFd7rdqyB79WWXxCyAWRwU55B/cY+xCJCfmWFBAiBcgtdbkxks6sWdEIjqRLN7+MVWK8E9nJjpbVsG/yNcEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255678}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/anthropic", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3b999f6b1f7539c6eed1d7f827542a599cd83b3c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0-canary.2.tgz", "fileCount": 11, "integrity": "sha512-gq7wzDW2/hGv19zJXYoA+FS49rPV1ZiTNRaNsoNvzLDONNrG6WpqepnFdqXNzFrEYIQ+3TxcdtBqDAB6vd8GVg==", "signatures": [{"sig": "MEQCID6fZlO6E9rjxkxWXio4TDhUB8B2TE1OpM/5qoy2daGrAiBeo4lFHTKO5ftjwV++gS8VFDgXdcn5059e4P0+fhbMKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255825}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/anthropic", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "208ec135b4f9dd58838d0ed97c14309b9201fbe6", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0-canary.3.tgz", "fileCount": 11, "integrity": "sha512-mpce8RBkL+w5GjRon8SimgO0omf4sD4CKxgXwzxsGjYLeE60+WRgsXOyQGAFTVNm4YXWYBCszD+VF63CYqZuDA==", "signatures": [{"sig": "MEQCIGuAir+zDWzy8n5bh8nyCaYr5n6sjKnmxaYUBZGCX+3gAiB7e683O07oxLv/k04R6ZOU3dw7mJUv69Coj12bS1xADg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255939}, "engines": {"node": ">=18"}}, "1.0.0-canary.4": {"name": "@ai-sdk/anthropic", "version": "1.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9ba9143c6c2ca0dda888b2063f30a6ede440fd9d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0-canary.4.tgz", "fileCount": 11, "integrity": "sha512-AjHyJ3V0Ust1ljWx1vzBl4H5Bv4W9Ns/E7ruzpT+o/NPu6E7Wzz3FLX5w40vaxoFePJhhashuboFf4Kj+1kHjA==", "signatures": [{"sig": "MEUCIQDicDoPwjRCN2g7dpD6oyPhMwqITsI0Q46uNZSItaYKGQIgUq0mHG8moYBv1jdOZgxvU3tFdqaHSHXQuMaU264Z7qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255121}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/anthropic", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "32a61ba4a688c2760cfccbe7ab075bd87996a87e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-xegqsHU8v9el0Ei98BEkzCcygOKCSuZ+P7zTHjh4sT3isNc+3uv7yotmHQsLovF4MSSUz/zt/q7SG79/lRjLYQ==", "signatures": [{"sig": "MEUCIQDc+IdbpWmPck/XtEnLfgC2GFbHGOw5XuHVWTU035bKkgIgSDgYn4ZN6B/hb7xrtne6+qWn39dUnpC0UOzIdK8fZwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223409}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/anthropic", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d8a7fc9024b32fa041fef7de7a90d7fa6a1b6d9b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.1.tgz", "fileCount": 10, "integrity": "sha512-/JWRn7hlRhkpiedWCwDCCAF4dTSpEwcJk5RFMAVCL0LW4oMruf5MW43RlzW7B117udnN57zoc7GaM8tybZPs/g==", "signatures": [{"sig": "MEYCIQCBd7nvVkU6sF1ZyWSuCiZZbJiUZTiRBevUNz3aAqf/3gIhAI4Q4HOqwIfap/7ifRMJymgumLDCdMz4GQ3dfWCj/pZZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223505}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/anthropic", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "50b5cb28052461891e2588f9b26219f352ae134e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.2.tgz", "fileCount": 10, "integrity": "sha512-f3Z356AgUe6vcaUJuVY1+UWdUdhx/JzIADaQgybHSBRQ+OXOYs7AveKist01I6fBXGl3DPPbJJr/i0P888Izfg==", "signatures": [{"sig": "MEUCIExaWOsaS3TzFIXCeCa55oPmgqtUYEDWIPnVE8LJkEGhAiEAs21XViXKylIkGdh1Myz/BPVz23GDEvhFeigoGc/C15M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223628}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/anthropic", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "239e39c6fe4c963f28e712708a79ee5261bfaf1c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.3.tgz", "fileCount": 10, "integrity": "sha512-bCIUmzF8646OguKI0+UGtXFbtnYUCJ5eaFzkgUbjZsbnRjA1ETDEs7ygM4OsigC/Ak2ZgL0rNG0dwkXaJQV1rw==", "signatures": [{"sig": "MEQCICSXK1gnTg4U2EHzTfTF9HqVfI4AVMIqNVQk2RTEWtdIAiBUuswiytPz6T2OQsjzK1ZkconhVd67dyu3Dc7e6jXDqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223724}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/anthropic", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5c12ccf42fa6949b88fc42754450d197c6b1ac34", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.4.tgz", "fileCount": 16, "integrity": "sha512-x5vkv0+7OJs3GUDy+Z5RDxotOmYQskxYUswQVQJ47f79JC3XRTjMVsPzkyvlb5MOW2NOFfYZ8920lHcu7zhJQA==", "signatures": [{"sig": "MEUCIEeRWyk19TsVeZ/LDt48yXBt4T9AEIerZNou8WV67N6HAiEAvR4fNDnYxyqgFbfykV7ADiaVE7oTF4fyng1/Uk0g+BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427465}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/anthropic", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c349c7d1c15957e55bab82f55a2a2c066246b576", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.5.tgz", "fileCount": 16, "integrity": "sha512-qNEB7AYz6W0HTHbhJk/brhGZtjivcRdberD1fn3aCdvzlQ321q1EOTc2k7TvfE+PmNCZbp/uutBbWPGHHODKpw==", "signatures": [{"sig": "MEUCIB0KVvYzzOZVtxXjHIfSAiKhPvX5a4Klz8fTD2k45F5BAiEA38JnaW4fuHleNIsBzx2pVIV7krUZPJBiPX1ooy1puMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427588}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/anthropic", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "302665ef25711307fdb7e7669f200fb8134f428d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.6.tgz", "fileCount": 16, "integrity": "sha512-/9f/CJfR+9rXRV55KSBExMCGeVV7QE5SlagLniak4j74t4/Zg1Aoa69AHftJ78rz/S5srvAB9YtTu52NY+E0mg==", "signatures": [{"sig": "MEUCIQC4HuoaXtIPEzBbct2zRSD5M40dkWGfLYWH+FhJhtenvwIgV9GIwCMOkSut65P0HPmogqo73np5QLrGCG6IEI28O/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427783}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/anthropic", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "17f8b4c69f1253f0a16bc9a5d1bff945767c13dc", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.7.tgz", "fileCount": 16, "integrity": "sha512-J3Lc749YWpDdkbtucYKVq/tyNpsYTqAgWru2BmiK6qWBABlhblqpDXvJJ0lSggJBPexlc6EuIVaAXIqmK7gMUA==", "signatures": [{"sig": "MEYCIQCo0vyMAfcHLiaBF2hhf2yFS0yc5pOKOpFZgP0EDCxhMwIhAIMovrWX0c/79kvLuO4XEQHOzzl8oAKdqqXep4lY3NLB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427972}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/anthropic", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d97481a64054d77f2027d3bb98fe73c688e4911f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.8.tgz", "fileCount": 16, "integrity": "sha512-SruTs0JOZ5ZnVV2hzeu0XDzRrT9WHcgx9P1p5vpjJFJVr9FlVaTxgxisL+8tlhZy8FX68zAhtj09rAaL4gT+jA==", "signatures": [{"sig": "MEQCID06F6GpMEWI/zm3ORongEl4bxZ3jSn5jwfHf44xAzVgAiArBwfTf1nTdMO4nFUMJGrfbRFPvR4JAjd7a5V2aqKKIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428134}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/anthropic", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "47b9a3ae05146a7714ee9db727d2c5655eb1a183", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.0.9.tgz", "fileCount": 16, "integrity": "sha512-gYKZu+lT0yw/St62YC+ZsQ9IR/PC8G7ssZtnGsht2Im4Ix8MWr1dQHMq9h9Sedulxom+IbdAjAUCgNfqy7F7ZQ==", "signatures": [{"sig": "MEUCIAm92nBdU/11wCjdImkIseLwkfMxKErxjHfsMG0eiSSYAiEAnhvirO16JaUrYTAvR3W5gIlk6gOgqDDgTIKLm/FjWBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428230}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/anthropic", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e8d923e6ca7ccca8afd84be72caa707f1dec7d2a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.0.tgz", "fileCount": 16, "integrity": "sha512-dunguWbV6D2CdFqGgtAXLp64ZqUet8W6AUkheb8C8oxXcaGium2BSqopO17kx9pCWFaBYWBFZZb2jCFok6zOUg==", "signatures": [{"sig": "MEUCICw7afLhGz46/CA/SeT3b7OurP2EqQqL+bCrC6dk6xzvAiEA3l+2j4PET2AQQ0Kz5qSuLqMjLAgP4svY8L86axPtNmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428377}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/anthropic", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3b5843f662df0b48876bb0405278dabe14fd50a1", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.1.tgz", "fileCount": 16, "integrity": "sha512-D2H9rf5UaknW0x3uNGVc5axqz8/KqYRC5v3NG9XGs07vFOWXBLu+9C8yZX0hP0VFJv4KcwDItTGjhzfgR5HQ9A==", "signatures": [{"sig": "MEUCIBWq7vDCEP8/nU8c0547GhfgyHaTMx+6OrwLdClTz1I3AiEAnKrVmrIYLuvl1ziQlAs8o8Lb7sndoqifnzU8yavGnAM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 424455}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/anthropic", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c9dc767a935e9c00bf2b15b0b43174353798df82", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.2.tgz", "fileCount": 16, "integrity": "sha512-AR5/zL+N+zVXABocbi8XTsDR44jAbylh77G4VytoE7rQRFTw2Ht/cOxwrTlRZ6RgJeGYABBMnHK9XbxRVOnsfQ==", "signatures": [{"sig": "MEQCIHUebT8fTKkXu2XuCknfkJHJg3X2vkW6sld/eWYqC0bZAiBMY9Pm8xy884FtEwRTm/PT5bOoWbWhg14MS7h0zq1L+w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 424611}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/anthropic", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "143df4cdfb36d6ef78bcf3dc093dc7f77f7b0ed2", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.3.tgz", "fileCount": 16, "integrity": "sha512-V3BVxGQaSSwDqSD9vKPJtnr3NG92D+LEXK5lwG+3nMnOAK+9lj5OABVXOg8Y79VMFfpOIKRjxrgLHWJriOblBA==", "signatures": [{"sig": "MEUCIQCwzc4VvYTpNdy1XLxoLEtB8ocEQ86jyz218fBXs9Qq8QIgCr3yUTThQZ3aUOV5vg2AI+lVwHFwnZSQfZCGpCNR5RQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 424707}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/anthropic", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1f24c8c1c1fbcd2878625407ae51ccb4785479ba", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.4.tgz", "fileCount": 16, "integrity": "sha512-qGT3oahdlUw3sb7LPsjlfLeZZxcwLTKSFE1/unC40+MqHG6K6cRFqJmTfsvU6fvG14643aA3G3UenKpGGWzO/A==", "signatures": [{"sig": "MEYCIQCpiHQjyR97V3n+bzRTX4jfGTRftwOqaHYT986yuq0c1gIhAOqXwTpnSFaDmNTVhJd3iHFM6ZPbEkUcemWvz4f5BQ4b", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 424803}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/anthropic", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "da60ac5655bf9e1d9ad5abcacf99d7c98ae695fb", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.5.tgz", "fileCount": 16, "integrity": "sha512-HGs69t2dsHlt+c4nCeUcdx5ojXs25dH8xly88TZZoyjfiCRWjmyjQxiPHZL2ME864a+vEpQSmvn8DgvMFA4p2g==", "signatures": [{"sig": "MEUCIGVMB58XOGWhoVpYOIkA3U81ac7iR1CTRXeU5vY0TC8SAiEAlCTzd34U7uqX230m3c7ajcPZRSEc2S49ilpiIB3HYS8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 424899}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/anthropic", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e6affe8a3d3e761bb945622e55695efe617e61d9", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.6.tgz", "fileCount": 16, "integrity": "sha512-4TZBg2VoU/F58DmnyfPPGU9wMUTwLP15XyAFSrUqk9sSdjszwcojXw3LE7YbxifZ+RK7wT7lTkuyK1k2UdfFng==", "signatures": [{"sig": "MEUCIQDbsEfyMlyXv8iYTYhYPVlKkkMb34cRcYuLrJFohwPrtwIgbB/cCachTMFVdHYJ1C3Hk+sepe1aZT9ZjzQlo/Desdo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 425022}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/anthropic", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2c35cc687b839c2c406e5607d1f62c12504e2cda", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.7.tgz", "fileCount": 16, "integrity": "sha512-d/w9VUah/KVxOj1BRdzPFJGtHG5/XcPYbvjnRSwm9hA5bzlP+Yl6f1EdabPruUOTDTG+dkYuccQkBe+5xPmF8A==", "signatures": [{"sig": "MEYCIQCSOf5xdLiW0qXW7IETsL7rtSOmCF3UyUM+ELW1C3p4NAIhAItOqPOo6ATKMEJtg0Pt0DZbD3WNGUpH+NgG/8vO/FrF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 425076}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/anthropic", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b93f82b575a3b3b47f6ad6750bee7b2fb8516b64", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.8.tgz", "fileCount": 16, "integrity": "sha512-QD8c3ShPIpXaqjCDq9tCBI9iI/GeZETnP/DxgA8wRTs13Sqx6HcLh1eKTaGyePOtJvRx5XBcR+wSdeZZcaeUQQ==", "signatures": [{"sig": "MEQCIEhwbQydRB0L4O0JVg+ml+P5WsiAEYgV8w4MTxX9frl4AiBtoK3/PByGI8T7Fh7uU+4pvLBh61HUuomqOfX+Iv9Ezg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 425172}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/anthropic", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4028880f4900e105b033b3acca5942a653d11f57", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.9.tgz", "fileCount": 16, "integrity": "sha512-oPZ0r1XyXHWYwOSFnUTRdPMjX3SOfmZjgb1YaRYQk5Zhrcm8DcmZLkdXBkIXbfMeeOnT+bidqGbQSejXEzk/FQ==", "signatures": [{"sig": "MEUCIQD/A/XPWwEdxKw3HWPiMBJIK3c58PISC/Ci696BVM8fGAIgW4eD/itN2W5Dkby4AnWVF2nStGfRRYMiRfhUIQqKVZw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 425295}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/anthropic", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "570c694451e03eb8c7807e6719d103fb569cf2e7", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.10.tgz", "fileCount": 16, "integrity": "sha512-kWmqr4ONAify8APPp+dVlw2X79ygUqPxLnA8buXrt1Sv+tkWUQ4bfTrfAkaavDJ4/VPODWqRyKcYADD482TtKg==", "signatures": [{"sig": "MEYCIQC5oyvYdO+TmymfG27ooaN8hdKR8yutWWp1iW2jA1oM5gIhAJk+8if2uv5XIedcRkaZysCCu0pqNWh8sJBr+OMbgaw6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 506646}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/anthropic", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "85347e06ae09fe65f2e30868bb6d3c278795374a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.11.tgz", "fileCount": 16, "integrity": "sha512-FsjF+Qdy4c56dbXyUcec+UDOOnYayL1gd3Mysu1rSZ4RTAPuvsDewNJeSObqhJ7kMXnuHHLmDA5CcM4WvbSD/g==", "signatures": [{"sig": "MEUCIBtjqiuWd0Gmlk/VnXkDL7w7ln9sEMsa5D+s1v7J/OZRAiEA3BM8282mFm20+udCD5+BY2hMMQ690cIZW34ORirkGtU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 538512}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/anthropic", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ffa91a3dfd37ca0b4385e4a9f05892a816411269", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.12.tgz", "fileCount": 16, "integrity": "sha512-EygyzN8Ng5r4kj5hI2ONn1HQv0Z0NJKAQqBu4U3ZE9pKotjYuU9xkvvyteJwhQiRm4qfpqCdWHeUuC99Q30grA==", "signatures": [{"sig": "MEQCIH1TlVDWdtEkWbr57zfXy8JiNwih5Kciea3x+/g08wVeAiANgfpBDMxoy2t18lJsu7mjqZDYHH8/jOGCHMjuD2RT7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 544110}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/anthropic", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "16ec172bcd5b12ea973078c7652e0cdff27f8ae2", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.13.tgz", "fileCount": 16, "integrity": "sha512-dBivw7ggokys0c9UmbhxHW36S+EHMQEHk/hVcakGO3sMEe6Vi0dR575xDjXJqs8uZPAmbcZjNb1s89U8cA0Y+Q==", "signatures": [{"sig": "MEUCIEZnHxbzx/TZSCw/gtziPnUN0IkNveWQTDJa957p2KW4AiEA8fG/TU0uSOwvdCpvtSARApXxRXjdWSux1s/JhhWz0oM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 591735}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/anthropic", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f72af5c36c1bbc7b4ae3c0d674d5482feb4b63c7", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.14.tgz", "fileCount": 16, "integrity": "sha512-dLrZIU1OFA9rWxEEKGcLFWNicwsxtIXSuL/7H3QuBHFKiyO5RstVUZGRhyiFRYquoEsazG/5Xxi4VjsxcakbMg==", "signatures": [{"sig": "MEUCIHGp9EFBhiEMXQT/B7CuxAxvJQzVaA5On0hD6FhFP0e4AiEA3rWnhcVTQv7wg2sb30BOMK4oGtpO3lWF8IYQGLOnCU0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 591067}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/anthropic", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f2587bc77928f722c061cfa01a339162353c1a5", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.15.tgz", "fileCount": 16, "integrity": "sha512-KqI2vjEPLieBmZh+QIB0055JGUh9F7QcMdqj+dOGrtBawd0zjhZ2uBxP8Ghvl4WhbuTEOo54mlAg7RZO0eP2Tg==", "signatures": [{"sig": "MEUCIQCt7SnZRDaClYsMCSVOKWzoSiaOLT7SK45iEipB7ErwaAIgXGkBZdA7jgtWXJ5Y9zKhvjSLiDC5CYDhYQoQdYGPTH0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592200}, "engines": {"node": ">=18"}}, "1.1.16": {"name": "@ai-sdk/anthropic", "version": "1.1.16", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b93d939037d2827a2fe3373c0972f1d1f7eaec0c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.16.tgz", "fileCount": 16, "integrity": "sha512-wRwi<PERSON><PERSON>NSJjPA1HSEKdkqcOD8MWyy7qNNkX0S+P/c3tlKsKaK/3DOfIrXXumrtVTLmV8Yjd41b9yU1cFHA63ag==", "signatures": [{"sig": "MEQCIDWqJTQ2x16vmd1vaaMIm86nyw1op+nr+Sox+F0EGO5eAiB7e2Bn5QdkV83fz0dj0N2lsSniyvlD7pFHgglDHgsyjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592298}, "engines": {"node": ">=18"}}, "1.1.17": {"name": "@ai-sdk/anthropic", "version": "1.1.17", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "69fceb3aff277dfcc8b8d147af22c15774c905bf", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.17.tgz", "fileCount": 16, "integrity": "sha512-t4ipxope34TFvWa4SrFsg2//aAFk3hUv2gxlM5W39IdsT7Hq2nMxJIcXE6C19YdhE/0Kep32iv38BgPaDZet6A==", "signatures": [{"sig": "MEYCIQD2t9MPMSx4lP+G/1ZoyY9B9ByzxgZByDN8oxV2rmPACQIhANqs+z4aFQKmL0WQsQJ/Coqhp9Pw+OvcySWpRhNtwuxN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592424}, "engines": {"node": ">=18"}}, "1.1.18": {"name": "@ai-sdk/anthropic", "version": "1.1.18", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ff99a6e51df95a0db16a6ad7c48462902197ed5", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.18.tgz", "fileCount": 16, "integrity": "sha512-OpCcDNWh62Masa9xfeyDbNu7UBh6C5xof0gzIM+G0pHvlsXWJmzfXoT5xwnshHltG9eiN3EjFPP/xxtpPNPbEA==", "signatures": [{"sig": "MEYCIQCrzf3ZE+sGZh1uIZa8ger5pen26Ec3h3ZLZ9Q5Tinh8gIhAJMts96h5qc774VvJNXvwaHg8MR7VghY651Bh2KglxDZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 590862}, "engines": {"node": ">=18"}}, "1.1.19": {"name": "@ai-sdk/anthropic", "version": "1.1.19", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7ce03efc76c5e4b7dfc3ab2ae65352c3437d3497", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.1.19.tgz", "fileCount": 16, "integrity": "sha512-2D/4NmyLv37LjUoZFwbaM/WwOWJFFDL8dfvdsaLFgcMshj9Ikh9aEnFXKpkyZox0wArtmBpn6YmtOAOTObKxEg==", "signatures": [{"sig": "MEQCIGEjFbHMezlYvTu7tgBu63Gx7GeBtIBnI7DUDSpwqR0RAiABeKTIoepof/qc/CiwcsDEcBQAzadX0RGz7vs7pmhz7Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 590960}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/anthropic", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f99961f52a6bc70ced2696dbd05a42e75e1746ce", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.0.tgz", "fileCount": 16, "integrity": "sha512-VMm+wAx7FhSMSjqJv7Zeu3GVxzUdohmWoFpzDCWiODpxPGFHw0d0spDR3sVpk5KcyGRJfebGd86ZtpFLCnagOQ==", "signatures": [{"sig": "MEQCIANdxl50xFXuC5WZyCeeRpT78bmY6QWOrtH2FrLYVSVkAiA18D2tmUCTuc9ypG5T3y+XPv7O3aBHiWxK74Aj8GAkyA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 591127}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/anthropic", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3ca5d9cfafda27ef4c69b7bbabfd99a3aa9a4e33", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.1.tgz", "fileCount": 16, "integrity": "sha512-X2bcuDXDKl8UCxUbiK+BX/xW8FLLzc36jngQmQycRvzr1Lmc8k8l+0pgLjgvmWRmbF+L9XOzP38Wm86bdTrFzQ==", "signatures": [{"sig": "MEUCIAzJLDLz4dvvwAq45D3dCwN5Do1BQ9uAJxNN6ShaUkdPAiEArjhO2DZQw5VWGb4gslNGvnqJ180+mUZrryQsV3k1FrU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 591223}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/anthropic", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e9d10d577dc49d134398f6ce480c1c3b85a05f93", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.2.tgz", "fileCount": 16, "integrity": "sha512-BNZTtbP+zuCzUf8hkpTx7YhwjbQ9oLh2yhjgXC7X1QcNsn9TFRaweX7CP8USM0g6lm/Crm1Qn+4tOCe1p10NTA==", "signatures": [{"sig": "MEYCIQDy4p/Vfv/ahiyLdQUSINSQjMi88LQKnL+oUGqpLXi6MQIhAJhEMcDHKm/0AIY/VHRcdrVx1bpH4xoQlNcBg1/UT0E9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592299}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/anthropic", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "611697cd292f877e980fb38a60aeec95240a3c60", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.3.tgz", "fileCount": 16, "integrity": "sha512-snfIh8XfJD10SmQYf6I3vw6NMi4A6KZhS1Cmju2OFvh744Jh4KS4ByEc7lQb6901gcGjqg59TNvfwUA2uvMRcQ==", "signatures": [{"sig": "MEUCIE0qd5CDrhmrpWuOPo1Lniw813TCCvS1eJ3NRnIN6ma+AiEAomNr248wvRtq/T/mTqI8lT+p+2zaU5hG6bPsCKQsbUI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592395}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/anthropic", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "31e73be896c29aa376d76afe69a7d8b9a228a91c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.4.tgz", "fileCount": 16, "integrity": "sha512-dAN6MXvLffeFVAr2gz3RGvOTgX1KL/Yn5q1l4/Dt0TUeDjQgCt4AbbYxZZB2qIAYzQvoyAFPhlw0sB3nNizG/g==", "signatures": [{"sig": "MEQCIAxz0lOMaFvLKH3oTJuJ37HHiwo4l76pHU9cj0eOuKqLAiA/HFYC0dMBSmflIsgjZ38/NBbDflazPfMRD8R8SxqLMA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 592491}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/anthropic", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c87926cab84d3b37cc8eba9bc4ddf34da2de1182", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.5.tgz", "fileCount": 16, "integrity": "sha512-Y4ZO6kpdgaK3O2HQAyWo1G8OGSSkjrQjGVMEIyzlHl19jBt4HspDr4ujhvo5lZFofz5GhqZWjlwyI9prYpC43Q==", "signatures": [{"sig": "MEQCIF8lQq9+nANuCkt5ft1p3ppm9ivGe/+goI0O4RBmDExxAiBaKv2Gx7B+QN9eenPW5adl1tocbL5B6ikUKF/dI25Dpw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593353}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7386c4fbaec8ef552a071221fedf06ae99a4d4c7", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.0.tgz", "fileCount": 16, "integrity": "sha512-C3ECz7RrUwK1q04mfz38c5O/gEiDNRejaiCMxlh0YtMDagJbkqbK2Fhm42/uWCbfUDSOKR0M8HEqghDCAJkubw==", "signatures": [{"sig": "MEUCIQDeQfnZwoaEtYFuUR9dXO5b8Yt9klK9ylLXudEqJusZhwIgX01xj05vkcX+pHufRgSimoDjF2mQDFWAaCyy82SlQmQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593540}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/anthropic", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7364d887df7eaea594408c6a5bccac2155cf1bd5", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.6.tgz", "fileCount": 16, "integrity": "sha512-Mt8ZSkhwnKHfwPPIviv3xgRE/nch2Mu4Fdh7oJDJvPDRJ6tNidCJd3TMwdlrlzPskF7hxCmXmd36yBgZZgt4cA==", "signatures": [{"sig": "MEQCIFH7KDL080EX6fqs2m3fGYn0lfGdaw09o50/LgxJm5p0AiAqCGWpGMqC86rSnOt7gpGQHumXKZ404m4rgkUP0zvZSw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593449}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "19dc71eabd1f5b878068cc4ce751093cb5ef2308", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.1.tgz", "fileCount": 16, "integrity": "sha512-Aymt7AIxX2tx7JLSl/oDe1e7dUJ4S+ld+kRWhNN8VjyroB3S8DJTuLLL3Q6NZo6Iddz2kMN7jB8oLQFSlPZ9pg==", "signatures": [{"sig": "MEYCIQCU6xVBYuFAqHoHNNNfpUBa9rp9vBAlTYQ2jc6KQ5aXhwIhAOdDwaL2GDhTzMPOHwnI2/MGkyZ6N+OLMoh2k9BAUfe1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 594389}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5122ac398d02ff1103c05fc26629aa5c08e7876c", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.2.tgz", "fileCount": 16, "integrity": "sha512-bz/hqgfWxLEpCn0SVcd0kkHxxaXpzTtDQykIn465pey2cUpGOM7cO3h163FM6+ji887WZ0Rk+RR96XmVORUlPg==", "signatures": [{"sig": "MEQCIGQvdV9LKYWGUoIUnOtqUePqAq27r/xyxLcS8D217cs8AiBGNXhHSQ97x/w39dAMaTNmqpAvsFjTb/OYj6/ICDbBoQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 587122}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/anthropic", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e7f96d30358d07600e38e3990420add953d85c34", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.7.tgz", "fileCount": 16, "integrity": "sha512-4U5ysC88kZvFSuUnTzoovUlq7ktQMVUlwVfU6CXcQvq43mpTyHoDGsVjDvf580Lelo9xnXLFodRiabKeKR/4Dg==", "signatures": [{"sig": "MEYCIQCGxIXVce++ntJ2F9G3LWrCCqCPdVDprg4+yPhWRNYiTQIhAPLcRVnqAU99DSNwBAK4dEMTanSuNI4wld7VfnlT1Hw/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593572}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/anthropic", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5338b37d78dfe054b82b5f14e31703dcf72733f6", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.8.tgz", "fileCount": 16, "integrity": "sha512-UchR+xGtTASkHyJ5uX4jVJGfxo2SCoqxtHJQzuO8INYEbpFwa9cUD06934tNqICxsNOhupS9y4b1sd/Uep6OXw==", "signatures": [{"sig": "MEYCIQCjBOtrY1kxXswTh3iB6ztSnbtD4m7qs29pmCHX6r7MbQIhAPSV2EM1MI/W1YzYyxlRoYBCO8DpZYLpbN63ZjMkXkIu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593695}, "engines": {"node": ">=18"}}, "1.2.9": {"name": "@ai-sdk/anthropic", "version": "1.2.9", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cb2ce44c431181667fe827a4a05e65d40a3b1569", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.9.tgz", "fileCount": 16, "integrity": "sha512-IsPKFt45rEhrAIeJisNr5hS1N6NPiVD/sBcS6wqJc2eKp32Q74SqDn6hmEgk4MlqxuWzpUCQ2kjsa75gAV3Hew==", "signatures": [{"sig": "MEYCIQCYJB6EYMSEuj6vrr3xJgRGD7BrcpFTkj/pWck6oQHL1QIhAM9uI4JzSs/iRFs4RGgo+PU0o026EBRwPkOoX+jwG8H7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 593920}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "451226aaef7cbd6b43a8a9ab8874f67731be873a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.3.tgz", "fileCount": 16, "integrity": "sha512-o3ZOxQ6mSYhC7V/v0YxbG6LjJCNFEs02jFxqIy7D4ZrmQX4gyTBNGF1b4NB1WWOVYMM5tJTEhLaCSq3hqc6uFg==", "signatures": [{"sig": "MEQCIFTp/bWifQJ56uHu0So+gNSfUOi02JERgmFtgr5dSOuqAiBYCGtfBbJLvCFDOpwgMdSoRjOgclFKBwTc0pbKPWb6mw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 587809}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "47061d48f29b70b5d6c2c21d00f20fd5a4308b2d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.4.tgz", "fileCount": 16, "integrity": "sha512-dTXn4K6l1IOc78R3n9f76ijffmRYSob7ceMIokkCj4mmd13JpPLZuHgGu0RdF98YGawlSwY8auOCrrW1V0pw8Q==", "signatures": [{"sig": "MEUCIQDAHCl8Y0QXw2l2+2KBFTV9/rtBXODLT7no7fyCer8MyQIgdiF0ZyKDTByUHTdQRuzoEYHLUgdgcYygjJgg5Dyge2Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 587591}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b9fcf14e6fe27d3e428c71251911700b1375964a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.5.tgz", "fileCount": 16, "integrity": "sha512-wV2JWj2Jy0K/73BWlhJLhpDchKOqMhjl9ESfxhU1WtZkqMxnIrzRS9j87eRaw1xTtbF1LKIzNX2qTGTvFVlz+w==", "signatures": [{"sig": "MEYCIQDuXdnuQvFOVhtQD9ZjMM1EZsof44VNWstyCPYNZPyTPgIhAKKPdYEXfy0k8HDknStZ0/qtLXNCbz2n9wPzx1Gw0Zw2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 586375}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b43264c2aed345ff7e6b3b0383684425520dd86e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.6.tgz", "fileCount": 16, "integrity": "sha512-uRBXTLjlXRDjcvrFayHdMCzGr4J0e0JBcznnchBIC8WJmI8ZLgWTqC6kTmYkjGxN3afzdW0AvQuMcUZqS6Ab7g==", "signatures": [{"sig": "MEUCIFdotyhpGlb69yXxOown+cisDo6gLwuGWeH31LSO3432AiEAznTWxVSdpvA5vPhOMyp6imcBXCsWV3b52TYU0Y3Eecs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 587064}, "engines": {"node": ">=18"}}, "1.2.10": {"name": "@ai-sdk/anthropic", "version": "1.2.10", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0591512d362a516acbe358d955384ab551dd731f", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.10.tgz", "fileCount": 16, "integrity": "sha512-PyE7EC2fPjs9DnzRAHDrPQmcnI2m2Eojr8pfhckOejOlDEh2w7NnSJr1W3qe5hUWzKr+6d7NG1ZKR9fhmpDdEQ==", "signatures": [{"sig": "MEUCIElKP6SycHyvk+blJ5xkyxZ5HwV5s23jepHQqG17fir9AiEAyjREFvAHrfVx9BbZKL7QRrUJPR2Htlw7bEhDMxEIhZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 594045}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "54220b276fd95726dd6cd6b1b4177ece44a0b255", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.7.tgz", "fileCount": 16, "integrity": "sha512-35AnRll+V5KhuRC+S86PQPLW9mu1XCMa34GsSf0PI28f34RubQU7WUVyMQN4Fi/3Y1NApuqxXN6ut6q3atZ9Ig==", "signatures": [{"sig": "MEQCIH5gj2cxL8QbZSac9X5qt/MvRG3FB+JAj/QojgHLklWaAiAvHi0PddL09V3FWZL2Uo0AAmSnXlqC0GaB+yfqKLeA8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 591533}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "802c87273a17738ccef8274ea37edf8234eb57b5", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.8.tgz", "fileCount": 16, "integrity": "sha512-ERPMZZQ35PytCW/3KU0cST+z9/SHg6Zu1fmzt0WbYWvHuEf1M4BNVwN9GNFLeedM7QOayGga+TnZ6TKXF38kwQ==", "signatures": [{"sig": "MEUCIE+pgLLCgu/dFIKSXiX+Pr6kCH6IFtcUCM1EIYHrH28WAiEA4kZR6k6nPJHJ6Ft1RP5PMh2YLKG/IAZLVNrcakA1pVQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 588846}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1e7df40b1dca792e852cd7faa91bced0c49ae1ba", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.9.tgz", "fileCount": 16, "integrity": "sha512-ElsHvgjH2qMMPbk+nd8TkUtx2CCBwzGHWY7NJR2ABXYkY27rSQqem7IBHgZVf8FCNj9ZJ5iHi0Pr4dW21BroNg==", "signatures": [{"sig": "MEQCIA2cRljuLiX2pdvKSl+4U9G5B5SAaabeeO/bgtI8R6MGAiA7cwgZUgyJedj2pbDGJAj5mFikvx3ZKAey4cSq/4aiNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 589472}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "559b3b1054a9ef444dbbc8fe5568def3784233e2", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.10.tgz", "fileCount": 16, "integrity": "sha512-Z2Gm5fJAyex4NZOkbVnRyhU2+PKIN97+BAs6t7ovBm8HociiIjUb3+UYJpbmdw4Mp5dsBTiXmMJgQRvO0En8bQ==", "signatures": [{"sig": "MEUCIQDHodstf9qLT/7gD48PwDNqhVvgtcYpN89MZG598g57QAIgOx9v2Jqkb3ZVmrtkAB0n4/SjPQUoPQkszy8uZG5zIwo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 589626}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f67b058a835a807272d6aab4dcc3ed472b6e43a", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.11.tgz", "fileCount": 17, "integrity": "sha512-rD+aI5ZQDOn6Lk/dMADhyFqh0k545ZXtoXKrt9FZeCnm5mfeNxffiwfEUsEn7XcUxT+y7VLzuvpGAJFz0KsJBw==", "signatures": [{"sig": "MEUCID/CCujUZtfs56ipgv9I0pY554YhN1UJUfU04zn4gZ4wAiEAmw7+3kqbrYCMSIYk1ue6UxA12j+q8FK28Fzpg4BgIrE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 606047}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b480f13517d083613145bbf6a05e46c468c6ce12", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.12.tgz", "fileCount": 17, "integrity": "sha512-xpb9Y/4VpqOxHXO7zjPUGhyOeJSSbpF67++DAQDWYWnHEqEW8PZVTsAnQo+UM+luI6lgRSbqmyBQLlut3qPW8g==", "signatures": [{"sig": "MEQCIA6fYl7kCgpYskD8kkBuyUUZX8JUeO/PCehO47LbPqhSAiBZ4gFP00AsjbpoHnQPGxDyluEOmJE0tyXiSqnQCYB+SQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 606618}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d09501811cd0cba1208e80a1b639056bd9762336", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.13.tgz", "fileCount": 17, "integrity": "sha512-i0+dXH3uFjkxi99kRUhBeVDGf05m4F1yFXciNxyY9NrI2T5hx8GIPv+oIcOLmP5oooigxEamUEUcApvA9mvZRA==", "signatures": [{"sig": "MEYCIQDgDK/VJlBqZEkfRN64yv80NKSXSutn2zIvZhHq/bP5CwIhAITwdnvR6OPL44VcDpzk7sgbHaGXT6Jfbc6M/AdCHTq8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 606624}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "afe900a0b66f89117fa68e7ab7f9b4ad9aca4be9", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.14.tgz", "fileCount": 17, "integrity": "sha512-iCPvzP/7Wr5xfK0k5CBvNwteCDQpYTdI6loKA6noqN02DKObSQK3sbFSw1ARc486oubtgEF43npAYtjuOM7GFA==", "signatures": [{"sig": "MEUCIDOZAg8Cp4fOZPY3mX0ayxN2hh4o+Fs8TmwmRaYk8AdoAiEAkN6HFl35eNuQTVXHheXOnEU9wfIrYK0aMMOcfQH8tgk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 606810}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "290e06d1519d4170c0fff4d793550caf4dee5f4d", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.15.tgz", "fileCount": 17, "integrity": "sha512-OH8lrVbnJNCAMrVh61TAq3wDlIsGIKyqpbipEvLZARAQ8DdLHHjIbD52H4pgKP4Yaj/xfke8uJ/1fAw/ByzJ1Q==", "signatures": [{"sig": "MEQCIAjsuOLgX7X0hp8BuxA5YiunZof/i6qMWOfBcOWa0ONNAiAbHIi1JHXnMDu6X87IzZ0Ht7heRBkwn5PSVqmOjqUP6w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607274}, "engines": {"node": ">=18"}}, "1.2.11": {"name": "@ai-sdk/anthropic", "version": "1.2.11", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "511e92552164225c491b3ba4cdc4f4b856a27bc7", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.11.tgz", "fileCount": 16, "integrity": "sha512-lZLcEMh8MXY4NVSrN/7DyI2rnid8k7cn/30nMmd3bwJrnIsOuIuuFvY8f0nj+pFcTi6AYK7ujLdqW5dQVz1YQw==", "signatures": [{"sig": "MEUCIQCggpU/JiliVia7UGGO6JEl9QSICt2KaBJMm/UqxAGqawIgLw9Ilesqv+eezuBlnQ2tuolUsF2f/b4n4l40fyiasew=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 594130}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "02b1ca935757617c60ca01a317f3a9340cd2accb", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.16.tgz", "fileCount": 17, "integrity": "sha512-P7s3tvZkt2+zYBoGCGOsXByuBtDJqC5uKxlxJMWTbde9+i+kr/RNSALjBhYtKOxek67YBDb7CHkHrlW2q6bMEQ==", "signatures": [{"sig": "MEYCIQCyf8kefGA6x9D3rzHoBBKkvF+oborIamFahJHJIfunhwIhAL2VlDUauhyeocPvD7rTs4VJBdw8nKQ9sTTnvEd+jQKt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607390}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b20f2cf7adf9d6cf41e63d4c06f0b08ffa50e9b8", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.17.tgz", "fileCount": 17, "integrity": "sha512-MxImpZQjOYafkgWNhk7WJtfNYDYIxaasirh7TIYBu3hQAYO/C/nvkNGayFV0yY5UWz6ujSkQTt7XkHXMJCeqYg==", "signatures": [{"sig": "MEQCIEH9c5Io3jJgtvhKZMqsaZFKsF2VtZ59RvAA++gs1DrEAiAmxZfiyYemIx1cQQUNRkheHkLXW4lv6XFPfWfq8fMmVw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607506}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "26595245da3b11e4709558d1eb35e2dc5271eb06", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.18.tgz", "fileCount": 17, "integrity": "sha512-5eoXeLLuaUbEhXSymzY1m2suxsylo0cObSDVV9fk27/HJ10tzJ+2u33jBlddrdje/BLWXOFSHw9hDCf0Gx6Cqg==", "signatures": [{"sig": "MEQCIE82nRnvCZuajYYdAGew63WPpyff8Ya0nu5BvsCCdzRrAiAFHA8VKCnXkVZx8ZJQiqLkevQFjaDMu0+wqO/2mSUYPg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607622}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/anthropic", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "df5717d85d3c4cde4ad42e6fbc5d3bd354ee6859", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-canary.19.tgz", "fileCount": 17, "integrity": "sha512-uU8UeRmzKfxmaw7x/qL4hGjysPKzzttORx82dv+24wzoxkitYn9sjn2Mu3l1Q3jzbNC/wkyMRQDs831FggukKw==", "signatures": [{"sig": "MEQCIE4r9icAf/kj7pyIMKmiQ4U27t9wj7SvM6TTQhCHFzThAiBpb3ILkwXilDCdqMwn5Xe/O9odVic38IT6XLjiccDgQA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607739}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/anthropic", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "f30415d9453daf487cfe5139f945b4001f963e5e", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-alpha.1.tgz", "fileCount": 17, "integrity": "sha512-1Pb8T5O8SldQIdOzhr2ZxL4MK0l3c6yaioCFoafNQMkFymdcJv3teG9oX/2SEZIWRW7A7jd2EWFWlrD+GOVyGQ==", "signatures": [{"sig": "MEUCIQDRJbwbEdO3NhJ1wQyq1VVJhim4EiBS0bbO3B040QAvUAIgC+Bx0gKNSbEigtOVYc3wbs4M0KtVzssc+qvar9qjGHI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 607880}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/anthropic", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "b2aa937ffd588acc13aaebe261eebfec9753429b", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-alpha.2.tgz", "fileCount": 17, "integrity": "sha512-ynD9<PERSON>HZBAoLKUh8wqmS0EROSZQGSYZkVoF5Y0ZgMQfyVkeDXTl3PCXfc0jqwBTjNk6OOHza6BD5hCZDSjh0Sqg==", "signatures": [{"sig": "MEUCIBPgH2atA29xPYWMWFXQImzZ7DP+oJbiTPQpqoQLUZgKAiEA06NoYIGYFm8HmDdxQCi5yaF70twdwF6RkKETvXFIu+A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 608027}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/anthropic", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "586855f412c069a5e4f3a505c82f68ce2296f3c6", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-alpha.3.tgz", "fileCount": 17, "integrity": "sha512-LqVBnL+YwKrJvqW9BwDUtvF+65ZXuY++ja8mAACxuQJYrlZXz2a5wJxXssuZ8ZfsqTTpkzrv8OVXahcExIx5Qw==", "signatures": [{"sig": "MEUCIQDffU6O/McQ3d6cyctCee+73JWMEjyCQUqTNi5zh2CWuQIgf3mfaEsHS5c4hJDfewiJmEXrqzt+O/Nscj2wuHVmiXQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 608174}, "engines": {"node": ">=18"}}, "1.2.12": {"name": "@ai-sdk/anthropic", "version": "1.2.12", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "80a4b2527c6bb120778fbc83da4af775aae953a5", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-1.2.12.tgz", "fileCount": 16, "integrity": "sha512-YSzjlko7JvuiyQFmI9RN1tNZdEiZxc+6xld/0tq/VkJaHpEzGAb1yiNxxvmYVcjvfu/PcvCxAAYXmTYQQ63IHQ==", "signatures": [{"sig": "MEQCIGKKXsczQsKvkGlx46ljA0bukrVpSOnpbLK6RIi3ExkNAiBPc4u5/dkjbdbPFL1zwRLDtHSeTsGMTXHPvrvqE+K4bw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 594445}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/anthropic", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-uknR4CfBVjkxhlLqOz0ThCtsH5GzRT6IBVzV5oI/n+CgOSwWNQXfsyBmmO8RopPCEHq+HEb5xSbG/l/FmkxafA==", "shasum": "e752faa859df72939e1e17f378cab9281655b185", "tarball": "https://registry.npmjs.org/@ai-sdk/anthropic/-/anthropic-2.0.0-alpha.4.tgz", "fileCount": 17, "unpackedSize": 608853, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCvsm7RwRjX1WRgkKggiFM0x901danTKeQUlVz4INB+3QIgG3rkAu8gNNQ7lhfQTvDpTMWJyPdlwKsuhgDBxAIVSD0="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:29:52.266Z", "cachedAt": 1748373701642}