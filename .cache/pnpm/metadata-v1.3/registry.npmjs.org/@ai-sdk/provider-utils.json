{"name": "@ai-sdk/provider-utils", "dist-tags": {"snapshot": "0.0.0-85f9a635-20240518005312", "latest": "2.2.8", "canary": "3.0.0-canary.19", "alpha": "3.0.0-alpha.4"}, "versions": {"0.0.0": {"name": "@ai-sdk/provider-utils", "version": "0.0.0", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.0", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "workspace:*"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b438d4525ff4dc6bf995ce4aed56f33c5a9dbcf1", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.0.tgz", "fileCount": 14, "integrity": "sha512-A6gFv6EHimayd2c2ayWMpKwADgsRrJg4DYYFnvJ71TXaWuSom7vUoALIobcaggmZ76xuzi17cXPFYg2vVu04WA==", "signatures": [{"sig": "MEUCIQDqqQKiPA8qUlN0UU7JfmqP9S4ZJefjhCZvOCwUkeKDvAIgBhnCuTXEMo4aG6fH7eACghEbjQuwNSdoR/SbEx/Xw5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617076}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.1": {"name": "@ai-sdk/provider-utils", "version": "0.0.1", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.0", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "66aa4d6acb2a9604f205c2e21f0cba683d02b7dd", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.1.tgz", "fileCount": 16, "integrity": "sha512-DpD58qFYHoPffBcODPL5od/zAsFSLymwEdtP/QqNX8qE3oQcRG9GYHbj1fZTH5b9i7COwlnJ4wYzYSkXVyd3bA==", "signatures": [{"sig": "MEUCIQCB8fr/vgoUJ2V2V9Q7rrtno1dWLrIthOWsTjRdLOvM/AIgeIpy1d3gM5qjmWc4WRZz+IQn+KZjztitPdSHHvtds24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1631187}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.2": {"name": "@ai-sdk/provider-utils", "version": "0.0.2", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.1", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "78eb6b34c34be9c4cb7ea4ceaea1ecfab931245d", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.2.tgz", "fileCount": 16, "integrity": "sha512-du8NIqHo0+8tyoocmK1xvT6Sl15rDAEzMEo+Xy1jSegEfYZaU/txhZ84dGAx3Fgcopi0VsXg5l4AHRH5nwxLQg==", "signatures": [{"sig": "MEUCIEuNdmTwX3DhDdSR/92DoYHfPl/X54EtUQZ1b6vleNl8AiEA2/Xq+CrgZkdtPne63yD4vetVK1BhdCHe43RbhMUW4nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1628326}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.3": {"name": "@ai-sdk/provider-utils", "version": "0.0.3", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.2", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f524be2d0fa7c78dcb4ce19c3e981822a5cdecf4", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.3.tgz", "fileCount": 16, "integrity": "sha512-13+4v62ylduGvwz8966SdnT1aoQRO4QkWzZJyVswdgmYeX2vrXuU4bssK578+FXIXc1jwi2hLZO8lqgLPT/xDA==", "signatures": [{"sig": "MEUCIH/vQW/ixwFtJzfM7nwhnyan+h7lfC8mf1YwBSMITzsbAiEAhHYIkDeV69CVaWNTByAEKP43TXoAccSGmFJ/nVvVsxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1636777}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.4": {"name": "@ai-sdk/provider-utils", "version": "0.0.4", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.2", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb758e6705368fcca9f3a0a7637bcf947d3cf6b6", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.4.tgz", "fileCount": 16, "integrity": "sha512-LOBdO+2Fs5rWA6Akjxj42byW31a1XWZc32HUst7Hz0wOQrbWFl4jnElz38BDDGBtIZxPqWpEvoanfj6xXn8acg==", "signatures": [{"sig": "MEUCIQCXC/CBrc3X4QPa7a4Q3kT2ZbMHfryeRnl2/PL0ixn9TwIgRaFf+o4Z1xzA6S5CV/FWBdeKZHz1KBwooLEo7YkAoJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1637476}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/provider-utils", "version": "0.0.5", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.3", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3fb0f4fac88b36ad2f14efe5bb7ecae22aba3ca4", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.5.tgz", "fileCount": 16, "integrity": "sha512-VVy9eQS+vS2j6cqTEQ9htMHz2nW/HFAkDXLvNFPoi1pZkviknJZEzb+DZUna6Od+jBf/TVA0HZwYnyGDaeI9cQ==", "signatures": [{"sig": "MEQCIEuT1ap9GHQmY+5jmIvApidV7nBgJ9R36whiV1/tWHy7AiAY0dow1ds7Y7uF8uOk0p+5MqxCITeT2hTAG6qabCmLuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1637476}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/provider-utils", "version": "0.0.6", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.3", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aa9268abe3a0912d6522b0dd38b54bd0f2c601d3", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.6.tgz", "fileCount": 16, "integrity": "sha512-SxOZgSxnaVlW04/SjfMoAD45kWOWTWx0QcZrHaQnePooLhyk5AqQpgauPijL803uoJPCKfzd0UBv1gSKvWiU0A==", "signatures": [{"sig": "MEUCIHXORCz74UYMj/sj3OuoTawyBBvexeb7YVNra7T0uyjfAiEAwk8iw/3mEK7RXlbEmL2PBVxfVC/2C8J7Q3CUZF8VsvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638949}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/provider-utils", "version": "0.0.7", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.4", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "530f0870dfba1c12c08e03ebdcb86dad36847e62", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.7.tgz", "fileCount": 16, "integrity": "sha512-zs/H1T2U4NCdiPBtBKI/FXQirAyrwgwI+qQ85Y8TZLAYY59+I2yVUlSb3WG7Zp+OmaXBlppUGAlEpUIDBrErXw==", "signatures": [{"sig": "MEUCIQDolw8LxF+WD0mrYc8kuLHAV6ai4Ky+MICjAJezrMOCkwIgCbAxJYk2B4s0N3JqJFTHmBGEeAEMGBWYhkEYGwIIzr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638949}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/provider-utils", "version": "0.0.8", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.5", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fcfff6263a0ac4e3868380af2a12262d836f28ee", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.8.tgz", "fileCount": 16, "integrity": "sha512-J/ZNvFhORd3gCeK3jFvNrxp1Dnvy6PvPq21RJ+OsIEjsoHeKQaHALCWG0aJunXDuzd+Mck/lCg7LqA0qmIbHIg==", "signatures": [{"sig": "MEYCIQCWVsF9PUIPv3eiVUSZ4U2r3crb4HsD9fuKZY2hUvqjKQIhALZmcRkRj7uBhupmF8pczy6Yn0AwguniItbeALpcTDql", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1638949}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.0-85f9a635-20240518005312": {"name": "@ai-sdk/provider-utils", "version": "0.0.0-85f9a635-20240518005312", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.0-85f9a635-20240518005312", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5c8043697b27193542bc2924b70ed75c6e7114d0", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.0-85f9a635-20240518005312.tgz", "fileCount": 16, "integrity": "sha512-ps1FAgEqhKXh7j2sCP1+ez1Rv5/8do2qXyiUeeo0wUn3GbueWArXaJ09+ZkAWDY1JfkpXkwLhzfrOobY8gcn5w==", "signatures": [{"sig": "MEQCICVRRPpqs3o4xXuS8rOJlUD3Cn78PZ+Wi/KI5UcZZLc9AiB0mjYAh0+sQzH2INFPi2HaHhf2Y/kiFzWokxZsfi8WoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1641804}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/provider-utils", "version": "0.0.9", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.6", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "18f459e04d897162fbd0c475fc642a971b2dc193", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.9.tgz", "fileCount": 16, "integrity": "sha512-bZFTVp/GIZ3Yz4VejYs0B93bmnwfk+RoSHAQxAe9WamkQFrZLuNgd2ngAFjF6b4naLynWe1ZCk+dLWEtozCciw==", "signatures": [{"sig": "MEYCIQDXkltDl3POLnFRi8yo2ywBt8zWVJtRPd3eQWY8Uw5VRQIhAPAq53urH8HhcMxYIo7/JhcI+bA8w8BFczjp2vtvsw/G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653765}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/provider-utils", "version": "0.0.10", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.7", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46bc92e52ffa71e83318f2709eebd09b12e42a26", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.10.tgz", "fileCount": 16, "integrity": "sha512-VAtWHXOKMXfo+Uz384wva5oqm6BTmtp3kVCZGcScubJYjIDApwAIER/a3WcTkeoq5ZXaN23bHT0KBfOhhBSgeg==", "signatures": [{"sig": "MEUCIHSPO+ctcjuQ6YsJmxNSxdqHQmnx15+ISz6Giva/ZKhtAiEA5/Bbie3zJGhmfb5ewT0VVM0USfMjAuT2lGdkJPzw/yI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653766}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/provider-utils", "version": "0.0.11", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.8", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f89cced1b016ce4e4700f9776a63cdd0db4c8f47", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.11.tgz", "fileCount": 16, "integrity": "sha512-JRDrqL2FGDmLh+a4R5qbS8UrWN9Lt7DpDIY1x6owgXjXkz3Umm1czs1X32VlL0M1dpoSxu4hGBFtXd56+kDzXA==", "signatures": [{"sig": "MEUCIEjlSYnmByo8OYhqJHdOQiDhof2NLBkVCZt8RLjy7cG+AiEA4F9qEPoQ4KqT0rjWI3ZazSJpsiADykEy8Axh3TIeWI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1653766}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/provider-utils", "version": "0.0.12", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.9", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1761ce0fbbcdc4a1e49f0e2434601732c23469e0", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.12.tgz", "fileCount": 16, "integrity": "sha512-jt3RwW68x+fVPrsmcKR3RT+G+ISgsO7mu/M+kCnZmxR4JtbypgS/JsAtnnoD7YtAcqLplbYBzJEsRW4CRqIWMQ==", "signatures": [{"sig": "MEQCID6wz+8vZkenC/alfi6A/MyNNOKm0HmnwbN4DKCdukdTAiBMbndnBmhgJtmWA0swWsUSARPVWvfvhp1z+yiOFr8W2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1660374}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/provider-utils", "version": "0.0.13", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.10", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "59b054adb125f665eaa6056955ea0d1d719ef779", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.13.tgz", "fileCount": 16, "integrity": "sha512-cB2dPm9flj+yin5sjBLFcXdW8sZtAXLE/OLKgz9uHpHM55s7mnwZrDGfO6ot/ukHTxDDJunZLW7qSjgK/u0F1g==", "signatures": [{"sig": "MEYCIQD03xr3pe/7UqFsO6/M+ZbDiBmg6L4SUpzEJAqdFXIMYwIhAIM4pOOEKixGvOV9xkE9GZQO0bFgJx9rr1/QhA4zkw6W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1660375}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.14": {"name": "@ai-sdk/provider-utils", "version": "0.0.14", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.10", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2f5936b995ab625a0f5d7c3509001a2f36e520d7", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.14.tgz", "fileCount": 16, "integrity": "sha512-PCQFN3MlC6DShS/81IFU9NVvt9OekQGiZTEowRc2AwAwWrDsv7er3UkcMswFAL/Z7xZKjgu0dZTNH1z9oUlo7A==", "signatures": [{"sig": "MEYCIQDLjUxrrFfPpKD3pDel/kL881TYRB1qAnX8ojHhF2IsQwIhAJI+OkX/AgSUIxId1/DNYZQyEdc8GVpHmHbx41v7p1bo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1661544}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.15": {"name": "@ai-sdk/provider-utils", "version": "0.0.15", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.10", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ea07c9092fb618cdda23387c9e71d72247318e1e", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.15.tgz", "fileCount": 16, "integrity": "sha512-eTkIaZc/Ud96DYG40lLuKWJvZ2GoW/wT4KH9r1f3wGUhj5wgQN+bzgdI57z60VOEDuMmDVuILVnTLFe0HNT5Iw==", "signatures": [{"sig": "MEQCICC3uDnpzHTlP1nJTJmGid1FseQfmXOgINp1qdLn7S6DAiAKkPRnEilrt5ru4IEgfRk9SFiQ4asM8V4tRQpLQkqWzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1672489}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.16": {"name": "@ai-sdk/provider-utils", "version": "0.0.16", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.10", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "515193209062fdcfc091ada0c3c0c6e134483465", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-0.0.16.tgz", "fileCount": 16, "integrity": "sha512-W2zUZ+C5uDr2P9/KZwtV4r4F0l2RlD0AvtJyug7ER5g3hGHAfKrPM0y2hSlRxNfph5BTCC6YQX0nFLyBph+6bQ==", "signatures": [{"sig": "MEUCIQDPttlLP5OWibuha24z25apQbHuwHBWiPoX95imkxf6CwIgNBfnIbs47XF9ab4Gz4Tg4+iE0CTE4PDb4utDpR5oSPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1674202}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.0": {"name": "@ai-sdk/provider-utils", "version": "1.0.0", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.11", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.0.9", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "52b4b7a05a1787f4bff17b8fb85979b92eb1b369", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-Akq7MmGQII8xAuoVjJns/n/2BTUrF6qaXIj/3nEuXk/hPSdETlLWRSrjrTmLpte1VIPE5ecNzTALST+6nz47UQ==", "signatures": [{"sig": "MEUCIQDI2PRqwoi5kbPE4+5YSGX5+Av<PERSON><PERSON><PERSON>Ze6sl3/kUcv5C6QIgBty2hbz8dt1Dj8Gu+T3yoc07N9+ayWeAlNq9i1EJZcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1679279}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.1": {"name": "@ai-sdk/provider-utils", "version": "1.0.1", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.11", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "695118bebfbb39d3a973aa90c82cd005bdf3b765", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.1.tgz", "fileCount": 16, "integrity": "sha512-PtbxRf/CQwNsXuLan8FmyseE/eQ3uF6LGAvjFD47MosHs9cYaWBDIjx2XraucdVWp1gmRYb2SO4P48xPi2r/NQ==", "signatures": [{"sig": "MEYCIQCHk/kvEaHPHwuyg7CRvVkLj6t9KqJvJIpDOgvjff3SngIhAMf5WVtDnXuaK/dsiZ2UEMBhKLZsymhezAM7tyHBTgnq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1264749}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.2": {"name": "@ai-sdk/provider-utils", "version": "1.0.2", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.12", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9ae1b277721b86985aec97a504c655993cce5980", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.2.tgz", "fileCount": 16, "integrity": "sha512-57f6O4OFVNEpI8Z8o+K40tIB3YQiTw+VCql/qrAO9Utq7Ti1o6+X9tvm177DlZJL7ft0Rwzvgy48S9YhrEKgmA==", "signatures": [{"sig": "MEUCIQCSH8tyupUr/7sbpikUs3MS19VWt5dvLwPj76Biv1Ik9wIgDSqb2+4kXuKa0TQubLIdDHzqIw7mglzB3QfO9C2AYxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1264749}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.3": {"name": "@ai-sdk/provider-utils", "version": "1.0.3", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.13", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73ac105de6872db9d7fd66c3afaca920d759f745", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.3.tgz", "fileCount": 16, "integrity": "sha512-QIHLu5fUT+9393SNQk0Zy6gKRIuWM+dOQtvUrz/wV/CpeqDSPbDmwPyvDVIgH4BvbVjivct1bb5vsiZom2xabQ==", "signatures": [{"sig": "MEQCIB6cAXIrzkbrzp8hTDHt8NqlDmXVbJj9g+wvRssSGoy2AiAszezPRT5BOu3aW0sqqEueugbRWG32Jepfj6kBktrN+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1270258}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.4": {"name": "@ai-sdk/provider-utils", "version": "1.0.4", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.13", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3b6e722b868bd312fdab1a8a86be7a766370ff34", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.4.tgz", "fileCount": 16, "integrity": "sha512-2TldHn54+HrvVHMCos9lrSMcDIp3cgnpphpYT/95RXcIzcGwAFM5MioDWnsrki3noplU5pVld6ylD3Vu/Xt3Vw==", "signatures": [{"sig": "MEUCIBDHk1LMKyX9ocNQu0kS48o2b300pN3GRi4thvsBgIbbAiEAju3JMjjTDhiz5x/Ri1ABatiaQueCCLDo5xBRcilpT/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1280521}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.5": {"name": "@ai-sdk/provider-utils", "version": "1.0.5", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "765c60871019ded104d79b4cea0805ba563bb5aa", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.5.tgz", "fileCount": 16, "integrity": "sha512-XfOawxk95X3S43arn2iQIFyWGMi0DTxsf9ETc6t7bh91RPWOOPYN1tsmS5MTKD33OGJeaDQ/gnVRzXUCRBrckQ==", "signatures": [{"sig": "MEQCIE531F6S0SjsS3Gd8K6PvlhiiQux0Ry/8O9D+U8Sh5sFAiBe6VNyRQBE9xErww/gy8EyDmP1frzrvGpdbNbC7nEVXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1274817}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.6": {"name": "@ai-sdk/provider-utils", "version": "1.0.6", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.14", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0aca8ae532feaa9762871c7b682b77ccac1b4fdf", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.6.tgz", "fileCount": 16, "integrity": "sha512-+wq/311mCRM+mtALiSR9e1E9gjAvQd8hLpONvUDFmmPG+NtaTpjyZIrMQ6fqZX3xm6DpUaf1d+VPooIVDoZKtg==", "signatures": [{"sig": "MEQCIDpDWB2bAPn4KgNf2jYqORmP6IInXoVPwDHztvYyMCCsAiBeK1zhQzgXIzT4qIjlkYi0Cc3TecES3OeCAbABNR35Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291285}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.7": {"name": "@ai-sdk/provider-utils", "version": "1.0.7", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.15", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fa0d09b69225cdba3aa7f3daa63231c337f5019d", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.7.tgz", "fileCount": 16, "integrity": "sha512-xIDpinTnuInH16wBgKAVdN6pmrpjlJF+5f+f/8ahYMQlYzpe4Vj1qw8OL+rUGdCePcRSrFmjk8G/wdX5+J8dIw==", "signatures": [{"sig": "MEYCIQCAiWecrDdeV68gYbKZUUxvG1GZGN/tPPkLgZwYeQMYmAIhAMe3ay1746wXkyej6zl77CXRinNHHK23iS2Sz9bqwDhb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291285}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.8": {"name": "@ai-sdk/provider-utils", "version": "1.0.8", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.16", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ea123b493d8af9ff706ca3c1a25524c6ea06f978", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.8.tgz", "fileCount": 16, "integrity": "sha512-FLAwVhycHSPINBSyDB4Y+t7UXLdXwhhzrJFXgKHXYmFP9K54KFhfWguKKjq9/MwhpNBt3hDg+Zty4cIASyX7VQ==", "signatures": [{"sig": "MEYCIQC8bROV4EYxcr4Hw2TX6MA+VHJsQ0HYN79dF7xy+hZR4gIhAKXnn1L/WrG3DPv4fABLSHA8U77CfZk2s4Flwn0J/QTn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291285}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.9": {"name": "@ai-sdk/provider-utils", "version": "1.0.9", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.17", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4582474991bf872f2e20a386ffc0a9e54cc6a558", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.9.tgz", "fileCount": 16, "integrity": "sha512-yf<PERSON>jUiCJbtGoRGXrcrmXn0pTyDfRIeY6ozDG96D66f2wupZaZvAgKptUa3zDYXtUCQQvcNJ+tipBBfQD/UYA==", "signatures": [{"sig": "MEYCIQCzgOq1aY3QqJysp31pQA1C64F4ylMdnncsCxUPq3/YbAIhALwpI4r08JAgrdp2l6d37cHaFSBWf40DJV0KMbAUoiWX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291285}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.10": {"name": "@ai-sdk/provider-utils", "version": "1.0.10", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.18", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "581b6a12f3ccd0e77c337b9035fead3a59ab78bf", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.10.tgz", "fileCount": 16, "integrity": "sha512-xciXF2PorLQMNdhYe+n9CafVkXZANHURsME85RXjtAoZSs631l2t8Blqwz2C/pHUb9bxLdMRRuIEB4PnHLnHvQ==", "signatures": [{"sig": "MEUCIQCbGpjYLlP32pw/DR4QCWr5ORn1T7qmcJkRn+lni7IysAIgZYkAqqUYKWrx7tP9VgkndGah8HeSOHSfX4Mqlrbwk9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291940}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.11": {"name": "@ai-sdk/provider-utils", "version": "1.0.11", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.19", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "91b5e855c93664a8f491107ae433fa3634a4ee8b", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.11.tgz", "fileCount": 16, "integrity": "sha512-u3BmXKg4MeA5s5eN9bWP4ybGJuOTRC2H0YacMCag5fcZ14S6kfukGE8MzRsGU2wTv6A16zLY0XqVvwcqe13mUA==", "signatures": [{"sig": "MEQCICQ5xy7VkY+Z/phrdKpXIBBbwWpPVe1IAD88I08ZnQGxAiBLz6EqIu+GkHyKlaWtGwTmyTL/SXfFMY+ajOSIzzz03Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291940}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.12": {"name": "@ai-sdk/provider-utils", "version": "1.0.12", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.19", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "45f282b530da5b02e5186c4bd2b509fd59d4cb5f", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.12.tgz", "fileCount": 16, "integrity": "sha512-j<PERSON><PERSON><PERSON>joJkeqApQYOWPLd3R/P+wUxppEBHUfupF7fN6aICzLVFH3hXUiR5PJ+0pcM/Ld3CZDPq1IKNZlufI5Bw==", "signatures": [{"sig": "MEUCIQDCCkBPgqG6GofXHktsJXM0GaVmSaC2nXtCwY/JxZPA7QIgZvejKebo6xZkL67ZcEQ4HKDo6Ixgewob9QuHnTzNwrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291733}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.13": {"name": "@ai-sdk/provider-utils", "version": "1.0.13", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.20", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "073feeeb4baefe9efb5106b99126d69b379f229b", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.13.tgz", "fileCount": 16, "integrity": "sha512-NDQUUBDQoWk9aGn2pOA5wiM5CdO57KeYTEph7PpKGEU8IyqI0d+CiYKISOia6Omy17d+Dw/ZM6KP98F89BGJ5A==", "signatures": [{"sig": "MEUCIQCRUHRI3jXeFa15E2Cb5fEfZEhDifc+BLZrqc8aR7BTvQIgUYmkzcLfcBXKQf8OirAx4LoFsW8SZ6kcjTTGlE8rz4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291733}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.14": {"name": "@ai-sdk/provider-utils", "version": "1.0.14", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b090d217c539606571b51ed372dd1157c3de075a", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.14.tgz", "fileCount": 16, "integrity": "sha512-6jKYgg/iitJiz9ivlTx1CDrQBx1BeSd0IlRJ/Fl5LcdGAc3gnsMVR+R1w1jxzyhjVyh6g+NqlOZenW0tctNZnA==", "signatures": [{"sig": "MEUCIQDrRQ1c4Eucyurv7yVNCioC3P4WP0FqEyya6GtzacnU0wIgCRNL5PYrRKTc+aMI541S6f3ZWHdNYkHx0744A62hkHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291733}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.15": {"name": "@ai-sdk/provider-utils", "version": "1.0.15", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b60e0a2e698e8e3e61d62cc42c0b538eb96abac2", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.15.tgz", "fileCount": 16, "integrity": "sha512-icZqf2kpV8XdSViei4pX9ylYcVn+pk9AnVquJJGjGQGnwZ/5OgShqnFcLYrMjQfQcSVkz0PxdQVsIhZHzlT9Og==", "signatures": [{"sig": "MEYCIQDhhwHnVCvm4hmhtSWDcxko/RLbJYfFOHDbeHTezGzY4QIhAOtk3vnlB2ZvNtFoVztA8MBID2leiQNMeh7EvfLe53da", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1297787}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.16": {"name": "@ai-sdk/provider-utils", "version": "1.0.16", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.21", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b866161032d5b47fc73ebc2dd5426990b3aaada2", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.16.tgz", "fileCount": 16, "integrity": "sha512-8Nd8vIkGTIthhfgJEdP9KyMlykehBNP/1J47eMC3vQqYgJV6r5Bgvl3LFVfWi9KzamiD8tp9nU2NJKTeo4MH/A==", "signatures": [{"sig": "MEUCIQDiGYmDh+84sfAZEg4J/a+IMJ0shSe+iNQ//ohxMxylUgIgf24SkAVo007I6+XhpQyG8FFSPrzOETBWCXX8q93FLts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1297981}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.17": {"name": "@ai-sdk/provider-utils", "version": "1.0.17", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.22", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "269cd194ee8e79bc308acb3c4b1f64be98d240ec", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.17.tgz", "fileCount": 16, "integrity": "sha512-2VyeTH5DQ6AxqvwdyytKIeiZyYTyJffpufWjE67zM2sXMIHgYl7fivo8m5wVl6Cbf1dFPSGKq//C9s+lz+NHrQ==", "signatures": [{"sig": "MEQCICCkMePqIWmP7zm/jnHkXBE9MdT1MKpp7U/hFG+7dSquAiBv9EvS752BdxD6e7EO8/SX3tuFYC4v37ydJwmiZxOc2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1297981}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.18": {"name": "@ai-sdk/provider-utils", "version": "1.0.18", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bd30948d6b4ad73a3fbc94f3beeb92f4fa198ded", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.18.tgz", "fileCount": 17, "integrity": "sha512-9u/XE/dB1gsIGcxiC5JfGOLzUz+EKRXt66T8KYWwDg4x8d02P+fI/EPOgkf+T4oLBrcQgvs4GPXPKoXGPJxBbg==", "signatures": [{"sig": "MEQCIFIWAvLXm7yglzwhVQa2XV8g9u+BUldmCMNbQOqEsaHRAiBjUZeSIQqBkogejK3Nzw0TxnM6uSfTlmh3+FkKoScB2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1306213}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.19": {"name": "@ai-sdk/provider-utils", "version": "1.0.19", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.23", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "58562b7317c706f5afb24352183e38be4e72528b", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.19.tgz", "fileCount": 17, "integrity": "sha512-p02Fq5Mnc8T6nwRBN1Iaou8YXvN1sDS6hbmJaD5UaRbXjizbh+8rpFS/o7jqAHTwf3uHCDitP3pnODyHdc/CDQ==", "signatures": [{"sig": "MEUCIFcpl36Di1YZJxyJtA0fou7x2Y1ThKHWs3in/kv4LuIvAiEA1/CYZ9fNWZxTnChA4KFJluwMB56jXypgIr+j3UiuobA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1306811}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.20": {"name": "@ai-sdk/provider-utils", "version": "1.0.20", "dependencies": {"nanoid": "3.3.6", "@ai-sdk/provider": "0.0.24", "secure-json-parse": "2.7.0", "eventsource-parser": "1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "46175945dc32ad2d76cb5447738bcac3ad59dbcb", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.20.tgz", "fileCount": 17, "integrity": "sha512-ngg/RGpnA00eNOWEtXHenpX1MsM2QshQh4QJFjUfwcqHpM5kTfG7je7Rc3HcEDP+OkRVv2GF+X4fC1Vfcnl8Ow==", "signatures": [{"sig": "MEQCIBUg5BFis/ARJUdBz6nlm3nM4gW+EotvlysqxCy9JH9lAiAIcl17uOnyswW21jQewtT2qSak01aUfybiBZmwQbhacA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1306896}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "1.0.22": {"name": "@ai-sdk/provider-utils", "version": "1.0.22", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "0.0.26", "secure-json-parse": "^2.7.0", "eventsource-parser": "^1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5397a193587709796d012fc04e2df9903b70852f", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-1.0.22.tgz", "fileCount": 17, "integrity": "sha512-YHK2rpj++wnLVc9vPGzGFP3Pjeld2MwhKinetA0zKXOoHAT/Jit5O8kZsxcSlJPu9wvcGT1UGZEjZrtO7PfFOQ==", "signatures": [{"sig": "MEUCIE9S+oYl5z4XbPNUXSkgDkEki91my3gFyLx+jj0uuSPwAiEAtCuyzPI6qQkK20nhvN+MjM1DrvIAqOx3/+2SFRyLPIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1313009}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.0-canary.0": {"name": "@ai-sdk/provider-utils", "version": "2.0.0-canary.0", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d05316203e7e1c10d86f9c022e21097b73ad112c", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.0-canary.0.tgz", "fileCount": 17, "integrity": "sha512-FWe9cmhsTLXP/SRXpPNMATnnS4I4miM5VLhNstDB8blaKjPG/TSq201ZlfrrIjdqvh/pdZjbYeXdSHN0A893Pg==", "signatures": [{"sig": "MEYCIQDeGoVSmh2hBZGmsMuJejwh0YJdsZABZ62xL+X/4R0dxwIhANM0zkyHkXCiYXVKR74vH0n9WrliMqDohBWgl/oox5Qq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1312384}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.0-canary.1": {"name": "@ai-sdk/provider-utils", "version": "2.0.0-canary.1", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^1.1.2"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "089330a14d46326ba094d3e9db5d199ae244c3d0", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.0-canary.1.tgz", "fileCount": 17, "integrity": "sha512-LDB2vFgZuCwkbRHMzqpwwxFZWKIwV4WdywJON2/ewo4qei/O+I2H4AFJOOfL2/7xxnOg5uZVE72BU+WKPUvlLg==", "signatures": [{"sig": "MEYCIQDEPnjnByoCo5MFTozOjHsXP9okZsEs8e7PIXnvzSsI9gIhAMD99ZW+GMeRayDTzRTUzg24vKaLXe+bFLiphbtZk/YR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311003}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.0-canary.2": {"name": "@ai-sdk/provider-utils", "version": "2.0.0-canary.2", "dependencies": {"nanoid": "^5.0.8", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8a2ae2f44b9bbd34c1c8fbaf04861e09e2414c89", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.0-canary.2.tgz", "fileCount": 17, "integrity": "sha512-+ugjqSNPYn8Zhkw1wwI47Bn1AMaRg4DGkeVGvChaeQVipndjDADqxaib8prcokYVoUmwk//Sz4EHGlx++HrAyw==", "signatures": [{"sig": "MEQCIAPt+XXrE/2QD1Cfu37GNDRQk01NLGCoNSbDGmcoZUE8AiBPgN4oGQOp/ow8P17fu4dWrSguxXlLDv2oYhuvzIaGcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311171}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.0-canary.3": {"name": "@ai-sdk/provider-utils", "version": "2.0.0-canary.3", "dependencies": {"nanoid": "^5.0.8", "@ai-sdk/provider": "1.0.0-canary.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.3.1", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "49e60274b4e7be45719f609ede50bb8fb2db72e9", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.0-canary.3.tgz", "fileCount": 17, "integrity": "sha512-2kxAkaESSm3295tJRs77NmCCi9Ty7eaEOpqA0xinEmYjobAP/VrzaGthvAVXIzjbQj6ndabDGsVzdnNkhLr1zQ==", "signatures": [{"sig": "MEUCIANSo/+Hbho7fD5rwzDX9fW2Nr13wgr7h1Iw8oz1ym1pAiEA4Auu0h5A4lj1vsVntijjbX0rDbWfaa1hOX7ixskMRe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311134}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.0": {"name": "@ai-sdk/provider-utils", "version": "2.0.0", "dependencies": {"nanoid": "^5.0.8", "@ai-sdk/provider": "1.0.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dc4da55bde547e5098566761547950cb278c60bb", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.0.tgz", "fileCount": 20, "integrity": "sha512-uITgVJByhtzuQU2ZW+2CidWRmQqTUTp6KADevy+4aRnmILZxY2LCt+UZ/ZtjJqq0MffwkuQPPY21ExmFAQ6kKA==", "signatures": [{"sig": "MEQCIDxH7eD2En5xd9Y9jfY+vVCjaisi5+Zgeia3X6YINkrOAiAHzAr5shqvaIemUJC081pXkc1+Zs0998hOE6svVFe4ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4258257}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.1": {"name": "@ai-sdk/provider-utils", "version": "2.0.1", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "1.0.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "953735eb8b000e96dbb1521518f9db2a9df88a76", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.1.tgz", "fileCount": 20, "integrity": "sha512-TNg7rPhRtETB2Z9F0JpOvpGii9Fs8EWM8nYy1jEkvSXkrPJ6b/9zVnDdaJsmLFDyrMbOsPJlkblYtmYEQou36w==", "signatures": [{"sig": "MEUCIGBKGv8y2ZxQBzhNb58LpaAGgY7wNSy8bx0ksqhFGRDuAiEAjY972l7rcV3kSi0KBs8ZxPyRtLCWTiprdsN7BLEZn/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4258379}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.2": {"name": "@ai-sdk/provider-utils", "version": "2.0.2", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "1.0.1", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ea9d510be442b38bd40ae50dbf5b64ffc396952b", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.2.tgz", "fileCount": 20, "integrity": "sha512-IAvhKhdlXqiSmvx/D4uNlFYCl8dWT+M9K+IuEcSgnE2Aj27GWu8sDIpAf4r4Voc+wOUkOECVKQhFo8g9pozdjA==", "signatures": [{"sig": "MEYCIQCBamevJ6TSAj79rGO9g5zLFAYkXJ3t5S65fRYXm72lagIhAJR28FkeKZndO92uPHNBRiOczsXXyTQGdg/f3BAobmUD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4258469}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.3": {"name": "@ai-sdk/provider-utils", "version": "2.0.3", "dependencies": {"nanoid": "^3.3.7", "@ai-sdk/provider": "1.0.1", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1f9fd6c1c17d3ee8813617b113a08a6d51328e83", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.3.tgz", "fileCount": 20, "integrity": "sha512-Cyk7GlFEse2jQ4I3FWYuZ1Zhr5w1mD9SHMJTYm/in1rd7r89nmEoQiOy3h8YV2ZvTa2/6aR10xZ4M0k4B3BluA==", "signatures": [{"sig": "MEUCIQCJpiGemieo+Lcjxq9OHGoKmYjVaNwvOuXx1EPQVwsPsgIgM0nN1HXV2X2wBaK1aCB9zKzyiNEi3/PyutkFdRXSfZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261101}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.4": {"name": "@ai-sdk/provider-utils", "version": "2.0.4", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.2", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e76ab8b592fb656013ada954cbbc81f41e3003a9", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.4.tgz", "fileCount": 20, "integrity": "sha512-GMhcQCZbwM6RoZCri0MWeEWXRt/T+uCxsmHEsTwNvEH3GDjNzchfX25C8ftry2MeEOOn6KfqCLSKomcgK6RoOg==", "signatures": [{"sig": "MEYCIQDE0LdnSBgphVUnfIMDzlDz2JqH8RQsvFBkx8iy/gkp9QIhAIhwm8UfN19KXh4mQDRY+mzGNjVCmVRxIrpB2uZ9X9HF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261191}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.5": {"name": "@ai-sdk/provider-utils", "version": "2.0.5", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.3", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7b200f535668e619b7dbc55619aadcc5c1a103f8", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.5.tgz", "fileCount": 20, "integrity": "sha512-2M7vLhYN0ThGjNlzow7oO/lsL+DyMxvGMIYmVQvEYaCWhDzxH5dOp78VNjJIVwHzVLMbBDigX3rJuzAs853idw==", "signatures": [{"sig": "MEQCIG7F/xoYy+9IeD/jJIFGOs2hJk/DVCqFb24GlGAqKU+xAiAWzam7UNQic5y8M0TyeCP5py8aOFu7WKoDGavxnnz/3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4261353}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.6": {"name": "@ai-sdk/provider-utils", "version": "2.0.6", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.4", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.6.4", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef26bd7f27a1dc5fe203ea99bebc2f2d40e79f96", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.6.tgz", "fileCount": 20, "integrity": "sha512-nB0rPwIBSCk0UkfdkprAxQ45ZjfKlk+Ts5zvIBQkJ5SnTCL9meg6bW65aomQrxhdvtqZML2jjaWTI8/l6AIVlQ==", "signatures": [{"sig": "MEUCIQCRgODtShY/6ebub6fY9pl6Vc3ouWeBE+hvs9k/4cs4DQIgTlcP6zbK/E3MYqIiMmD7DPbJmlXmIM1fqVtYx6D03H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4271869}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.7": {"name": "@ai-sdk/provider-utils", "version": "2.0.7", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.4", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2d905dd8688460d2ad3a3e3c41f1f2b6868da601", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.7.tgz", "fileCount": 20, "integrity": "sha512-4sfPlKEALHPXLmMFcPlYksst3sWBJXmCDZpIBJisRrmwGG6Nn3mq0N1Zu/nZaGcrWZoOY+HT2Wbxla1oTElYHQ==", "signatures": [{"sig": "MEYCIQDvQS/nL7/Wl8omt6p42lI0Oj2AOXMtX7yaOWO/oRqZ7gIhAIR+7EhPLPBySU5gvy0YWALJyFTUQ2uWlmvt0UxNImzD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4303631}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.0.8": {"name": "@ai-sdk/provider-utils", "version": "2.0.8", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.4", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9b02f7acf6eb2a3b23b8221b69bf011c6ce6fd03", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.0.8.tgz", "fileCount": 20, "integrity": "sha512-R/wsIqx7Lwhq+ogzkqSOek8foj2wOnyBSGW/CH8IPBla0agbisIE9Ug7R9HDTNiBbIIKVhduB54qQSMPFw0MZA==", "signatures": [{"sig": "MEUCIQCqxjkkK1RY4MO1ScIxcFCcxpJl9LZcnQRhlxWGBtZdywIgIwSjFwzMp37NYJYKbxUHab2SR1U3aUylcrsIaiqbUMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4304216}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.0": {"name": "@ai-sdk/provider-utils", "version": "2.1.0", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.4", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "40f060252a192a31063300dd277a04df0eba604e", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.0.tgz", "fileCount": 20, "integrity": "sha512-rBUabNoyB25PBUjaiMSk86fHNSCqTngNZVvXxv8+6mvw47JX5OexW+ZHRsEw8XKTE8+hqvNFVzctaOrRZ2i9Zw==", "signatures": [{"sig": "MEUCIEnqOz5RMQVDxBGhwuDAVAuN3mN3LFAQf+CFN0zPCcPpAiEAvOT1FxzO5LDifvf+WdoVWNDL/tKdMjZypRvwa+Cn2Ho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4304277}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.1": {"name": "@ai-sdk/provider-utils", "version": "2.1.1", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.5", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2e6c722750b3c545023affd88ff5d8402011f334", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.1.tgz", "fileCount": 20, "integrity": "sha512-+FRXSAdzPJFJN6TpyvyGWLo7WJuoBKI1g66UL+sli1HrxlldXSwxRPeb8tMMmNcyi3VKQogg2VsoJjlt4ort5w==", "signatures": [{"sig": "MEYCIQCyai32KjTptJtL1Erk8oTVMiLu1PW9BD6tErFd/7bcFwIhANeBpZVweHD7HqqVLxrODXyvK6ie2oYFFfZanReI04LS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4304945}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.2": {"name": "@ai-sdk/provider-utils", "version": "2.1.2", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.6", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ba5322c406912584411f9edc9ae83596009d7048", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.2.tgz", "fileCount": 20, "integrity": "sha512-ezpQT6kzy/2O4yyn/2YigMqynBYjZIOam3/EMNVzju+Ogj+Z+pf27c/Th78ce0A2ltgrXx6xN14sal/HHZNOOw==", "signatures": [{"sig": "MEUCIGSH4YtfDikIw+uUWOhtuO92yWGeGhd1gPAf4Zxasg0hAiEAtoTHI8iE23LA/A+Pz2vgQZ36GKxSJ4dosXofn+NyMb0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4305514}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.3": {"name": "@ai-sdk/provider-utils", "version": "2.1.3", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.6", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2d248e432d3fd53685debc959636e2fee3cc1ca2", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.3.tgz", "fileCount": 20, "integrity": "sha512-NamxUnRCnG7aSnYhnhX5in5KZtwIIrafURABuAmrV3mGT5YZPzqfsXdnsXY0GbzDgbl+A7R1IPxrA4at2fU/XQ==", "signatures": [{"sig": "MEUCIQCHyOR/HGJxg9gg1xnpTgJCve++EYhGDrNJd2rkXQpe/QIgWtYDTgEwF2Z9GlDGzo36O1xPMhCamZUIU2Wos9lCEDw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4327347}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.4": {"name": "@ai-sdk/provider-utils", "version": "2.1.4", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.6", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f6d112f9b955f4b8543d7acde6c6dd31fe76710", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.4.tgz", "fileCount": 20, "integrity": "sha512-KrGr76ebmN3onrb+bQihgXZyYgYu/kClB+YjFmR1uXQpJG067rCBDWuTHvpI7vwPdQQDvIIgZ+0U5G4V+dN74w==", "signatures": [{"sig": "MEYCIQC7TbENh0lcJHIDvsrviLbHw8mdBjcx1HJS/LYNNUshDwIhAIpRbN2Uk0xmR/g20iqEo9LbdQWXImbeYoizIBopaQZK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4329591}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.5": {"name": "@ai-sdk/provider-utils", "version": "2.1.5", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.6", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "acceca29bef1ed3855789197c3b587e260f995d4", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.5.tgz", "fileCount": 20, "integrity": "sha512-PcNR7E4ovZGV/J47gUqaFlvzorgca6uUfN5WzfXJSFWeOeLunN+oxRVwgUOwj0zbmO0yGQTHQD+FHVw8s3Rz8w==", "signatures": [{"sig": "MEYCIQC6Blr36zvNEyGYXK1dcpkUHX4bVmN1siP8xhEuriK6DgIhAOM6TPJEQyPv1I/0FCrccH2uVgDPdn+iPzbLzZ70x9gt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4330288}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.6": {"name": "@ai-sdk/provider-utils", "version": "2.1.6", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.7", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5b2bff2d44ffa8ec629109ea2b28d2a7528104b4", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.6.tgz", "fileCount": 20, "integrity": "sha512-Pfyaj0QZS22qyVn5Iz7IXcJ8nKIKlu2MeSAdKJzTwkAks7zdLaKVB+396Rqcp1bfQnxl7vaduQVMQiXUrgK8Gw==", "signatures": [{"sig": "MEQCIASDZvvsQ82/XT+6OzMbCHCHwaeUTunplr9klgYHSrP4AiB6cjjiGJHBB9A5e9ZS/vzBkPoWNFbUranJ4lxUK2IcQw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4330378}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.7": {"name": "@ai-sdk/provider-utils", "version": "2.1.7", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.7", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b644abce10e5b07388a0439b9b03a69d5fbe9d60", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.7.tgz", "fileCount": 20, "integrity": "sha512-5pliIKahXPlx3PZNBL3TtOBosFftoCq8bUNYdLi2M3YPBdQen9G9mR1BCrhC5hhEk4i3XTM8boqYeuRN6kLfsA==", "signatures": [{"sig": "MEQCIEfSwkg+bt/BIB9Jy0+r3YOIxwNDpr87W2l259Ohx5AnAiA7IT37t+wzvjpw6ODBxE3Y2BUqYhON0yrYC7gsYz6/lA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4331900}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.8": {"name": "@ai-sdk/provider-utils", "version": "2.1.8", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.7", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "370ccd30121684ce1bfbba20dd906481fad4b272", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.8.tgz", "fileCount": 20, "integrity": "sha512-1j9niMUAFlCBdYRYJr1yoB5kwZcRFBVuBiL1hhrf0ONFNrDiJYA6F+gROOuP16NHhezMfTo60+GeeV1xprHFjg==", "signatures": [{"sig": "MEUCIQCPZHVbnuHFU3xO4VqJDuUKhjXg77/qxI153oE2NnOpOQIgSVxrNgo26EepaJm5SyVeYlHUfW8dZ1lk1m9pUfBSPco=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4333114}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.9": {"name": "@ai-sdk/provider-utils", "version": "2.1.9", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.8", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aa1693212dbd7f36d593d20a1139bc421dfe2bda", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.9.tgz", "fileCount": 20, "integrity": "sha512-NerKjTuuUUs6glJGaentaXEBH52jRM0pR+cRCzc7aWke/K5jYBD6Frv1JYBpcxS7gnnCqSQZR9woiyS+6jrdjw==", "signatures": [{"sig": "MEQCIEohW3cFk/OMVg2a2UJlSVJrKbf/i59g6kbI4g9/LEtDAiB8LfGK77MTPrRLFsfTJ3mZInt2rICnMPbEHzuYWVOQqw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4333204}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.10": {"name": "@ai-sdk/provider-utils", "version": "2.1.10", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.9", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dfd671ccda12e321b58f347b6cbbdd982d139359", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.10.tgz", "fileCount": 20, "integrity": "sha512-4GZ8GHjOFxePFzkl3q42AU0DQOtTQ5w09vmaWUf/pKFXJPizlnzKSUkF0f+VkapIUfDugyMqPMT1ge8XQzVI7Q==", "signatures": [{"sig": "MEUCIFD/aEvy3RQSoaoQb6XagobqI19HzPmDxIWOV16LmFlNAiEA3X+1IkaYCeM/WSe0a48iniZQna1bXnJT9bvHre20VWQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4333296}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.11": {"name": "@ai-sdk/provider-utils", "version": "2.1.11", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.10", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8bc94b81234ebfbaab841a07ca234e7a79c5e92e", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.11.tgz", "fileCount": 20, "integrity": "sha512-lMnXA5KaRJidzW7gQmlo/SnX6D+AKk5GxHFcQtOaGOSJNmu/qcNZc1rGaO7K5qW52OvCLXtnWudR4cc/FvMpVQ==", "signatures": [{"sig": "MEQCIE9E60d+fLSkASocGqtf0nJiZl4VoZxqIgDd16GeGVsrAiBkv7f82u9BN6kGtMocjwHqPmlEEGCkK04OUP030CrPGQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4333389}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.12": {"name": "@ai-sdk/provider-utils", "version": "2.1.12", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.10", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aa6c7fdd331702d16aeae36b045e2a56ea8fd124", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.12.tgz", "fileCount": 20, "integrity": "sha512-NLm2Ypkv419jR5TNOvZ057ciSYFKzSDEIIwE8cRyeR1Y5RbuX+auZveqGg6GWsDzvUnn6Xra7BJmr0422v60UA==", "signatures": [{"sig": "MEYCIQCLi/0fPBABFinCI0I8qOldfIxxcYdatTKegss0qyAeBAIhAPIjY6Uet4Pl3aha2fat5ysp4PS93N1uCNU02/ME2ie6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4336885}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.13": {"name": "@ai-sdk/provider-utils", "version": "2.1.13", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.11", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "060700e5e8032820f297828fc0f47e81aa8ce978", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.13.tgz", "fileCount": 20, "integrity": "sha512-kLjqsfOdONr6DGcGEntFYM1niXz1H05vyZNf9OAzK+KKKc64izyP4/q/9HX7W4+6g8hm6BnmKxu8vkr6FSOqDg==", "signatures": [{"sig": "MEUCIQDRnQ8SElssk+YQM0m3gFZa4Alj4Ka2cnse4wh8gglGVgIgT3IrIVlTSScMOIB4BBZRDdrVk2JEoNrOKI57qOwX6pc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4337109}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.14": {"name": "@ai-sdk/provider-utils", "version": "2.1.14", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.12", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "363d9c111cd7ba29871e4a49c6a09b6860ca33e1", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.14.tgz", "fileCount": 20, "integrity": "sha512-0fNQwstwqHP+cE9bP9nQXeEhY/qJRGW5Y+AQrvwzd0EI2+gH1GmwI1IC/gIOksmz/gRy4G+kXXD+Fbq4T/jCbA==", "signatures": [{"sig": "MEUCIQCHFASlv8fEg050+Ufaig3qDlV+C6w8EUBLFmFgN0KNBwIgLLkubsqLH0tckhMbOAT5X72q95z0072QVshenkk8fyI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4337201}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.1.15": {"name": "@ai-sdk/provider-utils", "version": "2.1.15", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.0.12", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "20d23dfada7d988bebf176fdfc62b48fca7bd822", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.1.15.tgz", "fileCount": 20, "integrity": "sha512-ndMVtDm2xS86t45CJZSfyl7UblZFewRB8gZkXQHeNi7BhjCYkhE+XQMwfDl6UOAO7kaV60IC1R4JLDWxWiiHug==", "signatures": [{"sig": "MEUCIBL9M7Kqpiydhn7BO6JnhdEW5OxxDLFylKOrgxRt7YvlAiEAiIjc/yP8u1xIOTu+0ytLVwPNGNab5cCp7Jhez6P8ucA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4341778}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "2.2.0": {"name": "@ai-sdk/provider-utils", "version": "2.2.0", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.0", "secure-json-parse": "^2.7.0", "eventsource-parser": "^3.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "cf123935dbbe1ac346c7955713d50f8f8e3287f1", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.0.tgz", "fileCount": 20, "integrity": "sha512-RX5BnDSqudjvZjwwpROcxVQElyX7rUn/xImBgaZLXekSGqq8f7/tefqDcQiRbDZjuCd4CVIfhrK8y/Pta8cPfQ==", "signatures": [{"sig": "MEUCIQCFfk5iGq5vyj2ntTr71AB1x9wI7YXjwN/xQwSTDTtDQgIgcpQyW1b8f5WBdMXVt2gS9NTwqpSGby5mHurNi5IHPt4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4341997}, "engines": {"node": ">=18"}}, "2.2.1": {"name": "@ai-sdk/provider-utils", "version": "2.2.1", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "f8877238c5a5d665fe311d69e559f1b932ed1d4e", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.1.tgz", "fileCount": 20, "integrity": "sha512-BuExLp+NcpwsAVj1F4bgJuQkSqO/+roV9wM7RdIO+NVrcT8RBUTdXzf5arHt5T58VpK7bZyB2V9qigjaPHE+Dg==", "signatures": [{"sig": "MEQCIDs0QIh7LEygeGqWTxQGTfWRLAjILVeZXtLVcY6wcTl1AiBu5l9y5VwyNsoWYJamD7kR12GKVROqDIi1Jmv1ghycjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4357138}, "engines": {"node": ">=18"}}, "2.2.2": {"name": "@ai-sdk/provider-utils", "version": "2.2.2", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "f7f008482c0bef9d3a5e4f355073e565782698f3", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.2.tgz", "fileCount": 20, "integrity": "sha512-DgjbBseR6e5oRcXOF0LH9owj3eKDfZNMdyHM8HBfcfQg0IVL2KqL1lK40pQynuI3/u72YM+vvbtAZ78PRj6jCA==", "signatures": [{"sig": "MEUCIQCD0FsGMioaK0K+8MYQjySx0MRjDuVkOcbO/0EXXyaaxAIgBUPnPKnE0iH+UgH5htna11AuOMlEZNMnuVeoJoeRCCU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4359527}, "engines": {"node": ">=18"}}, "2.2.3": {"name": "@ai-sdk/provider-utils", "version": "2.2.3", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "e94f58537bbed7f90a1ed94f8cd353b79e3ab65c", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.3.tgz", "fileCount": 20, "integrity": "sha512-o3fWTzkxzI5Af7U7y794MZkYNEsxbjLam2nxyoUZSScqkacb7vZ3EYHLh21+xCcSSzEC161C7pZAGHtC0hTUMw==", "signatures": [{"sig": "MEYCIQDe7JogfKpcQp7FEWhBv7B/7AtsY5RHyHaNojlO3y7buQIhAM/ylFEptN0MjMh3wquUB3bdCSfIdmKhjcNXr+gzJg+K", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4360179}, "engines": {"node": ">=18"}}, "3.0.0-canary.0": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.0", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "2.0.0-canary.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "f0fca38ab9cb9acadf4a78a96b905757e77233f0", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.0.tgz", "fileCount": 20, "integrity": "sha512-alkqZEJsrS9HuypLg19y30UcTDA9GkbQYYbupBv6yUYdPrZ2YCtKa8dJ2F9uM5mNMftoMy/tmjLiAhb4ynWCwg==", "signatures": [{"sig": "MEQCIFe3tuWTMNF5Uxoz8MB0vEkRnWC5TRK+budo1gwSkg7yAiAGKZH8QhUFUEEJQjOdi7CfZcUcQ8Kle9ZUmSeA4urTTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4338308}, "engines": {"node": ">=18"}}, "2.2.4": {"name": "@ai-sdk/provider-utils", "version": "2.2.4", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "665834a7f0aa79618c1ca715c879e9975c6ff445", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.4.tgz", "fileCount": 20, "integrity": "sha512-13sEGBxB6kgaMPGOgCLYibF6r8iv8mgjhuToFrOTU09bBxbFQd8ZoARarCfJN6VomCUbUvMKwjTBLb1vQnN+WA==", "signatures": [{"sig": "MEUCIElIBXviWKuEWnr7IOCNckfw258R23fiWtrIUtc21iX0AiEA+S/6WWuGBrF4rh0gc/1Ssl6A8LhfodZTLhyAeHqLd7c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4360780}, "engines": {"node": ">=18"}}, "3.0.0-canary.1": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "a6e18b68cb508a70da9e4ec1ae292450da474db1", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.1.tgz", "fileCount": 20, "integrity": "sha512-7ZxzKLimXXcBTQjWT4qPFiMxjowY2J3lnjtT9/3KeqsYo496YDSJpxY6cmQvPaBkRl4I3GA4PSjU3CRZRm9xdw==", "signatures": [{"sig": "MEYCIQDS14hZhggEHgcm5aytJ+w7n6hfiZt4CHAj840ARJgrYgIhAPukzHWcy2md/99MUbof0U9yP5ziVHahflwpGCemIg3o", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4306912}, "engines": {"node": ">=18"}}, "3.0.0-canary.2": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "6a05f5434107f1c7ed413a00e84563df2db72e58", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.2.tgz", "fileCount": 20, "integrity": "sha512-QKFvCg0iGRroFIoxlNvSAmJsAfKORn/h67B1018KuKqV+GjZKfUugSONHDGue0+gy1ntdbcGFbRDGpQIDtm6QA==", "signatures": [{"sig": "MEUCIFTgPTQMgY/R92H6pgrOfTj+T1xHRd1fvgJVnmBOmGALAiEA32Qywd8mldEP+r/w9vZqe7wUdanAhaEiwZ2KBc+qHLE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4307079}, "engines": {"node": ">=18"}}, "2.2.5": {"name": "@ai-sdk/provider-utils", "version": "2.2.5", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.1", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "e0f061db67fa5e3d6bc03620898e72035a59dfb3", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.5.tgz", "fileCount": 20, "integrity": "sha512-9WNqJFsqrkSoSxM7eQEh8TdpMD+hZ+hjsZH8WuvEOxilRo1WAVrnrGwm160OG3IZVNlegRNaPgjYGm/VH5uwUQ==", "signatures": [{"sig": "MEYCIQDqwfLplP13d+Dg8IRrnaf48zczvvI8iwSH5EbK9p2m4AIhAOp/JLaLj3vvnaRBJf1RtCv6Q4YDwsFk3PkaLhcY2ADS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4367249}, "engines": {"node": ">=18"}}, "2.2.6": {"name": "@ai-sdk/provider-utils", "version": "2.2.6", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.2", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "5dc457feaf3698bb2d1acf33adaf6b2182e501d5", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.6.tgz", "fileCount": 20, "integrity": "sha512-sUlZ7Gnq84DCGWMQRIK8XVbkzIBnvPR1diV4v6JwPgpn5armnLI/j+rqn62MpLrU5ZCQZlDKl/Lw6ed3ulYqaA==", "signatures": [{"sig": "MEYCIQCOIVxHYidAbOCJfIZSFrYLhYF0x1LTESeTRGrB8Z5jQwIhAPNud4qOBCWQ+++L0/pJTVs46QxQdTeCk6xLN1f0X2Ee", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4367339}, "engines": {"node": ">=18"}}, "3.0.0-canary.3": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "6c831163f5797fdf7f423c286bf87a1e7a6bb4c3", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.3.tgz", "fileCount": 20, "integrity": "sha512-0f6bMAKs50Uy8iwj3SBcasJSJPJ/y9ztzysO6g9R2zziZSqNzIKnJ5AUPNsmDvShyjvM4x16KfhqTso1q5X5DQ==", "signatures": [{"sig": "MEYCIQDt+sD8z5n6xElrWq8MxNz1H6tLRwEjjl/g3mPQkB7HHwIhAIug6F0FgwrbK2b/CGO6G/7ta3A3o8fq44dxboVameB9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4324798}, "engines": {"node": ">=18"}}, "3.0.0-canary.4": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "58cceadfe2bb48bcd50dd28176f06dc7f8b5217a", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.4.tgz", "fileCount": 20, "integrity": "sha512-Zc+J3ZMY6IWhiwoPR1nW7WsA6+k4L2R8Ll1D1bVgj0ggCNlQ2h5j4UcDcZE1gKlbKqHeNgx+Qxg1uhedCGvoRg==", "signatures": [{"sig": "MEUCIFLiUSH7JpfhnfVKUYMua0kB4o/zSJNh0HUjY53abvAAAiEA/UGcMaCirDo+bT2hQUSspBh/c4IuSbOTTIJzhL4VZMY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4324906}, "engines": {"node": ">=18"}}, "3.0.0-canary.5": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "d752e84782725e7ed1e54983dca21249b907a0fd", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.5.tgz", "fileCount": 20, "integrity": "sha512-918ydb6W50nF81uf8s6DFL3uu/g0r37jPdhp9cyk09pWYr5ciMXGPA6F3miHT0j5XojG8LeGvTZHifjXzwTJyg==", "signatures": [{"sig": "MEUCIG7ooPeXqVl8aa+PNJncAmyfJ93f4y8A9jzhVKlpwTCUAiEA1qxLwlzo9L4U+Cfd5I/eU5zOflFZodmMJQjFT1WAr1E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4325044}, "engines": {"node": ">=18"}}, "3.0.0-canary.6": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "a44bf4b2a4f111ecd60fba0bdfaccebd39cd4a13", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.6.tgz", "fileCount": 20, "integrity": "sha512-TSGhcPRQwGGSWFpWUqzyPJigvx9vqgsm+PNViyeEb53H/jxSxl25B5wr31srlQyueh0+IN6jmI27UF8DVDfLFw==", "signatures": [{"sig": "MEUCIQCUkniGkcp9Jin+e8+5d05uIKMTK0Rw7JJxcORWsfj/HgIgEGoHWW/uE9FK1E8fOclvYVgq9YzYgtzFMZTaMYCmyQc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4326231}, "engines": {"node": ">=18"}}, "2.2.7": {"name": "@ai-sdk/provider-utils", "version": "2.2.7", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.3", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "2030c7104ce9f0831b6e78476571bf376cff68d6", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.7.tgz", "fileCount": 20, "integrity": "sha512-kM0xS3GWg3aMChh9zfeM+80vEZfXzR3JEUBdycZLtbRZ2TRT8xOj3WodGHPb06sUK5yD7pAXC/P7ctsi2fvUGQ==", "signatures": [{"sig": "MEUCIDv54FxvqOLEPIwZgisv/UR6ez3EoCvsi0BJ0Jkb0gJwAiEAtrvNHpWw1Ifquogj07iBQRZir0K1HERO3I0fS0eWgM4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4367429}, "engines": {"node": ">=18"}}, "3.0.0-canary.7": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "07ba0c13f387fcb58b052a7aa53aa3f69a3c2ed5", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.7.tgz", "fileCount": 20, "integrity": "sha512-eVsCJx9CJZ8ZMzgBHn02dYbKjUl54ji9yV5+mTIL7h9+iVpqRNX4/570cFc+dPuy/WuTf6Mqnw3esbK1WgWhSw==", "signatures": [{"sig": "MEYCIQCPXI9gdMJQ0jEWFw87v3BtD3DtrWj+9Cw0Cl31/HBGnAIhANkI5j1/XGH+2Z7Xj1tx3C/DRKquIXDE0l36PIL3Qhs6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4326702}, "engines": {"node": ">=18"}}, "3.0.0-canary.8": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "a5bb3c4dca4fe35d79513764997b9658ad08628f", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.8.tgz", "fileCount": 20, "integrity": "sha512-sgIp76MaOvP/FqiyM9i9RIdkA+S9aijZh+OqUBAXpz3cI4NZMHBid3HyGoyW4S5zJZv0tlFPbuXV54vF+LW3Uw==", "signatures": [{"sig": "MEUCIB+z2PKEotoKzkJo3ddf96aeNEoqojgtaxljsJ1BAWPEAiEA6f0goiAnoU2p+ijFjWYK5ZehUfcnxinjoM53GlqYjBE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4327664}, "engines": {"node": ">=18"}}, "3.0.0-canary.9": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "62dc71ec8784d9d0b1c29eb412d6cb05c817183d", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.9.tgz", "fileCount": 20, "integrity": "sha512-8ruLwKStjErg5GB4Sfwc4v6ZWGH8ukld7Jn0SFxL+SZfLpfv1z0E1cCcq82buE+mkHOALBj4sOzTj2t9tUDn2w==", "signatures": [{"sig": "MEQCIBFc2WBqVrXcP0VjkhQF/G/TQL68V+Oy0bSYEbi85sPiAiABHTHZplbFHIPf0aPrL+xI7sbBoRFHwWJqafo+2VrnDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4334415}, "engines": {"node": ">=18"}}, "3.0.0-canary.10": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "04fd8dd09a4d94747ee155c953fa44d5f7aeed2d", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.10.tgz", "fileCount": 20, "integrity": "sha512-HaYgTFgK9AkPWPh+xD/gPoeorKphE0M2d0q+ekUWBcCHYkvbrOvVPx9XtL69hLB+wZozqBjOcxIRbYeJv5a3Qg==", "signatures": [{"sig": "MEYCIQCcag1t40OZnl1Uqc0rFSfda2L0ADCShV3Fu9yVRAQYSgIhAOzkHqR9hEIU5DwRxYCBq0kM2g9B/l0skOpmUx4OdsPp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4334525}, "engines": {"node": ">=18"}}, "3.0.0-canary.11": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "5fa4c6e0e3380ec9b2258548d12c5452aa45b5c3", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.11.tgz", "fileCount": 21, "integrity": "sha512-Qp0q1KlskSDAC+kIPe+Xxywy9guGiwZnVDpPSef1n4ERTpobhMCFQ/T8V0hOKfdBo+xy0JBV7AjP5Sd4gQsz4Q==", "signatures": [{"sig": "MEQCIHGIfUuxnvYo2fu9QZK5LoRvOEmLjW2SnBAmnJvIytpBAiAmsJJpxBZmVqqF8QNsPp1IqRlkj2ioWPUDlNmL8TD1pw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4335648}, "engines": {"node": ">=18"}}, "3.0.0-canary.12": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "fba940bd3eaa319a32ba31280199df84496b13a4", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.12.tgz", "fileCount": 21, "integrity": "sha512-vXsOOM1XpktqA7TkD2FtBk2v23pNbDJ0VWEwP1zqcUpBtKM0ldlRAkMS+hMc7cfbcWtu+5UqZoDCsMiM1XjQKQ==", "signatures": [{"sig": "MEUCIQDvPZoMzS94rrcHO/AoEf4eP5tUkxiQC4hoG9ES7wwBtAIgGF47Yk4FFRyKKZNAs3h9eetv3I/EnLk2oXBgkTi/ZZw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4335824}, "engines": {"node": ">=18"}}, "3.0.0-canary.13": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "fbe142697eda2428ecaa86b37fb58517b1954359", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.13.tgz", "fileCount": 21, "integrity": "sha512-2g8KDY9aGV1cA46BwutEbraFmfq1SPSQM+6PRZctY88G2DPM1WmlmfZlAH6HqFq4DtWQqf8hDW4kNVF33g+0Sw==", "signatures": [{"sig": "MEUCIHtQ1RtpkPJEI5H/rLo1LZkVRMceIVrrmMV/qqsNjOGZAiEA147PwHd+S1JSD+WH9cfV7rUoiDU6NGIV3ASUM8eVAlo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4350632}, "engines": {"node": ">=18"}}, "3.0.0-canary.14": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "492d329fd1fcd77e30710861c21ef3ddd25c2fd9", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.14.tgz", "fileCount": 21, "integrity": "sha512-Mon7RD8rlJo28Ej4tbcvKxmWPdyhyF4vk8qwIvfiVSg3b4e/jX28xVvmuYmwZCR/cuuJgURNngdaBqq9mLJZJg==", "signatures": [{"sig": "MEUCIE2yzhTXsNyzhtBxib686laNGJjheBJfdLq6PO+4EEsXAiEApQpkAciTvjfLCK9WzvT7UM25mwE8G0l5m6nGaW0LIpI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4350718}, "engines": {"node": ">=18"}}, "3.0.0-canary.15": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "8ec0b7df6c92617740194d5ce01b46173f1b51d2", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.15.tgz", "fileCount": 21, "integrity": "sha512-CngJsLkavwCIrNvX+G1JP6b0aiqGozXDs18sO4Q6GFjdrORew+FDJGbTz/EQgQtW3gzOBEAaQOCgdxVuBLL+Ew==", "signatures": [{"sig": "MEUCIQC2JfYdPpD/1KnIHj/AUqNfudiMDDDHgl1RorabCjCKcwIgAeN/RigmckwSsR+6MvaP51sL8RYJEiWk0zGWruEt228=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4347502}, "engines": {"node": ">=18"}}, "2.2.8": {"name": "@ai-sdk/provider-utils", "version": "2.2.8", "dependencies": {"nanoid": "^3.3.8", "@ai-sdk/provider": "1.1.3", "secure-json-parse": "^2.7.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "ad11b92d5a1763ab34ba7b5fc42494bfe08b76d1", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-2.2.8.tgz", "fileCount": 20, "integrity": "sha512-fqhG+4sCVv8x7nFzYnFo19ryhAa3w096Kmc3hWxMQfW/TubPOmt3A6tYZhl4mUfQWWQMsuSkLrtjlWuXBVSGQA==", "signatures": [{"sig": "MEQCICabpYR//7GN7nrSzM5bb49+4cfo4UymDEJn7yJ8/YLcAiBMVhwpESf9kunDTIjxdLkmzGcdWnooVE8b9bg/SJ2IaQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4367484}, "engines": {"node": ">=18"}}, "3.0.0-canary.16": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "a61b8683fc5a8cd5c8cba0b5f75470c7f00d59dc", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.16.tgz", "fileCount": 21, "integrity": "sha512-memOY8vH2VFp03OKyfs8fVA8hwTEH3/2Bcad5IrNx8ljHJn19CDT5xAEK6/3Z4MzqZTGYMAsUcylBU0xzJktbw==", "signatures": [{"sig": "MEUCIQCSUqAwEVd9KGi4z0IebdeQ5AtE+Jou41UGrjRZLKQcsQIgQA4dG87EmtvBC0aVS11N/gk4zi1wtK9KNuaXrUWeLm0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4347570}, "engines": {"node": ">=18"}}, "3.0.0-canary.17": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "783fb570aa235ddcacdda21e07a94b2921b3fed0", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.17.tgz", "fileCount": 21, "integrity": "sha512-TDIHuhxfO+lEdDqyyqbBXdKBYFtwbjH73z4XTrcCcTG/aI4oEVOb5/BUlooMcerWrNPpjsKiCdo+l6m/RBshzA==", "signatures": [{"sig": "MEUCIGKV7ALlL0TWWwBwDvsNFI5+g56gtVDuW2NOST8uNNcsAiEA67Fao08jafhldfNgK9dxYpBoYAApeSRsbjgc8fT6g6Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4349944}, "engines": {"node": ">=18"}}, "3.0.0-canary.18": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"msw": "2.7.0", "zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "530e9ed56a16215e591f7e1d8eef81705d4c9967", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.18.tgz", "fileCount": 21, "integrity": "sha512-CjgNE/Stw1NgB0AQeGCmQ3QPH1G4gY3PxywoT1zWOyg8N4hPzLvzz/AIcL17fpoCxpcLgXZ2pV2yIeDFcI1qNA==", "signatures": [{"sig": "MEQCICpiIPPI6yCB5pssYSanLo3M0eoFybPLQXnuZIEmMm5yAiAiiEiKBs3m60/Aujoou40PDMY3o91rORjfs96Q1+1r7Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4351350}, "engines": {"node": ">=18"}}, "3.0.0-canary.19": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "zod-to-json-schema": "^3.24.1", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "53b10f0bf068cc2059a3e1881c35822cfeb28682", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-canary.19.tgz", "fileCount": 21, "integrity": "sha512-4IJw6/wkWYLYfFYPvCs5go0L/sBRZsIRW1l/R6LniF4WjAH2+R4dMbESgBmzx+Z2+W+W6gFeK8dnQByn7vaA/w==", "signatures": [{"sig": "MEYCIQC1pmenliADdWkzH8XDevCnEzcYKnBaFS+oTOHmMeMYFwIhALKrZR/qHz8ydEJR3BWAKqGwlMvUvjSTP5JQ7erpAcPV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4355508}, "engines": {"node": ">=18"}}, "3.0.0-alpha.1": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "zod-to-json-schema": "^3.24.1", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "a0a47ace049ad3adee00de815a597c59c1a03e02", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-alpha.1.tgz", "fileCount": 21, "integrity": "sha512-Hk/XhVB3Ugs6af6OPA3heFqtEcoSr4B3UadF4V7xtc1eIOoArUgt2zDlwvj+Rg+qkL5NaI3lylZJVmGLgiAo8A==", "signatures": [{"sig": "MEUCIQDQFjRUXy4H89RRRHb1td2/ZijNasl61zebLTzkmQ70TQIgfogfVL+49WD2EH3y1P9IUNvy2fcXm41CR2NSbV7shcY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4355610}, "engines": {"node": ">=18"}}, "3.0.0-alpha.2": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "zod-to-json-schema": "^3.24.1", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "c05c7ce7e6f9fd2ad110b26705aeaccd4e8c0e62", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-alpha.2.tgz", "fileCount": 21, "integrity": "sha512-oTlF6UlVitSdVPQv0e+kAkZmbuunJAUYdVEh7ZRvoti+kY/T4vOT6p22X0xTaWgl0+MI1igAT+c83j7tCMuo2w==", "signatures": [{"sig": "MEUCIQDc7ZUSzHeM49ieMJSoS+/vnv0QwWtN50lUs9IHv3kDRgIgTY5hyXgm3aWj4bwDPmQU2CWAHGeRZQDtFsJmOT2tHes=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4355716}, "engines": {"node": ">=18"}}, "3.0.0-alpha.3": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "zod-to-json-schema": "^3.24.1", "@standard-schema/spec": "^1.0.0"}, "devDependencies": {"msw": "2.7.0", "zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"shasum": "027fca68206965e0189d29daf02a6bbc8c4a077b", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-alpha.3.tgz", "fileCount": 21, "integrity": "sha512-W9C7ZsaE85FpleZt15j28cbynbApbzz6rnJfnGPewTjRzaELf6nixOi22RDU3d296ecAX/LrFeY7PNm7qR6YoQ==", "signatures": [{"sig": "MEUCICQCnr8yeDShFsuE+6tdZQjoOnfUOwko6D+Od7856PXKAiEAkVlBLai4iX+1freq7QcCxSrSwmzXugrTJWSwr8oTTS8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4355822}, "engines": {"node": ">=18"}}, "3.0.0-alpha.4": {"name": "@ai-sdk/provider-utils", "version": "3.0.0-alpha.4", "dependencies": {"@standard-schema/spec": "^1.0.0", "zod-to-json-schema": "^3.24.1", "@ai-sdk/provider": "2.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "msw": "2.7.0", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.23.8"}, "dist": {"integrity": "sha512-edEnOh8i676TLX8ibWD69gHTaU6btJ9uCN+7/8vXWlMHZJo2ST4mgjRP5f6xC4rH8XXVMYK0rVPwbnuyR/PGzA==", "shasum": "96f902f52573ca556b73378c11841133b1d88b59", "tarball": "https://registry.npmjs.org/@ai-sdk/provider-utils/-/provider-utils-3.0.0-alpha.4.tgz", "fileCount": 21, "unpackedSize": 4355466, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFu+5ZsQmi2kofwWRBXuqG0S7GDNVtswma6yETvz9T/HAiADKNeVrcwaYPQE8GTLc5qxRTHyUULmQlm2Q++wivWQcg=="}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:03.431Z", "cachedAt": 1748373702695}