{"name": "@ai-sdk/openai", "dist-tags": {"snapshot": "0.0.0-85f9a635-20240518005312", "latest": "1.3.22", "canary": "2.0.0-canary.20", "alpha": "2.0.0-alpha.4"}, "versions": {"0.0.0": {"name": "@ai-sdk/openai", "version": "0.0.0", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.0"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "workspace:*"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e7177a49b3566af69e80cb9638d47e1c500aeeb8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.0.tgz", "fileCount": 8, "integrity": "sha512-47AM6OeOAxdJXPpiKdmIRso+hukmVjIcIflK3giKRHcQm4mMjEZyVOnXS/H6KyFoDGycxghZk2sQzgzn9Fv+ZA==", "signatures": [{"sig": "MEQCIC49CG2oBlb+Wyp1UMFpKdW72WU8ssRfM5C43TPBV2p7AiBW9Rn61lFD30jKO7nxbT50Le3HzK2O+5hwDWGPemg/Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149338}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.1": {"name": "@ai-sdk/openai", "version": "0.0.1", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "aa5037696a0379d4a924b864cc49311c236997b5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.1.tgz", "fileCount": 3, "integrity": "sha512-KLQl56KzuMg0LbmUIXq3wpqcUGBzSoqEvQMUtyenFgUCwlgh47XvaWO1DzD/F5ymR4fwiiQFCvysQrnd+mC+1Q==", "signatures": [{"sig": "MEQCIBC6JDiFwHztHI2ICFP8G5oZsGXBeFd2kuWlbS0E4wooAiAUPTc7mi9PPww3mNSd2yeRgRKIrzYft3oqKmQGiqwY1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4652}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.2": {"name": "@ai-sdk/openai", "version": "0.0.2", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "fd436ed9d6c0e000096b51b79605d62c410ad041", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.2.tgz", "fileCount": 10, "integrity": "sha512-Dhq/DPjCR8CcyVzWo980yBkgaUulb4X8B1GMvJJ1/BokPv0e/fP40+K1XP6ARLWWJOf6JRCn1XXDdOkHj2Ljqg==", "signatures": [{"sig": "MEUCIQDKVaPm/P2gdiCJycnzbRvEPEEV4JIxH7L2jg0qII7gzAIgcw/ouE9nRfG9w2qYYCoFJAxbsFFmFii5DeEwI+zXzmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177703}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.3": {"name": "@ai-sdk/openai", "version": "0.0.3", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2e52fac64861ffe44ed77ffca33cb7f1fede3808", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.3.tgz", "fileCount": 10, "integrity": "sha512-idgGpi1Puir+OdD8Ss45lDpD+jH3oxiaxlYIfJ2/Q29eAcKnqsURMc7i8E2Zs4sxIEQcc15Xi2sRoemX+rH6Ig==", "signatures": [{"sig": "MEQCIAOgpc8VxjfMW9x4JX91amdRcYFXco3in0ntCC0gO1p2AiAWz1G2MVb8Eyn4/Fu/svHRnXBOlkW9XMV1ru92TqwxHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186797}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.4": {"name": "@ai-sdk/openai", "version": "0.0.4", "dependencies": {"@ai-sdk/provider": "0.0.0", "@ai-sdk/provider-utils": "0.0.1"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a8f23c856c41407cfda7b549a3a85eaa56c64137", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.4.tgz", "fileCount": 10, "integrity": "sha512-OLAy1uW5rs8bKpl/xqMRvJrBZyhcg3wIAIs+7bdrf9tnmTATpDpL/Eqo96sppuJQkU0Csi3YuD1NDa0v+4povw==", "signatures": [{"sig": "MEUCIGQBTDH0QKLpN6X+8saZNMdIAA07Cyz4EuFO2POcev3WAiEA7NLsvPFgwkZtAcHIxbKP7TOe+cYAO59a8zZOVgFYSjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188161}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.5": {"name": "@ai-sdk/openai", "version": "0.0.5", "dependencies": {"@ai-sdk/provider": "0.0.1", "@ai-sdk/provider-utils": "0.0.2"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4645790138b99403d1bb1f8392ca6e9abffbbf40", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.5.tgz", "fileCount": 10, "integrity": "sha512-Sdpkq1s/DcDIdTyaK3+FsI9rNtFszWFMUsa5xotXQwn1MswwVoDCZHtRS6z1B2q7+zRlOJNUDp0FAF+RZpsJTQ==", "signatures": [{"sig": "MEUCIQC8wk9OPwVafrrf4sAAXNIRByu4GDM9fhX/bHRTPBa3wAIgTndXzcpVlqW2M6+DMQhGFpO6Fc728D4vpui02n5YtRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183291}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.6": {"name": "@ai-sdk/openai", "version": "0.0.6", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.3"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6507d1676954cbbbeceeb1d32a41b311e3cdfdaa", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.6.tgz", "fileCount": 10, "integrity": "sha512-Ws+cDSfUeiyo3NDNVw+0Cr/7gAmHvoDIfEm2N93yKC28YbdTBWZUyb/hHMT+ih0VvERM5NJuOb3/5RSqFpb/Ow==", "signatures": [{"sig": "MEYCIQChb4zcAaoWeo2MOzmRuiqLwsn1gi4blzIhTJE8+4620AIhAJkIkKYQUm6CK+waNz2dey3dRcpGxTJ8kh7Y7JRkUVx7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212583}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.7": {"name": "@ai-sdk/openai", "version": "0.0.7", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.3"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0b98fa02132a95e2ccb5bf01618e4d1424027b86", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.7.tgz", "fileCount": 10, "integrity": "sha512-d/otWufiNY/wZIyty0NYm7QNPcWY55uDJCAJzJw90T/UKzHRHsAsYAXvJX3k23TVjo3np753f9oYzZGafBmXgg==", "signatures": [{"sig": "MEQCIETT2yA3D6jKDQn/3xREk3S8OraGeRGcDvgwVn3oCPINAiA7Z0zMI82rX2BbkiEgBXUuV0Z43D4F18fuOilvOu7XwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213352}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.8": {"name": "@ai-sdk/openai", "version": "0.0.8", "dependencies": {"@ai-sdk/provider": "0.0.2", "@ai-sdk/provider-utils": "0.0.4"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2d62c49874ddfdff2d0434a96f526bdb8c3652d3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.8.tgz", "fileCount": 10, "integrity": "sha512-fiSjiyBNqi8onQLsXyxSpn4qgqE8TWD3Aq0Sa3K9uIaOajCOwLiFZkMp7X3G8dIJjOJcJf6oqs6e0eSGR+vAGg==", "signatures": [{"sig": "MEUCIDr9uBtyo1KPIKCrlE9/tnoETVsQhszB4CU/A+KW06R6AiEAlDaMG8vnf3eoB2KlcizZmeu/VfHAoDePUX+kllipLjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217664}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.9": {"name": "@ai-sdk/openai", "version": "0.0.9", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.5"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a8c8b4693ef45984ea16d5cc09a04674de210e1d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.9.tgz", "fileCount": 10, "integrity": "sha512-SSZGtX4KFDXWYmQ9JuhVumo1XOx1JAdHybYy08iwVXuCud9xdjZjjxgZkNPytQK9gRxFsYDOw1h0V/WXO7XgfQ==", "signatures": [{"sig": "MEUCICnAj81/JoUQXo2MyIYTMKXpZ3Ut6w8/7bXOfUMALhk5AiEAz/gF9uHTrjfHNd8D5voQyisU+LTIxjMF7zu3QNotBS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217664}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.10": {"name": "@ai-sdk/openai", "version": "0.0.10", "dependencies": {"@ai-sdk/provider": "0.0.3", "@ai-sdk/provider-utils": "0.0.6"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3a1fbc8e8c98e347b10cf92c7232a661a9f018e8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.10.tgz", "fileCount": 10, "integrity": "sha512-q6x8UVkKE67n9Apy3toPPd5SpLfqxSUQ8t+ADm1A3FXRHMCJUD/1wt8EGSkIPmLZDsLpLXW/NZG+PdgOpHYvAw==", "signatures": [{"sig": "MEUCIErA50strwiRe20LM5d52a+3PEVAmxeMgFotx+Qy5VMnAiEAt97Tg2OHOjGqSO4fXurKG8p/XdiUfJYDvpH96XLqJ4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217665}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.11": {"name": "@ai-sdk/openai", "version": "0.0.11", "dependencies": {"@ai-sdk/provider": "0.0.4", "@ai-sdk/provider-utils": "0.0.7"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a2206a13402e05a586ba6ac4f3768fabb49dbc39", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.11.tgz", "fileCount": 10, "integrity": "sha512-LZJN1wpjwx6cMwpZIdIBTONjvRl9NPp1NC99NAZ6f5t+y4BfRVkQC4YW4ISODyybmIDHOK7OyZ0NtDU4UWUBfQ==", "signatures": [{"sig": "MEUCIEc6COjz7g8UKqTjUDlBppLAK8KK2O4peHOg0m66nVp4AiEAoy++gVRBR4laxGOigJY3yQFM8pf2R7GU0EGILbfYlcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217665}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.12": {"name": "@ai-sdk/openai", "version": "0.0.12", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f434ae5870cc87aba1e659a879c1fd9f26bb72a9", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.12.tgz", "fileCount": 10, "integrity": "sha512-+lWw1SiMy9C4oj/X2je8En8r4v60TYnEbCytOjkqThWIQJULuzqElL+9ncAePpS3XV61CN/4biJUdsbFeiE7NA==", "signatures": [{"sig": "MEQCIG5Cuvlv5mGThhVUQXV5lu2N0C6VSdb5/zVgEEFZbLltAiAbk9uiBCt2GdZQmFG8XE5mzSt4F3fCg0otvP2wpdPV6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244313}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.13": {"name": "@ai-sdk/openai", "version": "0.0.13", "dependencies": {"@ai-sdk/provider": "0.0.5", "@ai-sdk/provider-utils": "0.0.8"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a16f16e0e0f669d757bfc4c4d9933f7b1ca8e325", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.13.tgz", "fileCount": 10, "integrity": "sha512-V5Jjem+83sVxFNzhNUV25c7uXC33VKIhoo1qN098EMqTriEp9oNCRAaNXf95eVFbFs2bDLVZtkoACv++Go0BUA==", "signatures": [{"sig": "MEUCIB0X/xRAiWbXyXJHawatgqDKNyTA6p48eDHfRXU268+AAiEA9GiZcY8hZnl703a5RBYb4DXqTio24sV0j+HgXCLwVL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248901}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.0-85f9a635-20240518005312": {"name": "@ai-sdk/openai", "version": "0.0.0-85f9a635-20240518005312", "dependencies": {"@ai-sdk/provider": "0.0.0-85f9a635-20240518005312", "@ai-sdk/provider-utils": "0.0.0-85f9a635-20240518005312"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5349670e4b26b32f15853327caa5fe4957c35f42", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.0-85f9a635-20240518005312.tgz", "fileCount": 10, "integrity": "sha512-AJSUyQihc/1PzbLEPC16vMqjV3Z51CL7WtTvh5abaMEj8jGzvzl8nANp6PBhw8YCpN3QhaCqLqVDYXmoItkHsw==", "signatures": [{"sig": "MEUCICGzMIsAb/stcYx8e17DSJwNZR0vREj+5D5p0nFiFSmgAiEA86if6vWBbgjnxXgK3+0qodv6dj1l9RHYskM/KQe1q1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255771}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.14": {"name": "@ai-sdk/openai", "version": "0.0.14", "dependencies": {"@ai-sdk/provider": "0.0.6", "@ai-sdk/provider-utils": "0.0.9"}, "devDependencies": {"zod": "3.22.4", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c76a73fd4197ea19809bd415579e798897221d46", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.14.tgz", "fileCount": 10, "integrity": "sha512-ZiW2ETP2/K9eBUBZBAAnjLC0sJwViFPsRWpgRTy+KzMOYqkwkRScdQlFreczvfvi8M8ahzx+cavMd64O9pg2AA==", "signatures": [{"sig": "MEUCIQDL50pof1jbY8aEHQKGILocj7yn+93vCeneHan0WYwFdwIgI5wb85imeaX6eSiiHgjG6ct9I0Wm69GSqbPDVbCXpMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248869}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"zod": {"optional": true}}}, "0.0.15": {"name": "@ai-sdk/openai", "version": "0.0.15", "dependencies": {"@ai-sdk/provider": "0.0.7", "@ai-sdk/provider-utils": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "07dbbf8db5677267c915f4bd578e9b2752d7ea3e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.15.tgz", "fileCount": 10, "integrity": "sha512-WqLbR+hZRjAqh+JrI1hqP5CopeSg2kyHUOL9Vh68hY6QhRBELURxya152nlU9tcpMIE0eqZWPSK2RYu+RP7/vw==", "signatures": [{"sig": "MEQCIHp1kTtTUCwixQDuSwVbV7uYrthpzfZdAAwPEuwgqEFdAiBWdhhkemoYYejf1NBH6xvXnMNmTNqpGFkVNX6OfdknxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248795}, "engines": {"node": ">=18"}}, "0.0.16": {"name": "@ai-sdk/openai", "version": "0.0.16", "dependencies": {"@ai-sdk/provider": "0.0.7", "@ai-sdk/provider-utils": "0.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9255ffe912962c4191b7d20a74f0c905277acd43", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.16.tgz", "fileCount": 10, "integrity": "sha512-rORtaSb3jmx6TGgD9HdwvFkGKbpoSeJoRQR9lCZchwF/EIixIAaHpTc5uO+4KfhOlqIbWt05a4A7ABmkPmj1fg==", "signatures": [{"sig": "MEUCIQDwSg1QJm4OlJvkYkgZG2yWMk/vez6G3dvZeWF0NP0RtAIgRBsZrCMCaZuEDZxHd9877Y+Xh9VTCCIBziQKkWXA/mQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 242043}, "engines": {"node": ">=18"}}, "0.0.17": {"name": "@ai-sdk/openai", "version": "0.0.17", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6c8db30f895c53f6d34bb6f00ef04359b3c99d31", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.17.tgz", "fileCount": 10, "integrity": "sha512-5Es+u9ICw23FZnRfRQLwn9AxkRcTDxGMFKcZjaLdj8suBeUd3B/CioUTNgXUTvfRCOPhkH/sbLrMyo746LkOAg==", "signatures": [{"sig": "MEUCIF7iX5sMJDrRQHO0ol1a6oeryjULIwVYkG3U5bU66B+YAiEA7x3/6KGaYFeqp3yc2y+P/8hIpsaOBK8jEOgh0Qbp8Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247840}, "engines": {"node": ">=18"}}, "0.0.18": {"name": "@ai-sdk/openai", "version": "0.0.18", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "28b4ab9c7f154efcd1d755fd2bc9a0364b6812f0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.18.tgz", "fileCount": 10, "integrity": "sha512-5iJ+/mbns0uLbNpACMYGUONUqQmqGJYrPbQvmGC+XVD4DZLvyZqBi0NDoQguOrZTz6u5O7rl51o9PDO+bEtmcA==", "signatures": [{"sig": "MEUCIQCLM3/AjcP84pAQ4eloY4JyTJS8oFJwhuW0/BPR7XFTxwIgfgRwFFkWtzB+rtqvrEaLX/gDNJJXc+sOk0WXGHNikaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248628}, "engines": {"node": ">=18"}}, "0.0.19": {"name": "@ai-sdk/openai", "version": "0.0.19", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1357bfcc33473dd7adb8777e789dd1afec12af2e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.19.tgz", "fileCount": 10, "integrity": "sha512-VebCyn97FQG1r73ImX3kklfhyf2NliVYVNcVz3OleaIPUiYGvbWNVzpMUv0TltIwBYVg8CUGpJ3H6VhKUgHpJQ==", "signatures": [{"sig": "MEUCIHlYsljEHe0uGsqC5cNPGJ3/lUmtOfUBhkGUFpFX+JjeAiEAiHysatJRtPNwJ9k20EJKFtrPZKk1TWuBQKcK9VapMac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 248638}, "engines": {"node": ">=18"}}, "0.0.20": {"name": "@ai-sdk/openai", "version": "0.0.20", "dependencies": {"@ai-sdk/provider": "0.0.8", "@ai-sdk/provider-utils": "0.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a60100c9e58a4611c60676021d96518df8f3af1a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.20.tgz", "fileCount": 10, "integrity": "sha512-y4CFn5CotPAaCJRPfXt0vs+fMcYVijxA4TOvfaHOT2cC74PlOVY/ph4vQeyrm4Ng6lKDE28QJaiJH2nzVSbUDg==", "signatures": [{"sig": "MEYCIQCoQ3sdyIh/ujpmFoMO5lXD4nWLncTU5YRgnUfgk0sHUwIhAKQGcsvDoLDC6Jkt3E8gocwzo6YJ4Cm3mYoQl6ERTwQk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252770}, "engines": {"node": ">=18"}}, "0.0.21": {"name": "@ai-sdk/openai", "version": "0.0.21", "dependencies": {"@ai-sdk/provider": "0.0.9", "@ai-sdk/provider-utils": "0.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a777e6154719caeb6b0dddaa73255ecd7aac53e8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.21.tgz", "fileCount": 10, "integrity": "sha512-k1sLRDKIsiHFuwPa9xBm4oQZ7JQVPE9+KzwP/E4v4zGwsL8Sp5gt+OTccP5cECVhDcRKDYaj0wXtCcmFyAh5uA==", "signatures": [{"sig": "MEQCIGzaNu2CD0S8cXx1zEf670Ki8Kfqcd55wj3I6kIvep7jAiBFipwxKgO3yb+qIsCGpJqdWxvngH5UwEJs4NOytJJNOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258954}, "engines": {"node": ">=18"}}, "0.0.22": {"name": "@ai-sdk/openai", "version": "0.0.22", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c922daff8786e022b9230317d79a579afd0295e3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.22.tgz", "fileCount": 10, "integrity": "sha512-veOLgLLP96UP9k0jtt9HOFtM4bt3GxKINy+dQcXqq3kc2fexiBuUwhx6QvW8jiW+S61GoUuzATD0Dmp9SKcLiA==", "signatures": [{"sig": "MEYCIQC0iTNjE8rMmXY0iamxRCGjDwiQ5LS+I5z/Z9M2Y3vl1gIhAKZw95pIeSwI+SN2EZgzLVxT1H8eriOLYeuOqhRCXXcI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258955}, "engines": {"node": ">=18"}}, "0.0.23": {"name": "@ai-sdk/openai", "version": "0.0.23", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1674c18b7ade54cb93a238f82ec490ba4bb55125", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.23.tgz", "fileCount": 10, "integrity": "sha512-mTNUjuXXm6ctwkfBFN30yQDR1+0vI4aPmHJHOMa7dUxi/jXODhK9zeIXjUB3S+rdxzyWXN1jHtmH8T++7HJjRQ==", "signatures": [{"sig": "MEUCIQCuV1t/bQc1Oewv2AWQAwlJGC35SQt8Uu0GFFkaUFlfIgIgeMWm7NEgdiPsucESwgffmMBAYeS5UNkjxdTT5QUokVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258590}, "engines": {"node": ">=18"}}, "0.0.24": {"name": "@ai-sdk/openai", "version": "0.0.24", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1208190ccb0b44b09366916c0375f2d67cdb39d1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.24.tgz", "fileCount": 10, "integrity": "sha512-uLqisEHe6Xp45KXnxdTnwdq8UBJwU345GUdoR4QD/yhLWnoJZf9YSltIyWxaHIcrm4acLCxD5xoy+4L9jwXbBQ==", "signatures": [{"sig": "MEUCIFIY+iHKbE/owPdLGB3mopQ1xzX1Yqjm7Wu6gL+9ggFcAiEAjC0leIgfMPh2zJQP7s9P6ZaeyQRnZ6mXtXAJtAiLNsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258580}, "engines": {"node": ">=18"}}, "0.0.25": {"name": "@ai-sdk/openai", "version": "0.0.25", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "30c757bf4427aa1513e5d5cc2f9141f53d033784", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.25.tgz", "fileCount": 10, "integrity": "sha512-7quODs6057mQtfyRrAIw7cSVowaimF+ARF0RC0mx3Vf7/tkq43+od8XDLV2qJwEDno9xGtBBam5LQPUY3LVGjA==", "signatures": [{"sig": "MEUCIQClGyFvl1sBE9JzPw5ug1JiP547KOusGrfGgzOY/0HgEQIgRnfJZLYnSZQkUBUtrs1/D1K4YruJqvuwY4uzWwnwl+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260356}, "engines": {"node": ">=18"}}, "0.0.26": {"name": "@ai-sdk/openai", "version": "0.0.26", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cc1b5a13e912cc4b79a7f007cb57584e1afe62cd", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.26.tgz", "fileCount": 16, "integrity": "sha512-rWWurhBIionfbvPiuvSTtBkMAMgorSobIeljvLAV6G8ddS+3mg0xBNeW0of6BOGehcD8LbsXE9QyHajV2jSxgQ==", "signatures": [{"sig": "MEUCIQCXDrucQb/ZTmb/Wet4d/1K8GLvKuc/R2/JDs0jo6Q1HAIgYtGnXo8c1UKA6XuX14qYaDM0bGNQUB+VDgRqKnsHDTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451315}, "engines": {"node": ">=18"}}, "0.0.27": {"name": "@ai-sdk/openai", "version": "0.0.27", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "03bbec12f0c80f166833bd1c70b619cb04c16c1d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.27.tgz", "fileCount": 16, "integrity": "sha512-ktvlI/k8olXq61dXuxjnhu6TUYN0KTT4S25HzNcfAq47yu6jUZKVpn/tbjZlDqWnXNTIGhPc7K9E4moXumkGgQ==", "signatures": [{"sig": "MEUCIQDne5zVWYnneP23HvcsksH3/Fj/A2KHQD88yxOUjppdHgIgRxzTkEthntXevc+LvmbPpI9GLouqMIF1h3K/sg5dIII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 451669}, "engines": {"node": ">=18"}}, "0.0.28": {"name": "@ai-sdk/openai", "version": "0.0.28", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2864adc9ec7995f9ee67fa98ca622c42d9c942f5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.28.tgz", "fileCount": 16, "integrity": "sha512-1rVwdaIIy6TDB29IAp8qcu7JmkfQ9Y5LqtFDyCu8fQgaVcAllKffmCuVBoKhti8LwcnDlafVZDCdzXajWnTJYA==", "signatures": [{"sig": "MEUCIQC2p5/en7x0rEbVhfezFMRIBMLCvy5PqRWOcknjEsBU+gIgIeyvUkUao+hcW4D4diCrTMyPS2zLw+DxkiQTCJPagbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452841}, "engines": {"node": ">=18"}}, "0.0.29": {"name": "@ai-sdk/openai", "version": "0.0.29", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "375be6d359f6dd4cdcacbbd6e31bbbe10cd7d672", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.29.tgz", "fileCount": 16, "integrity": "sha512-LctoOAlOX7bI3dQ5IA9DXBmHhhb2l59pg+aFclIp0qR86bqrB7eEH/odu8wn+yWjPy90D40aU0E1sNDYz985vA==", "signatures": [{"sig": "MEYCIQDN8x11WejyzW5XqfVR1HX6QHnlYjevpEB5/6Ff/5OMqgIhAIyH6+AsHS5nCUx05ac0FGSaZMuVpL+wwQJzDsx24kxn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459867}, "engines": {"node": ">=18"}}, "0.0.30": {"name": "@ai-sdk/openai", "version": "0.0.30", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "276a3902be73bc779e1d8e4261da90c904f7d8b1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.30.tgz", "fileCount": 16, "integrity": "sha512-8rhIa4Z1MtrxXV5E3T5Cw9C0luFI22n0oF2gXOArQ/U2ult81WA+ppjefLlGQSyqexR5QwLpgSNealpqEAAZXA==", "signatures": [{"sig": "MEUCIQDXpGfNjE6gCWdfamN6p+t/RpsmResKJOZ+KoNpA1iF3wIgVpVSt9x/fT1f2ODY8RJ6I1fO16WaYVcbYHINxj9WjCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 459867}, "engines": {"node": ">=18"}}, "0.0.31": {"name": "@ai-sdk/openai", "version": "0.0.31", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dcdda35cc1edb5157b36b45457edb1193c570fb7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.31.tgz", "fileCount": 16, "integrity": "sha512-7ehX2N0NzCdxUOYXutwYgu6gdWO+zS/v8pWEd7VW8QpNq3equ0VZ0j+pDUNv4f3GJ449QwySb6+V+DHM9W/pLg==", "signatures": [{"sig": "MEYCIQCIzPDDSg/WKVpEJbxZdbA93jupDb5/X9CR7cJfTPJcewIhAIzx/PYacKD68e5ytabP/FbqVceOK2fzZQKhTw+5hF56", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463560}, "engines": {"node": ">=18"}}, "0.0.32": {"name": "@ai-sdk/openai", "version": "0.0.32", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8627f44940b1e03da7b7dc410dca93cc80c18bae", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.32.tgz", "fileCount": 16, "integrity": "sha512-ySwtOoGTwmK+CiXreJO9iJkV7SBhetdgrmoP0gOKbz1uWQ9B1FMQ6Zdq6V5koWFXeN91Sz9n4jRIZ09GjxjY2g==", "signatures": [{"sig": "MEQCIFYQEeotPX3rbLTW8hZm/wDAxQpgGQJKljc2W9QcAc4NAiAA38Ofy1o8dmAPlKDErvXL7SSeQDuOPLR226dh+Acnjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463844}, "engines": {"node": ">=18"}}, "0.0.33": {"name": "@ai-sdk/openai", "version": "0.0.33", "dependencies": {"@ai-sdk/provider": "0.0.10", "@ai-sdk/provider-utils": "0.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4584ac18f89aac9d237b8cd668fbbbd9c4de35ee", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.33.tgz", "fileCount": 16, "integrity": "sha512-siVeHnagh08UFgdwflPdUKTdrVvfU/JWqSa8nCsMy6DvSri8T7zTzPZoCxXiMKPXkhQDd/KsaXhweOShGLQ1uQ==", "signatures": [{"sig": "MEYCIQCcd8b2g2MLv++uckCNxdvc3+gEqsLhM7oo5PIHmUI3dAIhAKh8Ag1BGB7LQ3UvcZE/A8FtxXqqlj1aZT5wZFvsTm4h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463844}, "engines": {"node": ">=18"}}, "0.0.34": {"name": "@ai-sdk/openai", "version": "0.0.34", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "05e9438f7ddf9b2358f6a7ed229a18a03ffc3c3a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.34.tgz", "fileCount": 16, "integrity": "sha512-ArVGvhfp66zYqFBWS/rRL/3ushcjsDXQJ5HdcJaCbv9YCrFJzgSZwFj2tWMdBMeQn3O7SGgGsIT75zFgXJDIqQ==", "signatures": [{"sig": "MEUCIQCxgBgb32Rdg31zUtu0i6MOtDEAl6mOhRxJMw8L6UTobwIgHcboLn38OttNsY2XfaQ5ElWQWFiYdXFGgani9Qxl8TU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466921}, "engines": {"node": ">=18"}}, "0.0.35": {"name": "@ai-sdk/openai", "version": "0.0.35", "dependencies": {"@ai-sdk/provider": "0.0.11", "@ai-sdk/provider-utils": "1.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3f5409b32b020a6cd858e8ae01a5400e3fc2e644", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.35.tgz", "fileCount": 16, "integrity": "sha512-CRBmVLU+C4jwfr4kxc40l4hrsqi/Jd1kd9MmJqeCSBHzXA7iTjQPFddz3uDG+73jLJk2DSlG5wrBLZmUYSnARw==", "signatures": [{"sig": "MEUCIHmmkVBkZPVrXcjB3oYejC5urcz77Jiz7YFtGi0ppfrZAiEA+8R5k4dzYE6xl7fTxupBp38UFva+Nu5RYWriEDf9RrI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 466921}, "engines": {"node": ">=18"}}, "0.0.36": {"name": "@ai-sdk/openai", "version": "0.0.36", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb6db8d7c60d4cc88fbcbf3b724e8537822e2bc4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.36.tgz", "fileCount": 16, "integrity": "sha512-6IcvR35UMuuQEQPkVjzUtqDAuz6vy+PMCEL0PAS2ufHXdPPm81OTKVetqjgOPjebsikhVP0soK1pKPEe2cztAQ==", "signatures": [{"sig": "MEUCIQD3hZI82qLG4Uur+TiUHwNSRGE1DmwKcACJRlol0N2cBwIgB1Nt5Mi+rncopB/JdMRa/sMAYtGJyEKa8BC+RDeYeQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468584}, "engines": {"node": ">=18"}}, "0.0.37": {"name": "@ai-sdk/openai", "version": "0.0.37", "dependencies": {"@ai-sdk/provider": "0.0.12", "@ai-sdk/provider-utils": "1.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "84ff5b1ee45c0efe20a3a372d1006a825450d2a0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.37.tgz", "fileCount": 16, "integrity": "sha512-OBhnvptzOEn+jy75vySnhbH5ezidn4WI/yftc0C1MiWkS/m4CmRV9P5zFGKHOgC1vxtw6691gASGYpQbof/JoQ==", "signatures": [{"sig": "MEUCIQC67THN++W9o9Bsjz+qG9mZ1o+7UQ2TeIv9WNlJI+M5+gIge33tM7yZjUomY1mcNCOYzu5bz6+ltZfRcW2Eq5jfJnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468236}, "engines": {"node": ">=18"}}, "0.0.38": {"name": "@ai-sdk/openai", "version": "0.0.38", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73a4001447af1a881edc492d546a39f15f9beb91", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.38.tgz", "fileCount": 16, "integrity": "sha512-xL3Tr1dyRDHj9fGj2qdmVHNegSrJvJil21TD8ESIqV24j7tgWp5g9Me+SFkzpdOPZiA7fF2U6LJ6bCiHOUbV4Q==", "signatures": [{"sig": "MEUCIQDKdQWSbg+QUNQA+R0ZvRlcwzAFpTGteNse4eU93k9T0wIgC2P5W6U2LSzR3i1UJ41wFAukjfBH/0VwiZ+YJDUbchM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525340}, "engines": {"node": ">=18"}}, "0.0.39": {"name": "@ai-sdk/openai", "version": "0.0.39", "dependencies": {"@ai-sdk/provider": "0.0.13", "@ai-sdk/provider-utils": "1.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9f43e2dc5680a4de9f513987dc51d2e85beab937", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.39.tgz", "fileCount": 16, "integrity": "sha512-UNkT7394/aG7s2VRY53mRqKlv2tBBuRGvIOuqMcpgfVquf+NRYWsDWAYM4UxqcK5uigSKk4uV/lIJX+fRUXszg==", "signatures": [{"sig": "MEYCIQDeOHL32xvlXv+Sh0rRzHZwuJVwqKKT1iD5mzN3R4g36AIhAN+fGEXSfqLRP9/x/A1XXtnwuazKtv7UAYdE9Car98lK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525340}, "engines": {"node": ">=18"}}, "0.0.40": {"name": "@ai-sdk/openai", "version": "0.0.40", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "227df69c8edf8b26b17f78ae55daa03e58a58870", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.40.tgz", "fileCount": 16, "integrity": "sha512-9Iq1UaBHA5ZzNv6j3govuKGXrbrjuWvZIgWNJv4xzXlDMHu9P9hnqlBr/Aiay54WwCuTVNhTzAUTfFgnTs2kbQ==", "signatures": [{"sig": "MEYCIQDCznUpz2BZ6/DC1bfTto+n2EqCsTIwRYWzuAzdmvOE0AIhAPlbnU+qHcgWcIfCiNg3qZ+fJ+BAKbPd3BsUL9SAyaxP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525340}, "engines": {"node": ">=18"}}, "0.0.41": {"name": "@ai-sdk/openai", "version": "0.0.41", "dependencies": {"@ai-sdk/provider": "0.0.14", "@ai-sdk/provider-utils": "1.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7fd3e9630717ac5db9d7a11ac7236c40f7dfc1d5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.41.tgz", "fileCount": 16, "integrity": "sha512-BjJ99msoB1XilnAmjbECJcEwYedBMwgKkAbbtefyl1Ez1heGBuYE9KMcjh/t6SnUwnwJE1obNJCbeC9n9drpdQ==", "signatures": [{"sig": "MEUCIG6hu6o4UdFMdXT7OhNvR7oPCIMhJVLtNdt9Bf6Y1/Y8AiEA/T9pxp8pSGmhoAFlFg0yRs0dIXFXKb9JW7BMGlbLeV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525293}, "engines": {"node": ">=18"}}, "0.0.42": {"name": "@ai-sdk/openai", "version": "0.0.42", "dependencies": {"@ai-sdk/provider": "0.0.15", "@ai-sdk/provider-utils": "1.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ba631b0fcc4768061fbd3ce74e0ce90cf7608818", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.42.tgz", "fileCount": 16, "integrity": "sha512-Z3UTpkYmMBgJn+1UPlcoGjF0+mx53JKjAkqDvf+o8N5wi1/lZxnDc1KXdut2j08mZhaMZ4QxzuidLDhAtUGb5g==", "signatures": [{"sig": "MEUCIDGokRxIxAHqC+lsC9IEEowZ8ZyDLK2GZ0h9DzCBXK1MAiEAmkpeuylKwUHY00B+jSjH/MrqjCp3fK8E+R51U9Q+qB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 525293}, "engines": {"node": ">=18"}}, "0.0.43": {"name": "@ai-sdk/openai", "version": "0.0.43", "dependencies": {"@ai-sdk/provider": "0.0.16", "@ai-sdk/provider-utils": "1.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c3999c9b47284c9afde06ed3605b17717d7c48e0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.43.tgz", "fileCount": 16, "integrity": "sha512-Dkx0nuP+L4JRZwDH3g/c2atq73XhZXREgLrMc3hNB9nH6B+sJ1tMDNdKUfMaYF7IX4w0iauG+YJZhWZFP4NMag==", "signatures": [{"sig": "MEUCIA23xi30DFQChmL/QoL74v3/u9gLgSDH25HeWs1yWTVhAiEA2EQj4f1ZuUfpgDZi/CLLi7T7GJ8yNFFSATRsgqOttyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 537252}, "engines": {"node": ">=18"}}, "0.0.44": {"name": "@ai-sdk/openai", "version": "0.0.44", "dependencies": {"@ai-sdk/provider": "0.0.17", "@ai-sdk/provider-utils": "1.0.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8eb226dfd2dea98cf2ee9d288a78cb4cd3b2e55c", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.44.tgz", "fileCount": 16, "integrity": "sha512-fIUypicSnuWIOdoRG+dgzAnZDWQrhzqyUvQaLC8hQB6UDAUXkn8sIPdsqKEV35vs3mNXDz/KZ4EEeLxSVGrzMg==", "signatures": [{"sig": "MEQCIGovEEF5Cw9cCOV3goTfMr4njC6DW6lSwkc5WGjd1fogAiBmtFJl0qBgucpXEi7iF9Ox9WYhOXhDaqwQL8QDpcINkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538155}, "engines": {"node": ">=18"}}, "0.0.45": {"name": "@ai-sdk/openai", "version": "0.0.45", "dependencies": {"@ai-sdk/provider": "0.0.18", "@ai-sdk/provider-utils": "1.0.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8ca8cadfec3710a6cd364b96caca07bcd7bb4033", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.45.tgz", "fileCount": 16, "integrity": "sha512-+ywipfosdChbbAYxZDtqcTCUWiujNodML8AwMEBJJuIcffOpH9NjNYNFxctC7qf3xlKVcTezaIuHVYDFXVsa+Q==", "signatures": [{"sig": "MEQCIDC9Uaxl/LBe5SEE6Y56Nn5bsuLkBjrYtjl/D4ox5//RAiAUZPe2b/7ib6SOMGbZawe5en7KSqeHMsUZhSNXAISc8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538156}, "engines": {"node": ">=18"}}, "0.0.46": {"name": "@ai-sdk/openai", "version": "0.0.46", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.1.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3e1a45b0b7c89a6e2e051073910acb679cd15e9d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.46.tgz", "fileCount": 16, "integrity": "sha512-RdE/PjdGt5zeE19qd/aqEr6fz6lhA5IWzBMy+N3dkdGtijT+eJP7Kxta6lYwnevkr1yqRPXYkYX/6v/s6xVY4A==", "signatures": [{"sig": "MEUCIQCLP1Ju50WaSZigxSIKC5FUTDbnNLhTnKasjcvL9G16zgIgfho3OfB5TVufqCEl0C/aJwB2AhofegP60XCL7w/Inl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538192}, "engines": {"node": ">=18"}}, "0.0.47": {"name": "@ai-sdk/openai", "version": "0.0.47", "dependencies": {"@ai-sdk/provider": "0.0.19", "@ai-sdk/provider-utils": "1.0.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "709bdebe043c3a57b22b993caf4ee42e5ffd74f6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.47.tgz", "fileCount": 16, "integrity": "sha512-sda3sLf2FxgGOuRamwWGZFyf6t6E3hJ+Xq8dtDty4vFlCZJEjuHLA90OmfJZnlF92pdp9IPEKB8aNXfO//3Jng==", "signatures": [{"sig": "MEQCIGR9cf3SWkd70AA948WVS5cUEVV68X0jRO6wHZPF0dCOAiB484BkLY7IFWE2u+yNmbvAMI6l/S1WhQK4xCZP7iDxhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538790}, "engines": {"node": ">=18"}}, "0.0.48": {"name": "@ai-sdk/openai", "version": "0.0.48", "dependencies": {"@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7a9dcfc84f6f781cea58b6eb0edfa6edd17e864f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.48.tgz", "fileCount": 16, "integrity": "sha512-buw9cT/55VGjvtcNvQGDljHQMan8C3/D1UvkcpY8XPfnimffrjAQa66fXldahhFQM20didNJawh0vlTM1MvRDw==", "signatures": [{"sig": "MEQCIAKRTGttLBB5/AJ7b03PBiuptXbzLZAo7r2TaaXR7587AiB1Hz82MVXDT5957XYH5CSxfWRvVbqvqMn3N+B8AGBHmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538790}, "engines": {"node": ">=18"}}, "0.0.49": {"name": "@ai-sdk/openai", "version": "0.0.49", "dependencies": {"@ai-sdk/provider": "0.0.20", "@ai-sdk/provider-utils": "1.0.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8408b2bf98aed65c4caddc285a24703650a85808", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.49.tgz", "fileCount": 16, "integrity": "sha512-c0uitq0cs1++m2qrdcbQWASIRYpteSjx1o8rzDigfR4ZFHTsRQJRKBXetz9A555bSI+0UnvwA7WA8mBQhlMTnA==", "signatures": [{"sig": "MEUCIG4pBJ5ybP8PtD3asAac/DuyiI6vhYY9yUd4+uqh5SDcAiEAgteJOzpQYf1qRCE1Dp4cq3Ek/pbyH/6giCqzlGBk4HA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540841}, "engines": {"node": ">=18"}}, "0.0.50": {"name": "@ai-sdk/openai", "version": "0.0.50", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9a115e0bdcbe4b5cdfb34100a13929ada4ea5a4f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.50.tgz", "fileCount": 16, "integrity": "sha512-qBaSJ854VVySRSTcNzrw7bH3ZOoWQJgUBU8L3ByA2vYk/JYTZcQVPNpE+XgDfU73ShdTtiN1JPh5uv/0rWy96g==", "signatures": [{"sig": "MEQCICku8KgN+xjGqLuwNGvIry01lhKWqa8Gg2MEi4veE3VvAiBkAVrOYiDZ0ytpuUdI8xQkkAY7uhmwSlolZv2ZxRzpxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540841}, "engines": {"node": ">=18"}}, "0.0.51": {"name": "@ai-sdk/openai", "version": "0.0.51", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "96d38ec31249a93c7416fe6552916f8e35457829", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.51.tgz", "fileCount": 16, "integrity": "sha512-e+badQnVzAuY0CXThjXZM4IdbztGnntz0Oo44jyklVsWjhJxnpr5m47ALR+0C/Wdakl5oHFGy4CZfiJ9K6ZyVw==", "signatures": [{"sig": "MEYCIQCt2u5S1pgT6kIiADvaKpISXvDoReMqgNqisXAPV0AdmAIhANWFxq9ZefqogS2lDQ1T9CmOICN/voXkmxOLnsxEoAdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540841}, "engines": {"node": ">=18"}}, "0.0.52": {"name": "@ai-sdk/openai", "version": "0.0.52", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3c31f9ad9fc4508779bd0180970940fba9cc2198", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.52.tgz", "fileCount": 16, "integrity": "sha512-xFzi9gtqWxo9XO+xofLRzupws1IBuua1iLf38tY73r6Ks2sU0B6kKZq94773tYxrHcRv6aJOKAD3kOmpxuApLw==", "signatures": [{"sig": "MEYCIQDFV3ZBuHiEee25VZGypcRIudsAiDkkCh6blhvmFHG4xQIhANfE0omVd2mrO8qm1Z2TtFEtcdg7lHd8IcynKlbOcvc+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 543966}, "engines": {"node": ">=18"}}, "0.0.53": {"name": "@ai-sdk/openai", "version": "0.0.53", "dependencies": {"@ai-sdk/provider": "0.0.21", "@ai-sdk/provider-utils": "1.0.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a11c0f3282f329f21341f90e6d00b477ce0aee16", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.53.tgz", "fileCount": 16, "integrity": "sha512-Wm4+EYG2Zl5WmhvZJrLhrBY+C46FEQmDjQ9ZB5h2DvRoJZNKtNiVNFMEQuyBK7QwivvlCSMJkPRBfFcbJgNLMQ==", "signatures": [{"sig": "MEUCIGA6n2Qbolm98I7MUWU3GIWBvUoP7n/gZ0t5nhy5W3H+AiEA32CKpRtPt0awCbmrm7/0asXw/mGYqoVR9MnRbfcnBi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 543966}, "engines": {"node": ">=18"}}, "0.0.54": {"name": "@ai-sdk/openai", "version": "0.0.54", "dependencies": {"@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "53d10d13efa269b63245bed1a0e3176828cb156a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.54.tgz", "fileCount": 16, "integrity": "sha512-0jqUSY9Lq0ie4AxnAucmiMhVBbs8ivvOW73sq3pCNA+LFeb2edOcnI0qmfGfHTn/VOjUCf2TvzQzHQx1Du3sYA==", "signatures": [{"sig": "MEYCIQCrm5uZYBeXBBm0y8PyaMPVr8Qelj0H2P65my6ekV6HZgIhAKSq9ZkVl/a7QbtqYldwLqPrCQ5BICKxEg0SrlOdZzYe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 544005}, "engines": {"node": ">=18"}}, "0.0.55": {"name": "@ai-sdk/openai", "version": "0.0.55", "dependencies": {"@ai-sdk/provider": "0.0.22", "@ai-sdk/provider-utils": "1.0.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "29a15b7f93f1f66c118b3e0a93e95fff8cbe0a41", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.55.tgz", "fileCount": 17, "integrity": "sha512-mYKe5Zqgq+vTtVeyBNLw34TLsWL924q0V6w0x76OgV+tpcyO+MmIc/m6Ego393yRDol2V9BDjmTAfD5jiFv5jw==", "signatures": [{"sig": "MEYCIQCfAFs6/iGf0nRmJxc3AenKSfc8jmYaxWa+B+oE2yhwoQIhAPQ4OF8xUqPRkSOTcqgSL6CWV1TG54fdkT7S7Df0p/f0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 554563}, "engines": {"node": ">=18"}}, "0.0.56": {"name": "@ai-sdk/openai", "version": "0.0.56", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1b181062c233a687638536811d5e92b909d87a1d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.56.tgz", "fileCount": 17, "integrity": "sha512-+S98OBeLMwDqGlPr2FEo/61wNxlwVdB1x7lZg5bfZArht1psVDhT5P5jrDMeQUXlRR7T2H29VyUjbSr5yarmhQ==", "signatures": [{"sig": "MEYCIQCEGD09hlKd9Tedf+OaYnOyykRFnlJk1f7qm6ktzWsGRQIhAPDBLXSjkm+Lc+cEw+uwQFf9sRrUqDRvKk89QHq5KE6F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 570771}, "engines": {"node": ">=18"}}, "0.0.57": {"name": "@ai-sdk/openai", "version": "0.0.57", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d6ed9b46dda23cd35fe80dcaef5d8171484a3b74", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.57.tgz", "fileCount": 17, "integrity": "sha512-CNPCGo4euPCTXoQs0GAGVp2CaDHezaAu45zNvsl6iJfdCTrBp3YG/NJWegs3zgaTfMHHCE0ae+SZnHHCL+Q4oA==", "signatures": [{"sig": "MEYCIQD6mfYvyABzfLLMaEoEiGs/1avrUD+dfN6rMZYTCGZ5NQIhAP0t3JjuuGUtNto9Lhd6HqVoMUw7vLaiaHCjDV1RG5Fp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 573405}, "engines": {"node": ">=18"}}, "0.0.58": {"name": "@ai-sdk/openai", "version": "0.0.58", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "efef7afb25c83874331724ca17879a7ad439cff7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.58.tgz", "fileCount": 17, "integrity": "sha512-Eao1L0vzfXdymgvc5FDHwV2g2A7BCWml1cShNA+wliY1RL7NNREGcuQvBDNoggB9PM24fawzZyk0ZJ5jlo9Q0w==", "signatures": [{"sig": "MEQCIHW62TZcRW/FD3F8EIZWkPPDPFiAvp9w/j9LIrUJboh4AiAbLCK51hK8RCCYiVjeFkT5Mh/sKXWGHF2mGWDYWyFTiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 575259}, "engines": {"node": ">=18"}}, "0.0.59": {"name": "@ai-sdk/openai", "version": "0.0.59", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0ec9f6eef20b9c23106673dccab2ffebad166c2d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.59.tgz", "fileCount": 17, "integrity": "sha512-TsADbBG6EH8v/5Nu2M0wPLTFDYSJ3QFKWfON4P5d2iPeS+ZtFHCS8GVdC+ohHarh3yj+9oun9UCgeInARWAflw==", "signatures": [{"sig": "MEUCIQDyNQVWOUBomhjXd2hNonBH2mD2FnDMEHpf73KpJNqjHgIgLtRas1LlhW+xE53CEsyt1HCZDs3Q+FVhcBHCNY1Bl6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578049}, "engines": {"node": ">=18"}}, "0.0.60": {"name": "@ai-sdk/openai", "version": "0.0.60", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ff6f6e6244f129c710a1ed3f68ed0d66e39b726d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.60.tgz", "fileCount": 17, "integrity": "sha512-NEdDdv3o76jT6UeWHxP6I/lMYcjFQhQGQi/U2gVqW1PEU4Pjaud7tAVSy27IPbiRakg6GOzWrltI2JhZgAI1wg==", "signatures": [{"sig": "MEQCIEqr6lfUBwcIwd1RhhCoDQppLAveXZHarKF1jL9SoMLeAiBWGazyUKY+3JXOKNlbstIakxagD3A1FIJUIRuykvWoPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578147}, "engines": {"node": ">=18"}}, "0.0.61": {"name": "@ai-sdk/openai", "version": "0.0.61", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cb1c79657c2fda2373a8ed1e0f8bbd7c0524656a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.61.tgz", "fileCount": 17, "integrity": "sha512-yIJ70xU9sbDjVAaNoq+W+0jnAgIUsx4e9VTnoNPXNTIQRpgpLvQ7iG8GYNgujO4oX4sLiHsWpOEMzrSwD0mNmw==", "signatures": [{"sig": "MEUCIQD0ZtlW1en7LScs54ie05RqqGVqHBrbYBuV4CclpTPCrAIgKulaKkt9m3gohAIPcUuGKvc+wu8AYj4ANlPWBRovFoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 585975}, "engines": {"node": ">=18"}}, "0.0.62": {"name": "@ai-sdk/openai", "version": "0.0.62", "dependencies": {"@ai-sdk/provider": "0.0.23", "@ai-sdk/provider-utils": "1.0.19"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2bd6b43f5d8e230f7bbcbd2c7a4fb1ce0dfb797b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.62.tgz", "fileCount": 17, "integrity": "sha512-qNQmTgZXGS3xyd6XsDGEMLWV3Ag3uAVPJxHVkZLDZ7Dubmgs3Mp8iupkwZ7JhD7TIBx8yifyyBuE1dnE70v8Ig==", "signatures": [{"sig": "MEQCIF+XkRiH6/Ko/9IubMw7WDTphdiz7VlyXHifHzkqWvXjAiBuNzrGH+xS3i5nLFzA9aIYNHFIig/yEy9GRh7hi1UtZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600086}, "engines": {"node": ">=18"}}, "0.0.63": {"name": "@ai-sdk/openai", "version": "0.0.63", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1e8345d254e5ad775446d7f77f0d21c127d4de5f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.63.tgz", "fileCount": 17, "integrity": "sha512-VX4yDfP1zoKIoKqkFuBJGPjQ7aYFxE2GgPW8hMAolCBolQyx3+0+mgID+JmU5Jaq3PAPWtxWuuiwasPqh+e8zQ==", "signatures": [{"sig": "MEYCIQCUtHKAsQ5IauHdbHMx5MWSu1f4sxQ0/if+rfUGlsBvfAIhAOhpUc7SgZlJgIMTbj9fSAz4D0EiNcd83tdJzkMUtUH1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602378}, "engines": {"node": ">=18"}}, "0.0.64": {"name": "@ai-sdk/openai", "version": "0.0.64", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "60474fb9701922c96f72cc4220a598b51752aad7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.64.tgz", "fileCount": 17, "integrity": "sha512-0FhO4hpxCTGKuyR1QsPMsxS3I385/aUPylopzVUaVT6pDnMwIh4HZrVqkK+R1D+xCR9F+I9ZZ0uSaEDo0Oe7jw==", "signatures": [{"sig": "MEUCIQC/xmG17m+zkjEFheFBG2EZzRHQf77Wo0kHbTtNK7+rxgIgONcy5tKdq9n16mno1WLPZTJx6l8R9NA2/q4z0MUxiNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 603647}, "engines": {"node": ">=18"}}, "0.0.65": {"name": "@ai-sdk/openai", "version": "0.0.65", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "11528b26456b506ba20636c63bdcf98a6ce4c044", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.65.tgz", "fileCount": 17, "integrity": "sha512-QKd8GOLOQbofRuDjBlh6HMJGBNOwV0vPL3NIKUu3bg9HNUMj+Y6pFxEKgTokZzGts8Uxm/FaPDCp6ckEJHaEyw==", "signatures": [{"sig": "MEUCIQDFMl9QqwhyU7Sm9QVpBlCxxzRjgxM34bBR5qNtpgwbpAIgfL17nFy3M3PJELzLllwTOfaDqWKQ8+E9VIDrB+3RzlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 616057}, "engines": {"node": ">=18"}}, "0.0.66": {"name": "@ai-sdk/openai", "version": "0.0.66", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "abf46fee99d1603cd1a0b2ed5d40745ad2dfaf7d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.66.tgz", "fileCount": 17, "integrity": "sha512-V4XeDnlNl5/AY3GB3ozJUjqnBLU5pK3DacKTbCNH3zH8/MggJoH6B8wRGdLUPVFMcsMz60mtvh4DC9JsIVFrKw==", "signatures": [{"sig": "MEQCIGljjZyY1MLp0TrQpxjX0FlQYw8ZM+exT+WXU6l3zmE1AiBf6MOk+tgxqt3ewL1veFDkcXwCcygFFF1A7wf8vcURkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 617379}, "engines": {"node": ">=18"}}, "0.0.67": {"name": "@ai-sdk/openai", "version": "0.0.67", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "95bfcd772164f84a279a51b8452c60bb1b6db159", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.67.tgz", "fileCount": 17, "integrity": "sha512-LOvbQaKXuNdhlZ+Asinc4DGdh4v32wKTzFB8FIKvVYjPuXwMWAsK4ZjrS6sxwB37AjmyGLPTMVBUtwcHZdpk+A==", "signatures": [{"sig": "MEYCIQDKbG32MCkkE1BDSituqNvphTFx/e0lpjGPmBVlZjqGGwIhAOxg0v7zLV/tceaeMeF+HcCw5rpXrab5vemrdqdyK/jp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 618551}, "engines": {"node": ">=18"}}, "0.0.68": {"name": "@ai-sdk/openai", "version": "0.0.68", "dependencies": {"@ai-sdk/provider": "0.0.24", "@ai-sdk/provider-utils": "1.0.20"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "7507534a217355273651ad2ea0fffd6e208587ea", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.68.tgz", "fileCount": 17, "integrity": "sha512-WSzB7qpBTrnYvFbnBBmIsw1G8GM04JRMr+I7B5T7msgZfleG4cTvVrn9A1HeHHw9TmbKiaCKJrEZH4V0lb7jNQ==", "signatures": [{"sig": "MEUCIQDQG22PpRiQgz+e4g4UvxbMP4ooJ7XFlUBBT/Rw/NIrIwIgWefzpNUT3PMuxfPeq3pQcEMskGppaGVgVR5RvQLNTOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 630808}, "engines": {"node": ">=18"}}, "0.0.70": {"name": "@ai-sdk/openai", "version": "0.0.70", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e1c586cf5eb225f6298872b62362b8285b2651c2", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.70.tgz", "fileCount": 17, "integrity": "sha512-RYLfiIG093bq6a3BJe2uUTL51zjxnDQLo4qHlNk3PLKSOxbb9Ap/vmhCLnPKo+flqFhqiD6YE9wuNZv++reHaA==", "signatures": [{"sig": "MEUCIQC3N+JxiA0CUTPCRUhFJVv0/D5uRemaghz/TGE2Cz2ZEwIgPtoq50lSQcxzGEIvJAHqUBUDz2eHIDQLyZZMBER1lps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 647551}, "engines": {"node": ">=18"}}, "0.0.71": {"name": "@ai-sdk/openai", "version": "0.0.71", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "76211ea98a4bea1a7675da3b205ff5ec519f8fc0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.71.tgz", "fileCount": 17, "integrity": "sha512-ds7u3sWEnKyHxM3lAL9xTs72228HEKcPZCAEFaxmgrexKPJe2tyLBtvS/Kg39SPKPtY9EeaKqi/nbx1AmnXK6A==", "signatures": [{"sig": "MEQCIHTyzPbkbupuaOQ/WvhbtBHtB83UeIroN2qzMJg0dPH1AiAGRGyx2/yyjHbp3UXX3MN1w381ylrcdOe+mSfjMF2Fdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 648615}, "engines": {"node": ">=18"}}, "0.0.72": {"name": "@ai-sdk/openai", "version": "0.0.72", "dependencies": {"@ai-sdk/provider": "0.0.26", "@ai-sdk/provider-utils": "1.0.22"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "0c09652e49b67476c4e0a445a2efe5f0cddf6de0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-0.0.72.tgz", "fileCount": 17, "integrity": "sha512-IKsgxIt6KJGkEHyMp975xW5VPmetwhI8g9H6dDmwvemBB41IRQa78YMNttiJqPcgmrZX2QfErOICv1gQvZ1gZg==", "signatures": [{"sig": "MEUCIG8i51phyoc3njm21ytKxWw9M9S5l+O6bPdBeidUprmSAiEA0i1tdeg7votjY9GgYi6dYRaAyxy5ggvGfEOR2GNuRvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649959}, "engines": {"node": ">=18"}}, "1.0.0-canary.0": {"name": "@ai-sdk/openai", "version": "1.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "285ef057e89ce389b83ba285f54114e73b11aae5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.0-canary.0.tgz", "fileCount": 17, "integrity": "sha512-m9snkZT8auuueKiIFQv7RM3thwve/VcbI/8ZTnMTQpXqc6sc2UKKlRCwRHhIlp/xIUfP06qirrbP8+LfSMR0jg==", "signatures": [{"sig": "MEUCIBEJkg1kyfciZ0UK2ZDjBasz6DzJ3ZsOHuO0FgnQrI12AiEAjTG8a0RycjrXRz6Z6H8yf5BfcQWOnKkD2m9ZGkcxc9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650273}, "engines": {"node": ">=18"}}, "1.0.0-canary.1": {"name": "@ai-sdk/openai", "version": "1.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.5.4", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5e6b451a0d63a2759411fd380ae9d17e2fbf2093", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.0-canary.1.tgz", "fileCount": 17, "integrity": "sha512-n5EC9PQ9LXycNaHtDpE7TjsWNgGw2cbsh/zwwSzFeCmKEpi5jzAVR8955XlO3J9OJ+5ecjCPkoGZJE9M34lKfQ==", "signatures": [{"sig": "MEYCIQC7s3e9kmeNMRX74PS8KzfuWGgEiziEzwHvdthWgXu5DAIhANBHVQwW5KsaoxM0UIkv5KAtcN0K02ql+m58EBSozxz0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633804}, "engines": {"node": ">=18"}}, "1.0.0-canary.2": {"name": "@ai-sdk/openai", "version": "1.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cbc4ab7246dcf81d438c927824c1535e3358dbf2", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.0-canary.2.tgz", "fileCount": 17, "integrity": "sha512-qZxMkbWA5BsRvpTOigWx326dDh3daqX+To/JWlJKgNyXOZuYnlIwRw46ea0800LNrvZc5uzK1GPkWiEy8RGUsQ==", "signatures": [{"sig": "MEQCIAd0HJIiTSxBs2l0tFe3zlKWzCYS3Cx/3vmftEH498itAiBTFWbABrfQtl5T6eWWPwYiSkjmrU8TJjXVY2Of8soLuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633951}, "engines": {"node": ">=18"}}, "1.0.0-canary.3": {"name": "@ai-sdk/openai", "version": "1.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "1.0.0-canary.0", "@ai-sdk/provider-utils": "2.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "febcdc0ad4431e1bf4a0a789aaf412d8de6dc739", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.0-canary.3.tgz", "fileCount": 17, "integrity": "sha512-5xtkCL5ObmGCaGbk19AGnr5gGdFGd22JhSq9CmeuvjyeKy5xSU9Qc2PaXwx6GsKEYSFC72IT1U9TYxo+n5HRCg==", "signatures": [{"sig": "MEYCIQCf0O8GRuWFoy72OM6Mud3689DwiznBDWCwU5v/qdMj4AIhAIS/2vd8GzHyxbyk3QCBvsba0+nZ6sMAEUnK1MF0lb7d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 634065}, "engines": {"node": ">=18"}}, "1.0.0": {"name": "@ai-sdk/openai", "version": "1.0.0", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "4dd7bd9106dfac1e6b53ca0a3d141d68c22d1521", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-EZ2UDxTBb3v3e2eexKTFGXF9MEy7rEcfIrkdD3yo8RCpwIkwRjyxCfs6wzh8KAW6XQZRu3Rp0kqw1S4FQcQgJA==", "signatures": [{"sig": "MEYCIQDbbWWU3KJfcRXvitqLBduNtxgy5O9LVKK+i++hpOSt0AIhAJEVYdb0hH/JwbBLr7RQV3rDVtXm1461RNvGZcpIcIYO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 584944}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@ai-sdk/openai", "version": "1.0.1", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "300eac828dd7825d62ce6f931616d25e774fd074", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.1.tgz", "fileCount": 16, "integrity": "sha512-snZge8457afWlosVNUn+BG60MrxAPOOm3zmIMxJZih8tneNSiRbTVCbSzAtq/9vsnOHDe5RR83PRl85juOYEnA==", "signatures": [{"sig": "MEQCICWzBwwDb+bo2+FwrE7XZc2Oa7J43GSCAUGPp8+vaChFAiByl2KbXldz6btnYV4w4PPmLDXl55QWXfZM3fhnGCEGWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578040}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@ai-sdk/openai", "version": "1.0.2", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e353ab00d0e1b101307ef0c58ace38b91660a154", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.2.tgz", "fileCount": 16, "integrity": "sha512-yAoUiBp4EsPD0Ryc8mRBXYtQLSBkv7oetvhz4AELd37A5jgl79IyspYQa+itXyuVry7usw+Foo3RKoeAFupTEg==", "signatures": [{"sig": "MEUCIQCTRuINcoCZ3IKnFmOGjRwNdBWiyG3qgh7mvzcOXYgW3AIgaXSWCj1wrR7MFV76Y3W1jzN7kU7Ll7aWsdUFWJxoG+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578136}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@ai-sdk/openai", "version": "1.0.3", "dependencies": {"@ai-sdk/provider": "1.0.0", "@ai-sdk/provider-utils": "2.0.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e51c8ff5e4956ccd003f16ea6b0edfc8b235a64c", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.3.tgz", "fileCount": 16, "integrity": "sha512-l3JG+PZXl0Bt9xTeCBtWM7bI9wAYvK+8fLf31g4cFbLnI63DRxua9odhGvd9oQPM5bjFRJ7VjG/0qbOWSBb2SA==", "signatures": [{"sig": "MEYCIQC0PaODySO0GB66h32j6QRF/zbdqxbVIzsKaiFeANCd+AIhAOdF2vzNXyUGVVd3h3h3jnVuFUKoP3dpzQxBTytJjREo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578302}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@ai-sdk/openai", "version": "1.0.4", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5818aff68a3e3e4853da97f6f624bcaea852ff8a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.4.tgz", "fileCount": 16, "integrity": "sha512-3QpgKmkCeJvUdeu3sVRL/ZKWzg8biO0tN2owQW/lFV95o8qskE3bN95R9H136Mmu0124/C28aY6ScxO93nUrtg==", "signatures": [{"sig": "MEYCIQC1nXZq6i+q5Q2PinCf2bIFgqw5rknvyCQHN4QCetjZmgIhAJSwxEmj36tRoELnqyNsvSHMFBDNvut2Sq8hAWDOeH70", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 578425}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@ai-sdk/openai", "version": "1.0.5", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "cafc58f91f585597b855aa65aae743fee2e6c280", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.5.tgz", "fileCount": 16, "integrity": "sha512-JDCPBJQx9o3LgboBPaA55v+9EZ7Vm/ozy0+J5DIr2jJF8WETjeCnigdxixyzEy/Od4wX871jOTSuGffwNIi0kA==", "signatures": [{"sig": "MEQCIGgv9hp9zHAuE1K0ZLv8RPv0SEpH9xOP/m3e3g1yD6PCAiBXM3wisTg5wHLY3XGQQBJJQx2pZsUeck9vKss0tsXXIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 581843}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@ai-sdk/openai", "version": "1.0.6", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "11376d163217fcb92c18a1940968c8b656a162a7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.6.tgz", "fileCount": 16, "integrity": "sha512-AhNILXn/hVD91mrokg9Wph78BiWNP/N0tJiua+UCS5TYXCbSeeqKqiodRiNxw6tDA5sGnrC6yCv8TjgY1CwzWg==", "signatures": [{"sig": "MEYCIQCqlej1Iv8rKApNLqfwrZZRN93wZuwgVeKzvGBbMG+ZQgIhAMvsoRmlT+e7OmSJaiZvlk33/1FrRp2ehf33nFZDzDOV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 584365}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@ai-sdk/openai", "version": "1.0.7", "dependencies": {"@ai-sdk/provider": "1.0.1", "@ai-sdk/provider-utils": "2.0.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "2ca1816d8efc834829c0ebc65fb8e72501c45568", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.7.tgz", "fileCount": 16, "integrity": "sha512-eXWERuUrMyH3k1GpmYHzTj7Xzxx5/0dENAkbxnj7t2Sn2Mgfb5Td/8QYfGSxuuPd8BrfGTyknArff6yqPkuvPA==", "signatures": [{"sig": "MEUCIQDMHRpg5BIVvvZQd04HmnyfpufGN2vcqhO9Zb6dcdC1TgIgFeQAiA90eD8ROFq4ISt7b4ou9nMHNgPx265aBWl+Rrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 584461}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@ai-sdk/openai", "version": "1.0.8", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d00d0fcadae8b4523fe7053bd57696d4bc402b47", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.8.tgz", "fileCount": 16, "integrity": "sha512-wcTHM9qgRWGYVO3WxPSTN/RwnZ9R5/17xyo61iUCCSCZaAuJyh6fKddO0/oamwDp3BG7g+4wbfAyuTo32H+fHw==", "signatures": [{"sig": "MEUCIQDAkJTis6RtxQw6NHhziun3SHp1QR8t2mmTy5xT4B6AlQIgQdKoBnAEbF5w3GtOAtcm/QODn/KIiM6bFveiXSUwSWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 594999}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@ai-sdk/openai", "version": "1.0.9", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3e5518f55f7364fbe6b240872181f4c11e6a5c31", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.9.tgz", "fileCount": 16, "integrity": "sha512-NdOk6Gr70qkP2T9VXF3Y3wCm6Z4fQAeTbUkALXltGoNOwPWC7US2jScY8PxFMQFEqN4OSfQlKLwRvge7VWnjJQ==", "signatures": [{"sig": "MEUCICniwi6GTKSxtiWI4IWrRe3zgZaa+78a3CNnztgCRxVZAiEAwuDiWWXyjLe0CAFtDOsSYg/kifXiI3QJCpY2PPdfKuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 609536}, "engines": {"node": ">=18"}}, "1.0.10": {"name": "@ai-sdk/openai", "version": "1.0.10", "dependencies": {"@ai-sdk/provider": "1.0.2", "@ai-sdk/provider-utils": "2.0.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "06997ef447539619e7e38fddf781d7ad66150a13", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.10.tgz", "fileCount": 16, "integrity": "sha512-ltZ1B/qSHvNiXngJBVY1GJD41/kvvi9QCQeuiEdf5utJnjRlR0MKNHzb3YRhJaLKFuGFrq1vAnxlSHGANY8R7A==", "signatures": [{"sig": "MEUCIQCIQP8h0W6j1RnuVPv4t/XQlYA9KybQuaixBa/xV9RdlQIgfG4KWLum8J+qBbSkjuc9L/ffa+Hy6BXfHbPBS+RGIaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 609832}, "engines": {"node": ">=18"}}, "1.0.11": {"name": "@ai-sdk/openai", "version": "1.0.11", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "895d348d4cbc07a94834b54ff5192828d0e253a0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.11.tgz", "fileCount": 16, "integrity": "sha512-qI9s7Slma5i5bB4yYVlFdcG3PNDwdqivPT1Dr8adDX92nSSpILjgFIooS5yys9sXjvvcfOi/WXbDvVhLSRRlvg==", "signatures": [{"sig": "MEUCIQDhtlDTMZPzUK8UDtvnj7Bde7Ox2r5ZmwRDuaX4DFeQ3gIgKT7B959MuPfd6cK3B2t4ThFd7tBiaL62BUcwtsUP2X0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610028}, "engines": {"node": ">=18"}}, "1.0.12": {"name": "@ai-sdk/openai", "version": "1.0.12", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1dfbe5ab5c3448a70756f9cc8524e035c6d9c3f7", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.12.tgz", "fileCount": 16, "integrity": "sha512-w/fe7UJLbTAnSYO/o0phdVYC/Xzt/N75I6H4CRj5Zx+etbnNgTrfgFxYNxUY2kEhAeZZvuSm4xwVzWPKX741rw==", "signatures": [{"sig": "MEQCIAy5dSS9vY1uFttkeGBsFL0b5uuu3n0cov02U0yWstp9AiAuOFIFmR9mQN1xJptiwoay1cOOlpwFY1wueVD96bBeYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610749}, "engines": {"node": ">=18"}}, "1.0.13": {"name": "@ai-sdk/openai", "version": "1.0.13", "dependencies": {"@ai-sdk/provider": "1.0.3", "@ai-sdk/provider-utils": "2.0.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "03ce0cd6a3f1b3fb78ea3069320fba23c41ba71e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.13.tgz", "fileCount": 16, "integrity": "sha512-kuSLNM6nFy+lgEd6d0X9Bp4hXjPbEwtUbnIrI4jqa9uZZupHc9vh8rOF6XO8s6ZhrWYjnuYZmhvK0S4k+sHrsg==", "signatures": [{"sig": "MEYCIQCdj7kTkTEFdlI1M64IQJj7b8CePgwTVfRgOXdL9aicBgIhAMofsjm7QNYQiJzfwWJa9Po/+t7SKk8Q6LQpkxnSb8Jx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 617305}, "engines": {"node": ">=18"}}, "1.0.14": {"name": "@ai-sdk/openai", "version": "1.0.14", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "841cb053f4d1f7d00ef56af75a30138bafd20804", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.14.tgz", "fileCount": 16, "integrity": "sha512-uyOkQNtYsHr4qyV7y0rmMAtdW4LTJoThYo1qXcvQa30RDh/MyvLEOjKYX181Siyp8LcTqYvwf6Tt+eckdVTTug==", "signatures": [{"sig": "MEUCIA3xjEo6kKOT7GHflxbBXrkCF5XOleU/Kz1X70vGTRWbAiEAgShJyz/mm0e7Tw2GmoT8Ytq6FMj/26fXQ3Bp4OCHLGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 620988}, "engines": {"node": ">=18"}}, "1.0.15": {"name": "@ai-sdk/openai", "version": "1.0.15", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "929b85c1bd79aa7bcd2a83f7a14e0606238728dd", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.15.tgz", "fileCount": 16, "integrity": "sha512-acX2crxBrkyfIlTtJGGX/YD/ck6L+LpPdaYQqGiahacHX07yn1nUE1jlnNLmTnDFoMwgxJXXt/wI16LC3ybYZg==", "signatures": [{"sig": "MEYCIQD2x7CJiqv0dCO66jiPks3Y+pRYnpe8paQ1vo4PJ2p9zQIhAI4k+/Hte+/7xlGxc/1BuCpXLWt5sQCFc79mHEccxpmf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654890}, "engines": {"node": ">=18"}}, "1.0.16": {"name": "@ai-sdk/openai", "version": "1.0.16", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "11e6c87e7e04ced708e3b19811a084364e5a244c", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.16.tgz", "fileCount": 16, "integrity": "sha512-MRd0DHj9ZreoPKIqCfS/T6RJnDX5YZL8RC0SFBzuq1wdNyOrUqlGrklF0CbjrjXmWIMPE+SJFzMbvYWqVXTKWg==", "signatures": [{"sig": "MEQCIEkaV0LoNA61uP1Ksm/BGuOn5Str9ksMYxgHyMVVnmabAiBV900ZqWmWcTJ56Lu8ftPWvbN+Mvniu3qUUalTHv8gXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655053}, "engines": {"node": ">=18"}}, "1.0.17": {"name": "@ai-sdk/openai", "version": "1.0.17", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "00473bf1071e6d14b7b172af2b39060d8ca36169", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.17.tgz", "fileCount": 16, "integrity": "sha512-W0+VHIDuj8AFyuRJNIxunCf0WhjZSGM3ZtronMikd+QAqbkowN9ytah2fgW503nRq0Vvb77MGEV5mL/Zj7fmEg==", "signatures": [{"sig": "MEUCIEz9ZcsBcMmqnC9vciVuLbyu90Vj3Aa7Fy5UdMErtb7WAiEAohmoiOVpsaGjxlnr+6Uzo2pa/Ate46UumDlvfWi89x8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 660627}, "engines": {"node": ">=18"}}, "1.0.18": {"name": "@ai-sdk/openai", "version": "1.0.18", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "549043adaa4101dd66238b381b2028ea1ac2b007", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.18.tgz", "fileCount": 16, "integrity": "sha512-bienqSVHbUqUcskm2FTIf2X+c481e85EASFfa78YogLqctZQtqPFKJuG5E7i59664Y5G91+LkzIh+1agS13BlA==", "signatures": [{"sig": "MEUCIFNxNleWx1do9ryM+H+m6NVCxUSAMMhbfJh1FDQRnovFAiEAoLcjV0y9CctpzVtTvKkr5+LmL56Krkac2r5wznYC7Uo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 664629}, "engines": {"node": ">=18"}}, "1.0.19": {"name": "@ai-sdk/openai", "version": "1.0.19", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "dde2e2bf854a3f02003114a5137a087a7110ec45", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.19.tgz", "fileCount": 16, "integrity": "sha512-7qmLgppWpGUhSgrH0a6CtgD9hZeRh2hARppl1B7fNhVbekYftSMucsdCiVlKbQzSKPxox0vkNMmwjKa/7xf8bQ==", "signatures": [{"sig": "MEUCIQC+E60iODY57UhILTDtXTL7cNaH7lSlBF0FGrTdN7eeDAIgAvUn42PhmZesinB3lgNYPykSYT0+rm+DkDzNhmorFjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 666729}, "engines": {"node": ">=18"}}, "1.0.20": {"name": "@ai-sdk/openai", "version": "1.0.20", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.0.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bb0bcd460e73a4bc864c30875c1731d48a08651b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.0.20.tgz", "fileCount": 16, "integrity": "sha512-824Eyqn83GxjUiErX9J0S8ffSVw1JKh8iYJNVSFxPvOVzA02KNEIakOhcQHWxb65aTOYxytD+6YR7m/ppHY6IQ==", "signatures": [{"sig": "MEUCICTTTAgwUMulSOdzBeGPrYVtVLaEElyMOoptuJcoDpWeAiEA6epT+5d+WRrPog4r2vNMZvR1mBMvxp8K0PAmw8ZKId8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 666826}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@ai-sdk/openai", "version": "1.1.0", "dependencies": {"@ai-sdk/provider": "1.0.4", "@ai-sdk/provider-utils": "2.1.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6f38d6a28784e0a85abc4db0ce1988cdc0128a93", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.0.tgz", "fileCount": 16, "integrity": "sha512-D2DaGMK89yYgO32n4Gr7gBJeJGGGS27gdfzYFMRDXlZmKh7VW1WXBp3FXxDwpmt0CgLoVI4qV8lf+gslah+kWw==", "signatures": [{"sig": "MEYCIQDtIr6nyMjbHbDfog5+yVhtxTNjzDgOQRn0+VAoR6XZLQIhAPTAw7ut64g763WgbyOvxZAGfhi9kRkZapNdeLxJADm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 666972}, "engines": {"node": ">=18"}}, "1.1.1": {"name": "@ai-sdk/openai", "version": "1.1.1", "dependencies": {"@ai-sdk/provider": "1.0.5", "@ai-sdk/provider-utils": "2.1.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a1fa0b8513a3abe05f6f0959f938b715406d2d41", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.1.tgz", "fileCount": 16, "integrity": "sha512-0tUlrjSMWYYQxiC/6d6n5C6nxUYSHzlt/FipJgzKQleMts3Br5+u2cM4nwOVtuS14J2MsBM/SK2DGL0lFctirA==", "signatures": [{"sig": "MEQCIHTTLUophTUfQ5KAigIIRnnIV3vygYZSE6FjuKkucQoiAiBKpdLtCkBWUEKgbADxktipRFXIO5dvzKlF4n4/QToIGg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 667202}, "engines": {"node": ">=18"}}, "1.1.2": {"name": "@ai-sdk/openai", "version": "1.1.2", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "59ea82f9c487b110c72492bcaf1eb5b847441129", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.2.tgz", "fileCount": 16, "integrity": "sha512-9rfcwjl4g1/Bdr2SmgFQr+aw81r62MvIKE7QDHMC4ulFd/Hej2oClROSMpDFZHXzs7RGeb32VkRyCHUWWgN3RQ==", "signatures": [{"sig": "MEUCIQCT4+rylLEWQo3yz8BN8AKf4mSjSVxIe7Apobinb4UCTwIgWr+UJwKaCtyx4osa1TmyRWccVcoRh/bwEY+q6nNqDvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 669114}, "engines": {"node": ">=18"}}, "1.1.3": {"name": "@ai-sdk/openai", "version": "1.1.3", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ddb1c7502464fd48655b88aff6da449b322548c2", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.3.tgz", "fileCount": 16, "integrity": "sha512-U+LqNAyB5/4KmOrU1U8NEQMLlLTzKhuVkpKo8xvlpz662TnOBxyGjGnf8Kbqw7VXyeHg0tpuHincolOuvnpynA==", "signatures": [{"sig": "MEYCIQDiqLFbebinLNip9o2yXVglBACtrhfNMTLOj+7BES9pfAIhAPI1r/OhH+Tu6ohgt6Nn/BKcb0TdEsn+2TVVykBXkcZw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 669210}, "engines": {"node": ">=18"}}, "1.1.4": {"name": "@ai-sdk/openai", "version": "1.1.4", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "e788c3c6fc93c8ac65e940686171cf347af93df4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.4.tgz", "fileCount": 16, "integrity": "sha512-C1a+p8lXzy684TdgSqQqubmp2YHm1P/mPXNzlcpJUb/T3xl1Uvw597V9wVeEvCynmimXI9WKRvLMQS/XnBljmg==", "signatures": [{"sig": "MEYCIQDpvYCCeTJsvwywtZRsxfAZOqORlLfg5aa9eCWzFDt74gIhAOvZ+5NaeMbn4cCLlwqA7dwhSelA4GftdSeNFSWE2Aeg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 669306}, "engines": {"node": ">=18"}}, "1.1.5": {"name": "@ai-sdk/openai", "version": "1.1.5", "dependencies": {"@ai-sdk/provider": "1.0.6", "@ai-sdk/provider-utils": "2.1.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6d03802e8368b4be528fd51db32aa2e663724b13", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.5.tgz", "fileCount": 16, "integrity": "sha512-pmZPeb99oWlV3PqMH5DVWtMGZgwWznInjdF0Bi4q1mKoEjomDXSYw+imvzMoSbpCrcGB9uFFTVx2VeR/jvcppg==", "signatures": [{"sig": "MEYCIQC1j3Wni04Kk/VDsOMXw71dT1rROXTAvicsy+bsrePIowIhAKERJK7Bxyhx7ye2NEQcYL7TYLI24kecUDQ4gO1SKtOp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 669402}, "engines": {"node": ">=18"}}, "1.1.6": {"name": "@ai-sdk/openai", "version": "1.1.6", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c07714f914154cc64c326a3a72c81399fbed83fa", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.6.tgz", "fileCount": 16, "integrity": "sha512-vcPyq9HJuOl61I7l6vsaTzzMmJSkN7EGOLZreNpvNChHXgZ+gSKZ9W0tlg+YUyB3iVEhIiV3Fewc/8ofaAXe/w==", "signatures": [{"sig": "MEUCIQDTwxQ6C5t7c+7NVbPdsUC7P28VloPyj2wamZOnO8WHOAIgKDRJ0AbkmGXJ1gu7xAuT7nZTaljirk8DLXQAWXZ5OWY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 670425}, "engines": {"node": ">=18"}}, "1.1.7": {"name": "@ai-sdk/openai", "version": "1.1.7", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "18e67837d15865d2080915f9a254d3f7e10a97cb", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.7.tgz", "fileCount": 16, "integrity": "sha512-Ld/VZdvBojLAWhw+ZGsxKB5vASfruFeKeXc+95khRlSdDMLGe7zxfWDEOXqwJJzHcbxewoIQyCE0Ymp8Vm0Fag==", "signatures": [{"sig": "MEUCIQDtf734PtBtq3fRc/d/WPALQbO0NvF7V6zwJjDDMaynLQIgVYg0LGXhSg9V8vCVYKP0dQ8kxIokLauWbCvoD/sprws=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 670642}, "engines": {"node": ">=18"}}, "1.1.8": {"name": "@ai-sdk/openai", "version": "1.1.8", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5e46da7a247c0c6d0f44a8e8aa0a89dcd723f178", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.8.tgz", "fileCount": 16, "integrity": "sha512-pV4HlG3pVL8xQnGWP5/oNwI5vir94CvXZgvaQlpAfBbFEaeyHDGF4E+gUoJQbECi8uN5SyF0CB/5lozY7oTPrA==", "signatures": [{"sig": "MEUCIQDfxzd6+PTg2LMpSA5UUGnuFl2kHFDqu9tQz6GlQR8U3wIgE4a3RIJyY73qZLq+mjiWCbluhXeD0avPUgYzJ1Z2+Hk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 670724}, "engines": {"node": ">=18"}}, "1.1.9": {"name": "@ai-sdk/openai", "version": "1.1.9", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "8ea8a7403b70fafcefe0dbfe9e4170148e5109ce", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.9.tgz", "fileCount": 16, "integrity": "sha512-t/CpC4TLipdbgBJTMX/otzzqzCMBSPQwUOkYPGbT/jyuC86F+YO9o+LS0Ty2pGUE1kyT+B3WmJ318B16ZCg4hw==", "signatures": [{"sig": "MEUCIG5hgw/LLj8/QCPBL9rOlMJBcpIuLyPg2GrMJWNS8vniAiEAywrk8kgTuJIcDPwk9QQky3kJ9++3wxbD6fhBqZXaYxQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 672942}, "engines": {"node": ">=18"}}, "1.1.10": {"name": "@ai-sdk/openai", "version": "1.1.10", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bf2bc6402610e9086909e97918d3178bcae82587", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.10.tgz", "fileCount": 16, "integrity": "sha512-GbKAUwNRksJBYsv4SxyKs8RP+fQfq+6hsLBTUwe3aejfH2yGAe/I0h+hoqfCDre4QCR7fVNgzGO9VPs47Pnikw==", "signatures": [{"sig": "MEQCIEMMn/O1iDgF+swadqGd9qlTzkSK/mmjfON5upCmbZQLAiAy+vdM/5tefJIjpx/SEU0rsGjC+hgi3uHdDW6gun9XoA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 673040}, "engines": {"node": ">=18"}}, "1.1.11": {"name": "@ai-sdk/openai", "version": "1.1.11", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1f4c1cdba1099cef01824eaab66e7f391c8db9c0", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.11.tgz", "fileCount": 16, "integrity": "sha512-gyqjoRvycmN3OGeK2SJXwhROv2ZZuP+SXbiAOoJf0ehWkqwkQSVaHigmg6OYLznmXusVHAvYD7SRgysXoJmuog==", "signatures": [{"sig": "MEUCIFCu3PUGnjxtIAiZqZYVhx7m3ZWlOVm2FyI6HJ/BxZvnAiEApgQZbnVqJQdgdpM4ykSgHzZ0nMtk4lb2DehBQ/0cY6U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 673137}, "engines": {"node": ">=18"}}, "1.1.12": {"name": "@ai-sdk/openai", "version": "1.1.12", "dependencies": {"@ai-sdk/provider": "1.0.7", "@ai-sdk/provider-utils": "2.1.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5f313e674849ab41df99a63fa18d64fd12e13505", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.12.tgz", "fileCount": 16, "integrity": "sha512-lh3zN4J/XEqkjpZAOBajSttF1Nl2qV/7WxRbORn+4jZmQkmzWQbsEUpgQ6ME+heyRvnsRCNq3fkMGehWWxWuXg==", "signatures": [{"sig": "MEUCIQC33J8+b8Qrq+SXrfO7UbxY58I03zgD5M+TNaDVqqU/lQIgHlxJ8LNglUR1xZV2dkbOzP2hXfnK4K9lnCu8y02B4Lc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 667463}, "engines": {"node": ">=18"}}, "1.1.13": {"name": "@ai-sdk/openai", "version": "1.1.13", "dependencies": {"@ai-sdk/provider": "1.0.8", "@ai-sdk/provider-utils": "2.1.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "36e342e23f3973011a0a39c8fddcc04d8edcd871", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.13.tgz", "fileCount": 16, "integrity": "sha512-IdChK1pJTW3NQis02PG/hHTG0gZSyQIMOLPt7f7ES56C0xH2yaKOU1Tp2aib7pZzWGwDlzTOW2h5TtAB8+V6CQ==", "signatures": [{"sig": "MEQCIDZU2bVNQhv8RLczYo2+SRwgu2XGbrckVVpUSe5TVh+0AiBlW5SnY4LVmEg5NuIJF7z7zKStKDH+A7mHQLhS8uwyqg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 667587}, "engines": {"node": ">=18"}}, "1.1.14": {"name": "@ai-sdk/openai", "version": "1.1.14", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1dda9a00d9c5874febf8ae91900f1a18dbc5dcf8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.14.tgz", "fileCount": 16, "integrity": "sha512-r5oD+Sz7z8kfxnXfqR53fYQ1xbT/BeUGhQ26FWzs5gO4j52pGUpzCt0SBm3SH1ZSjFY5O/zoKRnsbrPeBe1sNA==", "signatures": [{"sig": "MEUCIBh3d7rm3Q2dmm5McgiDOE0GYhNAluHgacJexDvM1zuTAiEA76WHEOpB4kJLzqqfFON8GXsnyOumUGOqmQLE5ehrMJo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 668825}, "engines": {"node": ">=18"}}, "1.1.15": {"name": "@ai-sdk/openai", "version": "1.1.15", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6c87eba3794e1bb5c84207ee5358eaf7dd7b5a2c", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.1.15.tgz", "fileCount": 16, "integrity": "sha512-irGQx5lMrYI9gub7Sy2ZHu49D3Icf7OtfOu3X8fVKMONOyi54RTKRNjVZTewkfRNE44psTbZDO8j8qmPirOTNQ==", "signatures": [{"sig": "MEQCIHks19zjJ5AC6UDSrGQSoM3dZwVp8zV5zQdq7/gY2eZcAiA4yEalPZ9oqkF+ZzXRjs3eS9rFE3toAHuJ1arigcm4VQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 669131}, "engines": {"node": ">=18"}}, "1.2.0": {"name": "@ai-sdk/openai", "version": "1.2.0", "dependencies": {"@ai-sdk/provider": "1.0.9", "@ai-sdk/provider-utils": "2.1.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a7199f66d1797ebf193900922520503dbfbe2c11", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.0.tgz", "fileCount": 16, "integrity": "sha512-tzxH6OxKL5ffts4zJPdziQSJGGpSrQcJmuSrE92jCt7pJ4PAU5Dx4tjNNFIU8lSfwarLnywejZEt3Fz0uQZZOQ==", "signatures": [{"sig": "MEUCIQCGHGcUiH3M9fU1KQTAWGCmxMbjlGvM1N8V8VuSHXZ3hQIgeGKx5K2jPB4ZurHJEiGdPw0m6JJKYoE+BlAVPhIeNNA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 686743}, "engines": {"node": ">=18"}}, "1.2.1": {"name": "@ai-sdk/openai", "version": "1.2.1", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9451cb9354f0ae5ddd7a98bb359ba84de07e5b9d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.1.tgz", "fileCount": 16, "integrity": "sha512-UtVDNrmrKzb3b4T9bnpduXs4FKpWGn+sH5qaFP1mtxOPeYGUF9OoJfGilH3CNfqGAE4QdZh/LT3IV6GHacooGg==", "signatures": [{"sig": "MEQCIGNX8OPTx8hjU6dVLd7QCNtVw8EgDpUiPsR0c26cqITlAiAhcDRTHY0VxNlvu3j+PQixK9oAJLE0TZnV/OzZcnzL/Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 688319}, "engines": {"node": ">=18"}}, "1.2.2": {"name": "@ai-sdk/openai", "version": "1.2.2", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "76eaaeed5ea1494f517e87e731fb4a663288ba92", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.2.tgz", "fileCount": 16, "integrity": "sha512-5355FLtSOH8sz9N9fsSwWpTaEgfqKOPMMHgSs1j4Aih5kQc9PhJ/oAPZuH308c/ktrbx6GcCW/hVrITimYsQhQ==", "signatures": [{"sig": "MEYCIQD0Tqkx5E6d0aKBk8jTTjIoWikyNJQ1CQ7X4rXPrPjiLQIhANx1l49KbLIw0aUwEZinUEhDwWY2d7KXO+EIFmQMmyAA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 976714}, "engines": {"node": ">=18"}}, "1.2.3": {"name": "@ai-sdk/openai", "version": "1.2.3", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "070f1fd4b437e75f4363f4f2e9538073029b2ec1", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.3.tgz", "fileCount": 16, "integrity": "sha512-iIJKMjKYZN3XWVECDassufz3X7rq/b5BQ6Uhnp03i06T8E4QwCamwtLitJXtrqQ+OxL33Ugn3EIZKGaSBo/+qw==", "signatures": [{"sig": "MEUCIQDvG+0mLqbg+aqrmIxCAB8m51fYH92adqnEHJjzJzekLwIgVbHL9mCrs4TB4tO3DWdZstmatyxNFuxBmtKwM7RM6EY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 976811}, "engines": {"node": ">=18"}}, "1.2.4": {"name": "@ai-sdk/openai", "version": "1.2.4", "dependencies": {"@ai-sdk/provider": "1.0.10", "@ai-sdk/provider-utils": "2.1.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "850ccfbf73e454893b631da5ab66b92f4d4f522f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.4.tgz", "fileCount": 16, "integrity": "sha512-AGwcg1Mksf+fuy1DiR2Rf+PbGDTwP9YbuX4q5JZqxeBEHPvi7RAcL0r0qGXNRXUjH7lwck+TrRCaiRn3HlluWw==", "signatures": [{"sig": "MEQCIG6guQknz3QKqRKk5sU8GolK5jUiWh22/bypEU/hVgEcAiA/YmuA5DyktZPmgNS1dvXl0RT5l8oIROh1i/2KMG6FSg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 985854}, "engines": {"node": ">=18"}}, "1.2.5": {"name": "@ai-sdk/openai", "version": "1.2.5", "dependencies": {"@ai-sdk/provider": "1.0.11", "@ai-sdk/provider-utils": "2.1.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "26c2def7c091193677b0d49f201238fa407627c6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.5.tgz", "fileCount": 16, "integrity": "sha512-COK7LzspgQQh5Yq070xfDdVMvp8WX592rXRaMaYNNqu1xpzahxDcM24aF9xgKYWuYH0UMoOw4UmWGwGxr6ygIg==", "signatures": [{"sig": "MEYCIQDZph7Sr5qaomycbmHF63cutbDavGWX9g1314OZeSc0tgIhALZI2Vj4sCswPuxeSoT349Go7buLWBTOVXpD6hZHN7i8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 993202}, "engines": {"node": ">=18"}}, "1.2.6": {"name": "@ai-sdk/openai", "version": "1.2.6", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bcbd88182d5a1de724b28898aa6e897d15d62821", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.6.tgz", "fileCount": 16, "integrity": "sha512-0HxWjE3NDABAxu6u33Q253a4N+xxOCD/M2HeZuALC1O2gjmzLeytxjSbDA6QU0PnVN0uaYLOc2YT/keFF5eGrg==", "signatures": [{"sig": "MEUCIQDaIJBdvRelnzWHwVzcu51rV2ZHB31hiMGkwM/8R9SfSgIgXjiw3tP/lfb2BWTJDN94P3W5HNqrK8b+7YVi/BnVvhQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 990599}, "engines": {"node": ">=18"}}, "1.2.7": {"name": "@ai-sdk/openai", "version": "1.2.7", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "^18", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a4ef79080db2a30dcd101e5a5519886b4f859cb4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.7.tgz", "fileCount": 16, "integrity": "sha512-0yYAr7wFrRAoP4BnZ5g4kL6BpsLzDN4m4cmw8C273FZn3Sqz7LsJ3h2YJv2EVB12GZDsgzmsRlMkLeKZI5JCPw==", "signatures": [{"sig": "MEQCIGlfrDLrAtGTVZVG8aw6H3n6fuQja0rwfC6eRa3WLrD7AiA76w5f2watkPQluJLS2HPeLBCKBqW8yy2RPyiiuA5G4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 990696}, "engines": {"node": ">=18"}}, "1.2.8": {"name": "@ai-sdk/openai", "version": "1.2.8", "dependencies": {"@ai-sdk/provider": "1.0.12", "@ai-sdk/provider-utils": "2.1.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6fa889132e5296189bc037a7d21733551947fff6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.2.8.tgz", "fileCount": 16, "integrity": "sha512-P/kOu79jLpulSB5qaGvq1XkYOynPoCnLen6pHyk/P4J8QTbLBfVy0utN6kj6nXEUJ0SVNwbOo0Bi7VpCEgFTUQ==", "signatures": [{"sig": "MEUCIEygoAdiWvfAJkoaHsnRVVBFL/X4H87zxEYewSENNeb6AiEAu71MvKo7GLn7GqpeC+iusZlCP5Gion2VlnkyVccxdCI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 994591}, "engines": {"node": ">=18"}}, "1.3.0": {"name": "@ai-sdk/openai", "version": "1.3.0", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c4de8d80251290b246f33bed9c13c779e5a07dda", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.0.tgz", "fileCount": 16, "integrity": "sha512-zKKacGH8AyUjC63GizDpts+Nf8qAEtvAtO5O/AfVML8pIrtNWsbF+U3nT6mM8Oqvkp9X7ivuc4hCurivMFlJ6Q==", "signatures": [{"sig": "MEUCICOKaVLHnjhCvE5jNSp1znPAw6mpzN2e2g1K42lc8Jr3AiEAosX+OGDlBelfUCr+TiG25wqZ6JjDvwyUQJd1nQev1uE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 994754}, "engines": {"node": ">=18"}}, "1.3.1": {"name": "@ai-sdk/openai", "version": "1.3.1", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "c49e03d9862092d587a42d34c43f72b852352be4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.1.tgz", "fileCount": 16, "integrity": "sha512-Fhb9IYEJdS9vIFwOxr9igiWF8x0xYSqAB7eYuHtZ9D6ovo4RB2JO7lSg0qQWGIGYxtwoipXwLjgF/ov25W23Hw==", "signatures": [{"sig": "MEUCIDoxgAcHpmIAZQG3vHqq7EDVJ1uNp+fThW9+Tx8spRNZAiEA1VOUr3VmOHfXZBREGZvGCEr4UFl+fqAFzyaCKOk/v58=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 994850}, "engines": {"node": ">=18"}}, "1.3.2": {"name": "@ai-sdk/openai", "version": "1.3.2", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d6dc1a844ce6739888d9afcd1cb5d5d0c0eac237", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.2.tgz", "fileCount": 16, "integrity": "sha512-TgtD2NbDKiOipaLb5/eY6fN64Gu32V/sZ0VM5UndsWAGKkB1if3jSLic6TcQjNvnBuhkzZY6L9deVDe4z+2PBg==", "signatures": [{"sig": "MEYCIQDCuB80qUkd2Ia0+yaIU4bkoiQPOxjMQPYpPtw+sjxUCwIhAOL4LCAELTz3pcnlpsr97dpcc3oLgbsn5dWKnTZEArib", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 995934}, "engines": {"node": ">=18"}}, "1.3.3": {"name": "@ai-sdk/openai", "version": "1.3.3", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5344c3bb59a8a78ff13c3759a31fa0b635f63cac", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.3.tgz", "fileCount": 16, "integrity": "sha512-CH57tonLB4DwkwqwnMmTCoIOR7cNW3bP5ciyloI7rBGJS/Bolemsoo+vn5YnwkyT9O1diWJyvYeTh7A4UfiYOw==", "signatures": [{"sig": "MEUCIQCc59AUhBbv5w5AAjaiyQdYkFYloOULLF99dcOJEMawGwIgP4pgM4ap+ZbJo8bFkZYId/Dp6I7ZeH/b00ocCYxC4CA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 997547}, "engines": {"node": ">=18"}}, "1.3.4": {"name": "@ai-sdk/openai", "version": "1.3.4", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6689e10aa1a981f18464ae7081ed9642e2916ad3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.4.tgz", "fileCount": 16, "integrity": "sha512-BOw7dQpiTlpaqi1u/NU4Or2+jA6buzl6GOUuYyu/uFI7dxJs1zPkY8IjAp4DQhi+kQGH6GGbEPw0LkIbeK4BVA==", "signatures": [{"sig": "MEYCIQDn+XW8laI8CPFcN/Vf8GFT85+dow3fBeCELjsmxYbJHwIhAP0IT9lnppi3rXfs0NbpVpdZmanj51gjjy3nQTU4rNpz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 997816}, "engines": {"node": ">=18"}}, "1.3.5": {"name": "@ai-sdk/openai", "version": "1.3.5", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "b153fe02d733301164647d9d81cb41062cc0b7d4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.5.tgz", "fileCount": 16, "integrity": "sha512-kNXftkURozvxm54Jb+5/mIBR9TYVVwZODRL4lJCCTyj/9bp0w1aArz6cGbI4hHQx1vkXG9U10eKvWIV/lsHzUA==", "signatures": [{"sig": "MEYCIQCx4PkH2A3FKtv3x3Y1G2bSiXeRMKDn6tvjzuJO1u7GBAIhANlxHM8fHoBMsTuKKRhoypZntu2K2iZhZK/4mhFBm+az", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1000580}, "engines": {"node": ">=18"}}, "1.3.6": {"name": "@ai-sdk/openai", "version": "1.3.6", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "f6b92b060df000385245331ca347efe94d208337", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.6.tgz", "fileCount": 16, "integrity": "sha512-Lyp6W6dg+ERMJru3DI8/pWAjXLB0GbMMlXh4jxA3mVny8CJHlCAjlEJRuAdLg1/CFz4J1UDN2/4qBnIWtLFIqw==", "signatures": [{"sig": "MEUCIHO1vKCz/a6y7sg7eSmP4Dt4tDBFW9ff+SbyAWmL8gUVAiEA8WOndrWXaWt3JnXfJPyrP8aMrtVdjkxPY98u+4dLNoY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1000676}, "engines": {"node": ">=18"}}, "2.0.0-canary.0": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.0", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.0"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "96b51a01b1c776cd5f33586c7a3851c3c6956690", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.0.tgz", "fileCount": 16, "integrity": "sha512-/hq/xFgIXqaBqY5Jr8vHdVrZDTPNdWYQgWNiqdvnKC/x18ORxBYkCwdgJJxhyMrLGT1DhR/O1LG3dQ6MGiv/HA==", "signatures": [{"sig": "MEQCIBi7OIjNnLsW5G7qpQwUMUWniIu+Wy+/UXx5ITTOdmVRAiAeIzTR8dDY2zcOtqv/byeZrquD6FtizrYw478/buP4kw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1000893}, "engines": {"node": ">=18"}}, "1.3.7": {"name": "@ai-sdk/openai", "version": "1.3.7", "dependencies": {"@ai-sdk/provider": "1.1.0", "@ai-sdk/provider-utils": "2.2.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a848b65af86d49158c83a56b64d409cce62deb1d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.7.tgz", "fileCount": 16, "integrity": "sha512-JjjEulfMgH5kGyCWKdyhxNLSIs2qBkUr6LtzNJtYlV23e5RbOHA5qgbbxn6IbGxeZs4xpPhpGmOnUVzh7GZwcg==", "signatures": [{"sig": "MEUCIQCJXUQShZiiXcyD/a9cGMRnUp0Gt/LbuevvfsF8z+adrwIgHeUneVhsNllic8tTFovJRb1iWaDRxwTmmAcgdNU8Ugs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1000772}, "engines": {"node": ">=18"}}, "2.0.0-canary.1": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.1", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.0", "@ai-sdk/provider-utils": "3.0.0-canary.1"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6401f43bdc101374f1ea0500b32e8fb333924d17", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.1.tgz", "fileCount": 16, "integrity": "sha512-awej+IIJGlR/sYWt9Us3WbeBAtQvXElHnFqXGs8+FsLOEHFoYwc1D47jIzaxL1PO03SdnWR4DwpXVYdj0J1PfQ==", "signatures": [{"sig": "MEUCICjtRkNwzA39FHSeAuPhayUqEkLslair91IAm1HDQqXJAiEAib5uphK7CDXa6WlA54sAmPwKe3WUeltZOCV1YrS74t0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1001071}, "engines": {"node": ">=18"}}, "2.0.0-canary.2": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.2", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.1", "@ai-sdk/provider-utils": "3.0.0-canary.2"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "6506da2ce8e204ffd7ab7b24e8fba8a5a842dbc6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.2.tgz", "fileCount": 16, "integrity": "sha512-+8pNcuEOleLzpJKFfHAIMPCqvFnCbWjvIDLk9W5W1Rm3aiEcDMMtuFC7WeljnnjCaW+gRtzNr9b+2IWau4W3Mw==", "signatures": [{"sig": "MEQCID5nd79IyZEMe9yqJXAkMFc0kIr3frjQiNZ6BOCh+YEpAiAA07QRVHHIu+sekrXthDBpErRzrvigzxpGf/j6+BLz7g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 959714}, "engines": {"node": ">=18"}}, "1.3.8": {"name": "@ai-sdk/openai", "version": "1.3.8", "dependencies": {"@ai-sdk/provider": "1.1.1", "@ai-sdk/provider-utils": "2.2.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d01c0a50aeaefe1fceeb7232826ba5870e48ac53", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.8.tgz", "fileCount": 16, "integrity": "sha512-IwPoQeW/54xcxzJzyceb5Gw4MgCsXEKc0uwQNiSBw2yLwZdipzQcFiHetboOgIMnyD3HYTYGq4fc7o4pS+nJuw==", "signatures": [{"sig": "MEUCIQDWFJt+ZY5VmvujHO7Hxd7tRQhB83xU1uNDimQPRgH6mwIgPZVUxfvwzvS23YHEX+THWUhpYiSqpOs76D0JnWl43nQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1071728}, "engines": {"node": ">=18"}}, "1.3.9": {"name": "@ai-sdk/openai", "version": "1.3.9", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "715d1bb655fb92f7d9e2cab4fd3ea3a9a558db5b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.9.tgz", "fileCount": 16, "integrity": "sha512-XSkGWewA7UzyJu6Pfkzp4GsIqnyiE3tONlRh9WYBAZkD67bh3LY7wY+CGSuJzMkv/o4dBPT0gpVc9f7nn4D3rw==", "signatures": [{"sig": "MEQCIG6/QqVnf2ukST0OvB1GQrw6fGPqRvr9AgbFruwf9DtfAiA1tgQh/jQdxRRUuLixpSuWcUg/WbbKKZ2+yowoXEw2cA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1071068}, "engines": {"node": ">=18"}}, "2.0.0-canary.3": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.3", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.2", "@ai-sdk/provider-utils": "3.0.0-canary.3"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "bee32c6b59406e606a270abe30daa15ab7515987", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.3.tgz", "fileCount": 16, "integrity": "sha512-jXOqdjtXE8MEarWSyQQ+Q5JlE8FUtba3PD0CkvygBW2cnp6yXL44ciYxI/uDW+VwB30VoamxeXSrrybtnLxNiw==", "signatures": [{"sig": "MEQCIEl6n1DIbjJEDcAucabDXCYIhBJ0FnrRLXDzPDOVoaAcAiBinGa8WpzYmdu8yj4u/c4WNApfNhuiEviQaGpi/2t31g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1032245}, "engines": {"node": ">=18"}}, "2.0.0-canary.4": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.4", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.3", "@ai-sdk/provider-utils": "3.0.0-canary.4"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "1bc3833485caa30a8edd7d7bab73a11ce0e34adf", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.4.tgz", "fileCount": 16, "integrity": "sha512-z7rPO4ow5ebuziIXDga4+KrQd4K9oRqc0AprZUXPUXsCPw8NgMBYsvz4NLUXu7RmfS9zEwLhZ37ZbzuGkqLjvQ==", "signatures": [{"sig": "MEYCIQDQrF4H44FlZEluZChBzdKCFKjE0AtzJ/qnYgJVpW41MgIhAO9W8mvcMLv8Bxo7fnuRDIPmfEHbqwgLOuph3TVJwmdm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1032291}, "engines": {"node": ">=18"}}, "2.0.0-canary.5": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.5", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.4", "@ai-sdk/provider-utils": "3.0.0-canary.5"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ef10a812f906c58961cd0e1e3866b449065f7ab6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.5.tgz", "fileCount": 16, "integrity": "sha512-vVf3/yq5cL7G0ZZahm6vlcxGg4WbJdA4d8x5avmQwpvJbgEsfI1dGWUz9QvxLuleuFGVaJcWVpIA3SbYuJfjUg==", "signatures": [{"sig": "MEYCIQCvFOOxopTZAdVamxvhY6WijdvpWz7ES+Wj3t3Q2xoGtgIhALSziK17E1PnqqQj2uYgrvaZ1GlLdy8smXHPjTJ2cFSg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1025823}, "engines": {"node": ">=18"}}, "1.3.10": {"name": "@ai-sdk/openai", "version": "1.3.10", "dependencies": {"@ai-sdk/provider": "1.1.2", "@ai-sdk/provider-utils": "2.2.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "892286c4336dd3aa26a61f706438ac60f47caf7e", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.10.tgz", "fileCount": 16, "integrity": "sha512-XO0wF2lmAMWCYjkM5bLpWTKoXet61fBiIimTi+blqEGiLUjAvivt/1zZL1Lzhrv9+p19IC1rn9EWZI1dCelV8w==", "signatures": [{"sig": "MEUCIQC6EImnvo344/ZxCAZAaVnaYcAMexleLBj3AcSgd4g/GgIgD5PxUEFqgtYFQFlJwBBsRbGykSXR0pw6UOVrAbNKytE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1072807}, "engines": {"node": ">=18"}}, "2.0.0-canary.6": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.6", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.5", "@ai-sdk/provider-utils": "3.0.0-canary.6"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "691e84245ecc9442821e2a1a0d9562a23c759bcc", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.6.tgz", "fileCount": 16, "integrity": "sha512-wXlsQeyXwoQd/uCUg3H1bcx+LKglLikCiDtIJgD5Gc8dS7h2/Gs6zmi4a3keLBUwtMAOyZI9Ph99VYFbE1y3xA==", "signatures": [{"sig": "MEYCIQCSPe87zUZ+Rex9MaLYuQJCjLVqOXHRO4lqpvR/KeNIWgIhANx3nTFHZbqW6CJzSgIKdEkz2y5ko4AcrDm5Khx9nY/1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 992266}, "engines": {"node": ">=18"}}, "1.3.11": {"name": "@ai-sdk/openai", "version": "1.3.11", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5bf583033ef9d58b6e5a3adc28156acc3416ee08", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.11.tgz", "fileCount": 16, "integrity": "sha512-MZDh17JHKbSZG5MkbequDbjj3qcwBYnFQWY4DebAixOlpFw7aTDm5H38vKc6QPn7uQgS2MkggArVK6C0y6qGoA==", "signatures": [{"sig": "MEUCIEVwV7RL9lUuEuXBeWGjFocVoIxra3Rz3XuGIZwvqKVUAiEAojv8VNjLdzv7o80oJ1kGlTYeGRrGZ8FfKmOctwFC010=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1112353}, "engines": {"node": ">=18"}}, "1.3.12": {"name": "@ai-sdk/openai", "version": "1.3.12", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d51df2e3adef7e5ff50846102e4113f296975adb", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.12.tgz", "fileCount": 16, "integrity": "sha512-ueAP69p8a/ZR2ns+pmlr9h/nyV2/DAwzfnPUGZiLpXbxWnLXd2g3a7l38CuEhBydH/nOfDb/byMgpS8+bnJHTg==", "signatures": [{"sig": "MEQCIHRt0RUVXlZv/kOu9kZQRUOXHHngVGB9Ajf/k68MF9gnAiB7ptuxY5IRnqS6kVP2OqFLEalQ+/KrIfRbnUOmIuskwQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1112939}, "engines": {"node": ">=18"}}, "2.0.0-canary.7": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.7", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.6", "@ai-sdk/provider-utils": "3.0.0-canary.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5a58c794c587f7e2542ec72cd846c26f03ba996f", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.7.tgz", "fileCount": 16, "integrity": "sha512-IKonvkvBO10SZu4vRpo5yKiLDLnGwCbY+mW4CTxGQCDnku6mFA1AsrX5yPGYVOBzplMUWZCkfjSLCyxLo1SGRQ==", "signatures": [{"sig": "MEYCIQCKeo77apzNHI4EbhnT8ixYWXmJoD89d7UMXJ31jQwUrQIhAMhwWzBBzzPBGWmMEOOVlkgn1vlUzYZm37oCEkLmNlkI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 994969}, "engines": {"node": ">=18"}}, "1.3.13": {"name": "@ai-sdk/openai", "version": "1.3.13", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5aaf1990ed359a04c2b2f47ce1ef0264ab2b3197", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.13.tgz", "fileCount": 16, "integrity": "sha512-t7l11mXI+VcmeAALX2iisURFOBxm2tCqBi6biciDaQZSdF1Az2Z4oOPowuRVmH+VZugXT0GRr4YSyo2mT/HAZA==", "signatures": [{"sig": "MEUCIQCNiz3xluRiB4VzVVrVAF8Pw9wstkVF+df7QqZnXmSDcgIgBebT0alpATjJKYm5QZDB7EPQyVexNFE5OllBtvfnBy0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1113954}, "engines": {"node": ">=18"}}, "1.3.14": {"name": "@ai-sdk/openai", "version": "1.3.14", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "adcce71423300a1a44cd147b7d84a2492e859ca8", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.14.tgz", "fileCount": 16, "integrity": "sha512-mY5tU3QOktt6Qib3GwWN43pRxW01sP1IM4M6eieUAEDRXIiFcRb5fdmL2AZ8RK8AjAgzZRPRDxk/NN0gybwjRA==", "signatures": [{"sig": "MEUCIQCob34pEzC+sAeeABfQlGl/kGlWDa+VvZwxSviOSftZHgIgcurUX09SaM7buf3Ps98gpFzqScMNainPslbji68RMi4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1114569}, "engines": {"node": ">=18"}}, "1.3.15": {"name": "@ai-sdk/openai", "version": "1.3.15", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73de8edf625c2f0a86b21b72007daec2323f6f85", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.15.tgz", "fileCount": 16, "integrity": "sha512-sjUM1A+Pwui+fAn4kmBfVyhJGhqIbsqNzERNPmtRKRBhYG7p946+DTmbJ4lsZG+r4+kMn87aDCYyRexnsnbu5g==", "signatures": [{"sig": "MEYCIQCyWKkERbLOQ2HirRR0XVyh0VAzYOTzMlJGwjwGxGzXnAIhALbuMekJihNqBg8eqyqR/p/wWvyutI+O0PITYj4UwFG6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1115238}, "engines": {"node": ">=18"}}, "2.0.0-canary.8": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.8", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d062da0f2d60ff2864e829df65cc06534f15f7c4", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.8.tgz", "fileCount": 16, "integrity": "sha512-eqPlHDPa/cUXp+bOsYFbZGQWc0kGxjGAVESzsQk57TMmE9iVvQI28stqfiT6NgL0BHeDN+64PPy1h1qxCkxVvw==", "signatures": [{"sig": "MEYCIQCMrxEoIJebc8/u3RG2VFdoVpDd3rjS3RtLtpoPNwQNlAIhAI/18NI38sH0ccBQni6LGntwQIdfytFVOJszVEQAYSRa", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1037968}, "engines": {"node": ">=18"}}, "1.3.16": {"name": "@ai-sdk/openai", "version": "1.3.16", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "73d95c6cdf8b5383b3121eb12fc9b01e25649a1d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.16.tgz", "fileCount": 16, "integrity": "sha512-pjtiBKt1GgaSKZryTbM3tqgoegJwgAUlp1+X5uN6T+VPnI4FLSymV65tyloWzDlyqZmi9HXnnSRPu76VoL5D5g==", "signatures": [{"sig": "MEQCIFTy0botwZt67JMSFDF5kJan3gky0HN+3iXHFsMC0PLKAiBUQgsU3iyWMf+x/5D9uSq5owq9R6CyrMEtzT89NJ2SuA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1115829}, "engines": {"node": ">=18"}}, "2.0.0-canary.9": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.9", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.7", "@ai-sdk/provider-utils": "3.0.0-canary.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "818cce4261c6c44697e6c270c83b78b8169e010d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.9.tgz", "fileCount": 16, "integrity": "sha512-bqdupmAcI+mQEgydgvBCCvCdCo6UhJ+9pPbqVAT4H3aml9W3pZjcjhoBghxBk1PtMERpB98m2YLwJ3LeOidAHA==", "signatures": [{"sig": "MEQCIDyzGAef9j4R9JGwnIEb325hVIxFjJn1fd4qZvVhMjMiAiB/7SLkrcTi8cWVEB2pbSmKc/LONnbwr1WOQlPt7mBXXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1042443}, "engines": {"node": ">=18"}}, "2.0.0-canary.10": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.10", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.8", "@ai-sdk/provider-utils": "3.0.0-canary.9"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "787167ed8faab6e97c0ce9c87e34a2e95a4d564d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.10.tgz", "fileCount": 16, "integrity": "sha512-aoeGlgwEC+095dE0eBAZ9jrj44zF7gC+R8bXHKODvN5qFX3hzj3ufHU1CHB6t436heBQDPwF4BdxK8hQL52PHA==", "signatures": [{"sig": "MEUCIQCdg0KcUhmzB4DHeUgGLv4farx+2rYt69qJzkII/V49UQIgW2Ya/jm5mn5k5cs2OuiTjzTZAZvn0Dl60X4Qpn4GywE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1047156}, "engines": {"node": ">=18"}}, "1.3.17": {"name": "@ai-sdk/openai", "version": "1.3.17", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "df31160383995a4b30983d87c6a66634ffc88226", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.17.tgz", "fileCount": 16, "integrity": "sha512-ge94OcFLfmc1eTBA1b7ubHMRLJ98HZwnD0B3gcE25Y/lZ7ctLbihdn3wgwu9R6ZVD2/jgc73vrjyOL2+lmsoYw==", "signatures": [{"sig": "MEYCIQCJOSSTGqRINHfcDLZ0CU+eFktoEKK8RMXCXYJw4lYuwAIhAKBer7HJQOFEYCdCUvO+rytWGj6NN9F6mKGgvcjuiT6r", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1132244}, "engines": {"node": ">=18"}}, "2.0.0-canary.11": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.11", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.9", "@ai-sdk/provider-utils": "3.0.0-canary.10"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "69470e29d0d3507fdb8f24c120c05447c80685f5", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.11.tgz", "fileCount": 16, "integrity": "sha512-OP0sImKcuaXbpv9Uu6Fk6xb9TNGgiYrUt3LNxR95JwqMqt90euZBBTAL7do10E3xXu1LvOi4BM4rjQW3zM+MPA==", "signatures": [{"sig": "MEUCIQCA2a4GSB+/9WWgP4yNVaUtyzmVk2wwOptH/ZN3CmEvagIgDpwk8A7MGaUDxqP0i+UJMDotlrPcO5nEDE6nXoR/zR8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1010649}, "engines": {"node": ">=18"}}, "1.3.18": {"name": "@ai-sdk/openai", "version": "1.3.18", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "3d55db0ecae7ea7f79d849da5e9763b786b260fe", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.18.tgz", "fileCount": 16, "integrity": "sha512-gqOHTOu62Tm2r4yDQx/Z5tWAgUrcTK8wXnC4A8zF/VOCzIjJDxxPsqJRTtQTMgIdGXhwmsv2sZ2PzvvuLeZeEg==", "signatures": [{"sig": "MEQCIGd4e/WP5N6wDnoxBf3sa0jfcmwW8ReA9bM9f4WwWNbKAiBabkglOpo5sODbWPKeGjdFFu7WySp2kcN0kdu969V1eA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1134515}, "engines": {"node": ">=18"}}, "2.0.0-canary.12": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.12", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.10", "@ai-sdk/provider-utils": "3.0.0-canary.11"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "9d1824f1375ab307a06e6ccc66b7d5ea03dce8bd", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.12.tgz", "fileCount": 17, "integrity": "sha512-5EO4LzW0QTziHaw05dehtYm/4/ZnCGDfrpu6s3d7wFBMxePpM4xK01T7SHpM2fQVu+PLOHo+vxFSQISoBaIY5g==", "signatures": [{"sig": "MEUCIBKtMr5zlfGEghykqNQPjM33/s1CbNZuwEXJkA2Ktv6UAiEAsYDCIn1hKmz37sFt9hUNVPpv2DDqM31OyEjoK+3s7dM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1029057}, "engines": {"node": ">=18"}}, "1.3.19": {"name": "@ai-sdk/openai", "version": "1.3.19", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "a065c17a1def24a099575d13a01538c2aa9e797a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.19.tgz", "fileCount": 16, "integrity": "sha512-VB0<PERSON>utZwb6LJBIAV5pJZsSuL3O+GeTAeG4OifRpf014nJF/QlbZIWDvEWfcTq65d85fO/vg1NTv8jVKYRi+JFQ==", "signatures": [{"sig": "MEYCIQCbyMJzJ2OQb+3lH77PECEW4/nXYLC9yQ/2X6Ma5TolrgIhALKU97gohUKdMSkcfIZ6mPzrkeWHV6M9m7lWb/yJZg6R", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1134809}, "engines": {"node": ">=18"}}, "2.0.0-canary.13": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.13", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.11", "@ai-sdk/provider-utils": "3.0.0-canary.12"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "62949dfa0499857e528700aca5ad734590a5ee08", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.13.tgz", "fileCount": 17, "integrity": "sha512-PQjkIWg19CunZFTlvv9ukZ1dkF8bypuT+QLqEAPwwaLst+PR1I3Ueu+WK22T0urpzMZNMsmrIfHwHJSfp09HYg==", "signatures": [{"sig": "MEUCIGMSsbjfcxQx2bZX/kLRndrhwOLHXSszrQQWSuEyNxrzAiEAuJBgLGgGtP7zrqmkzFGpRZzrQsFKq3sICCyZndM9hXY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1022557}, "engines": {"node": ">=18"}}, "1.3.20": {"name": "@ai-sdk/openai", "version": "1.3.20", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "5ef85e61663c1a387d98d36ff6d24b5162c5aa61", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.20.tgz", "fileCount": 16, "integrity": "sha512-/DflUy7ROG9k6n6YTXMBFPbujBKnbGY58f3CwvicLvDar9nDAloVnUWd3LUoOxpSVnX8vtQ7ngxF52SLWO6RwQ==", "signatures": [{"sig": "MEUCIDXl+JFNUCgIVJb7UOv9CltpGEwe+bF1VATH8q4TOqfOAiEAmJWmX9fZBW1AVcM8ZvyFZRchJmOnMGyiliCJuFawPyw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1137536}, "engines": {"node": ">=18"}}, "2.0.0-canary.14": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.14", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.12", "@ai-sdk/provider-utils": "3.0.0-canary.13"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "00045024ca520aeb406f2dbefab4ba226074d2c3", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.14.tgz", "fileCount": 17, "integrity": "sha512-F2oc8dg8SZiZVi0FaOPmaNJVh/gy6jPhXFgQGwwL+CYdMTpcaXqXQ2Q0r6bTMOh4M5xZ95Fq1vDo7FVa+LItOw==", "signatures": [{"sig": "MEUCIGf5D2sC8Xm/Uk0+9auvHf1ogecQW2n6bKzd6mn8jGaOAiEAjTsmfpT40B3sSHBYjg2iAs44Dd6hyi7hQHJVsXEM1jw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1024451}, "engines": {"node": ">=18"}}, "1.3.21": {"name": "@ai-sdk/openai", "version": "1.3.21", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.7"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "d2ae02e2bc2e03a350c16c218c680d718ad30b40", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.21.tgz", "fileCount": 16, "integrity": "sha512-ipAhkRKUd2YaMmn7DAklX3N7Ywx/rCsJHVyb0V/lKRqPcc612qAFVbjg+Uve8QYJlbPxgfsM4s9JmCFp6PSdYw==", "signatures": [{"sig": "MEQCIGMO+kKwrCqp9/pNFqyM47eI+cvaCIudZGd4j99+ePF3AiBLUv2B8LMOxnZTz6OCVLFZcOMyxygtBEQvjV0wkHSCGA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1137487}, "engines": {"node": ">=18"}}, "2.0.0-canary.15": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.15", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.13", "@ai-sdk/provider-utils": "3.0.0-canary.14"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "82e247ae658f32d96cf1031ff4f11a1dbd8f961a", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.15.tgz", "fileCount": 17, "integrity": "sha512-azWktVacH3mE6p4uzdhPrHuq1C1prkX6yqEm7bEy3eg+0OtzB3IAwBd19LtmA3k87LAtxNNPtrmyfCfcwN590A==", "signatures": [{"sig": "MEUCIQD5aib304DWnlAtygAz0UDli0g9u73IeLFmFW9zbjFZYQIgbsitMwQy7BakMUdp2fcDgVlfesNioVifMfXx5Xrmqmg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1064191}, "engines": {"node": ">=18"}}, "2.0.0-canary.16": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.16", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.15"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "af1bab058dcedf057946977e93346e7a1f13d522", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.16.tgz", "fileCount": 17, "integrity": "sha512-BauVUf/O+Ti1U1Rm/QVpkOSf7uEXhT1HNRjK7onkArYqrprJpnTJygS65ZXHFy8bivNdpjqfjW73tonM3YoYKg==", "signatures": [{"sig": "MEUCICkfcQInb06lfgeabzgz4ZeeiRAdBZTVEA4g1eeL1VStAiEA/5ziJqZPdR0ZuiBwIRm0Tuur6LRn6YZq7ip+50shzGM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1069895}, "engines": {"node": ">=18"}}, "1.3.22": {"name": "@ai-sdk/openai", "version": "1.3.22", "dependencies": {"@ai-sdk/provider": "1.1.3", "@ai-sdk/provider-utils": "2.2.8"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.6.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "ed52af8f8fb3909d108e945d12789397cb188b9b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-1.3.22.tgz", "fileCount": 16, "integrity": "sha512-QwA+2EkG0QyjVR+7h6FE7iOu2ivNqAVMm9UJZkVxxTk5OIq5fFJDTEI/zICEMuHImTTXR2JjsL6EirJ28Jc4cw==", "signatures": [{"sig": "MEQCIBNPkT8em1PM/YBzQBw8oQtHHkyhVpUhp8PilYXsTA9IAiBmhYohpP0czvH+FUqdc+8mbHVK+9hrFqCURJ2n52Ka8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1137572}, "engines": {"node": ">=18"}}, "2.0.0-canary.17": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.17", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.16"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "31b5c8a23f0c61e887d7899e3e5e7159f0825644", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.17.tgz", "fileCount": 17, "integrity": "sha512-LQO/PVma1mNQGKeJQEkulM8DqgHvjNtEQxltGWtxRt9MXhxQonL+hjlbE843ujISfjqNI6gfrk31h3JK8sYPyA==", "signatures": [{"sig": "MEYCIQC1IrnmlwvNscn9QGVbglrogmK6TmEoDJNK5xlaFqggPQIhAPgqBCp0+6KN/ibh1BZotMy9YxkmuazIPIVqBS1ZzvSO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1064943}, "engines": {"node": ">=18"}}, "2.0.0-canary.18": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.18", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.17"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "94405b349775b1d5e372421d36f3ee82e4aeefe6", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.18.tgz", "fileCount": 17, "integrity": "sha512-qyG8tH2htd4s+VekFXePJpYjeHeYgrnexLZpff231byOnJy5VqFI1hkRaDlgS/kGA1xYbDJJZd7AeQujo0xweQ==", "signatures": [{"sig": "MEYCIQC5PatBRTODduigPHH+lkkpXNMdal6f+tfNIHOZ8699jwIhAOeBdICamd2ht6Cql6zbJ/T0Ae6a8OiM/A79qaC3A0wE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1062543}, "engines": {"node": ">=18"}}, "2.0.0-canary.19": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.19", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.18"}, "devDependencies": {"zod": "3.23.8", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.0.0"}, "dist": {"shasum": "198cd50e7d2c8942b292ad4437889c4971e72f88", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.19.tgz", "fileCount": 17, "integrity": "sha512-IQwyuWq+m8G4h9bztdwJILXpvzQ7QlvogOcL8htKDcoAydpyQhqWgKvatR0WYA4vG9EBAX1P4qodyN7RuxlsYQ==", "signatures": [{"sig": "MEUCIH7j6uWPPHWDFH95avj7C5C+smqOEogBfSK3f8m3MlAyAiEAtVlemGRVWmUX9Tmiu73fjnTAWXtntk7Pdy6DOvrgkRk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1062659}, "engines": {"node": ">=18"}}, "2.0.0-canary.20": {"name": "@ai-sdk/openai", "version": "2.0.0-canary.20", "dependencies": {"@ai-sdk/provider": "2.0.0-canary.14", "@ai-sdk/provider-utils": "3.0.0-canary.19"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "e8285f5df990830a98f04520ebedfa7ce6f6462d", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-canary.20.tgz", "fileCount": 17, "integrity": "sha512-AipaQeOz/nIPTtZLJaqG9sxf8zWqZ1UGLG1QOLhNpWwSBDXPVw5k0cWhLtReuZrL/ncKvL6BrGN9aEZLqcmWAg==", "signatures": [{"sig": "MEUCIGNfSCLOgGWHaWduqMtKnm7BNrBU7hvjpdhLImBuB6axAiEAo0BKNzo5P7bBxJ8kE4EN8kJMBeL1UjL3BPh+kLwr094=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1062776}, "engines": {"node": ">=18"}}, "2.0.0-alpha.1": {"name": "@ai-sdk/openai", "version": "2.0.0-alpha.1", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.1", "@ai-sdk/provider-utils": "3.0.0-alpha.1"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "c4cdc34f7dae4a2d177298a7f15eedc17a218dca", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-alpha.1.tgz", "fileCount": 17, "integrity": "sha512-MY1BJ9l4Ih5a+6kNXVutwIQ5mrGHiAyVEjyDAG4awZheq2nCpNdEImkG7SDDBBzThM0izUPvLJAhI3fZLCiHmQ==", "signatures": [{"sig": "MEQCIC6vJYd9FjPur9wgFeAPGyVPLIccQddFALWVZzWAUvvOAiAnCnEdOZllVGRGI7PpLW4cVt3+8Rfa2e/BdaUr/U1+7A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1062917}, "engines": {"node": ">=18"}}, "2.0.0-alpha.2": {"name": "@ai-sdk/openai", "version": "2.0.0-alpha.2", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.2", "@ai-sdk/provider-utils": "3.0.0-alpha.2"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "5daa71463f3d2b2f3d7408c9f5c69ae457051920", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-alpha.2.tgz", "fileCount": 17, "integrity": "sha512-4c9931AjL05qNgl9vmbjB+rZeUcgvZ10JOm86T5APVKAsIrS1cinAjxWKKQSy0qQVIkGXrcSVpnmE/32L/tBYA==", "signatures": [{"sig": "MEUCIF9da1Guc1hk4HhCL7ke44swZoaDLXjBah/a8BAVOXkGAiEAqe88UCdhDSZ4CyP9j81Oyn9A5+d6snM6UKLCtaljcsY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1063064}, "engines": {"node": ">=18"}}, "2.0.0-alpha.3": {"name": "@ai-sdk/openai", "version": "2.0.0-alpha.3", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.3", "@ai-sdk/provider-utils": "3.0.0-alpha.3"}, "devDependencies": {"zod": "3.24.4", "tsup": "^8", "typescript": "5.8.3", "@types/node": "20.17.24", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"shasum": "3446665e5428a530807b601cd0dedb6f902d516b", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-alpha.3.tgz", "fileCount": 17, "integrity": "sha512-MMgwyysYhuhbi2lnjzImzlJFjbhYzouErgMlx468NegAud0FVvo+VRlMYbpY2gR6oLjyk1nqJasuIPzp5oqmnw==", "signatures": [{"sig": "MEUCIAize5qmpUlO/ZDDbwUthLBqQNtEH6Q+KsPXQMHLyQP9AiEApTUUkrNoDuQyNxj/RhmgihMn+YZONEJN2QSD8REY9PM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1063211}, "engines": {"node": ">=18"}}, "2.0.0-alpha.4": {"name": "@ai-sdk/openai", "version": "2.0.0-alpha.4", "dependencies": {"@ai-sdk/provider": "2.0.0-alpha.4", "@ai-sdk/provider-utils": "3.0.0-alpha.4"}, "devDependencies": {"@types/node": "20.17.24", "tsup": "^8", "typescript": "5.8.3", "zod": "3.24.4", "@vercel/ai-tsconfig": "0.0.0"}, "peerDependencies": {"zod": "^3.24.0"}, "dist": {"integrity": "sha512-CCvgUxLrz+1I/+i5PchGAn4D5/XbQwQnDzzxFMnIKIvYhvwMIrxS2bbKXM0eWoZxNzpEOmlm4kKRaSfkNW5QAQ==", "shasum": "7ddf8be52f88fc3cf461a61a12d8a391b120c817", "tarball": "https://registry.npmjs.org/@ai-sdk/openai/-/openai-2.0.0-alpha.4.tgz", "fileCount": 17, "unpackedSize": 1063358, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCqjKMW/bID/mMVNezh3fekLS7zjYAwWrMnxhG72ua4XwIhALomwv7drUbL1EjyLzOnnRCC+nGFOcPRgo45seianoUn"}]}, "engines": {"node": ">=18"}}}, "modified": "2025-05-23T07:30:01.707Z", "cachedAt": 1748373701731}