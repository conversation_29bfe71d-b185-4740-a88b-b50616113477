{"name": "@floating-ui/react", "dist-tags": {"latest": "0.27.9"}, "versions": {"0.14.0": {"name": "@floating-ui/react", "version": "0.14.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.0.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "8e550c507a0f9b56df3d00c040dbedfc5b589dba", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.14.0.tgz", "fileCount": 45, "integrity": "sha512-IGzsoBR6gVRSF+EWjkUNQr/wThi/ypSJN8y/QZCt2MzddYvvibH4Wcw2VMXcOgwCMmMqXZGk9sQSMSBoixzsPg==", "signatures": [{"sig": "MEUCID+GNYsXel+iJF89dkwsRywh9/yzeycNoM5FXmGPZvuoAiEAzfOyywsRe1XjMZFuhXS0qyzKrlhjdZbHNZ0WreGG95g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 468417, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlRrPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIBQ//aOxaChxNxPtqHyDwy8plY1WkQ927N37G8Xr9GP+AFD95Q3cC\r\nv3s2qHf0VMOP0SgScPQ22/sHYifW2FRNDjT/viGvJZKHDDo07pnYacbTf0KV\r\nuwU7OdoynCNvBdEpL0MV9goJApGvd/WfflyxTQzDLYU83fV/zXFQwp3MAtcm\r\nOW5pmZxnzcU+Jhd4dzkMCz1ocl6/9aAZDbUt/k+2GMwBcR1owX113X3+Q+wW\r\nQa3mVYAaaZf+8szuH1v8DD0Ef9QKpYNPghqY9H3GMO/gLGjRBgBBxk+W/UAj\r\n0PRCGOPNbENRxKkCAAXawMXBd5YSz2tRaSwg6yS8BLgST5HRT2u7zv/XAked\r\nLvM3uIyrZ/VuEfiPbNkxck9MoebfsV+otPm9uSE6ijIJVMxLTLYTmzl5TG1G\r\nlXSgWhFLoWJnqKd8gyDig43Jrz3oI2CTd9YCFA/GP+z1/rTbvU65ubnWu5Cb\r\n91nFIs50wmkP1sLja3lWD5oOuCMaGqGtgELJaXn9leW0Y7poBWHjHBkHSqS8\r\nAC5jxScxrHrF/nBoVn1qgK2V2TRvbxFUhp8N0cpoSiuvR8FCDkm6JX6gSs0Q\r\nKMNYHBQ7tTaP4eYej0+0BIu5yBCHHmyyxuxiy7cB9B7YAss1xZ8BvWcsLdou\r\nMmb1Y7Y0koQjKMJgmWVCz0t9zcMk8/cGM+A=\r\n=6Lb2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.0": {"name": "@floating-ui/react", "version": "0.15.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "fd83fa4c759921b6d8463a6d81924d52563d4155", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.15.0.tgz", "fileCount": 46, "integrity": "sha512-zndl3fDlNWNaRgFKCwg3NmlI/b5U3kUA5Ub9GfR0WMdSQ0zUQjU8Y2Wj+eSMlC4+w7Tu/IJ1Nsol4Byyzf4PBQ==", "signatures": [{"sig": "MEUCIQDbzImiGU+CnvSdwK40qlE/uHlHGbne7SO/QIwXWjynqAIgA4iZ2ACN1pHcLsTg8wsn3teZTi/6eX9Ae33MtGS6SOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 480637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqTdwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdKA//Vikhyc4Kl3qVe93IahmcCCe+EPXQSPi9XaAMZ5zJ9Yp2fABs\r\njpRAJhtgr8suGW0dI7NLA7BtoV7BHnMoKH3Gxsor1zRGurrEdJq3Q64G/NeB\r\nozWQd8Ma9Jzju4acFZ2DHdz6v9PXLKjLTsxSMtqJvNi2bSIYI7Bmw9HL6JXs\r\nlBU7yFpZEGHtR+o5Xee24+ZSwLRZ+R9iNUz6iejbnlBtd+yt1fLKZyVJi7nV\r\nWQUU8fRM3fISJ9gz2yD4xjm3M0kyB+45qvoSIMWdEoxszYwovsXhfVi0tLe8\r\njZgd/gLGlZ6IHoEQOsOYA/98vJD6o8cNMQmbfdPxGRTKlLWwf9kn+NgpSjxT\r\njsfrPdLfyNIkikoNJ9b4luOXcAzSQVBisPcfgXlEpwXXn62Rnz+MbilZYWkZ\r\nejXOV+53TLGe2bweeKrqUq7eSbS9QhbZ5t/PJGPEFO/zcpYbwx12IbgdekGm\r\ngUwq65nO89SZEBiKTo0sJwglcRtvFQIEltAQyECNmrJWMXwfXEUcNCm3bhzu\r\nTU34Hv58NiHUesBIPc/42Xh7k4l9BsxQforfYFkvexHKdvt/GV3ScXfSJ9iE\r\nHUNUIvUHwqPSBXjNARPvELk9PSyGBa7p57WaM3Xq644GmJBWeGUjuYw6m1gE\r\nbe2ByKue8NosPf9BtW7dlsiVPeBcbK870es=\r\n=iY45\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.15.1": {"name": "@floating-ui/react", "version": "0.15.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "1eec9d369653627aeec5be325714db1ccdf983a6", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.15.1.tgz", "fileCount": 46, "integrity": "sha512-fbzUt+JM9o7z/uTBIvTvxUOGXAx8JlCFNpcWen7IjQqqOISkZq1IT7u8wwR+u2emE8wAZoRuoUvny7UMcCGXbw==", "signatures": [{"sig": "MEUCIQDnr2I7M4+FlbIbbnZc2c3I9RniQCYTwY0xnVd7nvtXQQIgL1jrfA9OFQ41hyZAXar+RRby/0yPfoTokuNVWVW7d+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 479950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsYRvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLJA//TkMjk0dXRdDCKUS5Ad+H9PKDnKxw3mEtHGG5sLYPb05dZxHu\r\nCOqmv/iEAsGF7embE7wJbo1TBBfpIB1Omog2PXnkIaot9aTYiQ2aIZX0ROOW\r\njiZjPfDACsZdC4kppiq10PBaklCsyLs7pc1KZ4doNIiD79nFbOGOh1AD4I5W\r\nDD924n+mKXAI7fmqmg4Ac6OvgC2uHrNADhFj5ld0TY7vJVx9et7BFdN9Im5d\r\nVaAB0wjQKZ4X6NQy/38NToPaOely5dA/fi/HsDIUiTxsIE5Vr3quDYJwm6ln\r\n5bhbiApsSd7vAJt5kVQKUS2k4NhY9/CvDxodBk5/vXEpH5oACPzBpaorj1aE\r\n5HUoc6Fz3xCTX2Dyy/0wyX4tvDoPXJwzD6hvBPK0+BfEnNOCs8bNxy6xdk9o\r\nM4whH3lBmYKqkH2E20K0iTTK4ModhEuvHsbx7yGBK/J1Qd5MKuztYBNyyw+7\r\nzTuMSl7Lnwd7aCdR5ExwIgd5ArivC4TKlsGDhMg7MU1qwagmrrjGn9xOBMb1\r\ng7A1vJRA3tFj9F9pAN1aptQXOV3m04bpfgQ3UAmSBARNi5vE2F8sJnd1SMve\r\npQlsZEteZDZzFn1m17j7v9beec/CDJbHBRQmkJ1w/3XGFYvAKzMelpLBWHXv\r\nbyoYdpI3+Bq7SSzarCxNAQCMqbv/dmiUJEQ=\r\n=F9Vr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.16.0": {"name": "@floating-ui/react", "version": "0.16.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.1.2"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4e8a05a87691a436c60aa9d6018d8bb190d43ca4", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.16.0.tgz", "fileCount": 47, "integrity": "sha512-h+69TJSAY2R/k5rw+az56RzzDFc/Tg7EHn/qEgwkIVz56Zg9LlaRMMUvxkcvd+iN3CNFDLtEnDlsXnpshjsRsQ==", "signatures": [{"sig": "MEYCIQCTPTfgDfFGS4riAKDA4W9hpU2whrF0H9bsUl3eiqCe1AIhALcpI+xqqoKoWt4E8dOtjz9weh+8e4p9Ob7QMZIUuujD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 502579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuVykACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXHA/+MEfeTqBGJF1zc3PspvU/S5UhKqmyvhlbgvogoeQGoIyIgS4L\r\ninw0+PoWwxTehMDHZZRpQC6Wfl+0xYy9VgG46ZNCd4xanL5GzlN8Y/RiSOVg\r\nRbHGsHdNnfFg3osgK8m+iHGNysYxCbpWJ3TQ26xHUBKxDxWano2PZpVPnfFR\r\nd0MDBL6NIvs45HSbsPEAZ7SO5C7kBqeD9ywGE5tV16yRZW1WLWZQHjj+xXZr\r\noxKSmZQNvhLPYvE6linVk/nhMEPjEtktrLg38JCeoJm52CXZUDPcKqzigEaO\r\noTO4lLHv5zkvtZi+gJXl9WQXwFkxyiebxg5VzAGkx6QZItlE7FJg2X1q1cd9\r\nkppT/5WRsK1mNULWRp1jkUqTlZrqW+qOogTAb39LlK8nRUxpAcsC72i7D4QM\r\ntxU5PtcQQ19UxwKryw6DG/Q1qJYYEkfazAz2GxypjznncX6RdmrBuEJf0pCX\r\nIvLZUMJ3MYg+dK1SlDIa+uFbYLHCcIs5LyiEFexbhMMSrGuYs0DTTcARTRK3\r\nqKRJZysbP49Ue4EUdKjQv5NAxzTfYtcCl/CnKpG0Nm7Jup5cXDE6BJ6bXu88\r\ngyaYf3O37f96tuMq6ZdMaHvUgojxJedNLVgHcKe9UbBl360C6acHDb2X6LKN\r\nU/72xEAFToJO1g/9TEzvrJyP1bixiXqVIF4=\r\n=2AFo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.17.0": {"name": "@floating-ui/react", "version": "0.17.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.2.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "f3a2fcb5c198df6cdc0aedac56bd3b2a6c0bb089", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.17.0.tgz", "fileCount": 47, "integrity": "sha512-LYlTmiB1O2TmeNub1agGYJrO7Qkw1B0Q53aElN5dgZDO5v3yzYJmrGm/xmfD+0T03yEECzPKdwlSFCEPWKMLEQ==", "signatures": [{"sig": "MEUCICSCdOEPhgwAqX6Dm5SJ71GFJ0FRAbYOtNfdZaXCGxTWAiEAjbcLTj14BF1Wlvrw93CSyJqrrKjqP9FPKWD3RJzI7OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 503622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvZj3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWWA//TiAcC9jOKQbJwZazl/vAY7hV6TgRWTUrtKEh2bMOQuso/dfT\r\nWd1OZzNcXWGec8HJ0xB1mG21eH3tyy9pIyAVZtiPXgld3ggKM8+rkyp0cALd\r\nXtLd3PCL9zOsDyLbKrpeeaaHYv+s93gTXK39tgN+0jG1cHr5VvqGbYZaORmJ\r\njDTPymqBPi4lSW0TMMDBFw2sxWApI321sRkrCdGzWiu9Q2r7zXoqP93FFEfp\r\n3YhGl+Y/lI2QUBtrukKoMCBcNIpoXHha7hg0MVO00vBG2izFOvBtGcXwZ1c6\r\nnwGlwZfh0lq3A9WD9hqfesJOqFjsVSRyF95XAbvq5x7/qcnAUL1waB1IuBPi\r\nWb5EjYWB3O7dYS5Iy3Mcbh77HjaVO2zQxwGSUd72iwuT03FKx0k+fxWPzzCU\r\neESwecEOugZ82AYQ7sfIi8uRQ1SVNO0A3NAxIA5jXnqyx6fLREMk7p+xDeUD\r\nDqXQunI5xelSmiFnvg0OGRAmZti+TxixFtCQyGAoaTjENqXxB6OnUdnTCQz1\r\nhffn9z1cqGBsBswRnxjd40Z7ZXhoJ8arBvbGHKukOHvzdMCc84QZrdG8szT9\r\n1EV0Uzdts5PFkUXb9QLcJdcp950JSh/TB2TTgWJoqTyyNWyQ4p/odPLfL6Uk\r\nUR76fvBWX6FbgAFOfeXz+dup6u5ukde9ut0=\r\n=1YzS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.18.0": {"name": "@floating-ui/react", "version": "0.18.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.2.1"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "fe6fb93d10a454b57bbb5b5d3721047191fcff78", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.18.0.tgz", "fileCount": 47, "integrity": "sha512-zxgwWigxaph1TIBdezCDdc3eo+4FUDKY2MiBR3gywZtx5Eeb0KBRRZ9PyzznTXuYO5Y9BFHAC0Yikqp7rr+oKQ==", "signatures": [{"sig": "MEYCIQCOi5vuNlkU6wuXjX4ghZjcuZNfooX2qAPR9EPCo0OmPwIhAKkqSDEkdO8NbOc9/lz7WFbEsxJQyrRXPk0l8K/w8ciA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 514800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxYUnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzBQ//bE4gxdKzatPax6vOHSdFMvx6iIlWM8ZH10nUgkUPV8utECTz\r\nSWsT4P+ucI5mugTuoYVs9ZnJeUFjdR+Sd2SwK6WrjW86jnS6kwQ0sc7L3iS0\r\nxKFDpyhpqBDfYSknucYhNI6vvJuBsuxnZxXfIxOqk1yx5xXnFGu12jmR4Jwp\r\nwY5zeemlEuPcClDCb9ykXuiSmJQP6+uftbezc7guhljUjkCXnIj3W4x4Cb6C\r\nlxvN019Jme2whkBhzokjDrKF2t38dK34En3+ZinKuKLLlh8yCTRvyH9ECB1D\r\nfQ1H/lRehKqFTcw2DtUAidE6JP8MiPvMes3rjEKSnIpYHGBNL7z/FqveJjXC\r\nKSwJBKyF6ptahMSTzqIDkNj5/Mg/OF1Zev7/kfIvsT0QTqzpqfTuz0PVkBBa\r\nhXZaARtZ9ZSKs8u8pZduXqN7uZihJ0SV+XeO7/VFLp1FUm/xtvyJGTi/a1yg\r\nuIW9/h+m4XGEm7HsrqIFyv9AQkSvLQFYUycteLDRR+hIpIRMIp940KpoaMDD\r\nMZVggkD+Mh3atXWk3jbEV/8t7CIDxfgMC+qOoyhgChqwI+M0KiGRN9T1Aypl\r\nSeKP+uxx5thA6lPlgxjFzgf9QL8i6KZQ+hOozWr/X8wtk/IQtWE140L28u+3\r\nc9nCAvgiF3BB3jJRZ/DEZ+YQnugQfx8bgIs=\r\n=Exo0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.18.1": {"name": "@floating-ui/react", "version": "0.18.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.2.1"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3a5ea19d22239f6c8d0250121488969680435473", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.18.1.tgz", "fileCount": 47, "integrity": "sha512-Uqntjem19/3ghAwKSaMU/719P/riiox13rkAzMhCthmAAhTzvvmNka52L5s9Gi/cjAfNNR5/RjkD0YKqTecWzQ==", "signatures": [{"sig": "MEUCIAWREd+fc6XDid6nQ4dTM2OOufpLJzubQQix+vuCe5ceAiEA9K6l4g1ZGXz8Hk3FPRg5o777SVnjTLT1hsot5TpVbgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 517324, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzec4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrC8BAAkx6aHkxBokXCCHJtsm4dXYx4o4jhSBNurvUSzj+BIXo+zqrG\r\nA1MLsoNL/+gQHG2AZv5JyShGK/bflk/DEF44JF0fXlVLuZCJV37jR68eO38i\r\nWoxw6j6xBpu4PQrZeJg+JKMuTgLZode6skSaVeeHTJVH3d9+lnohueQV6PJ4\r\n/cjc2663pc4c1x9s0mLpoXw3P2JlbPwBGcr89R8tDoIAZnmUcE7M7XTgcw9W\r\n/EbYjVORwLxla1yE9HJk4iav3ASZK5ubRetFg1xhXUqWy8o2l68ZZRHsK3+1\r\nkMymWYPbRxR7y7iBoLbYKX4NTyxvhnXabmrp5vntTJ/vB8BSAGWgxXeSfQvq\r\nPJ88hpf7J7QxGHamRHDxhXX/N9RcQrQKIQG1tRxM/6WWBvVgSImiv/FS3lc+\r\n7GwOF3Q4DqBuKYNkVqQb8OvRY8e2SbuCpTrs6reGNTQkIwqMvQ6unG6Bw11J\r\nGUEFVom8lih96lkkJg8+9kyJ7MGqaKGbFJxMx+rqiybiBID71nyyKkVBrdla\r\nZzag86m+sWX/Zpv07ganSD6lIeuDUfyjy0ONUOSnxycZmrwrEj6nM1PV/Bkv\r\nEHQKSC1KYjTstujrbTfTGhUJoVDq5/oEdKQ6kLRa1IQkDtq+LQsHfm8TuVhn\r\nrqJULvy79/j/msrbHCW+YyPl6Mmmbzz1mgE=\r\n=vkcy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.19.0": {"name": "@floating-ui/react", "version": "0.19.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.2.2"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d8e19a3fcfaa0684d5ec3f335232b4e0ac0c87e1", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.19.0.tgz", "fileCount": 47, "integrity": "sha512-fgYvN4ksCi5OvmPXkyOT8o5a8PSKHMzPHt+9mR6KYWdF16IAjWRLZPAAziI2sznaWT23drRFrYw64wdvYqqaQw==", "signatures": [{"sig": "MEYCIQC2o9HVuCiwG/fSNqHatGPGC5Zal+Hrv2avVfuepFuB2wIhAJAPdAnsot/KjMZ+O9XGBJDopjWNM7Hv2TSLKfNhBxR2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 518078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1TGjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeSg//Y8aMUX+kJ2WtX4+6gU/uieW71JizjHyEi+tZsK7qXT8bzAgp\r\nVXuckyc7Lot0J8s1lRdHJFi1D0vkC6Y4TrF5+HV3OrljCAX3MLTQu5Jgya+M\r\njh42zan84jTykLrDU/vmWqSIlz/jYYytp8LuJeh5HB66DhIhuN7LURpz+6QV\r\nS1vXj4w5i15EH8FjS0qecLUFEDnBK2B6HBDwmYQK5XE+vPNanj8mKIdE9rs8\r\nJdsAf52gVsgIYtr6ZWYWvFl12n9W8bIupv8YLZFUfdX8RQclGanl+GK2i/0X\r\nxXU5MBvvJUuwOjJGEgy8ArkF4wX1PsCau2mIDTJgcfwpH9gW3Em+ife2nAXI\r\n3OYU8JkhRsW/nZagzpIpM4Bk5Iq3ZdbfhNrm1vVD92vs4Z38ePPpvdQnJmwF\r\nndZHSbOpqNfOrCWjg1EcRw1rR7mmXC8LlT/iWIV6LbfDf1bPPYfPDq8rMTWX\r\nUVVXjcKfWPEGiJUQhbc1Uui4vmuUo9McVRXRqy99h+D9UIkXTeig/FMvdAyN\r\n2VWgR+/wmTW/gukxoJU3SVFA/GzCw7m+B+ZwrB5K/1A/xLPXdpOEvjHjdY81\r\nDNnqa9kD/SB+QOXrQ6LPPhEc7rVZFTrsQvUrvscLPdtTkK/obD1VCuIdekzS\r\nrQtYD17KLpABTnduHx2L4pbqyQw1vCljk5c=\r\n=y2Q7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.19.1": {"name": "@floating-ui/react", "version": "0.19.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.2.2"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "bcaeaf3856dfeea388816f7e66750cab26208376", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.19.1.tgz", "fileCount": 47, "integrity": "sha512-h7hr53rLp+VVvWvbu0dOBvGsLeeZwn1DTLIllIaLYjGWw20YhAgEqegHU+nc7BJ30ttxq4Sq6hqARm0ne6chXQ==", "signatures": [{"sig": "MEQCIHSXvB02zlYw0G4F05IzZrdOYQpdKb5q+UDuzwZj6UgEAiBcHWC7kQP2HOF2meGa+MVQ3J1fRHpj6sP2d9FgWiDxxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 517793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2nUPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoopg/9ERAj3B89rdFfLpqPiZhv4AQuFlSgZqf1g5PaRtEtx1t8o3II\r\ntwtHeop+b62k537wNggTiF9xi3xAGyXZ8VeI6lLD/etcxgdgwbWT1DW6hjLO\r\nYZBTOF9liXRWgdu0MGzopsyL1pwRn5Fbc4b8xBk5VzpcRp9mt0y5u0uYrwij\r\nAsxrzY5YV+cgTRxUD1XOal4h0FN6AiEsYF/Q5S26rmS1P012SUepjZEvzxst\r\naU4HrZjuQEEByvF7ku1/pC4kUPgMX5hTcmXhVaf1QVfATd378BlGGAs2KJ6d\r\nOJ3YKw3+1UoWafeWV5Ibrx8vhadSH1tG88VwQQagGw7XjNMEGw6j/kajUA+j\r\nICQH4dVjorZhXQcxn+15uoLnKqDtdobPxbHhLgqFixcXUtv2HOe9OCzN+zrV\r\nBY2g2YjSLMzNDdT8/vudjYC/HTDqUpLxtr3YpVZO2fa4NlRw8qvg4EtsZbAV\r\nb4730nzbisceSjES8S9jNZQQxpo1m4lzAnI1HFUec7VFhE4o7Co2ZtMLMgPQ\r\nkaZl2Wi8KEVN/oV1Fly5Yh3crSSoWNslfA91Uu0W3Kk3h4ZE7QmwlRrE9O5X\r\nf/YUTAuLPEUh75nl+5XwEfmEHbYWYKkRR7QXNdFO/KESJlorEooYV15Lmjns\r\nrQuHcfcTbBp/swcgdghfi7UzhUgyf5VgegE=\r\n=bPRR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.19.2": {"name": "@floating-ui/react", "version": "0.19.2", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "c6e4d2097ed0dca665a7c042ddf9cdecc95e9412", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.19.2.tgz", "fileCount": 47, "integrity": "sha512-JyNk4A0Ezirq8FlXECvRtQOX/iBe5Ize0W/pLkrZjfHW9GUV7Xnq6zm6fyZuQzaHHqEnVizmvlA96e1/CkZv+w==", "signatures": [{"sig": "MEUCIQCGk585S9Tlq0oiihz2pNEoIdQBI31ukRV+Y05Bj8qMjAIgbp45Dfjjy7WVPNwXbLs43Ev2hfKoBw2Ofzl1hx96O5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 526516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6LQRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrulhAAgpUPyJ/tBfy+88VYFizu2fPIQsHD6nGBngfWRARnWEzWHjAS\r\nf0FLKRUp5u4ql0ys84w7/5BxLEp6ewK+X+1B9LQviyzz1A+tiVbEIG/R9+AL\r\nP5a1UrWkl911o7V6pknNHt9DhP8p10QwaaDKMJpJGXEhkymu3MmK1bGEK9/C\r\nx0mqe79/Vn2sVTqJSmVBezP0ub4EvFir+bozoAc2tUQBXq/hXkBGlN2iOEB+\r\nFjWxnzQB5yLIPicwB4UhsLlwruzwO+ya0zjNHfP5+zpXbDe6dZhg5sd+d4Ka\r\nHKe3c7YpTa1nPIEQX/We/fzOmk0lUO0mGFaxufqPViwAoOCWqDudO8uGavzS\r\nArX4QRzmZ4Zri0Xf0qB5YFpHeG3IqjBLK0ir+WvCoCaRCgJMe43FiRjSGLHJ\r\nxRLqJoX4JWU11NnZ0fliu5ZE1a60YWfR1WSQriYXLkW+cIM/iWoClkPxZmRu\r\naaxXbPzXTuzdAODTPxzJ0V3Vu/kggONBAI+NXKpc7GINq7itappq7aHzPGBJ\r\nIzljXIiePilDgO6FDVTm6RlvBDnYLpoYYEFRYYMYMqjrM8+vZDrMHtv0VlJv\r\nTCGg9BnIBedwwAVKELbNb+OcuWRqjcCZ9M2pU3EgUFpGAVRJBilnCDJdYSre\r\nPHk8i0DJJnwbw4OKoTPuOwmdhp/sUXiQEbQ=\r\n=TC8V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.20.0": {"name": "@floating-ui/react", "version": "0.20.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "42fd5e1b0f187af8468f6a3be9ebd73e9efd4581", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.20.0.tgz", "fileCount": 48, "integrity": "sha512-AAbxjV/ETVVY0G0nBLFb1mpJdktuJ5dNlMSGXLewVT2Y/Ms9zTNwysX6UUSoIGXKTUMXmvNrRUk4aYJV8+GAPw==", "signatures": [{"sig": "MEQCIHd/BfAB3OEBP+sUHFBboo0n4Jf7DYoCOUdKqQ1N/izmAiBB+t8xcT4/WD/kuL56Hnebw7Dw202Xf4/sSoHkeRBgQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 538933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/3B5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolMRAAmux6qEJwBXeNXadT6yE6j8Cr1s181kH0H46s620c0jANtp4K\r\nSKemT1bfnXnsm+8JSkkL8vkbZ6yIeKqFDn8/uWffF0vx8cR4oaOfd8r8EVNj\r\nU8rDs6jeWeXGu7FHtBTkEa6qzEEuSnIVsN5wnownJn8GwEACGd9SFSOxWeKS\r\nckU9lWNKleoiecizXzEtnxmhcy/5zUmpo2BoeHpDIgpVpuOmIWj7+HveD7Er\r\nthNfHOneeq14YWTHEs0BZ2Q/BKmXP4dfgO9MkPOh5MVsMlMWZD3kEuEmMOwd\r\neVops8uGDEdlB+Crgxyq62f8K+XsvIUBV0MCuvqkiddeLm5C2GGGVNUGRhzW\r\nzpUdCd4TgSoeBMcWb4Q1sLjuagLVMJCCjW1pfErD2+6ojjlkAlC1AqLwBD3D\r\nY5iq74VoM5s/oTolQAUhHNO1ikkBoM7V0dOiAHr1eLK/wuZcMKJ6q0vi8lBs\r\nr4z0meyPaQhcO5eTTQ2VQIGhdN5in1Ny/ZDgZjY06RgfYyBDs0thJS0YcZfx\r\nti9s3VSCQJg3HPA+1nFYBtAtwlkOmV8BplWbwWD1aF22q2pKrrDwYM8oM+qZ\r\nGCP7nqNe3yz3K+z0zZAyCqXCPLcZwotEwHVlXGi8+Dd92XnJgJzOKiKnKrnP\r\nuTT1NdCKK18w7s6GHqtA1dtrKDtmr8pealc=\r\n=G1z8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.20.1": {"name": "@floating-ui/react", "version": "0.20.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "20f23cb5615f5beefa6e2ac4d7630a56ccdf7131", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.20.1.tgz", "fileCount": 48, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>+/YsIxNFH8uJDFa5OyI6dSUZcle6wAFe0zRTjgWD+rkACfBBoJtx2itTtn7C4a7xAz4jgxdEQcMel194g==", "signatures": [{"sig": "MEUCIGzyxPno9IewVcco877Taiu/0zKzlVSi47yBerCr+j5IAiEAhHVlFWvEq1/dPaZCxTdFTPuefnn7NzkQYjhM0lDh21s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 539155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAA8kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptCBAAj1WdpvsEKnTxaCLtmrGkDBKhSseK7Rc2XmHify1OwiellCZ6\r\nPKgLBmpotCPT+aFn0nuQXttzF8uG7CDGwhSzA0dQGtuWdX7NKlVReY7dRAln\r\nICdJP3x2Vkxn+KdOj5Ds/EbHFHyPhW443V/oqZG+/0r+/GtuE1MyAYDqnCba\r\n7cyfMLSJlZh1CZ3VJGnUmUC08P+Icgioa53S+CdsME1Te5U6I85AzGBUh3al\r\n/Uw54y3BbsESPX8m0EOfyiL5Df94rQmz7P4L+z+c1BBdKDI3PkZ5yQOL8F2P\r\nYu17c4rI/EItLxdBF2O+LnDO4Xj1ZJ9VyN3BI2UV0jXd1hsKwP7btOvbG0Nf\r\nn5XW0dpDIcJJRyVUxIkdb89Aaee/cymfc+qJSOzge+HsUhRJknhslTKW5td/\r\nbVHwCJeHImVQHoxcPgOU+znFsdHjCCBA/gUsqdZW+AhWqPzzy9it1pP527sQ\r\nlVglF7O7bOHXOBVMY6j2or29otesaaFJXdzzpVpjlc5lglzbh5NxVz8fZ0p6\r\n3QD2ScqcloJZDn8fG6JwNUe5peXCzSE3uAisXlN1KV65GgMLjionA8pq/YmR\r\nPQzIVgV2NgsZ8bUyd+aGnFWYuMzIYNrSUlEPq60lDN0oXLzyxPPpgiqQHt0s\r\nGf1GhIN5kGEEg/3r3mVvi5XATXsG+55BGj8=\r\n=bdkR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.21.0": {"name": "@floating-ui/react", "version": "0.21.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "5c888e1817d97afbcdf098c51e9fdf3cc44b44c6", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.21.0.tgz", "fileCount": 49, "integrity": "sha512-4Zut7tjeDVEKHaR6N3uG4m1dl114UkLuK4SNAeHlAb4pKu5KEkMkI34Y8NmCc4ARfXIu25UGUhYBUzShDhbofA==", "signatures": [{"sig": "MEYCIQCCREMBjjcZ4m7HleNbxobeTQz72F8RfGqRwTaf9qdg/wIhAMsbT0+RBOW4yCxyYj63OY8b4v0/NFsZJ3KPJiR1GZPN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 554939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDhpKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1LQ/+LZoW2FtY0r7hn9NshHBgdBTxJG2DYIcxdoWycSSs6xkZiJou\r\nWrPajjXcZ+JgCDwCRB6ryS1qjctdLc9emLB18Rq1IDM8oxbQtDl0f5Gcf2CS\r\nk1cn6bBRVnDqM7+2rody85cvrNCISTdHIlloQxrZTD/yJYzX8obJ10rXdqxG\r\nxwTNaCfFaXVX9HudZDWWtEnXC+n6be/52ACCNpEYC/6EAtACJpsV7Rc5ufrx\r\n5r1t6Naa76nFU//lRHyhZIGSl28fO8tTw0Rd1auObetCDJekuCayiZm24J5n\r\nf7oEDaTAY8N15G5ZdtgZStSdTPe1dxTA9V/TDbj1+UllNN45JItuloyi5a+D\r\nc2I+PUR26wqh3m3WBXTRFnoCRv3DOShxhlKEvMAKvREX4//xuo4kFOPDOUfa\r\nQY0xFsCZdxD5qLQNcIl6bd+JSgpa2G6ufpdMnhzeFD1YgkVhLfN4oED+cvG9\r\nExan6rXV8xzOWymnuhATSBBuGEmYTxMEG1/Ga1vdCO8sVe89TeWPm6H2LvRe\r\nHfmHAtlerIObkfB63RBt4myQbdExPlmPmA1e3u+wYuXhRnSmCVZRKxPVSUfG\r\nHLXlfAlOJK4XnnT/I3GSwASWHsZ2PQk2A/X2dt9fM3xAM5DmkPd1b9ilCNVu\r\nl8RR9v/rs2bD1EmpAZVr38tZoVDuCZ2wI2k=\r\n=+5af\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.21.1": {"name": "@floating-ui/react", "version": "0.21.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "47cafdff0c79f5aa1067398ee06ea2144d22ea7a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.21.1.tgz", "fileCount": 49, "integrity": "sha512-ojjsU/rvWEyNDproy1yQW5EDXJnDip8DXpSRh+hogPgZWEp0Y/2UBPxL3yoa53BDYsL+dqJY0osl9r0Jes3eeg==", "signatures": [{"sig": "MEYCIQDtSPtDSAgKcI9EgtVoaeZbT+AoYl1/pRNWgr0mVe4LDAIhAN6x57CMJAmdmwVAiVXlma20g0nlpgQli+zu8fknq8/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 552654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEFPfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Zw//Uvxf8ex7aTzZouOQBi7wS2StWksGsARlgVBBiJyRXoA78Hcu\r\ndZKkE9YWUXvC1gduRDhBPyPK3E868gYWZHSdi25p1ghfEAqvC9wYSzX8H7p2\r\nNIGBY4MUgE7jEN5SHAS9nzzCKMRkFOyRNa84e23/oNFjywR9qWSWOgW6OFIH\r\nCXzousXIKowgnZmWPJ3qoTBCCTyY8S5hMaFl9m0QUdhmDltkIrrqjUa3Bmvq\r\nu8XL6pFMfPzVu7URB/GDcoPnjK8zRcpxv4NdB5DhytR6CIZPvASyeTuW5rnQ\r\n9yNSWpFZBxWgm5afJi9C/jbsMp37Q6fcr5tuBWL4h5j6rZfREyrfgi167WE1\r\nTa5M9lOAaZ8kYCuIK/qvppDfmHXyeRVcJw2LeI1ZxIhydzZMUOsNarEGEGpc\r\nAI5d7RgWCs+xHSmM48+2GGG2gnDEkgfqnTaNst6XnvbqqpTzBJnaBrWNBa5t\r\nSPvVR4aIjS+Le/dNSE1KfP8ccI6w831v3L+optc95mMpQqHmgB40JsaiRLgk\r\nKUelPjfxvPTNribk8jwe4iShQgDb4uYxojmYIT9ODRCJ0Ksv6xTkIaWeAUsi\r\nHdSH/Wh0xFQJHZK8HcoTHHO9ANpyel39sear/sZ3dsRkKKL6EfzXjDENG3Nv\r\nHFKIoEbTj5/sxvw65rwsbRN/dQVIn71RM3Y=\r\n=w791\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.22.0": {"name": "@floating-ui/react", "version": "0.22.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "6ed6f781562f37bad487dcd88ffefb1b7d9da13f", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.22.0.tgz", "fileCount": 49, "integrity": "sha512-8bAm6zWBsGm3n4XU3qSDLr9Fq9eLqYmnLPn1k/snRi78Vlpp0PLOIE56NZdaLwVPPearlPpBIxQFhoJpDj/XLQ==", "signatures": [{"sig": "MEUCIQCWW2NaJMWmh4QaUPib2/JxbwyN5bA1o9LSR1GEZHb6zgIgce8kYDs0s8AIYj8jRAY20n2w0lf3zegp4wqX2XN3Oy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 551306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkF3amACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9lg/9HjS2ArsGAnY1Q2JyCOTkExs572SrdcsLhsazdf3kxAiJEAFn\r\nlDjCl2HHxB2YU2wkuYym0G+ZFcbX8iDf5zSLRELe9yEsjSayWNnMuHz0KWYm\r\nuRWob4u+T3/sjaY9O9/3a2e7doj8mEmwhMyAxQOXQKIYDoswcIjo1fdE+Mhc\r\n0KGkHale0B7wQQICIKBnr/HrWkFgZhb5FY0r3pJvJBlCee1B+5usIhoWz0+z\r\nLPesW9AA2x6NCAqtZdSOv+ovB/W7Eem3J7p+pnDYZ1JHRXjsh0F/sGwpK0iB\r\n0gOvJXUbMGZGAi1D0Tx7wdGPgMx6gCf51DJB1nKadyzmn9NK7SKa3JoY/q4U\r\nixDU4FpiKbAF7TP7SYjr2t5wbOAuk03WbDrzzKLJwyES2fXsgkNTTfZD/c31\r\n+NcZuMRLGrgVzZRU9StTKEv7AjnKCuqFYsGa2SoGMKae31gyOnz9Ek/ZwUAy\r\nCRtconJQUnyqG/wb9JrmkCbZ9XR88cF5A9as18rJwuVIjZ1MCzAfkSuGlyvn\r\nL9s6BIE++xbueasYWI06sF3vwRKSTy1ihdUjRYBuP7/L02jFHiKFg9nbvsQE\r\nMhndFvlhTEgQAnGBxMmbPMVgkgJZUKaN8i0ir8nPVtfdJxnXvFkusWovvFXr\r\n2q6mkB2Knu525wbhWd7iwIQToyNJ1fkLUwY=\r\n=qUpk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.22.1": {"name": "@floating-ui/react", "version": "0.22.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "111555ab5dac8cbadd71dfc4c40b6423de448b7e", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.22.1.tgz", "fileCount": 49, "integrity": "sha512-7jDNKvESSLNih4cB0PEcydcUcf2qeOoIfqL+QLFz4KIdUrpLToHDDgK4BV/zb3RkSl/OdhOoJOp349jU4iZFHw==", "signatures": [{"sig": "MEUCIQDqnagghgXJ0lITD/RxO8fGRrVZiRLY0lfgfHt2k109RAIgCKXg+x1C3xKIzvzXJNAzn24ixy2x/TGXUbK42hLxBa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 553507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH3v+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNQRAAm6m29WKXT4Qfhq/K73liW8I+287j+3/HWU+MDr5LlUR1c+qF\r\ncjpDI4ytYwajKNuaNWzCFZX/jBRY/1HOaCS1WFb/7jwQp5dn9Re7loyq+lh/\r\nN6KX8R/F9C7F6xmZH2epJ3Oxroj4s83Sy/h4ObBvnEdp4buqPdtF0tgn0/jD\r\nFPmjoBtkzfmnb6iyFGbeuyUSd2FD81y15p3EBRnarwCko3qEuCydqiD05Pfj\r\n5/spotTIMyH2CS9NIxmCXdw0wFVAungIsAjYitsLE7eW5SHlAW4pUpIWwdDI\r\nJ3pyCM2BjWGaiqv8pyulWJ+hndENU1Yegm/aL8b+wWBLXCH7vt7DymUWXbsz\r\n7lT1qRhDfUU/V+qyqM65yc9C07e5Sz2DLocJJmvf5Tj+JH38M002Y7H0h9li\r\nuCJj38DT9oVOGJ3aufdQdW6mVt9qSluYYnFk/RcjgqodRHy0upmC+go89Bto\r\nedHzKK5hgm+H+RWAaLkD6tc0IWrxMk6V26/OA1D96ZMgZQHeMKnxf9JfZGI2\r\nEeP/XG2Xso6cFaXVErvi1OZILT4K5RExIJaT13QVPwLhpsa1mPk1qmiWg1Hz\r\nsqqWJi16tLwrHiOsd8DGf3GiJxwMiRLv/GHI7aUBPCg/8qfGZjGLnR+SuJv6\r\nJRo6wLcUxvt+6dS+fV+g+Fj8KmJtoCSNrtc=\r\n=tK+F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.22.2": {"name": "@floating-ui/react", "version": "0.22.2", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "57d3e32dd82790be5dfcf1a42a44b4a3d0366ca1", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.22.2.tgz", "fileCount": 49, "integrity": "sha512-7u5JqNfcbUCY9WNGJvcbaoChTx5fbFlW2Mpo/6B5DzB+pPWRBbFknALRUTcXj599Sm7vCZ2HdJS9hID22QKriQ==", "signatures": [{"sig": "MEUCIFiICDmgohMjndsiy7bEf74rzULE4G+oxQtek/VyebElAiEAs/H317ULow0DB6DMckgkNvZuKerOOkvG+NRVO1XVfms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 553806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkH+QCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohPw//aWCm6Al4qVuD5cy6PE82xR3SY/FlMt5srplIQKA+s716gOiD\r\nb+cj8Hdw0lPNz74xRJTomrMqTLkLEx8G1xiy6y3qZ+g4EYQcR75wqx3FL2Bb\r\n0/6lBXxgGALf5yV9Pbc0JprTnUG0jkN1I8lS7z5Q2TMvL+ia9Nm/bw1mKxb6\r\nvOT4GRJer19RkWoCuBgrOSXtlIvj9OgF0vi7l/GpNo0oAvt3UabGP1nC3jC7\r\n5cTD34l06DpwLQ+0+duq6qpkZ0fxByx9enViVuu1uHT7sPCHKFlLEF9Y1hHY\r\nzyB+hJNKdnTFAGB0n4caZ4uMFwkX/jirD8qSxAMn1ckMpXEia8XaCGw69vbW\r\n0CpWs8tzLICgy91CrMY1eAk3NWdQj3MU3yJbT64O2Frvd3rRk5+xTG62BAJZ\r\nXHXazpQzSce612Rm1jk6rrvuZV6o3t2oZhl3zAGn26gs8HJwhJqi1Gb4sGZJ\r\nt3p32i3kMNrCtepiPp2RmpbZ8gsEPAutlBsN4Hjebi/kFRynHhCT8+jb57yx\r\nRO94dofH5jBGgHxG5pWyot0KBE124D4QmlTDhv0UtEQ1MGrMixQhZzIAwbLn\r\nErd5MqLCmXZIckXXwJZJNreFVZzOIff1+ABiKFL0SubJh95V3iv9HlAnMz2J\r\nger6b8AUoQd7uJ+mqNGJ2pm03xFr3WQjDOg=\r\n=m5Jp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.22.3": {"name": "@floating-ui/react", "version": "0.22.3", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "96effa223f7447e2252216e35818d7f654e71d9a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.22.3.tgz", "fileCount": 49, "integrity": "sha512-RlF+7yU3/abTZcUez44IHoEH89yDHHonkYzZocynTWbl6J6MiMINMbyZSmSKdRKdadrC+MwQLdEexu++irvZhQ==", "signatures": [{"sig": "MEUCIDFuthyHnRx4WIrRf4ycvrWXQ8DAnN8M3gpjWQ98pw4ZAiEAmoZQf5OQdl0q+pMrdvHMTa1cMXtaKQ3kuoTdtS0EylY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 554722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKRjlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovIhAAofugAD3c18jk4PfQxIvKOPMt4avYvwOV0hOV3EI0exHfsmar\r\n8U4nLVcKpxVXIFxEpUf8haKazaRILB+l199IFuKZHUrZtGJ4a53bOMI01TyR\r\nV7GF6KdQ2CLBNIa/B1AH+tRTzDnZKqtloHOUxdm/Gp4zk8lT6P7cq27PCk9A\r\ny7XjwtGrITWSZbt3ZnqhfgsEh7RK0oP+6UVGlrF8yU0lzSJ4+j9IjiIxLjRw\r\nNDhoYoqddC595nBHzSgF32084fXJOgg8RcjCo142/Le3ddm+YYgQuRG4DTYE\r\nx3ULGcGu3MoYC1hwTA2uxQUhAjf8nyCnnuvRAXfbDBUBubyRXZvxTYvR0pIa\r\n7on1sM+3HsciFVZkWypw9foOZWpRTI41DORHaNPmPpATw1uJL0lMJ4wgUb2M\r\n44sxUq8rWUFbOREn1LBOPOPwv7lVtxMr1YOb6M7J6I7CrT/W98FSjVeAL8bG\r\nY5x6rY8uWWPScytBEWkvdWJALw89fSuZdp95nTxqcQ5k2lKFG8ZoaHOCDxsf\r\n2rAZ+xqp1mLa4QTKWrcJ5jyXEt5lDZkC7fsp7l+FjBOhqb77BojKCehv72TH\r\n6rUDF4uv0LMOAAxBMtHLx6xf5XUgWg+sVdBXdfAldsDBauBgq2XjlNT0CD5O\r\nm/sAiZ37qd773+RVfx8VfO/a7i8in+LXx70=\r\n=/xLK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.23.0": {"name": "@floating-ui/react", "version": "0.23.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "8b548235ac4478537757c90a66a3bac9068e29d8", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.23.0.tgz", "fileCount": 50, "integrity": "sha512-Id9zTLSjHtcCjBQm0Stc/fRUBGrnHurL/a1HrtQg8LvL6Ciw9KHma2WT++F17kEfhsPkA0UHYxmp+ijmAy0TCw==", "signatures": [{"sig": "MEUCIQC6XIgRlrbeGgGWrhC+969aIqQ+qpk7mbSKuJEFm/xqjQIgFO+WMfhQgskc3maBzzCeB1UOFHDvX7Nhzml/uxEKDS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM6/ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWxQ/9GIZYwJnqjNmDqqPaxElXQvb9caA7JwcQrQr1o5z0LBTmOHwn\r\nqQZfDIGJcjSvmP9jYpdtBgI5703E9pm8LNu+qQcdLWF1ROM5pgEuRxFijihT\r\nuV4E/FZe3UJXPkLpfzOpQ9rRYF6jRQflqdmcJKSV7wV1bfF00gff74Brqme5\r\n2EpEdTcSRl9qfgF7opdRkaCaVIJkVPzzI/AYkBrkhgqpRoqh2eCaClN9ESTk\r\njP+9oSFx++AsLsdWZ2HdPvd7Gn/qFTJbdfFGGrzANRZ9TNBn1RQtwMJk6b0P\r\nU6EheL20q3RqoXGYOOBxO1S1KTFbD0xRNaPTLIdSvilmH2cccJuHEKHNa9VJ\r\nbY/D5ItlRLgi+Vo/yQeC79X09ykkbJAGZzO8ZV0MOn2rBOD69wMw4IsTJ7IL\r\nYvJ4jrDQt3S/p9fR3h4XvKzk0L+l7UEXpRD2ibcDY5yg0L2/xvmLYGZ1qFQo\r\nMrOvMci2wLEBN07i6xpov5vDF93jBD3007WjJRINl5KdnqWYyWFZXnqEP8kH\r\nhoW2j7Takwkk3gQc7TUn6e7bPWJ+WAq7PVQTLp1e6dlHgvZhv6NfIorQC8Tc\r\n42+oSRSx/0b7UmG7PG7xxsWvjfoczt7EgSQTbYeBxBnMstIPucZ7iRdF+swq\r\n+KsSNMCaDZEffF3eDH6tKo5SyCRhnP+i84s=\r\n=yHtO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.23.1": {"name": "@floating-ui/react", "version": "0.23.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^1.3.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "04da0621b042e02fdead26a7accd291f3134fc83", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.23.1.tgz", "fileCount": 50, "integrity": "sha512-OCc2ViQOBUKOGcE9NLAbpyqB+8Zz92IKIhxgz7XAkynKkVzcVSKtkWOcgyvO4SAzB2OybgRwk3WdzdzDRdh2QQ==", "signatures": [{"sig": "MEYCIQCEnYI15ocd953S8YSARxhyvnDMG3Ch6o+AuaYWVamCkwIhAKrhv/WDoPRMRpNrYuzKgC7L6lm302XhovZ7cLPHfFBT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQEH3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9AQ/+PKIpBpkEo6dn+FimKs2/wcnk8Uhw6xGlhvPZOvXxDZVmThfp\r\n357N0YcwEjgxKkGOlS/DslmGCeMGrWL6PH3YjnShxv8CTDBhrxRve8VtSs/S\r\nLi1OlN5EJ8mGKEVbW51/R7olN+zEkjRzhISoao7EaQsoM6u1eil68Lwxjc2z\r\nGFyfMuJzU+KEVHPf9M6kos10zsfcbWu+YVNn4qw/KIeu3dX8aGx7O/0WSSy3\r\noRALH2oS2XhXS6ut36CsfUnr+H5eyREvpKRRcnA7xHx5HU/OsgfOkTZkCvZA\r\n7sa8Jdc4nO7v/GRuidCHV2Faigdfu8r9Qb4m45JHUSVXJa7tkSU4IeDwwokN\r\nqMxApcbEPuAAk+8EX1HPR5oBDXwfGdKLKruS+s6igYcAwGp4IsOqeWSWmpld\r\nmSstMSZvWcHsgXz1YnIhb5osAxS3ck/1P3lH7oXV7o0NurVV7pcBBiyHGJF9\r\nQKcdwofk2BMt2gKDz6qY9elQlqatq6tMyB7IwLq43mSKC9qdfeXZRpRzUSSY\r\nsa9AiSETSWAjmJF5zaPX+4V7IDo1Ytm9T6y6KGyRkPmf9lN1JfLqGcWmrNyB\r\n2oBtsTFqXsAaAWnjz7uPDXrdqjymDJzNuujdl1u5IoDDDwZ59TE+htVPXYsn\r\nZs+qZgvlVYmKhV2m/FwYANSCXraD6ps51PA=\r\n=7yQ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.24.0": {"name": "@floating-ui/react", "version": "0.24.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d835f7041b9b4ed1b7daa83f33af412ae0c6c946", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.0.tgz", "fileCount": 50, "integrity": "sha512-/UxcKFV5WtD+CbInbosDmUwVpcKlTTo6sVllSWMYXiX/HHXWeMxbcUNIkilsj5EDlbKiw7nHQtHYHYQLQMsVdQ==", "signatures": [{"sig": "MEUCIG2HXk7VXjnlrMLb8CPcLaam7vaEkQAwgr/gE/IXcM7IAiEAg+7HCnvzYzWbZtQBRjL0hjEagtbw3w4ia0n8RtfYhr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 569219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkThZ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqb2Q//avrABg37l3S7uwv2PLAkRATBUkvSqy+qCOH0f1moIa0hjLO7\r\n4ofSh64xSiji92hzl+L23S/Rm+irY4Q3qOjGM6DCEi/dl0oSaGDqGAVKuWST\r\nLGtFvLI6tMjz4pZuBCQISPS0qVVr6lU+Ij88lICSESANCcQCNgKp/fEebgLm\r\nvvQL4CpY5IbIDjT6d2a9CD6xKg9FEClecewafA3aqz+5qK8RWpHxsGO13dRa\r\nUnZeemeA/IGLZHx3uj1IHBRnkSjIJE8ogM904xgt18sM6Gg1M89HTXi6NRPk\r\nfcJFQMAbm9NNVZkPHNdNhUHY4zKsK7f2mzKE+9Q7e8UHAo4Xo2TeEPbKULDq\r\nVLtIY0MCWuFZKaZNe7svBgJ/ubIeRZeKrDKthCehry9WTXCopGE6rBx9KdFQ\r\nX+1eB16ZZ/otHcqcPa4Gl53GY2JuR5dU2YzctIdI5ZuGyQ3rR5S1mDKbeaEH\r\nE4sC6Mu3Zcz5BBYLA4vey444f7M+qbjOBj/MRpLNofKo1+OPpVUr/gvZphwh\r\nmp4slO/NbHa1TgS6agvvcGy3L1KN8edPLDPSCJ4qB9K0hfQC3kUWjutYMMlo\r\n26VrAJAvn1MRCT5u6FcttbJsMOehYbN1fbr4ii28Kph6Djwk0C51uzQq5XiO\r\nEmgzpiVNbZebnm7Dzpqq4ngpzY36Gx+L6ig=\r\n=7ZZO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.24.1": {"name": "@floating-ui/react", "version": "0.24.1", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "9ef81c78045c4b88ac062755826a3daf7cbe23d6", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.1.tgz", "fileCount": 50, "integrity": "sha512-qjCKUZDEz/4bnJmu4gn66TqsoX912/re8JGEi3pXazsphmyh327l0UpTgpBAT3WkNbnzAH7Adt3wKlLMNtfupw==", "signatures": [{"sig": "MEYCIQCG/Ml0fsiqAqiAJIhJrNLxWVAVciZJ1t7O5xXttXtC3QIhAOn7DvSIrt07VcpryCRBKxcbfFUnHgrBBKHd3LrfHoZm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567510}}, "0.24.2": {"name": "@floating-ui/react", "version": "0.24.2", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "1f2e596da122341a3fa65667a6f26ed126d32cc0", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.2.tgz", "fileCount": 50, "integrity": "sha512-8sdLmcC85J6M2H0AL8yOQuiWD4T0gNMSLpuJjmXyEA6ndfmxXR0hwKFkczB4xRNFhKbwoQeuh8z561HE2vOdZw==", "signatures": [{"sig": "MEQCIGbD94T2jx48OUGgKjvQgLpsMBz2VMBq5BtbrjCcvvQ0AiAOyx3ulLSu0sreJQV6j/H5wpDGLnuuYBO1i2V3np715Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 567756}}, "0.24.3": {"name": "@floating-ui/react", "version": "0.24.3", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.1.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "framer-motion": "^6.2.8", "react-merge-refs": "^1.1.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@rollup/plugin-commonjs": "^21.0.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4f11f09c7245555724f5167dd6925133457db89c", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.3.tgz", "fileCount": 50, "integrity": "sha512-wWC9duiog4HmbgKSKObDRuXqMjZR/6m75MIG+slm5CVWbridAjK9STcnCsGYmdpK78H/GmzYj4ADVP8paZVLYQ==", "signatures": [{"sig": "MEYCIQDq3VyTf48jKu4RneXk1vjoR2JMOo+TpUT3j0YBpTOR+gIhANOHjqve58xDyNbTmmvtmOnPX4jBVXOLsUt1OS3eoYNQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 570458}}, "0.24.4": {"name": "@floating-ui/react", "version": "0.24.4", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "94700b4f702a437e904dc0489e78a1c854843de3", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.4.tgz", "fileCount": 52, "integrity": "sha512-4l7azXEPMLamvcxR3Nd9OAa/g+5Tc0IkOfpacYH6iXYNm1SDD+XZhdonejZqfTwGApVGWbXhSRlYDeC3YJH+5A==", "signatures": [{"sig": "MEYCIQCMI578Ipb13YtAN0sG1zOwGGoUWbQXeXGEmvRk3dk5lgIhAJoBnDqV0YjmaJVlZ46VudwxYnFbM5PSh5M9oGXm2Iq0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 575716}}, "0.24.5": {"name": "@floating-ui/react", "version": "0.24.5", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "a5ba451c308ce112e98c59dcb89b28d100236cde", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.5.tgz", "fileCount": 52, "integrity": "sha512-p/cOvACHFooJX5yTaim8PZgMAt67IIBAkynZfWiLsor5aUE6all1OJ73eVpjATUxFP5l8gqOszvP1Zr22T2UgQ==", "signatures": [{"sig": "MEUCIQDexV4ZQObscVwQh1SlUPHQWbOCjxbR3/Bon0+Stgd0hgIga7psR2QS9uFPc0HCBikibcrc/JPUnq/yO9RWSB9WG1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 577030}}, "0.24.6": {"name": "@floating-ui/react", "version": "0.24.6", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "427f497648f562a23d6648347a6ba47aa7365692", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.6.tgz", "fileCount": 51, "integrity": "sha512-k09FRla1s+Y8ZTnk9Km7QM5zbmYcENnirOFbuldODE/4FxmSfrme21n6XybOMs8EsP69qrkhS5zlTnsd4NVmyA==", "signatures": [{"sig": "MEUCICo9XggW/xGvNiG51dqtb+q8/V21mCGdgNSKLnfSt5aSAiEAomSoEYdvdKXtXJ2JRwIbhKj9GsPEuOPj+qip1NaubmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572927}}, "0.24.7": {"name": "@floating-ui/react", "version": "0.24.7", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "af4e85cc1ff4c8695810e6d3e83fc17956e4ce2c", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.7.tgz", "fileCount": 51, "integrity": "sha512-lPlXxAtnkEvdCw8KkErTrTkaTosNsvd1tTmNBei0+5SyQ17wr9Oz9z1rVG6ve6M18p5QJ61V8RltKGXUn1rxCw==", "signatures": [{"sig": "MEQCIFG24175CG7V+OTJ+hfxvPtOuIQt/qcLlGm/6jGsRTWPAiB6p30K93YOgCoX5epfepizi341j3N1b2fUbBpnCvzjwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572721}}, "0.24.8": {"name": "@floating-ui/react", "version": "0.24.8", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e079e2836990be3fce9665ab509360a5447251a1", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.24.8.tgz", "fileCount": 51, "integrity": "sha512-AuYeDoaR8jtUlUXtZ1IJ/6jtBkGnSpJXbGNzokBL87VDJ8opMq1Bgrc0szhK482ReQY6KZsMoZCVSb4xwalkBA==", "signatures": [{"sig": "MEQCIBwRtYIBHzRCR11uR/xL2I/N4zvZokrk0rPkdrafZvmJAiAhbp9gRMGo29wr9i4ihY5Rra4rUUaQCapmdMMBz2Yy9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 573189}}, "0.25.0": {"name": "@floating-ui/react", "version": "0.25.0", "dependencies": {"tabbable": "^6.0.1", "aria-hidden": "^1.2.3", "@floating-ui/utils": "^0.1.0", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "2dcc79b0892b570641484355ca27a0f25793c12b", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.0.tgz", "fileCount": 74, "integrity": "sha512-0ulipo5ZTmCpTlVPjIbLyqReO7S3gwyQSiIm0TjKU5LPN97BmMZ2crJZcNMQ4GZz/V4qurMibGl4z3+TjF5tRA==", "signatures": [{"sig": "MEUCIQDy9mety7tIG0rT2h4nDFWhEwLuvaL2kaMwIxnkzFWGIwIgF7QDsDE8alHtmH1FL96wFxxccXTfZKOcL8StFvgo61E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 588973}}, "0.25.1": {"name": "@floating-ui/react", "version": "0.25.1", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.1", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "174bf4322913aa3549aeff27a0755fe10c66686d", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.1.tgz", "fileCount": 42, "integrity": "sha512-lxuWxfSgDJwOeZK07PIDjTSlH0CY6LRDKo6eI0H7TnctP+5IAn0n8+npNveM0L2wNIVdAr0S8RvvoHfhzPbBAQ==", "signatures": [{"sig": "MEUCIAU691d5es1WC4BYDANkj0fVQ8cTfZ4x09ZNIk9/Ih+LAiEArqBoSMyj8frpSWkawtXM/Lej8RmSOu3GRIMQtcMJVdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572950}}, "0.25.2": {"name": "@floating-ui/react", "version": "0.25.2", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.1", "@floating-ui/react-dom": "^2.0.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "a8c63ffc5bc9372e33e3b0865227150411efcc39", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.2.tgz", "fileCount": 42, "integrity": "sha512-3e10G9LFOgl32/SMWLBOwT7oVCtB+d5zBsU2GxTSVOvRgZexwno5MlYbc0BaXr+TR5EEGpqe9tg9OUbjlrVRnQ==", "signatures": [{"sig": "MEUCIQDssvAzV8ubpnIHxLktxeFY5bZ065BDFtkCmRvwK4u9AgIgP6XjbYnuV5EL4vfAPZPGGsPjyLRgZHidITiDm0xuz28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 572582}}, "0.25.3": {"name": "@floating-ui/react", "version": "0.25.3", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.1", "@floating-ui/react-dom": "^2.0.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "241b3a18d0e99e8fc25d581002646a6fd75d4c40", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.3.tgz", "fileCount": 44, "integrity": "sha512-Ti3ClVZIUqZq1OCkfbhsBA8u3m8jJ0h9gAInFwdrLaa+yTAZx3bFH8YR+/wQwPmRrpgJJ3cRhCfx4puz0PqVIA==", "signatures": [{"sig": "MEUCIEO+i+hovnS4moj6vL8StbllSXQZTPzpZhGCehBoE/Q3AiEAyJcAKnpHC/+0BQfGhE+60pwWuoLT0KG26d8nr+S+4qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591377}}, "0.25.4": {"name": "@floating-ui/react", "version": "0.25.4", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.1", "@floating-ui/react-dom": "^2.0.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "82507e14460aee70f435ad2fd717ea182b6d5c61", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.25.4.tgz", "fileCount": 44, "integrity": "sha512-lWRQ/UiTvSIBxohn0/2HFHEmnmOVRjl7j6XcRJuLH0ls6f/9AyHMWVzkAJFuwx0n9gaEeCmg9VccCSCJzbEJig==", "signatures": [{"sig": "MEQCIFnW7rYhk3vhwDA5ZEvoGlwUumHHDdjvu7WC7jNn8UtrAiBc4w/mcnIBPV9Ct1nL41cF8jniOnPJ8sFdDlIjl56DNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602933}}, "0.26.0": {"name": "@floating-ui/react", "version": "0.26.0", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.5", "@floating-ui/react-dom": "^2.0.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "f33317652d382f21ce2584c43bb009154e6b7700", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.0.tgz", "fileCount": 50, "integrity": "sha512-W70xgicegogSoy+8Hfmpd/NWEuL26vsatIHkpVydmigJ84YYhs5/GlBCkLcETWajCjD9XKwlHUv6ezwbLLiung==", "signatures": [{"sig": "MEQCIGHSyUnkFIbPEd9Zfrzhvg4jec/dxFBo3or1UmDQ5dIBAiBmNbAUlmRQuYl3PlPb5I+KRNbUZX0iEyW06wHm+BIV1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624462}}, "0.26.1": {"name": "@floating-ui/react", "version": "0.26.1", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.5", "@floating-ui/react-dom": "^2.0.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "a790bd3cff5f334d9e87d3ec132a3dc39d937800", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.1.tgz", "fileCount": 50, "integrity": "sha512-5gyJIJ2tZOPMgmZ/vEcVhdmQiy75b7LPO71sYIiDsxGcZ4hxLuygQWCuT0YXHqppt//Eese+L6t5KnX/gZ3tVA==", "signatures": [{"sig": "MEUCIQDYTDXV+7SUGljT2BDhqxnm1W1l6Y/dor9uKdtePZVqhQIgOefRIQwV5xZFF1sZmi6xCgH60w7elAWuei6Ua4aM5uk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 630726}}, "0.26.2": {"name": "@floating-ui/react", "version": "0.26.2", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.5", "@floating-ui/react-dom": "^2.0.3"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.0.1", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "1a548f0f9aae64a742c868ae36aa620d15dec728", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.2.tgz", "fileCount": 50, "integrity": "sha512-ocpz3MxYoZlgsASiVFayiTnKukR8QZDQUMqxMdF0YFLbu8lw/IL6AHKLROI8SOpp6CxpUGPh9Q4a03eBAVEZNQ==", "signatures": [{"sig": "MEYCIQCLmyk92mS0lIZ0IUHcJVzMUtxADqIAQDnYz+ErslTxUgIhANYw3gADWHZHH3iLx/1Vc6MnfTQ5c4f+VtrTbJa7eWmg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 632497}}, "0.26.3": {"name": "@floating-ui/react", "version": "0.26.3", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.5", "@floating-ui/react-dom": "^2.0.3"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.2.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "1ec435f35e37d5e34577ee89c7abb1eedb3a0c5d", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.3.tgz", "fileCount": 50, "integrity": "sha512-iKH8WRR0L/nLiM6qavFZxkyegIZRMxGnM9aKEc71M4wRlUNkgTamjPsOQXy11oZbDOH37MiTbk/nAPn9M2+shA==", "signatures": [{"sig": "MEQCIFFbtrqnriKFYjF3F6Q9+CFH/oEhJhyPpxqFfBFKBJMVAiBUzrfiRbCJHyESt18C7waLie1Sj1NFpJR6TjIzEL5R2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 633100}}, "0.26.4": {"name": "@floating-ui/react", "version": "0.26.4", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.1.5", "@floating-ui/react-dom": "^2.0.3"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "react-responsive": "^9.0.2", "react-router-dom": "^6.3.0", "@babel/preset-react": "^7.16.0", "@vitejs/plugin-react": "^4.2.0", "@radix-ui/react-icons": "^1.2.0", "@testing-library/react": "^13.1.1", "@radix-ui/react-checkbox": "^1.0.2", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^14.4.3", "@testing-library/react-hooks": "^7.0.2", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7626667d2dabc80e2696b500df7f1a348d7ec7a8", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.4.tgz", "fileCount": 50, "integrity": "sha512-pRiEz+SiPyfTcckAtLkEf3KJ/sUbB4X4fWMcDm27HT2kfAq+dH+hMc2VoOkNaGpDE35a2PKo688ugWeHaToL3g==", "signatures": [{"sig": "MEQCIDIF9EAoibffzuZwm/rTsW8/5NrPLOGvyaOH41sCtWl6AiASQp4Rq4719Ca+OAWYSqFB5+P667xyY2HhKeyfmQwEVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 641958}}, "0.26.5": {"name": "@floating-ui/react", "version": "0.26.5", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.5"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "6a8658c88ff017b7530594b94433d614bff66e06", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.5.tgz", "fileCount": 23, "integrity": "sha512-LJeSQa+yOwV0Tdpc/C3Vr92QMrwRqRMTk4yOwsRJKc57x3Lcw317GE0EV+ECM7+Z89yEAPBe7nzbDEWfkWCrBA==", "signatures": [{"sig": "MEUCIQD43aPv6i6V9/MGej2OLrtk30WXfGChHezmcRaR+xnR/AIgVedhuPAp03f2HhOjM4fJGFonHIr1jdXeRfyVrya8mFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 863213}}, "0.26.6": {"name": "@floating-ui/react", "version": "0.26.6", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.2.1", "@floating-ui/react-dom": "^2.0.6"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "34c58aacb5efe46633a7c9bf87c90c027d7bfafd", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.6.tgz", "fileCount": 19, "integrity": "sha512-FFDAuSlRwb8CY4/VvYio/wwk/0339B257yRpKwNOjcHWNYL/fgjl1KUvT3K6ZZ4WDbBWYc7Km4ITMuPZrS8omg==", "signatures": [{"sig": "MEUCIEDFO4rAuC8WByr4VcQqSUIlP+2/JwcE0jGRd+XKQhj4AiEAqplGco4cVWuHRjKCvpm7IK7dRqkK/4PdANTS6Ungo3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655185}}, "0.26.7": {"name": "@floating-ui/react", "version": "0.26.7", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.2.1", "@floating-ui/react-dom": "^2.0.7"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "0565521962402e4fb87a6f47bdd724ea5d74b207", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.7.tgz", "fileCount": 19, "integrity": "sha512-0uMI9IBJBPPt8N+8uRg4gazJvQReWTu/fVUHHLfAOuy1WB6f242jtjWm52hLJG8nzuZVuU+2crW4lJbJQoqeIA==", "signatures": [{"sig": "MEYCIQCbgIVc5t4kmvYU7gbEZvriOmlIozQ+AdTE9Tp+r9f7WQIhAJ10Lzx49/FZLL4HFndQLHQcIsJZ2GdCUa3WW8eKkv4z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 696696}}, "0.26.8": {"name": "@floating-ui/react", "version": "0.26.8", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.2.1", "@floating-ui/react-dom": "^2.0.8"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "9f8dc9d21aa35456ccc32b536d853d365ce8b9d9", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.8.tgz", "fileCount": 19, "integrity": "sha512-fOZb8BnJBrVohGPZ8RthDM5cHD9SnBKgY/U7LFXHhuwafSZD7TVmCX67+ezkkwxFbWpQGTEbgcjuHUDRonGy1g==", "signatures": [{"sig": "MEUCIGNkpOZIQmbHuJHFZb6AHKGx9dAviI1vgaM3Ow4B/CuyAiEA/ffVZwPx11hUHRt99uPCzY1I1UeSFcJz4AJtVqScCW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 696696}}, "0.26.9": {"name": "@floating-ui/react", "version": "0.26.9", "dependencies": {"tabbable": "^6.0.1", "@floating-ui/utils": "^0.2.1", "@floating-ui/react-dom": "^2.0.8"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "bbccbefa0e60c8b7f4c0387ba0fc0607bb65f2cc", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.9.tgz", "fileCount": 19, "integrity": "sha512-p86wynZJVEkEq2BBjY/8p2g3biQ6TlgT4o/3KgFKyTWoJLU1GZ8wpctwRqtkEl2tseYA+kw7dBAIDFcednfI5w==", "signatures": [{"sig": "MEQCICfnBhurYfBUII7B5UuFUuCHd1zBjVKCB5+voINAbCjVAiBvdcdyOZ4PDbRh8j/ebX43cTkhYJyl+nJr4KuhqgdfSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 697535}}, "0.26.10": {"name": "@floating-ui/react", "version": "0.26.10", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d4a4878bcfaed70963ec0eaa67a71bead5924ee5", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.10.tgz", "fileCount": 19, "integrity": "sha512-sh6f9gVvWQdEzLObrWbJ97c0clJObiALsFe0LiR/kb3tDRKwEhObASEH2QyfdoO/ZBPzwxa9j+nYFo+sqgbioA==", "signatures": [{"sig": "MEUCID8/Z1BirbMzTrvqQXQaaE9OQS60DhBJD1TuuTGx6pNRAiEAv2Itlwm+blKz9GMj7/0rxska1vsxFvqGfKyI5yHCOH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 698720}}, "0.26.11": {"name": "@floating-ui/react", "version": "0.26.11", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "226d3fec890de439443b62f3138ef7de052b0998", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.11.tgz", "fileCount": 19, "integrity": "sha512-fo01Cu+jzLDVG/AYAV2OtV6flhXvxP5rDaR1Fk8WWhtsFqwk478Dr2HGtB8s0HqQCsFWVbdHYpPjMiQiR/A9VA==", "signatures": [{"sig": "MEUCIQDwqqDQY5Mj0TDz8qiACGuOb6SkMPJOi7tGTOk2hhYjDAIgUajoSncE7N2Sof60g/fdkt0L6apaH95mGlXZsHq8784=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 701538}}, "0.26.12": {"name": "@floating-ui/react", "version": "0.26.12", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "6908f774d8e3167d89b37fd83be975c7e5d8be99", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.12.tgz", "fileCount": 19, "integrity": "sha512-D09o62HrWdIkstF2kGekIKAC0/N/Dl6wo3CQsnLcOmO3LkW6Ik8uIb3kw8JYkwxNCcg+uJ2bpWUiIijTBep05w==", "signatures": [{"sig": "MEUCIHSTAJHoJkG+s/VCUPzVLNEoDM25s3jOT65+S8MdrEzsAiEA56MburUTm5xMK2t/yOsneM8+1yeLNb8i3tmy5HMUmz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 703566}}, "0.26.13": {"name": "@floating-ui/react", "version": "0.26.13", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "81dc03b08ec8db40c48bf2e1f2a2e1a5e9a1997a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.13.tgz", "fileCount": 19, "integrity": "sha512-kBa9wntpugzrZ8t/4yWelvSmEKZdeTXTJzrxqyrLmcU/n1SM4nvse8yQh2e1b37rJGvtu0EplV9+IkBrCJ1vkw==", "signatures": [{"sig": "MEUCIFLs8anVBiFjVqnEPcyieWiJnPDK4sPutK+/28CAIa6DAiEA6/Rm3DET4uI5gihaRWjcsdimgwvh1m6TzSatipeO8K4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702444}}, "0.26.14": {"name": "@floating-ui/react", "version": "0.26.14", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "6c47f77656f922ed5a0cbeaf83410ac779939fc0", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.14.tgz", "fileCount": 19, "integrity": "sha512-I2EhfezC+H0WfkMEkCcF9+++PU1Wq08bDKhHHGIoBZVCciiftEQHgrSI4dTUTsa7446SiIVW0gWATliIlVNgfg==", "signatures": [{"sig": "MEYCIQCplDZgl24YwijxrzkL1WJIdSgG76sAwTb9VAt+4MQ4FgIhAIwNz51b0O1DvgW/vMYitjv8iWj/ELUPCNMS34hg25+d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 709724}}, "0.26.15": {"name": "@floating-ui/react", "version": "0.26.15", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.0.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d3103a2c77923749458edb304598b37ea852ef56", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.15.tgz", "fileCount": 19, "integrity": "sha512-WKmfLkxTwCm09Dxq4LpjL3EPbZVSp5wvnap1jmculsfnzg2Ag/pCkP+OPyjE5dFMXqX97hsLIqJehboZ5XAHXw==", "signatures": [{"sig": "MEYCIQCehLl0HoiGDVl7Ogh+jwp6Bs9274OxkuQ0lQ14EdbWsQIhAOk+zJ1avDnc2TYGffhk5hSJP1tRvTRODI4uW9Lh/93C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 708614}}, "0.26.16": {"name": "@floating-ui/react", "version": "0.26.16", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.1.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3415a087f452165161c2d313d1d57e8142894679", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.16.tgz", "fileCount": 19, "integrity": "sha512-HEf43zxZNAI/E781QIVpYSF3K2VH4TTYZpqecjdsFkjsaU1EbaWcM++kw0HXFffj7gDUcBFevX8s0rQGQpxkow==", "signatures": [{"sig": "MEUCIQCEwsILmkiocRX7rLmu0ck9IDuC+bFPfVQPdnZnR+rdrQIgHxqNHfIZlp7GNLGJXWn9gT/oUU4NjF7jhe913ovB6mo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 712706}}, "0.26.17": {"name": "@floating-ui/react", "version": "0.26.17", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.0", "@floating-ui/react-dom": "^2.1.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "efa2e1a0dea3d9d308965c5ccd49756bb64a883d", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.17.tgz", "fileCount": 19, "integrity": "sha512-ESD+jYWwqwVzaIgIhExrArdsCL1rOAzryG/Sjlu8yaD3Mtqi3uVyhbE2V7jD58Mo52qbzKz2eUY/Xgh5I86FCQ==", "signatures": [{"sig": "MEQCIE5FCJh3svXbFXd3ud8vCSABcmGhksZI++CV3C4RVqeNAiAy4wufrNvJP2AC5x8SbJg2p4JHcB6Zbo+nCGYSyC7pgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 721254}}, "0.26.18": {"name": "@floating-ui/react", "version": "0.26.18", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.3", "@floating-ui/react-dom": "^2.1.0"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4231c9e76a88212524fce783007ca7fc3e7630fa", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.18.tgz", "fileCount": 19, "integrity": "sha512-enDDX09Jpi3kmhcXXpvs+fvRXOfBj1jUV2KF6uDMf5HjS+SOZJzNTFUW71lKbFcxz0BkmQqwbvqdmHIxMq/fyQ==", "signatures": [{"sig": "MEYCIQCYe/EDhWqLP0PLWXvAEBoj+uZQBb7X3ToP3SCLzfkg6wIhAO8jVVtk9+QNHotSnRqIghPpF9t1pgvrcpDlqpFZnQz/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723464}}, "0.26.19": {"name": "@floating-ui/react", "version": "0.26.19", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.4", "@floating-ui/react-dom": "^2.1.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e3c713bec8a7264caa6f8195e0865f9210f483a1", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.19.tgz", "fileCount": 19, "integrity": "sha512-Jk6zITdjjIvjO/VdQFvpRaD3qPwOHH6AoDHxjhpy+oK4KFgaSP871HYWUAPdnLmx1gQ+w/pB312co3tVml+BXA==", "signatures": [{"sig": "MEUCICvLwsHmpqWEnJTrGCdlIT3vRcRy+SocWiIucJ0asNxbAiEA4WArAhwKEd7Ttle8JBAEF70pdKhzZgqTTgzNWAJ7V/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723432}}, "0.26.20": {"name": "@floating-ui/react", "version": "0.26.20", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.5", "@floating-ui/react-dom": "^2.1.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "49ae23347666626db8671c2aa2df469bbec7db71", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.20.tgz", "fileCount": 19, "integrity": "sha512-RixKJJG92fcIsVoqrFr4Onpzh7hlOx4U7NV4aLhMLmtvjZ5oTB/WzXaANYUZATKqXvvW7t9sCxtzejip26N5Ag==", "signatures": [{"sig": "MEYCIQDazp0teFJ8JN0Q4uzyLlPPUrOZOGv798tu/GDZe2NulQIhAP/scybLLloHHNRJzHWkDZVX1Bu6/zq592Bjp1rxxoH4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727465}}, "0.26.21": {"name": "@floating-ui/react", "version": "0.26.21", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.6", "@floating-ui/react-dom": "^2.1.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "29fe23a5197650d48eb1b05c5c46ff61df368fb6", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.21.tgz", "fileCount": 19, "integrity": "sha512-7P5ncDIiYd6RrwpCDbKyFzvabM014QlzlumtDbK3Bck0UueC+Rp8BLS34qcGBcN1pZCTodl4QNnCVmKv4tSxfQ==", "signatures": [{"sig": "MEUCIBOIBOTlcc64XwB3X1dCUDvpIX430vH/EHHJf6cwTpFHAiEA7xdmtd5Xw5JkCCqKUlsZ+Uxvxngt99lziQOvx/II5ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727465}}, "0.26.22": {"name": "@floating-ui/react", "version": "0.26.22", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.7", "@floating-ui/react-dom": "^2.1.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "b46f645f9cd19a591da706aed24608c23cdb89a2", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.22.tgz", "fileCount": 19, "integrity": "sha512-LNv4azPt8SpT4WW7Kku5JNVjLk2GcS0bGGjFTAgqOONRFo9r/aaGHHPpdiIuQbB1t8shmWyWqTTUDmZ9fcNshg==", "signatures": [{"sig": "MEUCIQCEk86ah3RIqpIBZsi9tFJOUPYUCtWHHzaeOCcOnInk3gIgVH0bdNusxkB451ktvXfHdVeiqye+pxWN3exSgUuh1W0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 727465}}, "0.26.23": {"name": "@floating-ui/react", "version": "0.26.23", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.7", "@floating-ui/react-dom": "^2.1.1"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "28985e5ce482c34f347f28076f11267e47a933bd", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.23.tgz", "fileCount": 19, "integrity": "sha512-9u3i62fV0CFF3nIegiWiRDwOs7OW/KhSUJDNx2MkQM3LbE5zQOY01sL3nelcVBXvX7Ovvo3A49I8ql+20Wg/Hw==", "signatures": [{"sig": "MEQCIGbUQepjVj/Ea7i7XBknHfnCuPBmwQRxnYe915ygwP9KAiAgWTw3I+QECyijSqOm9fL9efhxXHwnLGfmlG8iM04zZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 733859}}, "0.26.24": {"name": "@floating-ui/react", "version": "0.26.24", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "072b9dfeca4e79ef4e3000ef1c28e0ffc86f4ed4", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.24.tgz", "fileCount": 19, "integrity": "sha512-2ly0pCkZIGEQUq5H8bBK0XJmc1xIK/RM3tvVzY3GBER7IOD1UgmC2Y2tjj4AuS+TC+vTE1KJv2053290jua0Sw==", "signatures": [{"sig": "MEQCIAKmzuwb7w+MCXanveALw8ESs4EGg4tdbanRbmkn2JdHAiAmW+hsYTgkiy69hx8rlN7piTMddoCqfaG5yuqyGxXmQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 731353}}, "0.26.25": {"name": "@floating-ui/react", "version": "0.26.25", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "cf4c8a2b89fab1a71712d15e6551df3bfbd2ea1d", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.25.tgz", "fileCount": 19, "integrity": "sha512-hZOmgN0NTOzOuZxI1oIrDu3Gcl8WViIkvPMpB4xdd4QD6xAMtwgwr3VPoiyH/bLtRcS1cDnhxLSD1NsMJmwh/A==", "signatures": [{"sig": "MEUCIGAqWHNYoMrF1BvfVd1PLCCXEqv706E2bitSKViF5k+hAiEApc5fr5DFHtEatSrFzAfbgZ9Y+90dY7XqTY2Hk8xgzGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 739777}}, "0.26.26": {"name": "@floating-ui/react", "version": "0.26.26", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ab4b8675b21a61c1c868c392d44b2ccb5d5359a2", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.26.tgz", "fileCount": 19, "integrity": "sha512-iv2BjdcyoF1j1708Z9CrGtMc9ZZvMPZnDqyB1FrSWYCi+/nlPArUO/u9QhwC4E1Pi4T0g18GZ4W702m0NDh9bw==", "signatures": [{"sig": "MEUCIFRk4ulfBX8JnK+3uZp/pTuIHSnnFaqUhFtF0kzUp5pRAiEA8LgVjj7hByyXYXvnAAxLzHIrMhQTbnRBDbBmGQF0lt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 740717}}, "0.26.27": {"name": "@floating-ui/react", "version": "0.26.27", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "402f7b4b2702650662705fe9cbe0f1d5607846a1", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.27.tgz", "fileCount": 19, "integrity": "sha512-jLP72x0Kr2CgY6eTYi/ra3VA9LOkTo4C+DUTrbFgFOExKy3omYVmwMjNKqxAHdsnyLS96BIDLcO2SlnsNf8KUQ==", "signatures": [{"sig": "MEUCIHhU0oKlWdxqTwIlnlYjjJEt28lr48NSFyFwMN7bLOoGAiEA+smraP9jrjorjGtu0KKM3lp+HpHpVmUJ5a70jcaQZ84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 742244}}, "0.26.28": {"name": "@floating-ui/react", "version": "0.26.28", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "93f44ebaeb02409312e9df9507e83aab4a8c0dc7", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.26.28.tgz", "fileCount": 19, "integrity": "sha512-yORQuuAtVpiRjpMhdc0wJj06b9JFjrYF4qp96j++v2NBpbi6SEGF7donUJ3TMieerQ6qVkAv1tgr7L4r5roTqw==", "signatures": [{"sig": "MEYCIQDa2+KQiY6Mx/eeJfBXPzwarSmOdx1f0Ok0gKzRqqYY9wIhAOMZLi4XGkV1No9wOVJ9cNPER4KP77YiGpXaFVgf4Vby", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 744942}}, "0.27.0": {"name": "@floating-ui/react", "version": "0.27.0", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "e0931fd09374ab4b8ce1a1af5cb44d1ccd1bb95a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.0.tgz", "fileCount": 19, "integrity": "sha512-WLEksq7fJapXSJbmfiyq9pAW0a7ZFMEJToFE4oTDESxGjoa+nZu3YMjmZE2KvoUtQhqOK2yMMfWQFZyeWD0wGQ==", "signatures": [{"sig": "MEYCIQDCnhoo74jseAeJTXkQEBtftYcHNMT+QS76I3v2P4n0YAIhAIkEywrOWy1dCFPb1YGzhHXp5TxsZCv824+ErZSVGXFE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 747382}}, "0.27.1": {"name": "@floating-ui/react", "version": "0.27.1", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "bcc840d35172b28dd41914e8226837273237da4a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.1.tgz", "fileCount": 19, "integrity": "sha512-/Ekg7W22DLGGmqP4vO/LQ6SqOrqIlx1h1PqTcRleVEuRZ0NdBDjtYMhehZqWxjSA13AKA1XCkr7QYz5Z7n9Hag==", "signatures": [{"sig": "MEUCICKPFOr9+k6g/u9cgoAs5oyW38k5rryf6cIEBKdIiv4pAiEAyfRj5/D9VyMGLLcAY9x8t/H/lmAYGXHhoDyd1b/jVhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 748927}}, "0.27.2": {"name": "@floating-ui/react", "version": "0.27.2", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.8", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "901a04e93061c427d45b69a29c99f641a8b3a7bc", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.2.tgz", "fileCount": 19, "integrity": "sha512-k/yP6a9K9QwhLfIu87iUZxCH6XN5z5j/VUHHq0dEnbZYY2Y9jz68E/LXFtK8dkiaYltS2WYohnyKC0VcwVneVg==", "signatures": [{"sig": "MEQCIClTpTyADrcg33J4IKPPFvIsBwaEH0rrlqwlnYtF7K6QAiBlcje5qi0Oir747LjZ7gyqfbIF3rSVPENBbOjLpBaIyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 749513}}, "0.27.3": {"name": "@floating-ui/react", "version": "0.27.3", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "f9a30583eddd5770f3a6e1f3479a258f3df0c8c8", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.3.tgz", "fileCount": 19, "integrity": "sha512-CLHnes3ixIFFKVQDdICjel8muhFLOBdQH7fgtHNPY8UbCNqbeKZ262G7K66lGQOUQWWnYocf7ZbUsLJgGfsLHg==", "signatures": [{"sig": "MEYCIQCuxQZ8T+D+eylp+Iaoklj/BlkOIGTwmgE616Ut4DWf3QIhAJ8fUzyAx4tMilrun3AHifDQub1w1fcKZQdfOyHRoajM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 749071}}, "0.27.4": {"name": "@floating-ui/react", "version": "0.27.4", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "283ceaa8539282d6b8e6a84ac0400c111bee1e7a", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.4.tgz", "fileCount": 19, "integrity": "sha512-05mXdkUiVh8NCEcYKQ2C9SV9IkZ9k/dFtYmaEIN2riLv80UHoXylgBM76cgPJYfLJM3dJz7UE5MOVH0FypMd2Q==", "signatures": [{"sig": "MEUCIQCR0DMuRbli16dutK9SzK4mLCzIuhFqfBsNG+dkcMtGfgIgXZn9QP4PmTqkv4I9ox/iNCfAriFmElZBpUXRp1lRO20=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 751095}}, "0.27.5": {"name": "@floating-ui/react", "version": "0.27.5", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^14.1.2", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.5.2", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "27a6e63a8ef35eb8712ef304a154ea706da26814", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.5.tgz", "fileCount": 19, "integrity": "sha512-BX3jKxo39Ba05pflcQmqPPwc0qdNsdNi/eweAFtoIdrJWNen2sVEWMEac3i6jU55Qfx+lOcdMNKYn2CtWmlnOQ==", "signatures": [{"sig": "MEYCIQDb/xRNAC1rf7JOICwpAE6+jtqm5k5mlUiTXyJGS3jVNwIhANHtWy3ev+uawAx1hZFa5MjYjrpFgtWevd5972X17m3I", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 755012}}, "0.27.6": {"name": "@floating-ui/react", "version": "0.27.6", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.1", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.3.4", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^16.2.0", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.6.1", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "f1636698dfd5e8c3d22d3323cf4e8e1405fbeb90", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.6.tgz", "fileCount": 19, "integrity": "sha512-9GLOPbW8jTeboR2ar9uMMUDUZjpTLscUvOjNvRw2EgppgoLHLUh/P/OW9evULosnvDjhYf2Gwk/gMOP9KvXD2A==", "signatures": [{"sig": "MEUCIEIWIKajYwldpc4hx5SFYci3HTpaVz7w/VXBygneVLYAAiEA/jMYnxbSY1heQeGc+NkR2J1JIJXL9aSFwALJs8MbTL0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 761265}}, "0.27.7": {"name": "@floating-ui/react", "version": "0.27.7", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.1", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.3.4", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^16.2.0", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.6.1", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "dd5512f84528b849a99d93266b963b84c83bd201", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.7.tgz", "fileCount": 19, "integrity": "sha512-5V9pwFeiv+95Jlowq/7oiGISSrdXMTs2jfoSy8k+WM6oI/Skm1WWjPdJWeporN2O4UGcsaCJdirKffKayMoPgw==", "signatures": [{"sig": "MEUCIC1U85G4x+isRPTnHwN5RAHMbU27NWqZSbhkASNy8mSIAiEAizmQ+R2dHt4CsMc0hRJ9n2yvC+KFgt3v8eaa/MOSQFs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 786965}}, "0.27.8": {"name": "@floating-ui/react", "version": "0.27.8", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/utils": "^0.2.9", "@floating-ui/react-dom": "^2.1.2"}, "devDependencies": {"clsx": "^1.2.1", "react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.1", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.3.4", "@radix-ui/react-icons": "^1.3.0", "@testing-library/react": "^16.2.0", "@radix-ui/react-checkbox": "^1.0.4", "resize-observer-polyfill": "^1.5.1", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.6.1", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"shasum": "6d7f4bf7bfe2065934ba18d54f7383b777f87dd5", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.8.tgz", "fileCount": 19, "integrity": "sha512-EQJ4Th328y2wyHR3KzOUOoTW2UKjFk53fmyahfwExnFQ8vnsMYqKc+fFPOkeYtj5tcp1DUMiNJ7BFhed7e9ONw==", "signatures": [{"sig": "MEUCICm/SK16VC0CuwZKSFVKHYmuZKVpuxPqtuefIK9PA1l3AiEAzI3Hs9Krm4a2lBccWNSFKG5Jc+VZoj9qwgSGuLh9e8U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 923471}}, "0.27.9": {"name": "@floating-ui/react", "version": "0.27.9", "dependencies": {"tabbable": "^6.0.0", "@floating-ui/react-dom": "^2.1.2", "@floating-ui/utils": "^0.2.9"}, "devDependencies": {"@babel/preset-react": "^7.23.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-icons": "^1.3.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.19", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-responsive": "^9.0.2", "react-router-dom": "^6.21.1", "resize-observer-polyfill": "^1.5.1", "use-isomorphic-layout-effect": "^1.1.2", "vitest-browser-react": "^0.1.1", "config": "0.0.0"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}, "dist": {"integrity": "sha512-Y0aCJBNtfVF6ikI1kVzA0WzSAhVBz79vFWOhvb5MLCRNODZ1ylGSLTuncchR7JsLyn9QzV6JD44DyZhhOtvpRw==", "shasum": "b3c445a85ead27b73715c978a19b5506532ff097", "tarball": "https://registry.npmjs.org/@floating-ui/react/-/react-0.27.9.tgz", "fileCount": 19, "unpackedSize": 927730, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDIzfJ2qghu9WTsDv6LkhaKrjkw62UTzpeBtqjZhC5rgQIgAbo9cQ09a1dcbvdXIVDhXRHFfBRxqd10tDeWhK6Zhr8="}]}}}, "modified": "2025-05-26T03:31:17.316Z", "cachedAt": 1748373704616}