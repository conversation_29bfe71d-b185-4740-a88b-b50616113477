{"name": "@inquirer/search", "dist-tags": {"latest": "3.0.15"}, "versions": {"1.0.0": {"name": "@inquirer/search", "version": "1.0.0", "dependencies": {"@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "95eec0a39b1b047a4a6054ea7fad0232dc820894", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-hDx//88Ry8UZSiIbJKLhZ93yVETrziKGhhsRr9x3zovqRcjBoNroVpSxTz4wVWfeaq7UWeRCSW5LfoKep5fiiw==", "signatures": [{"sig": "MEUCIQC1fTd+y3ZzHHgzu54gdRqFLg/wcyZUu71jyst2NzTwkgIgc8cgHaTjcXgWymvIs5GwwYjgc8z2jxRw158yGxu9640=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22405}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@inquirer/search", "version": "1.0.1", "dependencies": {"@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "f9bd2dca1aa701b9eabdd6c01cf74cd16c9dbb98", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-6nAKD4zisCqQwqiydRKZhLmnGNCeNAPQyy5WrmtBlbn4bSsObeLgyNq2nW24cH0ByR27nMi8L3bQ+YGvWax9qg==", "signatures": [{"sig": "MEQCIAsYe6Wy+HPRHOmxy9AdpqLjBoZZlgCwWpbq1cUkjO/lAiArFlypVY1AnXcNmKtgJpnFuPhMQl19XWsoxXlvD+rgFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22593}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@inquirer/search", "version": "1.0.2", "dependencies": {"@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "1650beab88145f7130c78f7865df6320160c0ccf", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-E/JD3MeJwJeNilOnRDmHGnlUksyVqrTQEUNqkRpioj8J0sVxW7+pFRHBM2coFsiCpvI4XKRRhWsai5VP8rrfrQ==", "signatures": [{"sig": "MEUCIQCa2aJYXK78+/xpkMblj3NuJsF8muhke3ZVlyUCiMhrpwIgcMVS9QSH4rbhZPXiZfhh74PUWUlNaZdA4F29dmUmDOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22563}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@inquirer/search", "version": "1.0.3", "dependencies": {"@inquirer/core": "^9.0.6", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.29"}, "dist": {"shasum": "6d6532099a447f092d4c13b92eb416ed3a5b2e77", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.3.tgz", "fileCount": 7, "integrity": "sha512-3R0gWkaahzu2vkYWlr8E2IZTwj1QpanMrrK6ANsrPlXFwMl5C8v8gdKws4buBEmVBW0gpVC15xE20dlsWNhTvA==", "signatures": [{"sig": "MEYCIQCRvgsZpl0j0ANlyjpz//522emYFYfJ02hmIksnnGbhTwIhALcm34yiH7U5HFu/Fwm5rSrYYYFwusrCwBhifhl+B8Sh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22603}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@inquirer/search", "version": "1.0.4", "dependencies": {"@inquirer/core": "^9.0.7", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "8bedd8454420993a332ad985ce75d1d422fc5c23", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.4.tgz", "fileCount": 7, "integrity": "sha512-h0dxuH+6t0BCHGoCqb66CM4pOQKmZuHZQARk8XYjSG/Tb8umZNVXyJUB1Rmx0s2voU4wd5A4QA0int0c9gkvAg==", "signatures": [{"sig": "MEYCIQDiVEQ2ELhDXbIcvcHr7/9J1TJM4p1m2yZ6UC8Aay8lMAIhAJHGOGjfyK5hT70JgqDDk7fLLAliz3VXmeXkAhREBakt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22603}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@inquirer/search", "version": "1.0.5", "dependencies": {"@inquirer/core": "^9.0.8", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "d715e16ed4b5d90f58a28f8f0d08bd99f82098f7", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.5.tgz", "fileCount": 7, "integrity": "sha512-25nyVAbO0NwFZTXP/cW++W1QGHlHY+hmsehMM1sPKvp4wYcxMQcm6xNCor0bU2Pv/L33IkPV/NV9SuJyFC85EQ==", "signatures": [{"sig": "MEQCIBf5bRu9R++XmzYnLvtdj86sLTBmHdtn7PrpcjUc06C2AiAady2UKmUrICfBQnk23chWYIrhPBc2RHjWObO6uIDjmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22958}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@inquirer/search", "version": "1.0.6", "dependencies": {"@inquirer/core": "^9.0.9", "@inquirer/type": "^1.5.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "9689f2cf7e1000e79bc208ea8b7bfd93aeb0d3d6", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.6.tgz", "fileCount": 7, "integrity": "sha512-dZ2zOsIHPo0NgUVfvYuC6aMqAq3mcGn/XPrMXjlQhoNtsN8/pR5BmavqSmlgQo9ZY25VXF3qohWX/JzBYxHypA==", "signatures": [{"sig": "MEUCIB7nfg5rGcBLRWUA4a+CvSsvC8SDe+IGj5CHpZ4lXFJuAiEAsQ9TIoqkwlXHcfrrdxELjHmUS0ddtXkEphuIW5LseCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22930}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@inquirer/search", "version": "1.0.7", "dependencies": {"@inquirer/core": "^9.0.10", "@inquirer/type": "^1.5.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "72ab9ccfb57f05dd81a8b2df26214588e048be18", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.0.7.tgz", "fileCount": 7, "integrity": "sha512-p1wpV+3gd1eST/o5N3yQpYEdFNCzSP0Klrl+5bfD3cTTz8BGG6nf4Z07aBW0xjlKIj1Rp0y3x/X4cZYi6TfcLw==", "signatures": [{"sig": "MEUCIArUhorKz0hJaB0ZhULBBsu0GPgW0GfZv6vYmZuHa81aAiEA78fOONxNKmk6QRcWzlbWzHc7FoTfG3EjtUopY2FemPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22931}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@inquirer/search", "version": "1.1.0", "dependencies": {"@inquirer/core": "^9.1.0", "@inquirer/type": "^1.5.3", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.32"}, "dist": {"shasum": "665928cac2326b9501ddafbb8606ce4823b3106b", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-h+/5LSj51dx7hp5xOn4QFnUaKeARwUCLs6mIhtkJ0JYPBLmEYjdHSYh7I6GrLg9LwpJ3xeX0FZgAG1q0QdCpVQ==", "signatures": [{"sig": "MEUCIQDzykk6GC4hkQ4EjOnnsk0gZiAeKPZSMtgJtE7ScOgJsQIgEDQdN4cdJzLIwJ30/ulvCu8sL2dP96/cjIH7pe8NU3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28075}, "engines": {"node": ">=18"}}, "2.0.0": {"name": "@inquirer/search", "version": "2.0.0", "dependencies": {"@inquirer/core": "^9.2.0", "@inquirer/type": "^1.5.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.33"}, "dist": {"shasum": "1d7be73d2f81060221706116542937e53e9bdd58", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-I8f7QWIwEVibIqZyalaL3rO2PNwPYYNrNHhafy75D7Xe5K0AjDiJq3wmjVvoO022F+Iib2/f7BOMAf1aJ5o9aA==", "signatures": [{"sig": "MEYCIQDonnS3cbAsM1NMHGfkNQ6pLomYfWFMGSSu0xBIEhxfAwIhAP1IB8iL5Ffrfe+/ZCaYx1FgL9pR5CC1FT/i4PFFrS+5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27975}, "engines": {"node": ">=18"}}, "2.0.1": {"name": "@inquirer/search", "version": "2.0.1", "dependencies": {"@inquirer/core": "^9.2.1", "@inquirer/type": "^2.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.6"}, "devDependencies": {"@inquirer/testing": "^2.1.34"}, "dist": {"shasum": "69b774a0a826de2e27b48981d01bc5ad81e73721", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-r5hBKZk3g5MkIzLVoSgE4evypGqtOannnB3PKTG9NRZxyFRKcfzrdxXXPcoJQsxJPzvdSU2Rn7pB7lw0GCmGAg==", "signatures": [{"sig": "MEUCIClx9A288Knxb71rrNE/YRdEEDXOy7NiFW9fXjhh4P5XAiEA/wnxW/B9NAyU7zB/EVhtclt4qdIQjGfz0mIPa2+aqAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27975}, "engines": {"node": ">=18"}}, "3.0.0": {"name": "@inquirer/search", "version": "3.0.0", "dependencies": {"@inquirer/core": "^10.0.0", "@inquirer/type": "^3.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.7"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.35", "@arethetypeswrong/cli": "^0.16.4"}, "dist": {"shasum": "a5a649349b2c38f06acbbfc92f15197adbfe75db", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.0.tgz", "fileCount": 9, "integrity": "sha512-AT9vkC2KD/PLHZZXIW5Tn/FnJzEU3xEZMLxNo9OggKoreDEKfTOKVM1LkYbDg6UQUOOjntXd0SsrvoHfCzS8cw==", "signatures": [{"sig": "MEQCIFaDVawYsPOVIrzaBzT31WYFaDpq6nc1rVCdyMaBCZgKAiA/9TXzjuX6zANT+S823pZS6NUuVZHGU4Tr8eBVs9Xq0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27132}, "engines": {"node": ">=18"}}, "3.0.1": {"name": "@inquirer/search", "version": "3.0.1", "dependencies": {"@inquirer/core": "^10.0.1", "@inquirer/type": "^3.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.7"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.36", "@arethetypeswrong/cli": "^0.16.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "68a4d23f6fca5a8eb99a61a72f74dc6b193be20a", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.1.tgz", "fileCount": 9, "integrity": "sha512-ehMqjiO0pAf+KtdONKeCLVy4i3fy3feyRRhDrvzWhiwB8JccgKn7eHFr39l+Nx/FaZAhr0YxIJvkK5NuNvG+Ww==", "signatures": [{"sig": "MEUCIQCYNwwadWKmOX9C5KKa9TiSjRYmzfc26BGY5t2BltJyNAIgEK8TrJ5UTTGt20HFZBYt3RpTnQTlU6aBcPXUgA3Sr7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27141}, "engines": {"node": ">=18"}}, "3.0.2": {"name": "@inquirer/search", "version": "3.0.2", "dependencies": {"@inquirer/core": "^10.1.0", "@inquirer/type": "^3.0.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.8"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.37", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "71fccc766045f2ec37afc402d72ce31838768281", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.2.tgz", "fileCount": 9, "integrity": "sha512-Zv4FC7w4dJ13BOJfKRQCICQfShinGjb1bCEIHxTSnjj2telu3+3RHwHubPG9HyD4aix5s+lyAMEK/wSFD75HLA==", "signatures": [{"sig": "MEYCIQCrHL+Oti79A9nEBwIfGCK6G5u1BKwP0toAhoJ9e/usjgIhALhwQlR+RJbVlRMCFGeTcSSnjHnDUBPBA3mz82RCKcj5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27141}, "engines": {"node": ">=18"}}, "3.0.3": {"name": "@inquirer/search", "version": "3.0.3", "dependencies": {"@inquirer/core": "^10.1.1", "@inquirer/type": "^3.0.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.8"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.38", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "791f19a4ee87d65816fd3bb17bd8d76bc11bff07", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.3.tgz", "fileCount": 9, "integrity": "sha512-mQTCbdNolTGvGGVCJSI6afDwiSGTV+fMLPEIMDJgIV6L/s3+RYRpxt6t0DYnqMQmemnZ/Zq0vTIRwoHT1RgcTg==", "signatures": [{"sig": "MEQCIGAqn77uSa/FYTjHzbb8NfTjyFOE3q/1w9LT6KsIW4NnAiAuu8O54y+J3BH/oG0SMP78JDbm9jnlk76wXz8LYVR4qA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27141}, "engines": {"node": ">=18"}}, "3.0.4": {"name": "@inquirer/search", "version": "3.0.4", "dependencies": {"@inquirer/core": "^10.1.2", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.39", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "fcf51a853536add37491920634a182ecc9f5524b", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.4.tgz", "fileCount": 9, "integrity": "sha512-tSkJk2SDmC2MEdTIjknXWmCnmPr5owTs9/xjfa14ol1Oh95n6xW7SYn5fiPk4/vrJPys0ggSWiISdPze4LTa7A==", "signatures": [{"sig": "MEYCIQDK7WjoVprBpI+QBEGlgXlTlwQhoSwJhr20B9d+6s7wugIhAIETvDY0qe1vEGtoi0G8KmeQ37W5EBFub4r4KIWhn6UY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27141}, "engines": {"node": ">=18"}}, "3.0.5": {"name": "@inquirer/search", "version": "3.0.5", "dependencies": {"@inquirer/core": "^10.1.3", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.40", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "798b926a79faaa33ccd9522c455abb050cb05901", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.5.tgz", "fileCount": 9, "integrity": "sha512-INqlGeK85gOmlVY8aosAdOMWgOmpcA7+eDlq5WBdbh8aZbAXX0HItf1GIdDj8zQnh+8Pv0DXU7OvdaLVcV4bWA==", "signatures": [{"sig": "MEUCIQDflYby40j6XJWxDWYhmu6HXaeNTQE2s6gbMrLJnKPQVAIgXCFAaLSnlPHyZqAuuD4uKx84w1BSMJJttAFwv9uJlVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27607}, "engines": {"node": ">=18"}}, "3.0.6": {"name": "@inquirer/search", "version": "3.0.6", "dependencies": {"@inquirer/core": "^10.1.4", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.41", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "5537e3f46b7d31ab65ca22b831cf546f88db1d5b", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.6.tgz", "fileCount": 9, "integrity": "sha512-eFZ2hiAq0bZcFPuFFBmZEtXU1EarHLigE+ENCtpO+37NHCl4+Yokq1P/d09kUblObaikwfo97w+0FtG/EXl5Ng==", "signatures": [{"sig": "MEUCIH4X1TJ8QzSj7hZ7QwFMobbLR1OAhv58cRZCfk37qX2oAiEAhTcGPetYNfI0U851WpTcHPuvnMjUWvPTSD/H3g0wwHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27749}, "engines": {"node": ">=18"}}, "3.0.7": {"name": "@inquirer/search", "version": "3.0.7", "dependencies": {"@inquirer/core": "^10.1.5", "@inquirer/type": "^3.0.3", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.42", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "78ec82bc0597fb27ac6bf306e4602e607a06a0b3", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.7.tgz", "fileCount": 9, "integrity": "sha512-Krq925SDoLh9AWSNee8mbSIysgyWtcPnSAp5YtPBGCQ+OCO+5KGC8FwLpyxl8wZ2YAov/8Tp21stTRK/fw5SGg==", "signatures": [{"sig": "MEUCIAL/FNUvm5oViZ6F/TkUiY5E3eyRVfp1MRrF3NjprefiAiEAyC0srDNYJgbiNbWXRGL7WJ96w9q80td17Bwp8DfuPSY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27754}, "engines": {"node": ">=18"}}, "3.0.8": {"name": "@inquirer/search", "version": "3.0.8", "dependencies": {"@inquirer/core": "^10.1.6", "@inquirer/type": "^3.0.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.43", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "38c25f5b2db15a268be76b09bd12b4599ecc216b", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.8.tgz", "fileCount": 9, "integrity": "sha512-ihSE9D3xQAupNg/aGDZaukqoUSXG2KfstWosVmFCG7jbMQPaj2ivxWtsB+CnYY/T4D6LX1GHKixwJLunNCffww==", "signatures": [{"sig": "MEUCIC4F0J9ykyNrKRpbZm1yVNYPZZWA8/KPCeyWKTp1BcAtAiEAyGPZTVOZwUMT326TWDuF4kWDulxsfp9TvGdYGDXb5vM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27837}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.9": {"name": "@inquirer/search", "version": "3.0.9", "dependencies": {"@inquirer/core": "^10.1.7", "@inquirer/type": "^3.0.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.44", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "00848c93ce86dcd24989a72dabfd8aeb34d2829b", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.9.tgz", "fileCount": 9, "integrity": "sha512-DWmKztkYo9CvldGBaRMr0ETUHgR86zE6sPDVOHsqz4ISe9o1LuiWfgJk+2r75acFclA93J/lqzhT0dTjCzHuoA==", "signatures": [{"sig": "MEYCIQCATVptnLpECF/iV2oMAacpfkJraWS7iUklEYfUBdRHoAIhAPcEo3Pq/0+YeCbXz9cHOPF0FpaLghQelUm2a6+9I80c", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27225}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.10": {"name": "@inquirer/search", "version": "3.0.10", "dependencies": {"@inquirer/core": "^10.1.8", "@inquirer/type": "^3.0.5", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "5e33547f953d4b8b30dcdaa104878c45aa41d433", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.10.tgz", "fileCount": 9, "integrity": "sha512-EAVKAz6P1LajZOdoL+R+XC3HJYSU261fbJzO4fCkJJ7UPFcm+nP+gzC+DDZWsb2WK9PQvKsnaKiNKsY8B6dBWQ==", "signatures": [{"sig": "MEQCICGDlnERpc0KhT7pSggTV74o6hMGK5YLXhloZCNzaly6AiByF9fbPnumQD5gEqzTAX2xQgQ4/rmxBcstEnJl1g3w2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27226}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.11": {"name": "@inquirer/search", "version": "3.0.11", "dependencies": {"@inquirer/core": "^10.1.9", "@inquirer/type": "^3.0.5", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "660b181516acc306cf21dbc1d3d49f662cb7d917", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.11.tgz", "fileCount": 9, "integrity": "sha512-9CWQT0ikYcg6Ls3TOa7jljsD7PgjcsYEM0bYE+Gkz+uoW9u8eaJCRHJKkucpRE5+xKtaaDbrND+nPDoxzjYyew==", "signatures": [{"sig": "MEUCIQDx6CuRRfPLIqgfKp9hB0JjVTK7YQQBLDeS7CX1o+Z+3QIgeztgEOXaxmwlksJsZPMNajct56J2CQqO4nNvu74E8NA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27226}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.12": {"name": "@inquirer/search", "version": "3.0.12", "dependencies": {"@inquirer/core": "^10.1.10", "@inquirer/type": "^3.0.6", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "e86f91ea598ccb39caf9a17762b839a9b950e16d", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.12.tgz", "fileCount": 9, "integrity": "sha512-H/kDJA3kNlnNIjB8YsaXoQI0Qccgf0Na14K1h8ExWhNmUg2E941dyFPrZeugihEa9AZNW5NdsD/NcvUME83OPQ==", "signatures": [{"sig": "MEQCIBDphUs4uYiD/KQeuIiY0E6s7ahZXpHjHCLRsiJZ+1PwAiBhW1RA5nPVxmLdOFZTXoTEP+OC4CpoVEJ2rDkX6NiNJw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27893}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.13": {"name": "@inquirer/search", "version": "3.0.13", "dependencies": {"@inquirer/core": "^10.1.11", "@inquirer/type": "^3.0.6", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "465a5786f3302be39ff94e23512fde51fa3cf062", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.13.tgz", "fileCount": 9, "integrity": "sha512-9g89d2c5Izok/Gw/U7KPC3f9kfe5rA1AJ24xxNZG0st+vWekSk7tB9oE+dJv5JXd0ZSijomvW0KPMoBd8qbN4g==", "signatures": [{"sig": "MEUCIQCijAMhXnXjqUQCq9e8d7r5rYi6JHyhlZF9x2LTZmhW+AIgRkhVC+HYuOcRSKLU8Kloh64fVFFm6mFFThIdWzlUlVc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27856}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.14": {"name": "@inquirer/search", "version": "3.0.14", "dependencies": {"@inquirer/core": "^10.1.12", "@inquirer/type": "^3.0.7", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.12"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.47", "@arethetypeswrong/cli": "^0.18.1"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "7a69c9a33476db652e263ba9f6e8647395f7dc65", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.14.tgz", "fileCount": 9, "integrity": "sha512-+VdtRD5nVR50K5fEMq/qbtHGH08vfqm69NJtojavlMXj6fsYymQZrNqjxEISPs2PDvtsemTJVFGs0uI6Zti6Dw==", "signatures": [{"sig": "MEYCIQCYnCcr4HkCSykCJqI7MWXmRremapcQ/XKZ11ZkkaJYSAIhAI9vERP0UhY7RaEksNu7wJxQO7FflqUf74BfyLKR9WsJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27856}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.15": {"name": "@inquirer/search", "version": "3.0.15", "dependencies": {"@inquirer/core": "^10.1.13", "@inquirer/figures": "^1.0.12", "@inquirer/type": "^3.0.7", "yoctocolors-cjs": "^2.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@inquirer/testing": "^2.1.47", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"integrity": "sha512-YBMwPxYBrADqyvP4nNItpwkBnGGglAvCLVW8u4pRmmvOsHUtCAUIMbUrLX5B3tFL1/WsLGdQ2HNzkqswMs5Uaw==", "shasum": "419ddff4254cf22018cdfbfc840fa3ef8a0721cb", "tarball": "https://registry.npmjs.org/@inquirer/search/-/search-3.0.15.tgz", "fileCount": 9, "unpackedSize": 27856, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGdg+LxOLVWlMyxyPXwrdqBTHSSVkWUzED4/1mV5H9lAAiEA4xA5UcF9MBNBgASRgLE5b0fhvhmGFV2pFmDJMpR5yz4="}]}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}}, "modified": "2025-05-25T20:55:52.616Z", "cachedAt": 1748373705490}