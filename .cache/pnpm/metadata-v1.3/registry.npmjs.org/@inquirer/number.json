{"name": "@inquirer/number", "dist-tags": {"latest": "3.0.15"}, "versions": {"1.0.0": {"name": "@inquirer/number", "version": "1.0.0", "dependencies": {"@inquirer/core": "^9.0.0", "@inquirer/type": "^1.4.0"}, "devDependencies": {"@inquirer/testing": "^2.1.24"}, "dist": {"shasum": "a684171fa67174d1efcb1943cf0333214629e7f2", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-CFQVgqXDi5Yd+BZMLPZN8wO85t0THoK1eDLlBY1wkRuUdxb87Umz4BkVcK9LVM9rTa9bzUSVgJ91waeo6ACp3Q==", "signatures": [{"sig": "MEUCICP5/xlA5w+BXCXdQV0rPHUKP02haOJ6jlEjALHYC7WGAiEA4GkdNI/tlNbiLgjq+s+9av/FT/10lROU7V1do5TkpmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16459}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "@inquirer/number", "version": "1.0.1", "dependencies": {"@inquirer/core": "^9.0.1", "@inquirer/type": "^1.4.0"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "4e0b613c305a7ca49fb286789b5af69a5d890bdf", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-T3q7mQTkaKgNJdgLJ5j1KK1ZAw8rNQwz47BmHJEoqI3nupvUEDHdEtvgRhpPq2brzXZXkcShE2nF9ou1NveIPg==", "signatures": [{"sig": "MEUCICRrp5X0EMKmjSYAWIChId8dtjfHmPkL5B4Sby/Nz8NpAiEA2BEDD1Cz9vZG8NFwQG1F73ZhiPs0tN1Yy0YVLgoq93I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16564}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "@inquirer/number", "version": "1.0.2", "dependencies": {"@inquirer/core": "^9.0.2", "@inquirer/type": "^1.4.0"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "a1e8cff02dba0e0a449df17d843ea73fa29017ae", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-GcoK+Phxcln0Qw9e73S5a8B2Ejg3HgSTvNfDegIcS5/BKwUm8t5rejja1l09WXjZM9vrVbRDf9RzWtSUiWVYRQ==", "signatures": [{"sig": "MEUCIQCShM2R703+sMBqIbFqblWCW2COIxNtXDvwk29bgCXAUQIgDrwIBLgXfWtZfiIbdo46J7BmBU7O4+IIFGFOI9z+DXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17055}, "engines": {"node": ">=18"}}, "1.0.3": {"name": "@inquirer/number", "version": "1.0.3", "dependencies": {"@inquirer/core": "^9.0.3", "@inquirer/type": "^1.5.0"}, "devDependencies": {"@inquirer/testing": "^2.1.26"}, "dist": {"shasum": "47f6f693b44b8aec6549745e7fcdb24a08f3e8bc", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-GLTuhuhzK/QtB7BhM2pLJGKsv366kv237iNF8hTEOx+EGmXsPNGTydAgZmcuVizEmgC9VSVh1S0memXnIUTYzQ==", "signatures": [{"sig": "MEYCIQCzoxAPYgEAqweDc46R8hG0HGOjXDbwvCsYFVRne2MUdgIhAMjYv89cwMS5QwM3vaZMKxL5DufOcecDlEdTohgIoHxE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17061}, "engines": {"node": ">=18"}}, "1.0.4": {"name": "@inquirer/number", "version": "1.0.4", "dependencies": {"@inquirer/core": "^9.0.4", "@inquirer/type": "^1.5.0"}, "devDependencies": {"@inquirer/testing": "^2.1.27"}, "dist": {"shasum": "2805d4c2644f2955ebd2de53c91faf4bf25e1252", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.4.tgz", "fileCount": 8, "integrity": "sha512-kDa06HLzkVUzWnp5APF6JeQw6PCUc5hSQEyFYl8MoIRoJP0Psbf3Ys47skEpASqsSXcCjBy+3dEiFyhL95cbBA==", "signatures": [{"sig": "MEUCIQCOnCQr1jhyWm1QODIGixLJpm64onw4wwTL/GpcqzmEcgIgBOYCY+mTohc6udhR/bAodgaIboWV6Gcu1kcQ8wRQQ/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17059}, "engines": {"node": ">=18"}}, "1.0.5": {"name": "@inquirer/number", "version": "1.0.5", "dependencies": {"@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "541524d8509a265f03c6583fd23e7017191cab4c", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.5.tgz", "fileCount": 8, "integrity": "sha512-+H6TJPU2AJEcoF6nVTWssxS7gnhxWvf6CkILAdfq/yGm/htBKNDrvYLYaJvi1Be/aXQoKID9FaP94bUCjOvJNQ==", "signatures": [{"sig": "MEUCIALpaOFHK8rtzpva2/j80sf5rCDzO+RjBWYOjTbncuwgAiEA4HRpvn2Z5eYlriakEMcioCJYAPzF6pCDvvWe9CF+xC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17225}, "engines": {"node": ">=18"}}, "1.0.6": {"name": "@inquirer/number", "version": "1.0.6", "dependencies": {"@inquirer/core": "^9.0.6", "@inquirer/type": "^1.5.1"}, "devDependencies": {"@inquirer/testing": "^2.1.29"}, "dist": {"shasum": "4ae5188ca1c231001ec28ae1c3fe0e47e963b36d", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.6.tgz", "fileCount": 8, "integrity": "sha512-e0qI1hFGRT4HGhzvd/lUvio7knGoUsj7sN+vVLFUJNyIUCo21Z+avcwoyCdSWzt5OxA0hLdTBSLzlZi6lHOsEA==", "signatures": [{"sig": "MEYCIQC7NrrchpJvENDYzIWjj8QlpM2Q8HBMiBmjD70LwiL8oAIhANKbsC1U9ppkVijDJcQIlMDdH6S1T+qVqMx+RiyU3/6Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17225}, "engines": {"node": ">=18"}}, "1.0.7": {"name": "@inquirer/number", "version": "1.0.7", "dependencies": {"@inquirer/core": "^9.0.7", "@inquirer/type": "^1.5.1"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "712174ca215bd70fa85e3161453dbefe4e46bdfc", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.7.tgz", "fileCount": 8, "integrity": "sha512-SGABJSqLJeoYV078OwqZ74XwhSLc3Yn6xA3UZD093TXnIm5ggts3h2Pb3+EFWDFekKmUzuTeEkCo4cxaj+bnEg==", "signatures": [{"sig": "MEUCIQC+gVuRkuKFYLKT6Gjr6z4dkkKVUV+UUhDPvjYZIZKiCwIgPfCkW4aZ8uxVsAXVQzkU7FuOCBh/MCTMEnSgMohzIOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17667}, "engines": {"node": ">=18"}}, "1.0.8": {"name": "@inquirer/number", "version": "1.0.8", "dependencies": {"@inquirer/core": "^9.0.8", "@inquirer/type": "^1.5.1"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "46d7380cd8ae99a8d36047e255c522bc17126195", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.8.tgz", "fileCount": 8, "integrity": "sha512-GamytM0a3fLh8xjgWbGb/DmDA1SmW6sc6ZyfiiWL1my2NAkV6mrTEKMOA4LSK2gB43uf8vcOS7Hp/LeVjIqLwg==", "signatures": [{"sig": "MEQCICJbBCnlR5XmnbRp+x76Ue99rGhqsrfr45HpH95DECI6AiAjX4bcADPLwwvNTYtEpyP1lAJbDnkRQIHi4Mgx+klNFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17667}, "engines": {"node": ">=18"}}, "1.0.9": {"name": "@inquirer/number", "version": "1.0.9", "dependencies": {"@inquirer/core": "^9.0.9", "@inquirer/type": "^1.5.2"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "f90c76aba3b1927bfb3691f88a40f39d89fb6f05", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.9.tgz", "fileCount": 8, "integrity": "sha512-F5JqBCPnJTlLlZavRL15jGAtCXZGQiT64IMe2iOtcVIHQYYWecs5FpyqfkIDqvuOCyd4XgWPVmjeW+FssGEwFw==", "signatures": [{"sig": "MEYCIQCgyyHLv8M4blHzhaGpLIVD0s3MrX4n4tlgBCUQxQ0fZAIhALrum7Hq2EqlJoAofw+r2rtrnZ089AJ2HEMhUabLZ45n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17667}, "engines": {"node": ">=18"}}, "1.0.10": {"name": "@inquirer/number", "version": "1.0.10", "dependencies": {"@inquirer/core": "^9.0.10", "@inquirer/type": "^1.5.2"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "ac2b440ca57b5de5a231e4898c12d4453683c055", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.0.10.tgz", "fileCount": 8, "integrity": "sha512-kWTxRF8zHjQOn2TJs+XttLioBih6bdc5CcosXIzZsrTY383PXI35DuhIllZKu7CdXFi2rz2BWPN9l0dPsvrQOA==", "signatures": [{"sig": "MEYCIQCN6os6Gbaoaas9Y2yFeAC867Y55HZbunZ6hcHDDd9PngIhAOdvyUPrRSeRrPVtPGRUxyoi5OYyA4oPkBkWan0ywcfM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17669}, "engines": {"node": ">=18"}}, "1.1.0": {"name": "@inquirer/number", "version": "1.1.0", "dependencies": {"@inquirer/core": "^9.1.0", "@inquirer/type": "^1.5.3"}, "devDependencies": {"@inquirer/testing": "^2.1.32"}, "dist": {"shasum": "4dac004021ea67c89552a261564f103a494cac96", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-ilUnia/GZUtfSZy3YEErXLJ2Sljo/mf9fiKc08n18DdwdmDbOzRcTv65H1jjDvlsAuvdFXf4Sa/aL7iw/NanVA==", "signatures": [{"sig": "MEYCIQCFh88r5WmvxiAHrBrIZfQgojDBdn6aCEyDRrfdfzqq6QIhAPYoWFxmyr3Db4aOE3sFAaFRFtd/LGDiPD34M8/9+RZf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17667}, "engines": {"node": ">=18"}}, "2.0.0": {"name": "@inquirer/number", "version": "2.0.0", "dependencies": {"@inquirer/core": "^9.2.0", "@inquirer/type": "^1.5.4"}, "devDependencies": {"@inquirer/testing": "^2.1.33"}, "dist": {"shasum": "fa292d1f4f87014b62790e58f248bfc3c4d001ef", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-nmmg5y6uJSz6RwBZMOkY1h2s+RQ3+iudxDPeD87Uesx7uC4dCZ/A/CRZye7TGCElLw+s/y5TY+Kkk3FEWdJV5g==", "signatures": [{"sig": "MEYCIQCvKFxsIj4rlSuV46O273o0wW++Z9vBiI2OE+D2nm2WygIhAOSfKYKm+2dGnZuPCb9panSTVDwF43r1+rJD/d2t8F4M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17641}, "engines": {"node": ">=18"}}, "2.0.1": {"name": "@inquirer/number", "version": "2.0.1", "dependencies": {"@inquirer/core": "^9.2.1", "@inquirer/type": "^2.0.0"}, "devDependencies": {"@inquirer/testing": "^2.1.34"}, "dist": {"shasum": "b9863080d02ab7dc2e56e16433d83abea0f2a980", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-QpR8jPhRjSmlr/mD2cw3IR8HRO7lSVOnqUvQa8scv1Lsr3xoAMMworcYW3J13z3ppjBFBD2ef1Ci6AE5Qn8goQ==", "signatures": [{"sig": "MEYCIQCDK7spdDVC1RlJFn74kyVL3G37PvnuUOYXUCuixvcypgIhANJyUZ1YOoEYqPgANNL2ybT5k6wXTzQCtefC9veEBV2w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17641}, "engines": {"node": ">=18"}}, "3.0.0": {"name": "@inquirer/number", "version": "3.0.0", "dependencies": {"@inquirer/core": "^10.0.0", "@inquirer/type": "^3.0.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.35", "@arethetypeswrong/cli": "^0.16.4"}, "dist": {"shasum": "6191f1c04b576a1c78ac3fa95af30bf1ef00ff18", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.0.tgz", "fileCount": 9, "integrity": "sha512-DUYfROyQNWm3q+JXL3S6s1/y/cOWRstnmt5zDXhdYNJ5N8TgCnHcDXKwW/dRZL7eBZupmDVHxdKCWZDUYUqmeg==", "signatures": [{"sig": "MEQCIEYEv9pCjK1ITLuS+r9MgIvvEIdI2xsPhgtTjhSpNehEAiBM3/RqY7L3mDaMMWyYFl/dSIbO9M11GWTb0Wau2QzZpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16538}, "engines": {"node": ">=18"}}, "3.0.1": {"name": "@inquirer/number", "version": "3.0.1", "dependencies": {"@inquirer/core": "^10.0.1", "@inquirer/type": "^3.0.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.36", "@arethetypeswrong/cli": "^0.16.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "21666eff686c9f97396d23ae58f62d244d6338d6", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.1.tgz", "fileCount": 9, "integrity": "sha512-gF3erqfm0snpwBjbyKXUUe17QJ7ebm49btXApajrM0rgCCoYX0o9W5NCuYNae87iPxaIJVjtuoQ42DX32IdbMA==", "signatures": [{"sig": "MEUCIQCk+hkP64aALQc0jUj2c2DrgwztiT2AKD3dXX4Q02yD6QIgHBNvqLymtOkk+GYeHcCYSZobVhDV9CJfRDSEC9Nqr7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16593}, "engines": {"node": ">=18"}}, "3.0.2": {"name": "@inquirer/number", "version": "3.0.2", "dependencies": {"@inquirer/core": "^10.1.0", "@inquirer/type": "^3.0.1"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.37", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "7e8315b41601d377cc09802b66f32b481e14fd68", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.2.tgz", "fileCount": 9, "integrity": "sha512-MKQhYofdUNk7eqJtz52KvM1dH6R93OMrqHduXCvuefKrsiMjHiMwjc3NZw5Imm2nqY7gWd9xdhYrtcHMJQZUxA==", "signatures": [{"sig": "MEQCIBGo28vpvkgfgBwdSppoOBj4ypTYjmfYDNDRiA+9FlSNAiBJ6omvY6RVAhqA47+CIzc8bhq8yZLzQ/R+1pafMBNzRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16593}, "engines": {"node": ">=18"}}, "3.0.3": {"name": "@inquirer/number", "version": "3.0.3", "dependencies": {"@inquirer/core": "^10.1.1", "@inquirer/type": "^3.0.1"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.38", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "e3dd7520e21e9708fea9465b73d20ac851f5f60d", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.3.tgz", "fileCount": 9, "integrity": "sha512-HA/W4YV+5deKCehIutfGBzNxWH1nhvUC67O4fC9ufSijn72yrYnRmzvC61dwFvlXIG1fQaYWi+cqNE9PaB9n6Q==", "signatures": [{"sig": "MEYCIQCEFjb0jtiPI/JcSAC3Ja6Kul/9g44DKEhTfrG294RCEgIhALZZHAEJ83u2l4JR3CQ5fEyrhTBwmGVIzfuknjir0sGk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16593}, "engines": {"node": ">=18"}}, "3.0.4": {"name": "@inquirer/number", "version": "3.0.4", "dependencies": {"@inquirer/core": "^10.1.2", "@inquirer/type": "^3.0.2"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.39", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "090dcac6886d0cddc255f6624b61fb4461747fee", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.4.tgz", "fileCount": 9, "integrity": "sha512-DX7a6IXRPU0j8kr2ovf+QaaDiIf+zEKaZVzCWdLOTk7XigqSXvoh4cul7x68xp54WTQrgSnW7P1WBJDbyY3GhA==", "signatures": [{"sig": "MEUCIQCfdCCqePBxst6DCL2ZX6fcX+3fLoWKwwiKH7dqC+VgnwIgHq+NgDMsJlUC5TKG+7/juUQl9JVs93BX1Q7ZkEUrahk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16593}, "engines": {"node": ">=18"}}, "3.0.5": {"name": "@inquirer/number", "version": "3.0.5", "dependencies": {"@inquirer/core": "^10.1.3", "@inquirer/type": "^3.0.2"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.40", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "603dc92b23ba1fc0c0c14f8ece0db6e785b37a72", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.5.tgz", "fileCount": 9, "integrity": "sha512-O/gcUwhS0TzBdBszYues3B4PTwyOLo51RctvXPRGtDfwIftuTTdPnm3K7oiK2OC2CDc7eG4UNa+QtdLlaJxIOA==", "signatures": [{"sig": "MEQCIBtpNfrdhz39RPDGiCbRt3/eDQvcaIogaTJuex6S66QPAiBqC53ScoHKdD+gGKmOnJQn3BdqB74JpIy7Py4ZBTh5uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17059}, "engines": {"node": ">=18"}}, "3.0.6": {"name": "@inquirer/number", "version": "3.0.6", "dependencies": {"@inquirer/core": "^10.1.4", "@inquirer/type": "^3.0.2"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.41", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "19bba46725df194bdd907762cf432a37e053b300", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.6.tgz", "fileCount": 9, "integrity": "sha512-xO07lftUHk1rs1gR0KbqB+LJPhkUNkyzV/KhH+937hdkMazmAYHLm1OIrNKpPelppeV1FgWrgFDjdUD8mM+XUg==", "signatures": [{"sig": "MEQCIBhdRZO2+jVXfq6D7FUtP9oNGcHLNW4TudJr1oUejzq8AiAYq/GcRbY3L2CUVbN8DD3Fagc7MdFU2B5MbK/VUro1Jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17201}, "engines": {"node": ">=18"}}, "3.0.7": {"name": "@inquirer/number", "version": "3.0.7", "dependencies": {"@inquirer/core": "^10.1.5", "@inquirer/type": "^3.0.3"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.42", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "50bc394cda68205025e098b0cdec716f6d100e56", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.7.tgz", "fileCount": 9, "integrity": "sha512-uU2nmXGC0kD8+BLgwZqcgBD1jcw2XFww2GmtP6b4504DkOp+fFAhydt7JzRR1TAI2dmj175p4SZB0lxVssNreA==", "signatures": [{"sig": "MEQCIGX/IIP3/eUt6vBwbNlhnzFdyAkEesKGQWKafWYZUAvXAiAzIqJ18r7WTcal7TNtyJofxzTChUV4FnQJMdUYAFcezw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17205}, "engines": {"node": ">=18"}}, "3.0.8": {"name": "@inquirer/number", "version": "3.0.8", "dependencies": {"@inquirer/core": "^10.1.6", "@inquirer/type": "^3.0.4"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.43", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "ca44c09a8ac74040e2327e04694799eae603e9de", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.8.tgz", "fileCount": 9, "integrity": "sha512-CTKs+dT1gw8dILVWATn8Ugik1OHLkkfY82J+Musb57KpmF6EKyskv8zmMiEJPzOnLTZLo05X/QdMd8VH9oulXw==", "signatures": [{"sig": "MEUCIQCJph9KYwaM8r46AbIShbn7dcciUm7F6Jyv9ljtGCYaiwIgaTniskWxQoyc0iJ5Qd++0IMc0Q04oTSKM/DYtOq4smU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17288}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.9": {"name": "@inquirer/number", "version": "3.0.9", "dependencies": {"@inquirer/core": "^10.1.7", "@inquirer/type": "^3.0.4"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.44", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "23dae9e31de368e18c4ec2543a9f006e4bb4a98d", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.9.tgz", "fileCount": 9, "integrity": "sha512-iN2xZvH3tyIYXLXBvlVh0npk1q/aVuKXZo5hj+K3W3D4ngAEq/DkLpofRzx6oebTUhBvOgryZ+rMV0yImKnG3w==", "signatures": [{"sig": "MEUCIFaVuSuAiRE++kDfI56Bmkstan8iPzCRP2HzfO4QPGy0AiEA7PjywWEAVQIkXHWip7TJzBPM+RlNS5NolUbr48sAIDk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 16676}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.10": {"name": "@inquirer/number", "version": "3.0.10", "dependencies": {"@inquirer/core": "^10.1.8", "@inquirer/type": "^3.0.5"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "3ad1d2b69849521169af8b3efe838f97ba010350", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.10.tgz", "fileCount": 9, "integrity": "sha512-GLsdnxzNefjCJUmWyjaAuNklHgDpCTL4RMllAVhVvAzBwRW9g38eZ5tWgzo1lirtSDTpsh593hqXVhxvdrjfwA==", "signatures": [{"sig": "MEUCIEuBzQHsUP2VLYgwvdgqybggb8wIMEk5S3jBOf6XwpFmAiEAg6NtqHjsKlb+FIU+opC7dxbkZzZ5tTh2MTkIkCDylZY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 16677}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.11": {"name": "@inquirer/number", "version": "3.0.11", "dependencies": {"@inquirer/core": "^10.1.9", "@inquirer/type": "^3.0.5"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "b42b7b24e9e1916d26bbdc7c368852fdb626fa9a", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.11.tgz", "fileCount": 9, "integrity": "sha512-pQK68CsKOgwvU2eA53AG/4npRTH2pvs/pZ2bFvzpBhrznh8Mcwt19c+nMO7LHRr3Vreu1KPhNBF3vQAKrjIulw==", "signatures": [{"sig": "MEQCIDYFse+88WibYC81EOLF96dlMk9MVLiTDc3pHDQo/yl7AiAtWoL2jiGncbA2UQQ0W4eG9CMwse3aC+8xOg76LzIpVw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 16677}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.12": {"name": "@inquirer/number", "version": "3.0.12", "dependencies": {"@inquirer/core": "^10.1.10", "@inquirer/type": "^3.0.6"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "e027d27425ee2a81a7ccb9fdc750129edd291067", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.12.tgz", "fileCount": 9, "integrity": "sha512-7HRFHxbPCA4e4jMxTQglHJwP+v/kpFsCf2szzfBHy98Wlc3L08HL76UDiA87TOdX5fwj2HMOLWqRWv9Pnn+Z5Q==", "signatures": [{"sig": "MEYCIQCItJIvG2jMVHW6hByr9L8phcasroLw8zGiiuFLh3p13gIhALyR1uph/4U85ihk6nC8ntrHHotsIMHhuBudvhta6Rmu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17532}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.13": {"name": "@inquirer/number", "version": "3.0.13", "dependencies": {"@inquirer/core": "^10.1.11", "@inquirer/type": "^3.0.6"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "7bef02085be742ede6771c5fb036201ee3eb6df7", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.13.tgz", "fileCount": 9, "integrity": "sha512-IrLezcg/GWKS8zpKDvnJ/YTflNJdG0qSFlUM/zNFsdi4UKW/CO+gaJpbMgQ20Q58vNKDJbEzC6IebdkprwL6ew==", "signatures": [{"sig": "MEUCIQDkoV6UfzztO6v8RoxmUjT/BE354qw8AkAQSrhrGPRVWgIgB7O75EGoh0OV/K5X40QYnh8S4vjOs0AgPobAQAViwgg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17495}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.14": {"name": "@inquirer/number", "version": "3.0.14", "dependencies": {"@inquirer/core": "^10.1.12", "@inquirer/type": "^3.0.7"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.47", "@arethetypeswrong/cli": "^0.18.1"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "2502f734f3e02ac643ff39a7a55a8eb052c6ebbf", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.14.tgz", "fileCount": 9, "integrity": "sha512-8B4jX8ArK9zvb8/tB04jGLja4XoFfjvrTLJ5YeLlFnJh3jPa9VTQt2kxJZubGKc8YHX68e1XQxv4Nu/WZUnXIw==", "signatures": [{"sig": "MEUCICEibTOIZKIas+F7/uyB0JRdRyEcF+xeMynC7LhwewXbAiEA/+KQrptKCNTRthJq+tH9Fw49lD4FtqUe0fr1oaylUjI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17495}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "3.0.15": {"name": "@inquirer/number", "version": "3.0.15", "dependencies": {"@inquirer/core": "^10.1.13", "@inquirer/type": "^3.0.7"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@inquirer/testing": "^2.1.47", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"integrity": "sha512-xWg+iYfqdhRiM55MvqiTCleHzszpoigUpN5+t1OMcRkJrUrw7va3AzXaxvS+Ak7Gny0j2mFSTv2JJj8sMtbV2g==", "shasum": "13ac1300ab12d7f1dd1b32c693ac284cfcb04d95", "tarball": "https://registry.npmjs.org/@inquirer/number/-/number-3.0.15.tgz", "fileCount": 9, "unpackedSize": 17495, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD3iW5mzNygB7hmihLz3QntBuUcLjvG8VEMn9JheAwAjQIhAKIoT7OpaaPwMXwpMEDi0IR6H5NNkalmB0p2U5NQm7Sk"}]}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}}, "modified": "2025-05-25T20:55:52.490Z", "cachedAt": 1748373705385}