{"name": "@inquirer/editor", "dist-tags": {"latest": "4.2.13"}, "versions": {"0.0.2-alpha.0": {"name": "@inquirer/editor", "version": "0.0.2-alpha.0", "dependencies": {"chalk": "^3.0.0", "@inquirer/core": "^0.0.9-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "e9234ae1a37f3c88bf2c44c4ad2beadbe8de200c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.2-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-gH7zpLVSWwlyy92M9Y1i/qcoCUABPGjzNT83Ce43K+5POExk+zcD2cT+H6/7N3PJbd1XxHxbm86qxpaQecwGsg==", "signatures": [{"sig": "MEUCIEGLbr5sOaoqbw0f1JsxthzRduvLL+1pZtfmZ3hT3iohAiEA4guE8MqIhmBDu263vGG9QzWhH7cvsGwK5nA2YiW9CwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWf9dCRA9TVsSAnZWagAAUtUP/ib+iVUO59n7vn/8x5/k\nzlXPVEuvMQwjQSwXTNsd92S4OU37hx+kYGjrQkEkGoSqALQk8zbfcBQk0LUU\nWenxc8ssc4ETCv37fhd7YDj6l4U6ubafH+WChr/+Nb1EXF/wiIZeO2N/QWKM\nu/g5/juOZ/2Yyma/tNhlh0d/0d7kG+uj4WghisD/AJr/3sT1sCYfC2inkE5V\nFY0lO4QDp/i62CoFsFmzCMDqByNFGJYq6rdn3e4Qs6Fy7OSac8dep04Ozb3A\nLHn4UQcHxfi+AjiMsyNW0FI0jAOMN9BELf/5LP55T7gFyP5vu+4YdvlJ3DMq\nHKgH6l063QX621Rmvpf+XXExm7EUi+Myz5CLKaSRv1I/Yi+JDjsOLlvDAfkf\nIXXUD0yy/9O2YutxbfZi5l+NWD5eh9i+9r6T9s08/L+DVl014A2hzifDQ15m\n+YgrR/e0P3twHPNGOao7IDdTewDfbheklLWYQNdz8GWha35xEfC3m9+FpPu9\ngKU9vniaXyYlvzR8jBKrWze7EJEEASjE3J0+ye6+gqkYwuGj3FsodCMzxKUG\nL0tkbDTrSC3GIXLWH8gPV5OLoOPJL01oX/3SEzZt8J/TD229oFI4kRvJ2oJh\n9H0ANlZ+aft64bJHxZKW0ifyFJ+jYHvyZAPSzGQvleyutrKVjg0ViXp6V1eK\nn4kr\r\n=HUcD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3-alpha.0": {"name": "@inquirer/editor", "version": "0.0.3-alpha.0", "dependencies": {"chalk": "^3.0.0", "@inquirer/core": "^0.0.10-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "5f79a0878177c8c49cd549d52b12854addd64921", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.3-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-8LS0GkClEvYVBG0K4u4CnGr4koupfPhMEHtbnJqkMM7yjGhu3S4VDRc0pzHN+jmXMLzf/nAZgkFrfQJwFEYMtg==", "signatures": [{"sig": "MEYCIQD2JP7vh5H7dGfh508MMXER8ffwYV32KddAB+SoFHGGMAIhAOOwjoBFVqgu+Yp6CW/Jzpy26PEd6USgJ5QY4RnGeut7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5tRUCRA9TVsSAnZWagAAIqIP/3OjuVbC6XnOGG3GhAM4\nrwuviy3JlK6iuDK3Zq2KDN1k5UGCXBJtLOP4ic2L0QaeyUJpKaLeXXrJS/fb\njSKjBTLiIlBL6ACTQDNF7zUi1/osiVwadh1j44zhGtCa7hOeVjNY1nc6zXe1\nZm/VdiSIaJXrxh+ReRhuPXTOr8uSgGqXDJXpN3qXKSnOEkgxJLe0m0If7plf\nKflOwKFsQ+w3dcftNwYC+kyFQNOpecj5jqUDQ0d0/MaRx8odMumMpHmeZBdv\nzLk8tmHPmJemNQSusNEGmiNAI0R/nElGQsUXTzJCZ/FziNTcKYBZH1r0hPy0\nb3Ckc7/qwDi27JLXrxh8kauMs/Z88YkV5+3+agbvpXOaqONrK/4zK0Ia2L9r\nEuwKBnh/KpVrLpEKIrlxK7NTJVhPXl4SJD1IpomX1orZ2GLsiSU7tn29U50b\nix++rT/Mr9IasbqQ5gbEtTn6Kl/SzUJ3XZVoHI8LuaP/lMjICUTf+KVsVazJ\n9dCdMrTXivKuYEi1CfFVLxb2QF7Ti5kMe6DL6G6Kxj0mDfX2UzBzP4sAJqc1\nje2sfX0mpziyaQ/gQ/LjFwg7ddjXfn3jBHztx6j7+nfGllGGeEdNEk9LAnvr\nJ8PSCqL7X+toZ4cs/i5xoQy1Wri26PV5sro77zyGYhhDSwSqhy6T+nouKrMx\nvTRj\r\n=XzXW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4-alpha.0": {"name": "@inquirer/editor", "version": "0.0.4-alpha.0", "dependencies": {"chalk": "^4.1.0", "@inquirer/core": "^0.0.11-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "deebee2a0983898afc8de620cc1e6532d5669521", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.4-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-ATklUluPcWlT3NstcLFE6TpKmvTrAJ4gHGidrUtM+rT+8WvC/L/2k3xzAlh+4r7d8eG2fVw1lG4/OODYatWiag==", "signatures": [{"sig": "MEUCIQDiBbbrgH5KVMr2SCM/9Iu7XhfJ26zWzJRBv3FtM0KadAIgKvLxbZEOgM+8mWNqd3FW0F1Y09B9Mht2Mg8SmKATCXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/WEBCRA9TVsSAnZWagAADkwP+waXSX7yz/EpOApp8Gzn\nKnxavP8WcVNPDNU2oWzbnorXl5nTmwfKNjGdv2hLSugk0y4/Ikz228m3mvi5\ntvXMy+h+PA138gjbWzYrmWU27m6Iypdur2onQxQJPG+DiEoG48sKeQNai5Hm\n1JIF2nBcZpxZ6FCcdSLrqgkMrkm7Y42aF14Ll5ub6hZ2IeaNdRz2scRRqMUN\nuV4qThNwpVbJBgIaWA6FplEyfqFfX9A6YsbhOrSTqbl4V81echYw+EXMFEnZ\nrn/6TZfpXepW4qvIrdrnH73JUJrJQFS2w6zlBDUgVwJnSW2fML4xjmygmm/A\nm+0LCx2AuyGjpVmUFmrfEhDsqEjiPtnp8e3tL+bOOz2b9OePpJSxYOQD8qB8\ncF8VU+k4jIkeLRHNFDqy8QtfGItJb3K93M9si/ZJcrZffWBgKPlrTvSPMAU5\nblKic45rWLueWgvTTuiMWVAGsRZx8XBUcX74+8jNWT9bZwKnYrTrQxqi3Pjq\nyh11dW73UHMvZYFZjn+EEO4WSyKTF6f/DpsVVjuYaVpAU0fP5MJBg7YbeaPE\npPhn8YDJJgc2uPxI1fsAYznR8FNqy8rh01UunfCHYa+gUPDPFNFzUazbi1Eh\nH4Py5IIchxUdfGuz1bcFZWFtyzE4OUjjt5J6BeeFHiXMWmrr8zLOWvn6cHI3\nCUan\r\n=/FYD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5-alpha.0": {"name": "@inquirer/editor", "version": "0.0.5-alpha.0", "dependencies": {"chalk": "^4.1.0", "@inquirer/core": "^0.0.12-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "025fa42a1da6481aaf958d6b796471fffb9c597c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.5-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-sVlaXOe36aMWd8efOe/yUk3gx0EuJBRX/jxfRGKM4GI1E8VfoSaT2uC6eS4EcI/7esomsCf4jdY6oDzARNb0Tg==", "signatures": [{"sig": "MEQCICQ/6bNyHA27hnsG5duXTN8dRcxoeChae4/rpJIOQJRxAiBYjqRh9FENAjx7kjjY0D+gEzCIybTw0pZ0nnol7acYjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCIynCRA9TVsSAnZWagAAo4UP/A+I3msHzdKH11os3e4B\nzdMwvtMlT8Eebotdhva/Ocvi8E2BFgbwcDTVaKq9fbpT27IHbI70xBFZ4IQg\nGQUwPAmGtSm9F2WF9h1cRuL4kF+9IEGbIp4zmkznJxQv7YV8untCnURhEHeN\najkn1bO3WGkIMzDAtgHAVuG7+0TDuiQzsQm0g9R7H9TKVFKNjUg5byN3J1YH\nIbWQjUx4jpsMK2dDGE9BOpb+2g8FYap6Pp3O0xjFWzUNhKLw1oI2B9Vn3D1w\nCQD2Sol4C4VwBjzWxjU22d9cF/1mXo/yyOQ4cdgGV7AzTBdMqq0iHC2xihKv\nPEsw58tVmJHTle7G+7YUA3MXFMEd5tnPUsqQSXXx6+OmnrWi3GCv8SYkEuXo\nY2xU/yFgjSEh7/qkkog1zz0mNFUahYKURywL348SJofsafq0prOHoEaBjGho\n1bkL0EOcu2lPFmJBar7sI7dxr64dyB27DVenZUBq5ttpfTNC75ZvqFZC2gaO\n+r+EHhVACCHQR1Juna77nNpJzyZuii5lp1s/vmy/PbXTm9lxEUFSSzOuzmlM\nZADnAYSYRdHtiegbu5L1zEQaamoV8tgSE5ud7p7+VYCNWXDxmRoXDWhfHTqB\nun4K9S6c34AexpM6tWfWkf+Y8g9WUIL1mP/ixLRVjNpxAXwaDYql0q7vIC1b\n1hxo\r\n=thP1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6-alpha.0": {"name": "@inquirer/editor", "version": "0.0.6-alpha.0", "dependencies": {"chalk": "^4.1.0", "@inquirer/core": "^0.0.13-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "329491ec07c4f98b2f297ad98864ae2a83041b9b", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.6-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-vqvMyUcH84KpCLucKv2Y7e2PVE98xNRa8CU7LPtKUaWvyRL9aWf3jPqFYFO8Q2HgEghbq9WoCuFo+D305cbt2Q==", "signatures": [{"sig": "MEUCIQDNYjzkZQLwYzctFy5RPsRoYe438p36kt0JZIbdPOfXUQIgS9a0yJL5XB1MNifp+ksybNuDNX/vNTrJfVGU3Aejieg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3560, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGPbMCRA9TVsSAnZWagAAwHwP/R723xaGpyTKGKAFvnmy\n8rqG9L9fZosy0KmtkzfSqJtbbjv1PNhPk38U7k9/RWNvJH5d00l0R92HSSPy\n0sRsuINhjY8Ys5iHXWS87VFxYCH+ZVW5FeNPLL8y/eBP74EwRxJA8jeWOKT8\n8fvXJ/DfUCeElPdX3XbuoeXugeuTKcrC3MxHO9+sazfsrCCAIr/h3R9gCqnH\nAP1xwVjpjnHg0LfnJKMuuZxjcEzqOQnpw8QRAXlG8FmWp18Q7P7uuSrJGT3G\nd3vFB/NnyTZDZh99TsqNjMyuBwBzTvwnlqZy/ryBUc8Db+XuYrABNA13H7yP\nozq2Mp5SRS+N/g6US4hOi5PkuNUYG5bYr8ahfA9Jt01cdxF6G9t2zhYpA5GA\nXzUrvFTxyXJfUk5AQtR4uHhYWqXoYh9oBUTvPTKKDJ5zzexUcGrD/9xSIhjl\ng2wj6wW8c/aiFLzzlOuAsl6zxwDnMLjelNHDMZ4KS7pkCL7ikbgL6EjvKnTm\n4ZsgYvb+rs7hH/DR4gkXtIA9Z5vNJ3k8pKlX8/KsbWYrDrVf65mPmGbS30+9\nfzqM4lS+Kr5GVH8P0VMsD6cuaCq/bNBY9bIwbco0uYzemj06v1TruHse0XJl\n6vhH6tIi4swktyU0IoiDNoB2k6rL5BsGnD3aUV62Psu8KFuShexly90Sf1v/\nqhMF\r\n=0asm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-alpha.0": {"name": "@inquirer/editor", "version": "0.0.7-alpha.0", "dependencies": {"chalk": "^4.1.0", "@inquirer/core": "^0.0.14-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "c5de83a8dadec32a850960da29f2392df64cf192", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.7-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-clNCmP6cBb02pbBmgqV946hZKxNr1xgAN3BpfxwSR/oyD3BccHS8h57ONwayyh+Dw0nfOd2OO9pm3QQpBZZZMg==", "signatures": [{"sig": "MEUCIQCdWTiloRsxhR+BP7EOjxRQrLI8CvI5nAYxxSbvCrbEvwIgHshlw9jDaPXY/0b/KbrtPpu3otOUZRW976KmJmEdokg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODZTCRA9TVsSAnZWagAA6vYP/i0ZtlnFnHPxMVhgLZ8o\nnuUMYfetyt2oab3VBXUWHrfvsdOFbmmZwlqfnh8w3Bv+3NIUsk68e7q8gy4M\nwDpxUHZHaNQ6lup8JayXEpvGdd7JvGbYx6TecQuW3aEvhNsLOeA0bhPYb8D6\nwWRfPE2kMsXMh9ARk5xL7JaGdd95keO5sCZrk1iCpOrR/I6CXyU8uOoutAGE\nZ97qMZ6Fc4fBoljpfDeCMGCjtDbW7ZSy+iaqYsRNS0vyhX3AC/jU/g1zNAix\nsMGU7Kubzo6No71yCO3MItRJug785vlcTbqOsR0r7qBJQ0Wr0Qo7vgC1KHx8\nMZnPLdO1O1m8WuoxaRhmJBVRQ3xoPU2Rt4yK8672OYY2LoC3PMVReHmzBp7h\nbZkHtmfmK3ETQMo9dP47ia/hTvf49puPTWv6Ie9DNR0afkvufx+kluz6fFUt\n3SzXjjEX1b4V80xn1mYS95TANzWgWson78eX7spVwVJwxBEOVAW1S0tYCWTA\n4aBclCr2ZLscOvqCfaGm3f+GYWwFadDXpI7n03/hDNY4/SzYKcoxtVyjzoxA\nnay2c6TNUTSsq/6JEVArM0DBzquGTiTQW6m3A+KaSuOM7pKkGkgnNQ2oqtiH\ni0uo2d9wfApSOpk5SrXL6FQ+O3RCgUOG7AL/kiG35I0WFppzKZNW8a3m0Flz\nPB3l\r\n=V4GW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8-alpha.0": {"name": "@inquirer/editor", "version": "0.0.8-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.15-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "d8ca00f912309e51bf012d109a37bba28a305151", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.8-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-cDtRlvx421kgNGWbNFAa17sRWmmSUwB0nFvPsiy7+fbMa+vbFWK9vSBtdXiG6qpm+2iBdeE0Zp6VxgbBxhHxsA==", "signatures": [{"sig": "MEYCIQCIVhU/u/fIYBIWNPg/Bu5+nQKgXIgPBhWWLmZZdQSI7AIhAKwmvWR6437RskAHdI0NU2s+TdUo2A6QA/Nbm715O2vC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqBNmCRA9TVsSAnZWagAApqkQAIz8u9FGr+8fwezsugQ/\nRmhs9jV6PBSVZbq0aNW7fTWTZb4aKtFCwf6fMbF/CT+Ta0fdg6bVqYz2hEvf\nj9dgpcL9BaFwUmocpMLwVAikfO+WK5KPcA7qk5/ZzADdQLfLdlhhdA1ni/I7\n6afV+sv0kezWkWjoPoE7r3wQH4VOcpBs6DQ/GeTYg5yNc9NHMG097zj4/0Y1\nEc57cP9W9nYafh6HRd1YWtzfykfUZ2VJ77LcP2tIRSIBzs+avqvauRCMfwok\nmvq5IKay8ngg1CPcKgPD3QTSy0wjIc48RIRpFV8BaiYZ36pnqWqFrPr23ByH\npNerqKEKyn7p6eImdW2uZziwXgYPB76xU1ukKHMERluyibmF+tWftiUeUyHC\nVsTOP1lGpccZsH1pnRrkgFW45I+uJianQIEfodqxXc0fbJ3OImlHIQ9GAQJ/\n/A2iOsWJIt2gX77wpmzA4wTI/HR8hpn5kvpSBrOHgIEsgc+Ck6XV1mS1dfcw\n7qbEKk5dAq915aqQiwxtJ/htUgB8iLG8Nd4H83+dMC8zzZpbVB7iItgE+WpR\nhQMWk2cwsSUK0LD/Ww27CskfzEbHy9EG2vxfgyQsswbncJP9rQH5iCpqUN7h\nuU128pZG6DthAzhYCy/5+CXkjrS8Zr/1yN4Xv902bxnK68Xv+orCkQjDaivH\nckxW\r\n=XIBV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9-alpha.0": {"name": "@inquirer/editor", "version": "0.0.9-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.16-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "e48c65f011853f57d81374fbf61f1009488e29a9", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.9-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-iwQUW5Ft8zFDoG1J+npuqp2SQp/GhKBtG8vWdVSI0UIwHlZdLI/ywRv0VRhY8f6lFI39bBV+Netg+UMR0SQr+A==", "signatures": [{"sig": "MEYCIQCZ7l9OZph//uTvVJ3QMJlUB0dYvfBnQuidtB+XpXZTcgIhANUJYe5aWejUCOXUOL6knoBkcXJER++DnRVTJkEhXIrC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7wIPCRA9TVsSAnZWagAAmeAQAIBKZVzv2Du4O7e+GaBd\nbtS/elI6ZPNYIoLP1pLrhJ0OPMWRsWVjMoA1x+g3Tw2vIrBMj2aTTdfYSW+m\ns1qqdcGxRxogViSs4CfEwek1+57Ws69ryD60Qku7ghmisOlNQ0Rw62npcceS\n+La0ZmUzlb8EQ0NioIVeiRmFVM5mQjFDH0xhsiWiX/9KQErSikxWClyifaUm\nAY05ezHKyoIkCEaOaYM06OFJpeH+pPXZJ5onwWLaaLvNVSDvmP91md1lum2T\nnHasECNvYB24MDhGLInjA8gsFggZwHlLGFD47SvKxfjNJXtDS/BElJHMx/FL\n2g+YTX1Wmi43CphrUmTgIek7KeXuSWYW5ST3Obextn7P3mSDsj+W0VJwTTLC\nD0z5ucnDYSgQNP/Qdk+db6YXUi+MT+wfxcZ3yVUSmXnC5BXc4pJCrt19G0Gb\nL3ZwW5vxJKulSlFhcc12QVe5bUpy8yXsb2d/hZNIqqIu19sLLStXNZBQxMBH\nwbiFsNgZaxp2aivWt4DZaRUX3jpPBYdBASnNxJ6A7qNT71zrVDj5BQxVP0dl\nRoEAwBlOWBwhBdTLswoN040DfpcN/FObhs3vvAyRQTh/4xYA0ZYqKzWNdxKV\nI94js28me85GuPtDHyMj9gUF+5cSnY476ng9E4fCd30t5QDU1q1MgxcccGuX\nh4pt\r\n=ABNR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10-alpha.0": {"name": "@inquirer/editor", "version": "0.0.10-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.17-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "4aafd2ccb5621c97694488bcd579edf995e830f4", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.10-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-RbdAhhqVdlkZafLVqqTRm4l71y0cu1tEbLGp6vtvdrYxVRcEyCQvw8i88kca3TAQIuH4ZQHe8n7u50ewMtIppQ==", "signatures": [{"sig": "MEUCIFZrCMCHBso9EOUWoV+DUYXY1taUcVU96HdOfauxoVHsAiEAyrXABrY++uTv9OqC8Q+0/YjWEqrpzLChvBiJXXFe8oY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP5iaCRA9TVsSAnZWagAAdz4QAIKPvl+QAwZFGt05hUyS\n5VkFwnILoPd4qd4gxtWSB+OZT2skjlE6hHmbb9oO0iiaoBkBe0aWC4AGSxqz\n2IDlUwvxFJJ0CtPG3unCdko2wpMDSAV3Ax5FRVnyLDPbtzNPuhT11IQPXwMz\nUwM1w2mA00hM2can1XPa1vkiobjtTO+CA/OviLcY8Uy415A0xeAYVQFBpJnZ\nFQQDdbRXvNJtt10/SnBroXxgfNQsuUeiocUyciAmLN0ilQHPlNvB573rCPIo\npcL/0FM21I8HNoVjmzJIc0rL4tiHGhToYcTR5AQ4UiYyp8YgQBBCerYlQQ3s\nZoo0/eF3r1VvSZZGvd1K5wv0xbFxl57DNu2ueFcAuHOOVUuHtgHf3nJBxwi+\noESOQtPypsfIfxB7KA+UXYhLnQ9l7+ypQ+NzyZ1uatHptTYYzOQwTFYo2HHy\nW4jKA6oQZHoYXdL1cld1dhBVrXvK7ZkOM4LTg6cVqnVfQmTcJ2szOe5vRAvR\nshswDJsPyA1rm/FzBPFeaUtS5510gjUeJa4HX8GAWS+A3rvq2TKTXU5Gyl5t\nsE+oGNGyUkYta3PpgEHqe/8PkSfFs8tNO+YeeURmhfdtV9ErQ2GdVORGGWIa\nRwwY/5xCORsmBgypmB4iYvENVUtm6J0YoC9Cy7/Jaxl5IqwrG7FfiiOMysvn\n3C1r\r\n=xVz9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11-alpha.0": {"name": "@inquirer/editor", "version": "0.0.11-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.18-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "556653d3a90f064e7995bd3990c9c684a79bc06a", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.11-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-czY+hvOVAc2VhMupL2ZKX86hzkbDPrbAc5gyU3fSlNO/Di1tG7yBF14z9h4+UWKZ11ZJlq3syJewyBbh3hmo1w==", "signatures": [{"sig": "MEUCIQDEUWOFtTs8TuHp2nKxiUiLUHX2JYrIsgRStrozVqd7gAIgX4WgbcXDf3HIAtZMSaNa7VJetbtU11Ck6cIVqhBcaG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKAGFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNTg/+J3CnK+q14JySQGUIN8znPLm/wdj2n9+geNKcrrWJTsVA24Vg\r\nLZ1mh8boscIu3cRkljfy8J28cwHimldkB2sNmuzH5WzKr3IQXkD0y55zzsme\r\nLUF2lGdtCN6vq9XNKwpihhrdZUez+hO5FNo/SJqPSKSoLPn6k46sT6RISPWK\r\nny22qCH79RVkNAU2QimMF4H+Fi07lOvAjgPwXledK5o3ZjAdOcu5LsAGquFZ\r\nN+HGlUXmAeDjZPlE2ilZCq3J1NtcPpxW/5CIpYhkChEumZmbnA0EYqMIyuce\r\nyt3tpwfCmxKDA0ppc8cGj27U5Sgqz5a8jSvlfZF3HQpzd6USKZRkISDDKOHD\r\nmr81G/Yb6uJSP75Te/tkGXXZ/5Cg9hMkcLy0s0UpiKcChku5dzCnRcuu3LDV\r\na8ZUokCujm2glTX99iOMscC57UGHU9jue/2um8rco3/68YPFRpMvkQgYil4b\r\nQ5zcXnThpxDPXOPKw+zcBkR8XooJ4kQymZXRX+oWaQbX4RxblpbE44zHgDx0\r\nsGUOY5A3gicuKvF023/LizOeZIQtSdQwFwM3PiC9iWbc9q8BNl7r/vDO2UE9\r\nGJiE50tpyy9d6MUaxGha6OjKMrmkbAOHQjTKrxS5JEwziaj8t4TfAQtTKcDN\r\n+VU8WCSOf+odKUaHJGh1BPwTQYFj9b5d1b4=\r\n=jdKe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12-alpha.0": {"name": "@inquirer/editor", "version": "0.0.12-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.19-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "7d132920ff63b28173b2d914f16b188f41f406fb", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.12-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-BDbhPblGc2yfilksh4c/zoMzynOfH6uI6RDSEFC7rx0PdBCcwEBYZ/Pin2HjjxioMxLgVkpF3ZDcW7JsQ2MZ4w==", "signatures": [{"sig": "MEYCIQDiWkqsKTmBPAut/uYin3dnfBM+QTTy171uMCtqsiodBgIhANafr2sxX7ckK1G5uL00dV1d0BtJor/dYEnV0CDI9nt7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaEfnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8Vw/9FvJ9rR1fBFRl/CImHTbJ/R12Es3u1cLAiqQOv960zGm+5wAj\r\nqFcCiqTR9y2gw+PWd0mpQHjIKjyywjtjUzNQxyxlff0GQCK/tx8E2U8su+Xs\r\n8mMx4kqpPx1igMeHfqagRziiXnRg+jP/M/p0WVcGWRWrOBGz/iorrzwoV/03\r\nL8oGcbIOqqf6uc94jlJPJSsCkDtauBXwI+XfOAclfU4zQTx5siK4mT9USsZ0\r\nFcUcJMa2gwaK36T4EVIiKkWXZJm3P63LM2ICyCmTkqN86OuY1gv4z3439/32\r\nyQh8BLIFJIARuHi8G8Ij6ZQSdN7aWt10/R1W6EZTl93qmyMiqxfvLqlf7LYZ\r\npbh1nM2RSsg088ZauCOElpTLV3AV0FxZ+FZdRINpBEFiJOnm7i/lPnDAJbjr\r\nousPojjDuriTaxZwTKdHMLEmOwbb7GL8Kcbyo5rbP6LspUfCGqKFiPmzLayp\r\nxxnxK0MS9vGFbolmmTx4P5o2eZdpOiDcDbK2q3pTWR72pvRW43QmGA71Tk89\r\nwAiIEzMX8lymgkw2sPVf2kSY4VVROZukbjs2tz4eIwWxe1NcoUNtJd38928w\r\neoGv6zqfRMpVHHZFJPeYx+e8sRksovJ992BvS9IHJ6mkSGr5qUCWj8YJb4sx\r\nKEiC14NPE850YXEogTrZMFVRU1xpGZ7WSZo=\r\n=u7ot\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13-alpha.0": {"name": "@inquirer/editor", "version": "0.0.13-alpha.0", "dependencies": {"chalk": "^4.1.1", "@inquirer/core": "^0.0.20-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "449d815b048b649bbceaffe15807ec1e77014457", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.13-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-gFONt31ZJGt7IrUu90923JJNuZZGuvC6+Kyw7JHTraa9mBv//nAdJT8V0s4GQcG/nsVc6drbTibaB1GlahN/fQ==", "signatures": [{"sig": "MEYCIQDWb0YncTzH3u9AcuLPT2e6XJtLnZZB0yiOClq5NSzYDwIhAOhZddkf/q+3c8t+B9XkwUnG8su+fwyp2KaB79l052Ip", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJialmWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrScw/9HeiulZ5dFR/yfLEWh+5dS1Z2rgihdeVSQeONpjRYIrG71d4Q\r\nzV/gSKyq+PuddKTwE4zCMvjUTjaWf8j6WBTRtDfw/cIKKjbjhLGL18mKlSx+\r\ngVWPgcoxZPiLCZ5OjzoNlI+Ml7QX8gsV4Slnl6FbZjqKb+bMZFyxtVPRrM1y\r\nXPEaKdhYXwnNEL6wrd0Ge58s0Y5kDxZvP60HVXV0Q7RZVybcoGnqmVIdRg7y\r\nhtxKd30PZR/GG1PrKKFXP3wsrZiV1wK/qsN83EKZcZ2JVpU836nFhsXOwXVK\r\noNHk3NpbWhUZvCzTKfwkM4nN+Pa9hzpD6TRvkiieZ/FTGU4hDYL8hzg/Z28V\r\nczKuBWFdfR14p/PPOYU7EpOawsqHLpPPXli+sU8TUJ2ThWxo7JCgHqfw++ds\r\nRrxsoCStzcDwAu5DBco0PsFJWsLD8sVFeo0l1H6eJjJVQRQckpZeqELGos0q\r\nSVgC84itwpXg9w0+RqvuiJWAqp24HOinJIMhtwErljeh+AcGGrqwaoSyhyOP\r\nB6b9T+EuV602TGU9cXtIqTgChaVmJMrRS3JDu0hWI1lr4j8JTxbzfk+kq/7X\r\nf+/MLd0SCA7mUFPkHn37dzmB2EhMTb8TN4u9vWmdsjxruSqWZ13y9dwGor9A\r\nLDPcaJ3lQz9rFch6sZzErWmKfFjy86imY4g=\r\n=oR6r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14-alpha.0": {"name": "@inquirer/editor", "version": "0.0.14-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.21-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "eed6af0d97738a16e4f557e4157dafffbb7a5045", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.14-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-iiB9wP+unt+rTDCMgPP6hm8V2OCvO57R9iaD0YxSzqQjNcX9P6Vwaj1qa9QsQIkwrzVDOahuL4ORRhH0bveVig==", "signatures": [{"sig": "MEUCIQCCuKAwqyQfwAU9fZoAB477bsM5PBLaM6SxEW5l7o8IGgIgWSscLBzZ7gHmUO85RP9q6qCP0DxMhagDDwvKFdyv9TY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirhB2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9Yw//eSig8CUc8vW/jOJqf42JbikGNZMUCjvrDmRVgKL8Iidcdh7b\r\nFXOvSHAHZvH/osW8Dc3dYrr4cXmvA7w5K4QLUf+DX0jtZnBArXVlHXdEKc+l\r\nhODiEuqf0ycYhWTt0VRQAulwP8bXQr4m0P6gxN/0uIfFlabNtSENiAlsOeKS\r\n4oqw5ItVelWp+gUfaZyzvr2uWpxZXzqRCB1YMoTH0VzZO0XKsP1aaU9VVAE9\r\nuUeoMOIXYi54iEmsEQJKgrk4mDCcQzdhHu9DDXOPVHObESCR35OhPFg1LE9+\r\n/nuxxGXnjpHzZOSd4iJAVZPkXiqIkPYyR61Hr7W1+IDV5BnMauEkIZuNQdCO\r\nXMjZP8SM6y5yKb/Kwr4JHM5PG7KHjmufKMH55+7TFnToLgZSWTvcNdXJYezP\r\nJWoh3WTOtRYanOTkeIJ7EvVo/NjjuvRDvmXtpc4Fivap5cXY91qm/WYxX8Rd\r\ngRYkrUJyoufWy46Tmvas4m22Y5nn8hW3S8faCLAUQAnYdPzWvoIRis6Hpt7v\r\nD8bzjDk7qTbUeCTbKWr3y1+5QBSjjd5pE9+WWvydM1SZnjp47b9YgFWFDwP+\r\n8WvKOfu4J47ORlmULnr8z5+vWNl+4TEpfrO2vhYs+kVl7+x8XffSsLnsGuVU\r\n56HvnsKsF4siLK4qNC8q5MAa2uL9oriBHFw=\r\n=uq4z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15-alpha.0": {"name": "@inquirer/editor", "version": "0.0.15-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.22-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "1c210d75f63c2921ca3d016162decacb8dd97b76", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.15-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-usetwWDclRAfi9pP7LbeOigQsrst8rFBpNpnDnXwNwZnaXZOzB4WZvZhrbmU7X+aBvZ+hJ1BaXsfSArtK6kRLg==", "signatures": [{"sig": "MEYCIQClKZ5nMskYWJgwC/oX9zlrSvhLfePjeq9WFhXwusGhXAIhAI7Y529xTCfJNjOD3i73b7TjlR+PeElhyrS080Ow0QKv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizzDpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqrvw//co7TvoUkhIjiGSKRj+uJTcQe/7u4JUmreTGwAaQICcTyMmwy\r\nX9gXQErDVC1E1lAEen6l9kFXttWDNTxUWFZ+WtvzXK3oUDhKfWosy3L7aYZY\r\nNLpL1H/Zbz0Af+j9n28S6aVGbxrW+qy8p91Lv1yk9ovKtdjpegDpd2fKwVtL\r\n4RCnpcytHFzBDYmi1SFd57ObV7rjAEOCVAd6RXY1ETCB6x7qsUYQt0HVvnJJ\r\nOVi7Y78wFQcZKVrr17pS8teZrNdHsybtIdUacpUTus4M+cseImiSn86vamuk\r\n7Z7BT50uB7Qv9LH30f3XypVMS38J9i+rFd1N6XAQ4jtDQxPDGK9k25dCK+zR\r\n+yMUAm89l2e8g+0jc+7ipAdOgAMeWIRihRucU2bgge8PymauGS4PIHTRz0Bs\r\nzyPaHBet1OKYWr8yCk3bYKcuihltmoseFFPiW8cjANbSrzdkvq+mBoeaKJk8\r\nK0b8wd9pmnBA6mgDSdKXq10r9Z2NAO0Hm/Eeb4MpbbqhnQHlRH37rwSe4JVR\r\nepgME6ao5lUz3y1+mMXopjOKjynC/q/qLGYWwR2g05UJ3pNWQluXgzDXgsvV\r\n0Qogsdg+uszRgj+PPGHIHYj9F0EanQsWlxb2wyyygeTd+GSTKN9QpN8dmnx8\r\nx5DxuIBJppfX0RuAwbLccBsR9jTvCaz/iaU=\r\n=eJ2C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16-alpha.0": {"name": "@inquirer/editor", "version": "0.0.16-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.23-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "e34d82e4410eb8be71fd07d877ee59483d2133e6", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.16-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-dueVINR0ymqFHWE/dRkUeTA7kPXn0Ic+AvYuPk9b4QMYml53HNErgBicy/iqghppcfHqkptmjJKgQHtNRrPgdg==", "signatures": [{"sig": "MEUCIC+g3F3fYA43ActwIu8Ak1dB+Opawt47w61cJSNgrOwSAiEA03nAvPpYI9xfzNS1rQOFfH/4Mn6Lf/j38rjGd33yvys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi67L2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH9g/8Dn1XkEIv4tGiW9RK8lqGQ7Xtd4ufL6PV3gQ038i5xZ1gehQi\r\nayP3fz7dg41yZ3GOIuuIS3h9SiyaIddt9MZztXCQ61rynxAdtRz/xni9Y3kN\r\nkZAPvrIH5A8UkYnfKzK9wCISMLGFBi0JKDZQGiDIsnVZJrGlnCnFxg85D/kP\r\n647g63ykK5vLzWIKIZXNtpiS7+cbgP8eobEPtBKa3k5Fg9n+3SbwLvV6J4P1\r\n9qzomzXSfnJrGd1XB6n4qXg0leeUSXFuDdigUCqcL80S6x/Fq2+HQM5iC2pA\r\n6uaTXSHhrXSD/5TCsGO77AOBLaYXmgFPyjiUFK86062NBh02Lsh6KmO6FYUK\r\nRjPBXNWlIh2yoQU86AHBm0H9jTCgBP7skiA9umkVj/cHwz8IlmWtHyTl/2jt\r\nl4rL0eU+caqZSaYaN9rhfJORTWeshOyo0Zt42gOs9W+iJq9xulT2VAeMoPy4\r\n9Gdrup5LcpNwosUFoNMt7P2MBDtcu2CHrguy0yOgFUz8jUPKYPvU4WjMWMjT\r\nPzzzrB3WI/n/z7TivJdq2iA0m1l1agHX9P0G1tE8wCnsV9K+xELt76O7U1SM\r\nD8VmmPvORU7/wzeEeuMBznzrMsziLgmLjCrfuHqetrI9kW65G8dPFtBz6RUC\r\nlq8TeJi+g6qycrNtGm1k1f5N6G1gh95OE/4=\r\n=0Rl5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17-alpha.0": {"name": "@inquirer/editor", "version": "0.0.17-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.24-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "c6033c41ad0bbfa5ad446a945c1810902e07b48f", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.17-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-mvVJ8k3Rtbz4uiQiZCnanxUegNqCzNcfC2ylLVwP25qdb3w4kq2Oawrs0KBrrxbJNIrbSlVw7xzhFNFWFt6s4A==", "signatures": [{"sig": "MEYCIQDQ05kdidXvqKytjh+lB+1N3Yvg05AfCIxyrkl0Zpfc5AIhALaSAVfpkHRwxmegSMFbwb9SbK5pc/NKbkCip+HsbaxH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/49+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpmlg/+PaABOJ+sLuUP5X3iINYWDUjUK721K4NhbORgOFuE/x4u/BBU\r\nag2jUZyTldKz2WvqzoGUgjvf7u5tQqcOKa6WkT1D8ZvTwTRNfjOrdVcQOaS0\r\nZW4JRV4c2+nJ2MlQ8I1W2XQm1iP1ZJoezeSQ4tz8jWB6P7KNl88S2GTGrjF1\r\n/vvQttYfBnI7qNyruMfgt2lVMFx4y8qeADivwdCQfOAYiBvOMUynI5cpnz6T\r\nJsi6c6ebUH9KXYVTP2tAKGfqgjzW1bOqqgv1MF8QpHp7OMA7B2FxRleFiyeL\r\nxglv9MTw2Eb/qaGl+CwvuVN5M6VaE6P8M6RpOTUNA0hpOZ+etUMYZs+nLivJ\r\n0Dqo5aTkhc4fxdmu5gn/8eC/7gcGEgqyq9+Xq9JsYdjsbX/Gic8+xZExIUcX\r\n7HAE5j+agfjsx39+4u/JnrxORdeJwVFqz5Oup5RZhH6YrYYWa2nH+CTZldyp\r\nJ1WEidalfuIom9ESUTA7ZPmkLo025JcFeYmUvG7XK7GzvdSOHGay0CuMHeBw\r\nantEcYgDQRZyMLA4JW7cS9sZdwYz61nINsX25IwF+JO7Vs53MS0qpyS1/3XA\r\ndxrGPqnml3muDXlyfgxIPmDZrYjRuApgIplnz9m8/P5ahPkynNHE5fpL/Arw\r\nI5lYG5PH3tYK4wjdiIlxpq5THIyIWcMMzfs=\r\n=F7TO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.19-alpha.0": {"name": "@inquirer/editor", "version": "0.0.19-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.26-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "bfb5e2b995c6b94dc904822d3f9aff4e770c5c35", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.19-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-Xwznjgt/cK+RW8RS3r1CogjphECT1znZkw8PJi8bMIPYk56VEf1kjkyAbubYs3sX02EIPcGngJfUb3O1KCZRuw==", "signatures": [{"sig": "MEQCIDL2lvUwDQ57wYVvOw3UQIJFos+Dzz8n3RJsB9rbM+LuAiAo8+eYieaGXC3K+jDVN/9ejTT6zEwDSUx2QF1cIKncgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD6m5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK4hAAkOuhYd3J28lRCKOAlZl5HY18FFbD7HvHjx0q4i5gFZuSfRuS\r\nJLEbek+ifLObbKzdHzYy3ysGaO55EIMAdi5xvKI+BrtUtVF64pOIA8DpeC9u\r\nnGugWoadPvM0M3WsEx2PUIVAWTqPzpTOUepLmkLVFLtCjoNUxxPoDmnbYqPC\r\nttX8+xEFDQUVKtUHVJmklqou1qZMxcjRmAZk4xEYAMFShKV7yACkOGTIqigV\r\nzBPK3tGVpW7vW8F6b8Ss6lOhFLBvtxDuBkc0fcKDxs0GejAcySMXF66oXf7u\r\nc4r0Ru1mzsZi+Xfm8BkuS3SRVstbTweq0FcgqpVgxtxMzBHpW0nXRH4ij0ox\r\nqjUMfle1d8+Oqf7pGJUPF0oez2In70MHJ1YmDSaTmT0f6VAWHAJE3EVrNtLZ\r\nB2dXRnNNvs3Ee5fKmARkIc5qhrKQSoLeG8cT15kKt5Aiv+SSLlfQxKroo3YZ\r\neLNLjhMgUwPjrfAu5L3xFuGIXjKi+NMUFYJwPpN09OOIc4kz33tUetyTm77p\r\n1pdEb6zC5PTf0mxjHDJiEJbuXj3cBRFz6Hq7l6GPdwz2V/6hf8oG+h/o0H6x\r\n3ZhCrhJEbOaoL43XWkCtA32P7cXfT++fTqacZUbVfyItK2zP9AXMJt8uSsMe\r\n02wvWglla9RjEGKBBlHpA28PelucxNmeAfc=\r\n=ofXQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.20-alpha.0": {"name": "@inquirer/editor", "version": "0.0.20-alpha.0", "dependencies": {"chalk": "^5.0.1", "@inquirer/core": "^0.0.26-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "cd1a8d1c6f479e50972022d4c8d72d0e1d11dadd", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.20-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-ATrkqig0Lezguy7yz4QIYCRj6+GRrqE7WynmMDBU+WZUx3gJ5k/GxHw5+I/dJyFZeLwfcqKd4WshHuq7HP7Mgw==", "signatures": [{"sig": "MEUCIQDDuyYNGwAofbRjbeV3qxFoBpo120kTzFSE+fKgRdNOVwIgRFf2wyUcVQPlvKIKpNbhePGbeRxNUEB7PAl9wMLYNNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ2MxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpXxAAhvXNesq0FMx57m5yIhxmcN0TMK82J86qHKe8UDrD+WodErOa\r\nogICzIcRP/Fb0rLMKr7/LJtZfUm6lSdQq+JAv8UXBLhjI8Z9EY2TLC3DGad7\r\nlDmGBxJ8sHDTj0P9emOHln5ZuaB4m4JEFdN4uOV3I1DvM4xSAOXxr2yGlz51\r\nQElXxhsFscLMf5qwvPa9n5ZI2UuSWUo8MgKq0xPZM6/MSYVXGBXZNCwa8m33\r\nvuNQ2BzaL9J5QKqzB/WmLnbG9rNceVZdODLkL3YwIfsPGdxiDoPmwdpRmAlf\r\n6P5R8w1CMjuFx5K4kuv6ln5+BHIhwgT8/uQS/LtaktCWs8tBZRRNIx0K5UaI\r\nGlIV9Rnx1UpLPXO+o/xqI94kjo8ifvqVBDgltxtmP4LfmyJXLhWLqC6pREC6\r\nI2BgRM/jmRMmbsQYSbU1fbV4M3AOU5mq+gVd8PegeQrlOMoKmIxd7/8uX+CY\r\nwIfDxBiE6bEDRNzgu8HMidM1zx2cVEyx9JYZXfK4I0z+j/AwsSV0cQ8SEAsk\r\nih3rGdk3pJPlOz2z5hDVUZT+AUE1hrZYt8ZTP1TlX4+UhBnn3qlDj1b4NGZm\r\nY5GqrRXVNcb4hNBPpYvJVaPQNTAKeQTIlBWhNc6go+jsZjnav2ViDl1B1WH0\r\n3ipqJR6blGxzOVBNhD2WBCqobvV2MDHRsQI=\r\n=6fUv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.21-alpha.0": {"name": "@inquirer/editor", "version": "0.0.21-alpha.0", "dependencies": {"chalk": "^5.1.2", "@inquirer/core": "^0.0.30-alpha.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "1c5e28a0d63e2173d10f5a5f4067631cb2430034", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.0.21-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-5t+NJN9hMPyqh1e62BrCGhFJUUfFvsHVxV0Nw4s5ntyhPU7b42FBr0PgMLF5iQRypn1URmxh752bYEgU+PV8Ww==", "signatures": [{"sig": "MEUCIHx6aexKmjX2x2XYtDPXQxRzx/TYIv8LkIC5lEIshH2eAiEAjWHLbo7fHhpZ1yHFIOfzv6Tykm6VrO5ajbXFX75Gfjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTcH6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouLQ//QMSMr46W+AMJPTUrZfEeKYy4wv4pvCHEyH5Tifz/CJEkhCaz\r\n/xDxvuj0+mO+y1bgdIPjGgjqrrvaX+9tZ5j8zHN2+3vwghRSfPR15zPh18UT\r\ngYWw0KTF44l+AHWupIAq+T4fdp2GKpzbs9qrZ7TmpLuQrHn2fe0ig66DCMQZ\r\nGdK8e4nRhYgvB8AwRvyrKBDFdYvyc2LfDzcVIEcM2kN56O+5l2bXWVYa0UYe\r\nxyCwgh4J4sSaKp33Pav0qpOYVIxg6St/e8is4W2LomlSTCRaKBPSRjflWiAD\r\nqiiuIO2i6DSAptlQY/bZIlc2idvcscwKqtPi5zBKKKw6bRZOSC0N5KiMa+A0\r\nXGfsC++HIJd3ryfozVmAaZHdA1dCjyqATaOBmMOMi6o1vCIly+JrdOP/UYVM\r\nxxWpAjB2SHJH2cAhWR2wxuCA/Z+i3nKZHfNkpT23ffusDdApjt5oHCdtElEB\r\nDVeedCcKMud3cklOJatlTOUzOo7Or6WPiNDMVDytkTCcEr+2n0gT5gi/uPDK\r\nwZA0U3FXboicSrfvhdhBfXb1a88Ctz7Qop3tB/K/H10/RIeHaiJFjoG3RF0M\r\nGchSBbluOXOyXSFZEP9sMI//4gLU3VhZBavf5Fma+V7xDgMJ12X1xz4h2inP\r\nEUzQdnWeZj51m6cUlVdGhubW9n5n8maGMKc=\r\n=9ruD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@inquirer/editor", "version": "0.1.0", "dependencies": {"chalk": "^5.2.0", "@inquirer/core": "^1.0.0", "@inquirer/type": "^0.1.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "cc30f0ff87e74f08b5adbd7eec290f9c5a0db0f4", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-OIMxnP3xzSCuP6Fk/J3GxjTWbex+HDKX2jIfUOf+k7JXUpd4P3Om3YucwW0pOfDNpnG+bhgepWPPI7ivP3Kwzg==", "signatures": [{"sig": "MEQCIHgcFpKRkaSxfxppcQnDiqLrlLRglVoRTKxwFeYDZljyAiBUqLSwHsPnuOOAnBWSHPsYPnOjxMpsGFwpAo0g7Fl3oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFe5JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqduw//V69huY31ubap5jLwJiH27UY/X2tccOfaPNxZFV2DTFeXDHAY\r\nz/BU2SHJjQXkeW7Mi8KEPNNP9gXgRIh6cvUMGzBV7SHsnM/7qJcVSOtYzVMr\r\n/ztkZJIHMyk/Xb5XMfQsBXwgm/c7BUXDf4D2xXww3FYjWNBqxdEq/DDbCGJG\r\nBoqhcGHy074uzePeK79aWsc/G+JAJ5NjTFnxiZOG5tqRNtO7KKjFum5swrIG\r\nNOmlNE4+t8StX8NSWpAO9BNLVKAgfKflBYKcjQlkUdNHoYcK2UODtF9mn9uE\r\ndm18sflbKGew6nafljmlgfc2ymVm6pHjMqfjKy0WilQVJU9c1PHWlth196vQ\r\nbFbkWoTaqqacqfWz6YZe4iKitJA1xif8RiMxaNzURhxpYpdk6u+qFnL1e2j/\r\nSy0zvumVicIVNcrmEc25Lorlkv7YOtZ4HMvBlIHPZMKZmmQWCskQJnL3BRtf\r\naS7rgCktSe48PP9/KO8EqK5/vJp0lnXff6tOILuhnyOtmuEqGtUz3bXsJA5h\r\nREaUMoM5JvEM+0LEfc5Awo5qdkN2F4sB/DESvD6IHSyaqYiHawgwX0wd2iY0\r\nGC62Q59h64KdPCyZNUnUSe3huzbOjIuBHni1WKC20vG7aipixu5t6yinvPsv\r\nszDb+HzHAHuplHk3C+qY1yOjP8fxLraIVJc=\r\n=bmNr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.0": {"name": "@inquirer/editor", "version": "1.0.0", "dependencies": {"chalk": "^5.2.0", "@inquirer/core": "^1.0.1", "@inquirer/type": "^1.0.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "28bf0e7b63b7048a256cedfa8de2ebe455ae9ad1", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-jBj54aQIHBHnOyNs8Lc5j9UcImJfbOBrlxmqpEEYKdWaDfN96/rBE5S5eJGKIFg4v5ZxBPSDHyS5VXqbo9uK/A==", "signatures": [{"sig": "MEYCIQDlxRUwNNDEqXa62E7HbhubKe9Z4pHEXLxjw+CXA7ANPwIhAMQcxmaskTrDRZCkPdhHturZmBOZ8LI8AcGdJ+IJvOEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRZ/TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaDxAAl7QXU6fTQLJ5puLP1flZs2lWByD2aqDX8bQxnMkspfecZbtV\r\n++rJXcp16ItiMCIXoZAN7xJo0mkEOJ13d2cH1PLX4iq4ZV5ieJdParyvIB2J\r\nqXpuzrpHQGIYj0Rb3lDcH/gNvSqudkILPqD6xO1w2E/WcvoptweRmztACDFr\r\nZ9SIF2k//PokbINKLb6UpYZ0rSexeFgAMRAF17PuaY1MJS3YPhQx+oZjk1wm\r\noOph+jaM33BoydUyPGivZh9qBeljvtzT5r7IXge4naj7ebZ5zkByqrDW86yW\r\nvXeiwkLncUq6vivoyocoe1BXK3cKzF2Ar9cAwjwBGRdpMhQBtgPm3EWUa26S\r\nzQKz58pQHrpzpQX3xrS8ptlU/lmmD/C6MbTrrUcbthBwbq1I//m8COzurf5A\r\nfNujXp3WtuGuE4lK85s+BzakJzvETks+CYYvKDk4ArHH7JJUJ/v9DTuw8BLW\r\nNx35KJdtYHxiN5ACjfijMpIora50EuaA6Qj9xYF7ojEEygohxdDn1XhMOWOY\r\nLOc0aTduOHUpIWH9HmepKEWM/3c3iJz7Ise95U5xvwCZl7yhs9JmPRMT2HhZ\r\nOplPrwBeE2AsDRtTEHRRLXrLZBwGmeCzPetxzq1no5nwwhcVv7Rhm+SYJZHB\r\n+gZGqeschxrcijD5d13UZ1Up7ys2ooZQCWA=\r\n=G52Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.1": {"name": "@inquirer/editor", "version": "1.0.1", "dependencies": {"chalk": "^5.2.0", "@inquirer/core": "^1.0.2", "@inquirer/type": "^1.0.1", "external-editor": "^3.0.3"}, "dist": {"shasum": "2a9d5c5b459dcd1215bffe37bc835090249932ad", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-KKZbr1I4/o99u5Vf7dNdFjg32HCsunIjCBGLKncmiqOIvUJch5APCASuDns8rXRDa+zBwRy65bHZ03cg9k1ylA==", "signatures": [{"sig": "MEUCIExL12xDnfkwGk1QmLVUpyiBA0LGuQDuIG3AUQ1v91p9AiEA7B2CFrIG6bVpgQOmUOTTDJ7zhhLwJuoy0WrphpnWglc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8LqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe2w/9FyqTifTRypTSHjvwrkUOAlPHuzKKxHuftkLSaHOuz7h2AX3Z\r\nJnn1R1eN9hOby2PlRTrtYcmpzCZM4WHQ2WDbNeR9aVj6fd8CuZ73eWLXo9E7\r\nIqrPS7NJLm3tY7NVJjmhNAAYc/mTLnINUCYBbptG9Skgzrn9wDxOfj44HLf8\r\nYmIwuOrLwPFUIIc76STwIs4klXCGQGPNSzy1Zqd37l+VI9ANvEYyyM5cIMYq\r\nDOxO5Uuz87Qx9NGxOwEUzXSlAVR2dVifo33mra0kYQb76lCOt2jLVMFGBbO7\r\nkomCfhS0xYNCAfLgrAiuuntyQSCF8hRnY3RG+6iLNwSjGnRgp0mHXnNggRr6\r\nCqC6S+6oSSZ7bDVAltNxRUm8uDlAOXFm7cqAV2eBJBvQMJjJ/h6REvS1yMoW\r\nbR/DogCGDgBqL65fgMi0aPWLQCWINb5WMb5OsEtfrdLcoW5Sq6Ys5SnOLQ4C\r\n4EAKc7q+5lLQxZr3JQZOGLwRVlP7UPO4kNA/aRad8308Uw7DmsgkruKOdRzG\r\n6h7UnO4ckLAKZ5I1nA6xyBr6tEoysnozdsAmmlJx0Y/hTax/vIc7waMyo5eG\r\nsv2pLIv6IHY2Ek0xrjyldw+6GEMX/AlQSjt3G37gh/AIz25cvu/r1Ji859Hi\r\nXljyB9eyiUTm4U9l2wuEcxTfQyQjfu51uxk=\r\n=Gtv6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.2": {"name": "@inquirer/editor", "version": "1.0.2", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.0.3", "@inquirer/type": "^1.0.2", "external-editor": "^3.0.3"}, "dist": {"shasum": "43e77afe331cc6fc18477d29bcea1f72b7ac76d4", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-LaLs3gfU8ZcQ9GEWTfdz/w0EH1bNgg/NF7cgDLpRInRcyljKcJwwZeyfA/z21c7fi0ndDEuiRrLBRvnEPDFW+Q==", "signatures": [{"sig": "MEQCIDHc8S6rqB+DQMTMmXI412exK6lA0zVL8Bz9m2YchE8XAiB2XGGRFa9LRoPj/Tiyi3YeWzVNQZokqw2OXUTG0N4RoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10785}, "engines": {"node": ">=14.18.0"}}, "1.0.3": {"name": "@inquirer/editor", "version": "1.0.3", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.0.4", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "7efaf869408b6323ef46cc7a2687201a5fe00425", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.3.tgz", "fileCount": 7, "integrity": "sha512-QQx8VbuiA8JPjIvsxkZot5Mf6UHEvogdGrkT/kys8R0wckiBCMIi2dTyKzrjDK33IoPwBTXPoSCT9V6PKRFUmg==", "signatures": [{"sig": "MEYCIQCtghBHn4BZKbQ8KnSEdjWbhkD/SsFd3q5RUNXoTVyTXgIhAOaLFgtNuRqyjZi7JpSEtfRAAJT5LOxVPlSz+wMRnCdh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10785}, "engines": {"node": ">=14.18.0"}}, "1.0.4": {"name": "@inquirer/editor", "version": "1.0.4", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.1.0", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "704644b22a5d58afbec298682edccc3f548bbc65", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.4.tgz", "fileCount": 7, "integrity": "sha512-Ca870fpu7V4cvfRZQCRCxSkwUa7apUvgWuPOV+dj3qxU+m4ft41i9YpJGR4z6kYfOSqQMXSa9jF4Zj4ASb+UHA==", "signatures": [{"sig": "MEUCIQDIQFS5P+5OIonxv9MMNn6b8fZQP1wPRCDJdyj+7avRHQIgBEDN8cL8z2E4YGwOvly9Jg3GyakSdJ9PITBFegOZgFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10785}, "engines": {"node": ">=14.18.0"}}, "1.0.6": {"name": "@inquirer/editor", "version": "1.0.6", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.1.2", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "9688c929eb868f30e43a3d191a660091a1826489", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.6.tgz", "fileCount": 7, "integrity": "sha512-gOPGCDzA6zjUw4qy1fPSGbCIh06J+vrcAcYq9GcmkJ9yiIC8lwGikIqS+CuzaSuQjf58pVefK5YXYlyr6yefbA==", "signatures": [{"sig": "MEYCIQCOAv9Cey0x/IJ6qDWZz4ylL1KhLV0caZBZ6t64t7v48AIhAP1epwt3nlA46El8oeSuYddLY7dfT72GI7MdVhAqrk65", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10785}, "engines": {"node": ">=14.18.0"}}, "1.0.7": {"name": "@inquirer/editor", "version": "1.0.7", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.1.3", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "41a2f20f6fa9010b93d376f36cd43a3a964d5a69", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.7.tgz", "fileCount": 8, "integrity": "sha512-2f2W9v6fFIQd8QNc+VaBKmg/4ZWA6HE/uIdyQRdn2KiNiujS2CMF3s323ZOsRzsSTLOCPSnILCnkd2svTRiumg==", "signatures": [{"sig": "MEUCIQC5Dz1o7fQVuGh9OutCQGiVdLSkcr3p8XNHyXWtK25ozwIgfHqM9Aht8+xsvYZEWvX/bjBY970mell5wjYGFctOlQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13823}, "engines": {"node": ">=14.18.0"}}, "1.0.8": {"name": "@inquirer/editor", "version": "1.0.8", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.2.0", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "b26d1a22bcc9e49f4bc8d06be0f38f9b8de2f6d0", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.8.tgz", "fileCount": 8, "integrity": "sha512-1zbCv/A5cRZ9FqLtGUrTZbuz585Z1U2ojjN+9QA+QLnxBVK5UMvuz96zd3X5/f3PJimV8lJ9z1GlaSD7VmNjig==", "signatures": [{"sig": "MEUCIQCK68qFSGYekRtluAzcSeyxIuSnfnXOBM5yVuUlybLVFQIgQX5+vRJpTtvaoBttAH5XJB9ZykEmjRSK3QVWdBr30Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13823}, "engines": {"node": ">=14.18.0"}}, "1.0.9": {"name": "@inquirer/editor", "version": "1.0.9", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.2.1", "@inquirer/type": "^1.0.3", "external-editor": "^3.0.3"}, "dist": {"shasum": "42b3346e8e3e4d21ff569c084a855906117a5f4a", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.9.tgz", "fileCount": 8, "integrity": "sha512-qni6thNw3gL1tPtafTrhS5/jNXS6QHTwEvBtfp6qCfkTS6HkR2TOo3DH+4SYndwPzB+h+q6D/ocHJAawsFdSpA==", "signatures": [{"sig": "MEQCIBMnIgUP+EtP2J9LyjfkdhFakjg63/XQG/LHq150IITCAiBsS7p8EInxZxJrCeWNJhHE1p3GiJ88MlR6emGNOzezmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13823}, "engines": {"node": ">=14.18.0"}}, "1.0.10": {"name": "@inquirer/editor", "version": "1.0.10", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.2.2", "@inquirer/type": "^1.0.4", "external-editor": "^3.0.3"}, "dist": {"shasum": "b4a003b5f814fca67b2c8995775ae4b18a7012f5", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.10.tgz", "fileCount": 8, "integrity": "sha512-UuS4XzImgl4GE9jLJLZvsyN6l19HeIMShMVJN313LZ3ZwWnlzbTAKfzN3E4RUp50qkc6dRYAEuZYFmgcaio61g==", "signatures": [{"sig": "MEYCIQC7E1IQDoySO51FEa+A+40A+5yAMmMNzUKkexRaLC3vOgIhALGDHaRYnU27fb1p6d80aR0t/BrQfElcjgopYduvarRn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13824}, "engines": {"node": ">=14.18.0"}}, "1.0.11": {"name": "@inquirer/editor", "version": "1.0.11", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^1.3.0", "@inquirer/type": "^1.0.5", "external-editor": "^3.0.3"}, "dist": {"shasum": "d113ec6a2ff18488fa54025b223b3d82288f02aa", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.0.11.tgz", "fileCount": 8, "integrity": "sha512-fAvkEKVRelMe5NzB1GM2zmAqbV0OWwAhXd6r34VgCKBAEfwI622A3M06g0ObL9qkcs0U+YW0G943X0ZqcUmKnQ==", "signatures": [{"sig": "MEYCIQDOfYXUhVZqaDjWcNlpm8huI1z87s5EfGLUlzHDdM6R6AIhAO/8l6ziozmoyZRIxZb6MLygp+uF/eTq+1Ohk8BmgbeZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13817}, "engines": {"node": ">=14.18.0"}}, "1.1.0": {"name": "@inquirer/editor", "version": "1.1.0", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^2.0.0", "@inquirer/type": "^1.1.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "58532c26b5de0b478ca0d440fbfeacae4c575164", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-ll6NfzutAuzRwNELERhECZCnAIzb2DdnRaNwtJ3Gfy6MrQBdYpFzGLwDyxB8+yf2iJoMOmsKWnzGbmvWNvSUhw==", "signatures": [{"sig": "MEQCIFheWquDVOSkl/XSBsyK0g/SkuuDONN6AUTpUTUfOrPwAiAf2v/UFZsEo3aRjeBYhU2KKHG+G+83peg16xWX9Uev3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13816}, "engines": {"node": ">=14.18.0"}}, "1.2.0": {"name": "@inquirer/editor", "version": "1.2.0", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^2.1.0", "@inquirer/type": "^1.1.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "e2646bcc471da142285b067433e7a83b88b0e320", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-NMXLLNadvqIR6TD6mNZRa/PKHTvdaa4ndGGeXl+DwybQ4K7cVSJNRrztixpM1KDEoG8Ape5ightNwq25cyugTg==", "signatures": [{"sig": "MEQCIEzTf1RTh7aThLbGKwJed1m9PC75dPCiYxbxlJf+DsYZAiApqWIc6m/3vJ7oSntEzCp3s12lAXSLZRlGIAVvCeQTMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14781}, "engines": {"node": ">=14.18.0"}}, "1.2.1": {"name": "@inquirer/editor", "version": "1.2.1", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^2.2.0", "@inquirer/type": "^1.1.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "9ce91273127e048cf3933446b8b7c0755ca4a890", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-BbmApP63G5bXW7+sLeJUgpeBvu29W5A1rD5BzMO93ChgfSLQRKucICKwZTc+UuJh4eiJ/KpVeVozoyttmV11AA==", "signatures": [{"sig": "MEUCIH1bm64CKfoluHrZvBs122X9Nd6UD8/DMZ4dQdDmzwe/AiEAot40es5XgG/ArCwmhb9ctMysL1yZx+crd5bvkWu9Y7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14781}, "engines": {"node": ">=14.18.0"}}, "1.2.2": {"name": "@inquirer/editor", "version": "1.2.2", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^2.3.0", "@inquirer/type": "^1.1.0", "external-editor": "^3.0.3"}, "dist": {"shasum": "940b80208289cdbc767d2051c5a1af67c157957a", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-jIUC7Wy4LXZU/7/DQ2W/sWsyTr8k00QRBWc2fsUlWg+rgoLWV/Gy60irbuyp/VCu/jQ/AHRnEz4sS2IJnSxDjA==", "signatures": [{"sig": "MEUCIQCIejYeaS6lO9YHPMmzypSg0Fp2u4r3hS3H7Tj92NeLxgIgeFHXUzOZcMjMDUJQD2bHqBpfi0HBKQMinmVmT9d1+Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14781}, "engines": {"node": ">=14.18.0"}}, "1.2.3": {"name": "@inquirer/editor", "version": "1.2.3", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^2.3.1", "@inquirer/type": "^1.1.1", "external-editor": "^3.0.3"}, "dist": {"shasum": "126dffc232e3058bd7da8aec0b2ac9427693cc86", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-kxq0cIfqzQlehc/2FYVD9+M2aMBudxMVTcVcvHQ5ZSbzgAvmERmBtxynnETab49MrNTeh8naFEZqqwFvR2d3Ew==", "signatures": [{"sig": "MEQCIAsOXK0NL7y74javlITMnfIuVBiM0MJZ+2wx9DwSKiy4AiA1ZzuNHGH8ZS3ctvIYVXlQmLjKM/9mVJmWiiBotoDNow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14779}, "engines": {"node": ">=14.18.0"}}, "1.2.4": {"name": "@inquirer/editor", "version": "1.2.4", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^3.0.0", "@inquirer/type": "^1.1.1", "external-editor": "^3.0.3"}, "dist": {"shasum": "11da59db4e5af7637b3a4cfdda2b261133e8bcc2", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-ygTTYJ2Y6HMhC180Y7/Oem4Cx0vjfvCQTLvMwUWPv5wxAgizWF129n8u4k8NqavKxV2dybjxa8+0uyv40397jA==", "signatures": [{"sig": "MEUCIQDd0lXkgrKAAFVIjlipLfo21N3UyuttF8SHtNHNM8I9VQIgcGmr23oar21zIfuj9lPCJc9tlXFs8tlaKzUNCdEdIKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14779}, "engines": {"node": ">=14.18.0"}}, "1.2.5": {"name": "@inquirer/editor", "version": "1.2.5", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^3.1.0", "@inquirer/type": "^1.1.1", "external-editor": "^3.0.3"}, "dist": {"shasum": "d79904d46dd431d6e0aace3e78da7da7dc06bb44", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.5.tgz", "fileCount": 8, "integrity": "sha512-OyNWKv87U4BpmPSaKNFjiaayH1GYOrccbXiA5s0Zk+b/j8ESo67rMBv9GiBvm9fl1VbXydZpxGz7WKCIbn6cag==", "signatures": [{"sig": "MEYCIQDRefkqu1OsTdwK+/LLwHeXCUK6PBnfT90glrgjiNfNfgIhANb9uttwjPRDWjrU913ZSk8asp7p4nIk7wDGOAOv9SQD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14779}, "engines": {"node": ">=14.18.0"}}, "1.2.6": {"name": "@inquirer/editor", "version": "1.2.6", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^3.1.1", "@inquirer/type": "^1.1.1", "external-editor": "^3.1.0"}, "dist": {"shasum": "07c75e4c17bb994a6fa756855b64a56d68345cfb", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.6.tgz", "fileCount": 8, "integrity": "sha512-DTKL1eW2oufo21jz/qrAOELX4qJGNKjRQVzejj46pHhcSVE3ox1H/rf2Wkci4SdbsktPPsUSbYfL76InSQcK/g==", "signatures": [{"sig": "MEUCIQCO2a6jkBFJPes9Al2dx76wDPfhW/p2wkZQNyAVJLDPLQIgeoeoa5Xb5JSiV4B852PP+ipgfX8mNAoR8exJ/FCrJww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14779}, "engines": {"node": ">=14.18.0"}}, "1.2.7": {"name": "@inquirer/editor", "version": "1.2.7", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^3.1.2", "@inquirer/type": "^1.1.2", "external-editor": "^3.1.0"}, "dist": {"shasum": "c2c5c4ecac465dc76656ec4454c749885e4df927", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.7.tgz", "fileCount": 8, "integrity": "sha512-7W1b7PHi8l9J1bcNeJXivDnF628QJ9XS3iWneMcaRFuqNj91ZYqfCsYiSwEdzuDRhdqqh13SlbDWvAHRAyBKgg==", "signatures": [{"sig": "MEQCIDkiI9PWlwEdSQ0xvHbikPZHbvDukkc6y+bjNohXiII8AiBGN+Ru2Kd0Ka+E3pQvhXkdGrqZ28p15xAGvUyXzAOgqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14689}, "engines": {"node": ">=14.18.0"}}, "1.2.8": {"name": "@inquirer/editor", "version": "1.2.8", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^4.0.0", "@inquirer/type": "^1.1.2", "external-editor": "^3.1.0"}, "dist": {"shasum": "2721b4da06221727f2bace2fb62a423b6d85f11f", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.8.tgz", "fileCount": 8, "integrity": "sha512-DzvzhtLmkVEzfphCbEQz63IuTwmgpeSyyaKrHwCsKYr/eUaMLksQz33VrHbwYbsBq4oNm14OkikrVIMC/XhhPw==", "signatures": [{"sig": "MEQCIBeuO4vcyJiwTPe7ea8KX4cIfbO0U046+7khNvWroGa8AiB3XqrsXryLcyNQZe9ukuE08VRcqe5RDEGQQfV2YmTsYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14689}, "engines": {"node": ">=14.18.0"}}, "1.2.9": {"name": "@inquirer/editor", "version": "1.2.9", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^4.1.0", "@inquirer/type": "^1.1.3", "external-editor": "^3.1.0"}, "dist": {"shasum": "620c01fc59123e0cd17a1b25c2a8dbc7b78268ec", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.9.tgz", "fileCount": 8, "integrity": "sha512-fD/69q/qOY+mPxnYXKID8NnHTa9J5S5EpSQci9YeePOBKG8HxRFGScvwYI5cnQqEe3SWkpjJHGgdU2czdlb2rA==", "signatures": [{"sig": "MEUCIH46B+ykyCkdn2W140v0c3irp/0tgSDWIpv8txFd6dhGAiEAgUNpxmf0ZdtBQiQ/jCZxbxkD4bITt3TDUz960I+JRtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14369}, "engines": {"node": ">=14.18.0"}}, "1.2.10": {"name": "@inquirer/editor", "version": "1.2.10", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^5.0.0", "@inquirer/type": "^1.1.4", "external-editor": "^3.1.0"}, "dist": {"shasum": "7792e241b4f2b0dbc373b8983a89fb2c8f4df7a5", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.10.tgz", "fileCount": 8, "integrity": "sha512-aluYpazbxIdM54L+xNmVHzOjoXGwkliTCvHxhtPg6itmqDGMVmU1z+T2akHt6Xnx9RyrTpbumFB4xn1iI0UfnA==", "signatures": [{"sig": "MEUCIEM695gmCSfduR/d7ftyrjuo7uITtDWK3v7r3h6Hws0eAiEAjeLZGt7cfyhonfFP79e+0MSfoZ6x275BbNKM9wkRjxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14898}, "engines": {"node": ">=14.18.0"}}, "1.2.11": {"name": "@inquirer/editor", "version": "1.2.11", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^5.0.1", "@inquirer/type": "^1.1.5", "external-editor": "^3.1.0"}, "dist": {"shasum": "81e821f283e457d643c81e8b9f2a082e5dd3f69d", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.11.tgz", "fileCount": 8, "integrity": "sha512-5W<PERSON>jiTZsr8utlmfOFcHIC0o2Mdg3nXUf1ij5IZtgJTFd8QOy7N/sfsZHW0p1wY+YSbX6Sxd+YyUCJ2t6xQiyqA==", "signatures": [{"sig": "MEQCIChA9eXs+W1xVAuO55UD8AeQB2UxCEKpvARH2l1Cgm1eAiBN6sVQ2+iDS/kyCJrTXZHxwyZbEVTvE4bD1/Yslqws6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14898}, "engines": {"node": ">=14.18.0"}}, "1.2.12": {"name": "@inquirer/editor", "version": "1.2.12", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^5.1.0", "@inquirer/type": "^1.1.5", "external-editor": "^3.1.0"}, "dist": {"shasum": "3dfa72253e8a9d915b43f3c8dbc8df85e3c627ff", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.12.tgz", "fileCount": 8, "integrity": "sha512-Y7zXQqcglPbbPkx0DPwx6HQFstJR5uex4hoQprjpdxSj8+Bf04+Og6mK/FNxoQbPvoNecegtmMGxDC+hVcMJZA==", "signatures": [{"sig": "MEUCIQDg1EcoY72LaNyKdjIin6C0zMGMFysLL+NZ1AzCnpojbgIgCEIeSrxPG4bzr+KOarl8CJqqjQaS7ZqVo9yzxp/rVf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14898}, "engines": {"node": ">=14.18.0"}}, "1.2.13": {"name": "@inquirer/editor", "version": "1.2.13", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^5.1.1", "@inquirer/type": "^1.1.5", "external-editor": "^3.1.0"}, "dist": {"shasum": "94bddeeabc043d4a05fbde8523add4db221555d5", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.13.tgz", "fileCount": 8, "integrity": "sha512-gBxjqt0B9GLN0j6M/tkEcmcIvB2fo9Cw0f5NRqDTkYyB9AaCzj7qvgG0onQ3GVPbMyMbbP4tWYxrBOaOdKpzNA==", "signatures": [{"sig": "MEQCICCcuJ2/jmW2l96pjobawxRTicKRtQjwAlnvqklYJGEeAiAj79OQiDk7zhMILbYwPbaGFZ5PN65pjLS2w1XuMq8CQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14898}, "engines": {"node": ">=14.18.0"}}, "1.2.14": {"name": "@inquirer/editor", "version": "1.2.14", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^5.1.2", "@inquirer/type": "^1.1.6", "external-editor": "^3.1.0"}, "dist": {"shasum": "52a3e2a9f0735a5ab6eff16ab4b04e3c2173f75f", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.14.tgz", "fileCount": 8, "integrity": "sha512-KFqhjGHedy638WdCIri4EM1ZmaqOjk3Uc2tm357iHWdzhP9I+Bg9N3XkhvKi75RQpdCi1KG4juskwuwlmtoRFw==", "signatures": [{"sig": "MEYCIQDWHQnZmDScnwb4b9xtpNgB7znlO/FSBSV7Tih6QDKvAwIhAPL0xU/Q5DYaq7ctcsrbadD9GdPy0/U3yLrhGJ+lA3Ld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14898}, "engines": {"node": ">=14.18.0"}}, "1.2.15": {"name": "@inquirer/editor", "version": "1.2.15", "dependencies": {"chalk": "^4.1.2", "@inquirer/core": "^6.0.0", "@inquirer/type": "^1.1.6", "external-editor": "^3.1.0"}, "dist": {"shasum": "28de2dabbcf1e07a37149320093798e3f4856f91", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-1.2.15.tgz", "fileCount": 8, "integrity": "sha512-gQ77Ls09x5vKLVNMH9q/7xvYPT6sIs5f7URksw+a2iJZ0j48tVS6crLqm2ugG33tgXHIwiEqkytY60Zyh5GkJQ==", "signatures": [{"sig": "MEQCIFxxAEUWbhxLXtBYUvXM7RxVo0IySBmDfjvH8c6PPCVEAiBUVMoyQruashcxHQ3FatYXJqZELv5G0h33IpOKXvfL2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15033}, "engines": {"node": ">=14.18.0"}}, "2.0.0": {"name": "@inquirer/editor", "version": "2.0.0", "dependencies": {"@inquirer/core": "^7.0.0", "@inquirer/type": "^1.2.0", "external-editor": "^3.1.0"}, "dist": {"shasum": "42e335d46106b5d880b4e548670a46effc8ef4f2", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-0n3agxb1X23A/lx+MI5sV6s/qeywGr4xmKAzZS7ZhToee7L/6DXotWa/VvvwNEoBT0mSuk9SDIAoQ0zLkJmpHg==", "signatures": [{"sig": "MEQCIGkSruM0C9HyHZxNaOUBNXVga4pzEQ0PsHY1HrsVW2JfAiBsi+leXpogJ7sNidZZnyT8wvTeo2Dhg6/LagHSr4/5Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15899}, "engines": {"node": ">=18"}}, "2.0.1": {"name": "@inquirer/editor", "version": "2.0.1", "dependencies": {"@inquirer/core": "^7.0.1", "@inquirer/type": "^1.2.0", "external-editor": "^3.1.0"}, "dist": {"shasum": "7ae9ba70e7beaac173670ae9922b02a1bc08c6cd", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-zEuySGqc405BfKDTM1zL3oFK8ZDnXgERqu0T1+IvqLJUbC8ZKef6UnfYnKygdpCjVjXmV10e46xr0Bj1GRGHLA==", "signatures": [{"sig": "MEYCIQDR1sUs1N9rHFqaT2dQ7ss57chkLgKtYFILfgGS2gOM9QIhAOYmx/a/JEE9qT7Zi6Y1meBiBKsTIbSkvkXqr5IYLhk0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15899}, "engines": {"node": ">=18"}}, "2.0.2": {"name": "@inquirer/editor", "version": "2.0.2", "dependencies": {"@inquirer/core": "^7.0.2", "@inquirer/type": "^1.2.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.12"}, "dist": {"shasum": "bcd4b864c03b9d5bd7d747225ec616ad5651c27f", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.0.2.tgz", "fileCount": 8, "integrity": "sha512-kWEb0IP2/AYngZdNLIWDT3Ki/ZacAs7xazo57sRGaxvRLCi2acbV6pj8tELxUA7a0WFM6gSHqnq6XZnBzxELiQ==", "signatures": [{"sig": "MEYCIQCk+GVNQYXyI5yjscuQVW6x6gStORb0WfRq5+eepyGBkwIhAMnSp2f25YHyMQOcRRiXobfqoWzu3d6KjwtWNaqS5K+y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16567}, "engines": {"node": ">=18"}}, "2.1.0": {"name": "@inquirer/editor", "version": "2.1.0", "dependencies": {"@inquirer/core": "^7.1.0", "@inquirer/type": "^1.2.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.13"}, "dist": {"shasum": "513c4bc7b41c6aa3795d096aece73b01f3a7c357", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-gBxebaZLATrQyjZnuPLcfM2WxjZG6rjEmnzepJb/0bypi1PgWt9rZoH+a/j1uJx/tF+jhYrvSBr8McEOWcyAWg==", "signatures": [{"sig": "MEYCIQCvnCjYwZwpDKFOqWZz88gpG+d30kokRh9oIHQk1R8qUQIhAPmb258lXUbthpUmfhqLOjorpgVF3774ybJW9o2HKqhz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.1": {"name": "@inquirer/editor", "version": "2.1.1", "dependencies": {"@inquirer/core": "^7.1.1", "@inquirer/type": "^1.2.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.14"}, "dist": {"shasum": "e2d50246fd7dd4b4c2f20b86c969912be4c36899", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.1.tgz", "fileCount": 8, "integrity": "sha512-SGVAmSKY2tt62+5KUySYFeMwJEXX866Ws5MyjwbrbB+WqC8iZAtPcK0pz8KVsO0ak/DB3/vCZw0k2nl7TifV5g==", "signatures": [{"sig": "MEUCIDEa+rjGDETaVsxG54Ym+QLYKdE0AtOOwBaZQIuo8gz8AiEApFW+Aaoy4Qq6ErXN8CVTN0LSUet3B/oEdjoaHdawd/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.2": {"name": "@inquirer/editor", "version": "2.1.2", "dependencies": {"@inquirer/core": "^7.1.2", "@inquirer/type": "^1.2.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.15"}, "dist": {"shasum": "25a1e431db95e3565aeb0a3f245d51f484dc5cba", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.2.tgz", "fileCount": 8, "integrity": "sha512-758pnVt3WTk5x7ITlOTdZ6AIcbt0fGmIMpbZKJAeAejDvFsMyN+A1ODRgPi+yzpng4OsF8p9LamCH/F+5FhtGA==", "signatures": [{"sig": "MEQCIHSQrSPaky0AUxTyhoShb5JssLcXv3GVCGVMrd4oEPDzAiALnUpbUgIsExpISvjLnI/3QNai8d8ku9RdHWo9Pr7/yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.3": {"name": "@inquirer/editor", "version": "2.1.3", "dependencies": {"@inquirer/core": "^7.1.3", "@inquirer/type": "^1.2.2", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.16"}, "dist": {"shasum": "65593954cb053f7f0cbc3c628069d8776f7fe98c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.3.tgz", "fileCount": 8, "integrity": "sha512-+V8/6HogAGKDccBf/i6JA73gP/bDq4K9UbaNn6JPo44k+8IHu80SC5ntOKF3M3h0yEL9UhYS2crloCzJUsSMJQ==", "signatures": [{"sig": "MEUCIF8hCbvLj6AhzevFozgK8L9mYlD5almsO7RYP4M1qPB0AiEA9iGPrHhFbbdCKu/cEYP/255NPZnVz6xlqVqA7gdOZmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.4": {"name": "@inquirer/editor", "version": "2.1.4", "dependencies": {"@inquirer/core": "^8.0.0", "@inquirer/type": "^1.3.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.17"}, "dist": {"shasum": "90ea37dd041f2aebbc06ddf15ec7f416fa47e2e7", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.4.tgz", "fileCount": 8, "integrity": "sha512-bZ/YDEWNzQaKPhwyspy77Hntk9UjqXmQPMc3I3Cqn1pPBlPzliylgJDhgErxyIMFMtd92FpbDoOk5WWlaVpBMQ==", "signatures": [{"sig": "MEUCIQD0xDqkY6e49Gt9MnnCxgxkwGGQ7he1qPjCTtQsRQ/QKgIgDnaIpKccuYocP4w4TSbqxYoHg3SS9GDvbfSVGHxJwlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.5": {"name": "@inquirer/editor", "version": "2.1.5", "dependencies": {"@inquirer/core": "^8.0.1", "@inquirer/type": "^1.3.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.17"}, "dist": {"shasum": "bdbeb9d476e9553440a4dd2e90fd2a265082c3b2", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.5.tgz", "fileCount": 8, "integrity": "sha512-wzMc/lTt79gOCPbBH7LVCrsn36+JHwYPXchDPATYphFTS/XLy50DuM2D79/dn0VIZHfNSw4C8RZNLDUH/3YmpA==", "signatures": [{"sig": "MEYCIQCyxb+d2+GObernY8/j0dqJD0EwuQlhzhY+0uyre5A7ygIhAOKIW0Fj1HQ5OlwTqVo4UsE2YTsSzpAWjdUxwL/VhcJM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16594}, "engines": {"node": ">=18"}}, "2.1.6": {"name": "@inquirer/editor", "version": "2.1.6", "dependencies": {"@inquirer/core": "^8.1.0", "@inquirer/type": "^1.3.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.18"}, "dist": {"shasum": "26d6bb5a3e555e7adf55adcd6c9d0c399f4df516", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.6.tgz", "fileCount": 8, "integrity": "sha512-CWmp6XhfQye6xwH6/XV1HGvY95rUfzw7EXyNDHzj5s5Qr1t/X3t6c7uRkfK7OD91y+sbSy7aL6MJv2bbNrMoew==", "signatures": [{"sig": "MEQCICyNjIoFMlzhoAMvv6IsCVeR7/a+FHAISmuAcUDLN51SAiAxzXhAaC1S/5SuW7UVtmt0loTCHp37fGCgRuDOO4Lm1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16567}, "engines": {"node": ">=18"}}, "2.1.7": {"name": "@inquirer/editor", "version": "2.1.7", "dependencies": {"@inquirer/core": "^8.2.0", "@inquirer/type": "^1.3.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.19"}, "dist": {"shasum": "eafc8a73c97a06c22850d60d1032cfc7e860247a", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.7.tgz", "fileCount": 8, "integrity": "sha512-CGZk//rg57zgXqMp8q8tE2HCc5/rwCC0IwIEtZeb1BF/GJIFlijp4wvN9PeXHsEQ+ul2qRz/0dEk1JqmZzbSbA==", "signatures": [{"sig": "MEQCIFmPfG/HSZptSK+lClHmvF3aCpGqdMns0WTUXZM1/2wWAiBoXS5tRe/uQexyztxb7tUMfpCYqAyhsf0NjASZUrOnbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16567}, "engines": {"node": ">=18"}}, "2.1.8": {"name": "@inquirer/editor", "version": "2.1.8", "dependencies": {"@inquirer/core": "^8.2.1", "@inquirer/type": "^1.3.2", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.20"}, "dist": {"shasum": "652224b651d4fd71c162ab52de5e36ba6110962a", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.8.tgz", "fileCount": 8, "integrity": "sha512-Ob9GdfiDCi9PJSF/tU7+bfbp2bVoFOPxI8Aosqh39TOum3FTyHzUDsZzSmi36KYNWHnmMH33t6bHxg1bBFTwng==", "signatures": [{"sig": "MEYCIQDrRcioFGsHK66eGTE5XjiA3u3XmYc7XvW5WU4UzMVaQwIhAMGM9RM/DvMqPEzBP6drX/KHtDOzfBHuocowKeEHDEaH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16612}, "engines": {"node": ">=18"}}, "2.1.9": {"name": "@inquirer/editor", "version": "2.1.9", "dependencies": {"@inquirer/core": "^8.2.2", "@inquirer/type": "^1.3.3", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.21"}, "dist": {"shasum": "d3b0ad504e6eeed888a3045835ffbc9a2729679f", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.9.tgz", "fileCount": 8, "integrity": "sha512-5xCD7CoCh993YqXcsZPt45qkE3gl+03Yfv9vmAkptRi4nrzaUDmyhgBzndKdRG8SrKbQLBmOtztnRLGxvG/ahg==", "signatures": [{"sig": "MEYCIQDXPuVlZ3JDV9P5mScH7Xx9Jvw4XQQoVGDWfG18EK0utgIhAMKZcKtyyawOZpvit+QhKqLiLsU52PUM7JBrVFG50vpx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16636}, "engines": {"node": ">=18"}}, "2.1.10": {"name": "@inquirer/editor", "version": "2.1.10", "dependencies": {"@inquirer/core": "^8.2.3", "@inquirer/type": "^1.3.3", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.22"}, "dist": {"shasum": "cb7c792bae681eaecbfb209102059007210d0e0d", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.10.tgz", "fileCount": 8, "integrity": "sha512-5e4OlRNzi1TFVKJVBk4WtWYPtVqpKyIGvltP/bqnZ0AQ9bA9Cgukcs8LniUXsgkw3+IAPFQfP8yBxFX/qIz+2g==", "signatures": [{"sig": "MEUCIQDVCtNA0kIzJnVR2dYjBbOp8qMtJSHJKUWgLTI2ck73XQIgdEKFCgwV0N19xv3gRaxZ4HViHESLaqn71CN+zWy6CKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16637}, "engines": {"node": ">=18"}}, "2.1.11": {"name": "@inquirer/editor", "version": "2.1.11", "dependencies": {"@inquirer/core": "^8.2.4", "@inquirer/type": "^1.3.3", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.23"}, "dist": {"shasum": "3ed3c15d7fe5b953686e0137b55c98b42d1174e5", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.11.tgz", "fileCount": 7, "integrity": "sha512-SwBPrf+MrkAz3ZkpPFCI5ZN3Xcj47L7NhfjfEuLvHigmQgKDAwawcO3DdNPT0xYBqy8d5acdPYYWy6wWCVdAig==", "signatures": [{"sig": "MEUCIQDK/xD8k5ABuIo6BJXpfTQiYRT8PEK282DKGs3nJly++AIgWD5YQX4Yaitimipij8Ay4g6wMu/xKcKta9fi8xWAMu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13327}, "engines": {"node": ">=18"}}, "2.1.12": {"name": "@inquirer/editor", "version": "2.1.12", "dependencies": {"@inquirer/core": "^9.0.0", "@inquirer/type": "^1.4.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.24"}, "dist": {"shasum": "bb26dcaa7e49505e19ab652a26acd5ad3bcc8e88", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.12.tgz", "fileCount": 7, "integrity": "sha512-rKd/mOkLlqVqQqQihgD6KM8FqRYRcgF1Y0oiYI8BYUk/KJOit7noWxsydJFQoHfrSU6mrpDtc6a+6wcVYH/V6A==", "signatures": [{"sig": "MEYCIQCmNu/zMBi95ZT2zEpfSCRrelf45LiGB3ZmMje2EKeW/QIhANRqsznDJ+ZYH36K4R8tST9tIvG3f6uXC/y8Tu1jYWbp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13327}, "engines": {"node": ">=18"}}, "2.1.13": {"name": "@inquirer/editor", "version": "2.1.13", "dependencies": {"@inquirer/core": "^9.0.1", "@inquirer/type": "^1.4.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "f7589ba1005d842ecd61cb16d90ec456ef1d4d10", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.13.tgz", "fileCount": 7, "integrity": "sha512-bYNvPyRjZ4/xWcbaIt8pfhoq05oFckEjeflzletAwFLwDYLdqweJftXE/PpyL5HLC6OZ3UBdQ9zkwjGlluAptg==", "signatures": [{"sig": "MEQCICCOqRDQxV65MIzbQ1Kx9ihFMCm61rXi/hOTDIZARlWsAiB10q8F2U07Nd6D00rPJhVKIxgqwntDNdHn17whtBsKZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13432}, "engines": {"node": ">=18"}}, "2.1.14": {"name": "@inquirer/editor", "version": "2.1.14", "dependencies": {"@inquirer/core": "^9.0.2", "@inquirer/type": "^1.4.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "f353a053a41c8c5564de3eef621dd3f38addab9c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.14.tgz", "fileCount": 8, "integrity": "sha512-6nWpoJyVAKwAcv67bkbBmmi3f32xua79fP7TRmNUoR4K+B1GiOBsHO1YdvET/jvC+nTlBZL7puKAKyM7G+Lkzw==", "signatures": [{"sig": "MEUCIQDoHDaN44Uixzp/dbdXM4D7pvC0L3lfad0pxK9TvYHl4gIgD0YuZOBLciJObLt1l4hd5GTZNy4Y3VziWdywfrB2Irs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13865}, "engines": {"node": ">=18"}}, "2.1.15": {"name": "@inquirer/editor", "version": "2.1.15", "dependencies": {"@inquirer/core": "^9.0.3", "@inquirer/type": "^1.5.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.26"}, "dist": {"shasum": "e1488c157033eae889d68c8fb44548a9a1ceff2c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.15.tgz", "fileCount": 8, "integrity": "sha512-UmtZnY36rGLS/4cCzvdRmk0xxsGgH2AsF0v1SSlBZ3C5JK/Bxm2gNW8fmUVzQ5vm8kpdWASXPapbUx4iV49ScA==", "signatures": [{"sig": "MEYCIQD4poE7WeG/2kOZ8bNJjnNhKuPawzQ9oKeL8IOdwDwD9wIhAPEIlNVtHF9QQzcV7lRdJWgXWom5rYPITIVhYTwnRR4Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13871}, "engines": {"node": ">=18"}}, "2.1.16": {"name": "@inquirer/editor", "version": "2.1.16", "dependencies": {"@inquirer/core": "^9.0.4", "@inquirer/type": "^1.5.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.27"}, "dist": {"shasum": "f71c26658391e08be24c1f1d8ae4a0611a42c38c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.16.tgz", "fileCount": 8, "integrity": "sha512-SkrpBFUK1XqCS5a66v2dnsjMoXyuxC+2golkM0NoT7XYq47eY8RVFnt5oOjj257MmXjbuSLcc7iQb7bFasHTfA==", "signatures": [{"sig": "MEQCIEavL9rGho8Ii96NyaTeAcsmOnxTiByaW/KTAuWuOev0AiBto0NBaY4M6nGpapQOi0VvPj/JpcB0sWCLxXxCjqEAvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13869}, "engines": {"node": ">=18"}}, "2.1.17": {"name": "@inquirer/editor", "version": "2.1.17", "dependencies": {"@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "954dffb07a362edabdec3e8205c2efc215ab44a7", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.17.tgz", "fileCount": 8, "integrity": "sha512-hwx3VpFQzOY2hFWnY+XPsUGCIUVQ5kYxH6+CExv/RbMiAoN3zXtzj8DyrWBOHami0vBrrnPS8CTq3uQWc7N2BA==", "signatures": [{"sig": "MEUCIBBk6p/n5wPLtOMH13uv3kW9RW8ZRzF4teOvuLpruznOAiEAnEpAL6PwXRparw6/VRoPQDv8qyqNHGwlwmaME0cewu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14035}, "engines": {"node": ">=18"}}, "2.1.18": {"name": "@inquirer/editor", "version": "2.1.18", "dependencies": {"@inquirer/core": "^9.0.6", "@inquirer/type": "^1.5.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.29"}, "dist": {"shasum": "872e92d3cb2c35e55fb04a5af5c0c0a264816a22", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.18.tgz", "fileCount": 8, "integrity": "sha512-DwDgYZziNSw2icITM80LRpALeFFpV6flBOUkb0EYFOTb9TnH5xp15lcExVIIr8bzjnzcQ+tAXZXJ6S8ib1iv9A==", "signatures": [{"sig": "MEYCIQDvIn7hKUgxhg/9DTzSOWD9VLMLBYloymfAyRW+b5aXvQIhANp1jkb8jcS4DlFc6Gx1L/VCghrv/IEoSyJw4Z19TZxY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14035}, "engines": {"node": ">=18"}}, "2.1.19": {"name": "@inquirer/editor", "version": "2.1.19", "dependencies": {"@inquirer/core": "^9.0.7", "@inquirer/type": "^1.5.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "32254a60ee4fd5dcf559fc65ecf3e768f160cec7", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.19.tgz", "fileCount": 8, "integrity": "sha512-Tho5lqe3LNpPnZSC0B3KiK+pUMPt7sJAEf2bFr8891qhsOkymj/FFE0NKkdS5oFlPSdTalP0bgjvOhXVR73U5Q==", "signatures": [{"sig": "MEUCIEXgF5KJKtgRwlWuYkKcoEV7wQa7UUvhHivKNrqVMFF4AiEAwOkkbnEwlZNXndttKN1XD5gRRr2n7Y17hzYUJkO0iKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14035}, "engines": {"node": ">=18"}}, "2.1.20": {"name": "@inquirer/editor", "version": "2.1.20", "dependencies": {"@inquirer/core": "^9.0.8", "@inquirer/type": "^1.5.1", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "1742e83de76b4b5fb49fe91fbefff0fafb787a13", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.20.tgz", "fileCount": 8, "integrity": "sha512-vtIN9NwXldX8SWbPt5biJhnTpHJCzF5nSymcv4hcOxiCrOpXmgOvFYGpAY729KODF+5e1OLqPbJ8ApiwPu/peQ==", "signatures": [{"sig": "MEUCIF7hl/f15tncAoiiVJc/haZQG2wKWiLZacch/r434rVrAiEA6QOv1S+IQgNnWkHnqtrk9QexP5UC4G2bRp1DJJDBs1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14035}, "engines": {"node": ">=18"}}, "2.1.21": {"name": "@inquirer/editor", "version": "2.1.21", "dependencies": {"@inquirer/core": "^9.0.9", "@inquirer/type": "^1.5.2", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "2649552daa1f0e6e444fdf1cbce71ba0ccb8b3b4", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.21.tgz", "fileCount": 8, "integrity": "sha512-p5JYfAmEA6nqqDVCX0Cuu6EACA6/qejVBVataMew29mld3mtBWXy1g29Co86UMDQIiHA4HpOkH0hQGHlOvbGSw==", "signatures": [{"sig": "MEUCIQDMk8YTARloOFrcERZBGKhwe4Xe9XuVqP8ZJXPAx/CsnAIgVLRyDyVGaryDgNuvLYFipEFL30JF9p1uyyUM2fl8Kh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14123}, "engines": {"node": ">=18"}}, "2.1.22": {"name": "@inquirer/editor", "version": "2.1.22", "dependencies": {"@inquirer/core": "^9.0.10", "@inquirer/type": "^1.5.2", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "f97eda20954da1dab47df9f4c3ae11604d56360c", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.1.22.tgz", "fileCount": 8, "integrity": "sha512-K1QwTu7GCK+nKOVRBp5HY9jt3DXOfPGPr6WRDrPImkcJRelG9UTx2cAtK1liXmibRrzJlTWOwqgWT3k2XnS62w==", "signatures": [{"sig": "MEYCIQC4/thW2j36iE0AekIq8d1zcfVzXgEtN8LbUgCk3sQtJQIhAIWyKmVJYjSsWXZbv7a2A1pV1aj1KtWDJSQwyX3FvUOF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14124}, "engines": {"node": ">=18"}}, "2.2.0": {"name": "@inquirer/editor", "version": "2.2.0", "dependencies": {"@inquirer/core": "^9.1.0", "@inquirer/type": "^1.5.3", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.32"}, "dist": {"shasum": "a41eb7b151bd9a6bc3c0b69219d02d82547bc387", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-9KHOpJ+dIL5SZli8lJ6xdaYLPPzB8xB9GZItg39MBybzhxA16vxmszmQFrRwbOA918WA2rvu8xhDEg/p6LXKbw==", "signatures": [{"sig": "MEYCIQCK7Zez5D3qcgKwnEU6sWNsZEzDRGe8i6kTj3BybBSfpAIhAIMKYr1fg9PHiiQwaxhlxBp/ilpJag4tYtkO4gCPbTPO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14122}, "engines": {"node": ">=18"}}, "3.0.0": {"name": "@inquirer/editor", "version": "3.0.0", "dependencies": {"@inquirer/core": "^9.2.0", "@inquirer/type": "^1.5.4", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.33"}, "dist": {"shasum": "1d6840cf468509c8712218d7805dc5387a9305ac", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-aFXpH025HBeIPdyytcriLzyxAP27Rw17BjwhthvH7D3AAyGd1foo8dIR6oGUnMTZViuoNODwIQmoN0PUu4bckA==", "signatures": [{"sig": "MEUCIQDUmxoOmsHTOPkSdOIjBykBarwd20DtD1ecSpeho1QDxgIgNS0TwRmU7kppQggH+YPFodJONjUJBWtGjlJ8LvZOaFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14090}, "engines": {"node": ">=18"}}, "3.0.1": {"name": "@inquirer/editor", "version": "3.0.1", "dependencies": {"@inquirer/core": "^9.2.1", "@inquirer/type": "^2.0.0", "external-editor": "^3.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.34"}, "dist": {"shasum": "d109f21e050af6b960725388cb1c04214ed7c7bc", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-VA96GPFaSOVudjKFraokEEmUQg/Lub6OXvbIEZU1SDCmBzRkHGhxoFAVaF30nyiB4m5cEbDgiI2QRacXZ2hw9Q==", "signatures": [{"sig": "MEUCIH69uxthKz/SQv05181nti4RLySGcpGvLEPaSJqxntZhAiEA0DHY4GPRrZrodPgnGy5/3vyfqnQzlp7AkyLHRSDF46c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14090}, "engines": {"node": ">=18"}}, "4.0.0": {"name": "@inquirer/editor", "version": "4.0.0", "dependencies": {"@inquirer/core": "^10.0.0", "@inquirer/type": "^3.0.0", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.35", "@arethetypeswrong/cli": "^0.16.4"}, "dist": {"shasum": "9fab50aa83a8b8625d1fe78a45aa239e217d63d3", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-bhHAP7hIOxUjiTZrpjyAYD+2RFRa+PNutWeW7JdDPcWWG3GVRiFsu3pBGw9kN2PktoiilDWFGSR0dwXBzGQang==", "signatures": [{"sig": "MEUCIQD71pjpmbOr/fLPPq1Or94j6/QjG0Z02si2yz4VVdT30QIgOVSa8F+162ybTYh2/9Anvje3NyuOA2lLqeu6X2S27J0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13143}, "engines": {"node": ">=18"}}, "4.0.1": {"name": "@inquirer/editor", "version": "4.0.1", "dependencies": {"@inquirer/core": "^10.0.1", "@inquirer/type": "^3.0.0", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.36", "@arethetypeswrong/cli": "^0.16.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "5db61ad3f1ce1b468b4b353d661787dcfa52a3a3", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.0.1.tgz", "fileCount": 9, "integrity": "sha512-qAHHJ6hs343eNtCKgV2wV5CImFxYG8J1pl/YCeI5w9VoW7QpulRUU26+4NsMhjR6zDRjKBsH/rRjCIcaAOHsrg==", "signatures": [{"sig": "MEYCIQDBNlHmnMSXM8jf+nJb7QjPDrxJ+wFPiVASN23T2Jg4ggIhAPnJymyLc0j05dqf2xwopwuA8DtCIL6/k+zOAnypbP88", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13198}, "engines": {"node": ">=18"}}, "4.1.0": {"name": "@inquirer/editor", "version": "4.1.0", "dependencies": {"@inquirer/core": "^10.1.0", "@inquirer/type": "^3.0.1", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.37", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "bc1a8bebe5897d4b44b0bfab1aeb1b5172f8d812", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-K1gGWsxEqO23tVdp5MT3H799OZ4ER1za7Dlc8F4um0W7lwSv0KGR/YyrUEyimj0g7dXZd8XknM/5QA2/Uy+TbA==", "signatures": [{"sig": "MEUCICYgw00j4j/XRWclN+f31N9BZjgbUJE5aY6zvxHRaJGSAiEAmBbcZn6tNFAePPnOKy7ah22VCIIGZ/SuKEv4ffLCb1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14045}, "engines": {"node": ">=18"}}, "4.2.0": {"name": "@inquirer/editor", "version": "4.2.0", "dependencies": {"@inquirer/core": "^10.1.1", "@inquirer/type": "^3.0.1", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.38", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "469a00e876afebcfc574bf8114e40c40795688c1", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.0.tgz", "fileCount": 9, "integrity": "sha512-Z3LeGsD3WlItDqLxTPciZDbGtm0wrz7iJGS/uUxSiQxef33ZrBq7LhsXg30P7xrWz1kZX4iGzxxj5SKZmJ8W+w==", "signatures": [{"sig": "MEUCIE+C3u/1abC33bUrzaE9a8gqPNDg5G4KmZ8z0dxL8wvbAiEAx0f6hhonIEVp4k3xfDlix2f6iVO9nBx3dHSr89eC2tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15042}, "engines": {"node": ">=18"}}, "4.2.1": {"name": "@inquirer/editor", "version": "4.2.1", "dependencies": {"@inquirer/core": "^10.1.2", "@inquirer/type": "^3.0.2", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.39", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "9887e95aa28a52eb20e9e08d85cb3698ef404601", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-xn9aDaiP6nFa432i68JCaL302FyL6y/6EG97nAtfIPnWZ+mWPgCMLGc4XZ2QQMsZtu9q3Jd5AzBPjXh10aX9kA==", "signatures": [{"sig": "MEQCIHH6kfeLIp7ovEFkoJxJzzqCh8FQvZQgi6jpERCU1c/kAiBPjV8JQ3VnjpjedlD/PJmnmcL5k/G7Oc+O3recFKuJ8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15042}, "engines": {"node": ">=18"}}, "4.2.2": {"name": "@inquirer/editor", "version": "4.2.2", "dependencies": {"@inquirer/core": "^10.1.3", "@inquirer/type": "^3.0.2", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.40", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "1a6d63eb3dcacb54f20c499d7e67544d5a125b07", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.2.tgz", "fileCount": 9, "integrity": "sha512-BPLJsWxLO6r47wU2qtGG+akQuoSCotDlOu8GTIkJVxJpNNVYnA60xKHkSGbEAALO+D3DFeRXE0JFvFJ53sVbjA==", "signatures": [{"sig": "MEQCIBChb2rGuR0ckvRBTwo/Wi47Q76U1B5/+7EcALuAbKI0AiASqSBzRUEUmMBslnG9rQW2EGWBpQNXNEEtQaJt7++naA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15508}, "engines": {"node": ">=18"}}, "4.2.3": {"name": "@inquirer/editor", "version": "4.2.3", "dependencies": {"@inquirer/core": "^10.1.4", "@inquirer/type": "^3.0.2", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.41", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "0858adcd07d9607b0614778eaa5ce8a83871c367", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.3.tgz", "fileCount": 9, "integrity": "sha512-S9KnIOJuTZpb9upeRSBBhoDZv7aSV3pG9TECrBj0f+ZsFwccz886hzKBrChGrXMJwd4NKY+pOA9Vy72uqnd6Eg==", "signatures": [{"sig": "MEUCIBpKBzOrbbQVmTyauIRlRKmnWzInpuSXxXR+S6oN70/oAiEAjud9pqYzEbzhiFURu80xx3VfIwQ24qLxGIBaBArEgUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15650}, "engines": {"node": ">=18"}}, "4.2.4": {"name": "@inquirer/editor", "version": "4.2.4", "dependencies": {"@inquirer/core": "^10.1.5", "@inquirer/type": "^3.0.3", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.42", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "1b2b6c0088c80375df1d7d2de89c30088b2bfe29", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.4.tgz", "fileCount": 9, "integrity": "sha512-S8b6+K9PLzxiFGGc02m4syhEu5JsH0BukzRsuZ+tpjJ5aDsDX1WfNfOil2fmsO36Y1RMcpJGxlfQ1yh4WfU28Q==", "signatures": [{"sig": "MEUCIGbX+U43J3HMvwtfI1TiS7/LoHL1wkg97tF8G3fx0lqBAiEAqe82RtMX4jVWb6ZznGvfTzTV8WOc+POGF5qEGynfPZE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15654}, "engines": {"node": ">=18"}}, "4.2.5": {"name": "@inquirer/editor", "version": "4.2.5", "dependencies": {"@inquirer/core": "^10.1.5", "@inquirer/type": "^3.0.3", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.42", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "9b94c0c290d6c94bf71d0c9d9b054b510b63db2b", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.5.tgz", "fileCount": 9, "integrity": "sha512-flpme7eferTprqcQkBu+xYLBWvt2nA/IUG/lOEiE/Sl0AGWWE8zFKk1H6HaS3YxPTPPKHJ2I05M7oGBPwlcXVA==", "signatures": [{"sig": "MEUCIGb+5AyO/fM7vY6UqJ8XuhnElXJlgBNfVapCOAsZGz23AiEAjR7V0pfo+rDvec6DfSYn4veku/WKBety++o/gEH05ME=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15819}, "engines": {"node": ">=18"}}, "4.2.6": {"name": "@inquirer/editor", "version": "4.2.6", "dependencies": {"@inquirer/core": "^10.1.6", "@inquirer/type": "^3.0.4", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.43", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "dec442b9f7ada0804bb9ba689370cc05fd385b20", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.6.tgz", "fileCount": 9, "integrity": "sha512-l0smvr8g/KAVdXx4I92sFxZiaTG4kFc06cFZw+qqwTirwdUHMFLnouXBB9OafWhpO3cfEkEz2CdPoCmor3059A==", "signatures": [{"sig": "MEYCIQDR7BUQFK0CBuQ1wmaSp5EdxpIccuuk94U3w1AP0TeyxQIhAMghPLAQaN9AWzu2fpPZF1Ija9jeY+Xw7gzy7nzobwUo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15902}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.7": {"name": "@inquirer/editor", "version": "4.2.7", "dependencies": {"@inquirer/core": "^10.1.7", "@inquirer/type": "^3.0.4", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.44", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "61cb58486b125a9cbfc88a9424bf1681bb7dbd2d", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.7.tgz", "fileCount": 9, "integrity": "sha512-gktCSQtnSZHaBytkJKMKEuswSk2cDBuXX5rxGFv306mwHfBPjg5UAldw9zWGoEyvA9KpRDkeM4jfrx0rXn0GyA==", "signatures": [{"sig": "MEUCIHYibSsKnaUxW8wx1XuAoMQPWHOQLmpt+dpcpzzLRxfBAiEA5DYjr20DmFqHMogpBA7Y6YZp8RzJ60ISsMnCRMJ3yiI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15290}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.8": {"name": "@inquirer/editor", "version": "4.2.8", "dependencies": {"@inquirer/core": "^10.1.8", "@inquirer/type": "^3.0.5", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "f8b5536b248c84aed198e8044084c4aed6995ceb", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.8.tgz", "fileCount": 9, "integrity": "sha512-UkGKbMFlQw5k4ZLjDwEi5z8NIVlP/3DAlLHta0o0pSsdpPThNmPtUL8mvGCHUaQtR+QrxR9yRYNWgKMsHkfIUA==", "signatures": [{"sig": "MEYCIQCrFkR3QsIbq+ZrFRV7X6KnMUF0UsQp8BVjMlaOuQPYeAIhAMd8Uw9DZf0DfmUdcomPPeIyfxmNxICAfwhjHlRKOvXA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15290}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.9": {"name": "@inquirer/editor", "version": "4.2.9", "dependencies": {"@inquirer/core": "^10.1.9", "@inquirer/type": "^3.0.5", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "4ff0c69b3940428d4549b719803655d7ae2cf2d6", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.9.tgz", "fileCount": 9, "integrity": "sha512-8HjOppAxO7O4wV1ETUlJFg6NDjp/W2NP5FB9ZPAcinAlNT4ZIWOLe2pUVwmmPRSV0NMdI5r/+lflN55AwZOKSw==", "signatures": [{"sig": "MEYCIQCWuubmGVOS2r29Km/CPHHLHTDiL5SH/KWL3ylWtmS0DwIhAMle4zoiKH0MzuV+ey6ShsOi2Tc82dOgyJvMWor7BBuA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15290}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.10": {"name": "@inquirer/editor", "version": "4.2.10", "dependencies": {"@inquirer/core": "^10.1.10", "@inquirer/type": "^3.0.6", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "45e399313ee857857248bd539b8e832aa0fb60b3", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.10.tgz", "fileCount": 9, "integrity": "sha512-5GVWJ+qeI6BzR6TIInLP9SXhWCEcvgFQYmcRG6d6RIlhFjM5TyG18paTGBgRYyEouvCmzeco47x9zX9tQEofkw==", "signatures": [{"sig": "MEUCID2CawYl9qNoOSpjh9yhgG2NBN+GaKdxY1junkd7xJEcAiEAjMA/EkbhRtJtAMPHOMia2RyPAU8D/Xo7Z8xg3rOciHs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15732}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.11": {"name": "@inquirer/editor", "version": "4.2.11", "dependencies": {"@inquirer/core": "^10.1.11", "@inquirer/type": "^3.0.6", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "71cee5d50bbcebcbc5e6e8c513b6a5cb7292d990", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.11.tgz", "fileCount": 9, "integrity": "sha512-YoZr0lBnnLFPpfPSNsQ8IZyKxU47zPyVi9NLjCWtna52//M/xuL0PGPAxHxxYhdOhnvY2oBafoM+BI5w/JK7jw==", "signatures": [{"sig": "MEYCIQDQb9MyvHTRqNqVvR5I7gGxSVKiaWXfPTUDdF6cODi6ygIhALOO44Ma8J6SLQ8CE0N+CDl+3VQa4IfBo1RkZnmOnUJo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15695}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.12": {"name": "@inquirer/editor", "version": "4.2.12", "dependencies": {"@inquirer/core": "^10.1.12", "@inquirer/type": "^3.0.7", "external-editor": "^3.1.0"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.47", "@arethetypeswrong/cli": "^0.18.1"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "babea42162417b96ef87ef801c8ecf1028a8fe27", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.12.tgz", "fileCount": 9, "integrity": "sha512-YNOCY79iqI/ksWohdudGtnO02N/a2j82b6akK/+hy1/C6xoU07dsKFUBfQ36nLCxE98ICS74Uyandq7nBS31Mw==", "signatures": [{"sig": "MEQCIFj2WgJyPGMIGOAK+a6A1fOCRniyzMDGBNBAXbW1YecjAiArjTmRycPlVysZhXRyz7EDOLBOLtLKb6KnYofq9j+gIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 15695}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.13": {"name": "@inquirer/editor", "version": "4.2.13", "dependencies": {"@inquirer/core": "^10.1.13", "@inquirer/type": "^3.0.7", "external-editor": "^3.1.0"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@inquirer/testing": "^2.1.47", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"integrity": "sha512-WbicD9SUQt/K8O5Vyk9iC2ojq5RHoCLK6itpp2fHsWe44VxxcA9z3GTWlvjSTGmMQpZr+lbVmrxdHcumJoLbMA==", "shasum": "dc491ed01da4bab0de5e760501d76a81177dd7d0", "tarball": "https://registry.npmjs.org/@inquirer/editor/-/editor-4.2.13.tgz", "fileCount": 9, "unpackedSize": 15318, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGZnmLbolVG+LeUFuJQRSq+T+WzWVU+QG12ePutmqMKPAiBOOK2jaNzCSttnWnda1ZHWpHcZ/ndagpaQBf3AB8tgug=="}]}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}}, "modified": "2025-05-25T20:55:52.372Z", "cachedAt": 1748373705403}