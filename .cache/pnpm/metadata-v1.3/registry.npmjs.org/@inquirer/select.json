{"name": "@inquirer/select", "dist-tags": {"latest": "4.2.3"}, "versions": {"0.0.5-alpha.0": {"name": "@inquirer/select", "version": "0.0.5-alpha.0", "dependencies": {"chalk": "^2.4.1", "figures": "^2.0.0", "@inquirer/core": "^0.0.5-alpha.0"}, "dist": {"shasum": "4a53d91cfed1d676cfc9a2eebf09af22212a4f09", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.5-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-mg0ULH3e2QStr9lGE0I5ZkzUOHmR1Jiwn8sYoY88MeK5vW2IaIG9bvSv+V/znLR3xAcv/Z4fnRJVeUzNdxVVJQ==", "signatures": [{"sig": "MEUCIQCK/L/7UUTaHeMe7L1ufErxuKmiatfvM3648e9Xf6nX3AIgAsoEGxxbhLxYUbcEQMY3E/CnIIBdsHcwj1OV4IkDKWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCxErCRA9TVsSAnZWagAAXUMP/AnBNSNPp9u0VZ3v4L69\nZAMsFDe8N4Zv/QxrXstPVaIe1B+vQhycbY8+qLGTDyE8KJHAjh7BWrcSWq68\nSA6W2o8ufpn98pNRST8UbqnydmQaHAdQzemwBRw3l7+oUO31EAqWa2l9xii7\nPKcNsCY3rbPn4OZ3HmRdB5DBeXaeRaP3E50vLbgyQL6ZQovexUQl+b5q5+Ll\nGMnvnO1u5vfd2aHWE9DiZ3lXcAn4zfSIohxKnarik8HLciPDALkBGFdLwaBn\n+BxERLc4b9Mku34pjXZTQtAiKuaJVciHQxiIB3HFqk5fEjqK/RP5wxxtbA5P\ni5kHzoS6Fu25NBkvNHvZGjLGlE1dlSylQxzGM1XVuLBfdzPONJGeXfN4ze9g\nTmVzetLB2sX7m+YhwEpAnZyyB11Yyei3HjCfYCzRpCHj0E9yJij+WHwS4Ipu\nU8FpeMwu6WEv6VB7Hv/NIArawy/VmqWRsRIdFg/EkHR1bk5PfWd2mrPponbK\n4QgQcKDKBoUAngmd6uqFZVZVle2z4Q7sgycsjqIB8K1f7C2io2HcKfPY2T4Z\nwn1hbNehoYNrSJq6E5Ci881byQJl9jcEgUwGjjoz2q043+UW+ArovMj4wA8P\n49jsmTfigbpSIMW07jCGPjkiO83tkgIGhvBd5Tcgaxw4ho0dmtyVKlKWFH5q\ntS4z\r\n=9E8+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6-alpha.0": {"name": "@inquirer/select", "version": "0.0.6-alpha.0", "dependencies": {"chalk": "^2.4.1", "figures": "^2.0.0", "@inquirer/core": "^0.0.6-alpha.0"}, "dist": {"shasum": "ead541dd58ed9d0c82cf7d11404d93cc4f5a4d29", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.6-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-YqBEvXDjcb10L1L6fakml/M4tT1bVXCmM2nE2sds1CmV9fR8NjFqikzZAi825YENmFRvsZfPJjhqn+4Z5p0Wyg==", "signatures": [{"sig": "MEUCIFJilszYgfZnDPGSUuyInYuA7m0P1/lhcYPL2hI2vmbXAiEA2iuhXgFsLp7dMrx2G/zkXEqVYpXRshcsH/mJyjtDKic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJr86CRA9TVsSAnZWagAASGIP/02JfIi0oMtyeNDg3iP+\nKZXr3yUIkwhAYqqT67krnJ1IRKtS/dkOzaibrEWzw2xdxRWNBDVtN87/v/ZC\nws9CLbp6pYk4JybwuUxIZ7qLQCq4Zj9sjN1vWD5516EpM3q7PMtadnBfVJIi\nI2STuHuR67hV4rpD/N6Led73wt1YebeL8HN/cjVWFdXJ0lNG/TpdC8o8bbyD\nUKA+YXhkhxb5XI1TsMzCTE3JW35/U2OEoVe4pLU6ddVdK6jfh4K2oaRV9eAg\nrRWtOqwBr7l6vDS80MPJ6rFCvonHPX/tidOBaz7QQDKWbYafNrxTxucG9OcJ\nPk93RRnJe7f71a5N+QlieIVH4Jq3m1FPho7pvfNOsxtk0fpTXcliKdj7NgAz\n19Ok7y/iDFsdH/VEdQCvmo19rsoiYVzi2JMPRNBS+TJHtfNrIMJMeG+ud1hy\nrE5Go2YDnvv/51Mdp3HPM9ql0jofmLSpu0a+kmG9nHkATLfEtsYR15gtDxA5\nz2N1yW05DANpSgd0AmpGX0v2OpLOzus8nARnMAlmpHeRUa/C/XOMZp1M733P\nQ5oDGgtu8HLwo0PIkH/ejOZcwEG+XOlI1S4pdHtxmeQ9vWYbS1eH0kJAZHe/\nQOoaVSstlzIEGkdew2itXplvUQ8crqvS8+673Qpb14tZwcrB78ADqE1SJokq\nJu8Y\r\n=ljUS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-alpha.0": {"name": "@inquirer/select", "version": "0.0.7-alpha.0", "dependencies": {"chalk": "^2.4.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.7-alpha.0"}, "dist": {"shasum": "66305ee6f5848ef4e4bd82e4a48396fe4b0e1218", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.7-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-XTrErO+dVuyZkH+s6fOvlBB8lpICs1hnu1sOVn/NCoezyiOGsYhKS0pBjnDJzzi/Mak+QXkqJX0YMQY91VnQMg==", "signatures": [{"sig": "MEUCIQD3dsflxXxEYt7r4J3kIAYko5Zb0h6FnvCbmkC6JGMM0QIgNe+5PopxM7jaNOzT88XfauEPkIuXeY0eY8+LfFhY0vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUEZjCRA9TVsSAnZWagAAD64P/0AaxilMS9LyYIJskm7h\nfczWPivHW6lzuqiBskl5v0vPY13hYcS/KC4hx/x2xQIqpCGKIV6kUmh514cS\ne1OlvNJmYHt5kk7Imu47oaW2Vglx0Oa1D0bMfCB9qWBUCcjhG4KuLhrwF40K\ngAIe+2LOLb4FUJdzUk/0EsmGDJgNJIOcpS06cVLJmLeysFlMpR2Tj9fOSnBm\nfkK6dNKCx8AZY2oDNkV7j0u1Ab9BYQm6tAJJZ5Im9BdkyIv4+gWVy0IYb3Fm\n7rAlW1njTi65xNfDqAi8juvUtUrnIdsr6d4wixN6ypaCysc0kQgBUjlOED9Q\ni4s6L8S68zN18KA798Awi2vqGz/crvTdI4HwEAio0jJB6W1wwK5bEAK2qxII\nJ5QKPxpASxM/QKgwajlgwise4aloOFt8dAfVhIYepOeUeEM/aa8y6T+145pW\nqIBdasxbl/+4QRFLkV1AthqoSZvh72V4X0kkhqN4ReqOhsIP3VPIO6kSzAHl\nvNlyne676KJ66cp4r6cyfIGWGMBvt6pYEE2pUpala14SsYfYYSj/WTYn4Wxd\nljjnMfthDi6v0Hnb/tNFSFpb3ZQoVr21BUFWBv6HDIozhiKeS3N0KjfU4q5V\nPX7p6SrIw/zI2v+YCHwyIXmPJ9oCjtdkSjXHltF0quc59AZIgrBvL0S0kYbz\nKi+Y\r\n=ibQ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9-alpha.0": {"name": "@inquirer/select", "version": "0.0.9-alpha.0", "dependencies": {"chalk": "^3.0.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.9-alpha.0"}, "dist": {"shasum": "2e70a77d44e23a74446fec67d6dc34de8a54d5de", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.9-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-aDC5VMWBeKpBVZxKWic3uHCRkGrF5c6NUcON7qeiMuYtht8bzrb8tULuuV+UoPKJ6QtYg1qhC/ii8a7zW2KVHA==", "signatures": [{"sig": "MEUCIQDZ3+53rnekcFVBS9YO5FYpYtYYMb/Dy1AkczXj18wZJgIgYUcZ9yvqIRXwrKZpTwdAjZaYgm5Boa/FUckhYftf0ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWf9dCRA9TVsSAnZWagAAgw0QAILMLBhQXIDKvAh9H7v/\n0x9DHmU+xijJpOQnylf+R20zbCyhERthIs3c0vXOJa51ZIfZ9GwpnMOyseGE\nC2aRg/aWA/TWGCI7IDYsKHXueoiCBAoGOlXik4pYRzBC1FeJ4G1IWnCQe2X0\n5iWND1g0ColgTVMUswAl7KVpLIMbNN3PybjVr7b/vNATyRYv4iGzhshj5HNC\ngYLJsKHFljmrdy+MG0FQEqArJfHjIKzAGOpysbMJTifdpD0k/yTYsJGSvjjH\nt7kmUxTg1EufCJ14uDiRqZYtUzbbg5Rqolmrp+1421wU5LPvRQX3iASPc0gx\nVD2BiQYko2ZdrInSFipA1Lq7qE96w4YB+gFloBgqbe7Ps9pEa+LJfIHNFlrp\n2mDbfP040uQy0YUr3Q5uAKNZWi0cnHG6pMOoswqLAz1ITcrrhhmLAYNlvIBJ\nzf9iDzBQ4yt2a6LRA5RNG4ftP6k5oNBxoj9O0uRYs4WtvJuMY5Z7/Y+b8cGK\nhI3ElcE7aKTomzQYJi/ZXE0LidRVm16pLXsVD6vwopF6GlWgB7rk6N+rDNpY\nHbdqGuw7BHmy9tyWowaGyO+0iAmO5sgHtwRXoLImSI8hEfWSJevC9fLN/IeG\njyJlGcwY/Oh0dCktRjpbvCjt3gfZ3MqpcdG4QQtcOFW+ZNsvcgMnOsZu1PNb\n2ryZ\r\n=PaUS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10-alpha.0": {"name": "@inquirer/select", "version": "0.0.10-alpha.0", "dependencies": {"chalk": "^3.0.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.10-alpha.0"}, "dist": {"shasum": "0d09ae72dfc5a45a0c7887632f1fd45d8d6cee50", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.10-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-USLIa0w+/8v4yaMOkfksHktI4fE1SZAmg61bN+x44DCKA109hvChPkBE8fH+zGG2rEkIwAN77anYFg7xeX8QIg==", "signatures": [{"sig": "MEQCICpp/sF9xmy6kh/em4s/tEBPrWz6tr0S+bhMW4GOwRENAiBuuimpwHwpUiORb2zFzNEF+9znfq+A59JzhUR+cuX+hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5tRUCRA9TVsSAnZWagAA2p8P/jA0XAtX3TPQ7EWfjBQS\nd16DbxuJolsQvEvzwWk6kETBUFIr8GlZ0GiHY0VQ25YqdUU8hKNBmsALHQMF\nXKh6niKqDYRjc7sGtndgafT/sAyJ2RPX2NWqnA9FR4zENWfauDmiq9odW5ZQ\n2Wz/SLZtFhjifZzrwsJwcs00qXyf+BFC0LI0P3V+7GKk/WwyeV+3uRTQjkxU\ntRLniSP4EpSwFmsE+qPqqAUdYLWCjMjctSNAJqN1I4Hug44053t7qM5QjkZT\ngLcVCTULSF6hWYpLzNGJh7fq22XBbdW9v/OHM//RVHrkBfAz5P2kq6rjxOuv\n3nX/8O2aHIN/0hjOHwR48oQh1IiCIgsGyNBWNXPBEQcHXQKNogAs5ccdi3gA\nccGsnv/EaT/E+MXtebklP7s9P9wZhj8LJrhkxdKV/QJ5hcbYKSR7tZ4LlVlk\nyI3u+2Qg9apDJ6nmlrKlHFQrtkYc2qWC2a6TVC+HV6BgciLInZiCbbvVScyw\nhWrr1fJ6GMXVuqWg3NWL062PZSyTcMMLu6Ts+elGYvDdbHu0VUmWHLOJK/Fz\n+6Wb0+QWvmKDvCgEcKuRhKE89GC3cidmN/+sM7YApO0gKhY6HxovCpxTjXn3\nWzoBF9/pv1fmSkw4veqNJEWa/XqrJhQQYDhCGXAEA9y4t0Kv8LTD+mCmEK9e\nq266\r\n=aWVF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11-alpha.0": {"name": "@inquirer/select", "version": "0.0.11-alpha.0", "dependencies": {"chalk": "^4.1.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.11-alpha.0"}, "dist": {"shasum": "3286a551dff4d8f0903b39cef937d48b75dd9be4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.11-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-LArOFw1ZNrXkcMYigCaM3yxPa2oORhZBteW4Sl5W5/D0wvDpMAWXohn/McRmRSgk/twx+cNQitdO2TC+6lWvBQ==", "signatures": [{"sig": "MEYCIQDa4WOzrb+Q3151f19UkVCPBJuWeoQ9IM+A0jhpG8UgwwIhAInNoMNgVixqVgFnT29bTiR8nefIm45wE0C0eaoEESX8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/WEBCRA9TVsSAnZWagAA7c4P/jy+vk/p7NyzJQ86ktQI\n883NhxL7rTYHaZPaMzH44x6R2nSJKw1JckZhnqDj3V0NI6UPjLnc3Ho3rLv3\nqDhYj1+uuKc3k8pDab1BwWUifiDH8wfzYgEZpfSSdHpFcz0NJTlGCVDIf646\nH3VEZ1xUrcVAIUsO7Jv0pJHVnMW6VOg3kEjcbX7TY2/W69aG/FOSMth18FL5\nKcuMxckuNhmRwf7RHS/Iri1N/vvaq808GiVkRiUNoz76umi7JrhnsVKlkoX1\nVdpTXKRQa8Rcj1HunNL9Ku9CZhJQZ4yS4bYSo5JNG+XLiypBX+xAKsZimoPN\nE9OcpWaJRy6hutLObKIlj/JKkSjXtVayeAq8Grx+PotTch8NGmuqk0SuhVWJ\nqAezkT8eDvl5gJlz1uDbeChdKOAGIrHo7u+m90tR8XSV3qAD47T/NjWHuW4g\nTyjsF0RMc5RNcgkXaLHgLPP2ixGuZRuiyMqEAspj2d8Z7gzlGwAH7q/Jphuu\nFcJz/3mmRxaftIyturcPDOMHORNANATggVw06sr6GPy9p0px2BMIsMYERbtY\n4Y4S8n9rYHDe6iWdRCO/sirqZ5p+6TxSV+LxGKrC2IPilwu3kQ+UxTD0vOkw\n6KwPG6ehNx06LOYTcCggtszX1z8PdmESDz2uxZpctCKeaZgFz/z0OFYuESQi\n8j47\r\n=uHxS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12-alpha.0": {"name": "@inquirer/select", "version": "0.0.12-alpha.0", "dependencies": {"chalk": "^4.1.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.12-alpha.0"}, "dist": {"shasum": "e0f8af0df03126160b657d7bc7b756c3e0db469a", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.12-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-N/11HbMugS3lVmmorp0H4pdXfofyQ9snULo2SxgBxeRFrQ4wN0PJ7FWbvc2/rV8JVb73HkN47X1GfmOSxZXgmA==", "signatures": [{"sig": "MEQCIDcvfTxmmRqOTv3dMOt5OzlJYFmTPnRewnGOdh3+iM2pAiAcRU7EPKxydtgZVaQTy+MEFM0NzahPEvhySOwb1M2l7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCIynCRA9TVsSAnZWagAAWgMP/1jWch2oTt7JMokv0p7G\ncigBtwuPpTzMPxTOEq1iarXqTOC7g6KxXoNpd9847kMBBWH/S5CwnzkceDd8\ngknJpwuAFSGTg5mFVpPPu7vlgqXrgHdZJFdp+YhZVvmZ14ytJxFAgz+moHYf\nPjOI6xMI4yQEbxvZbEoUIIZ0NkEbUnyHTHlPDv02saKAx62yYUgkoavWUqiw\ntUEy364pQVyHbgdHto7lkoXKMkbpTbZHYlx3B8eVpl0kU0HO/Zc9c5dnfYo5\ncwTFV4GUpvUcafF3yLx8BkwiVD1bQ3vx/ZZAnFIm0xJvj1K2QqeT/t8KrQSl\nl6mtYKw9ur9WofpNrYYTFdAjZJSGSd/Y9wVU0caRNm6vKjku23kl4iyesEKZ\nmEXBy2FUhp3RitjP/+G+iO7mM6B/ODFuUpN9ay2m7aQxF+LnPrx/szWMfLw4\nsq0i4l0FsDc+upgGPza/aJmL/PegRZouG0N9BMtTPwd9RKv7EJjEm/3FYA2t\n9K294mGztrexlEaEtpyyBgiIu+aDedg3NX147CHgcDXTml279+cK6lwWtF+j\nojCBvDV06Fyg3Z+mX2GpakZbkLqh2yzX2tJ0ZuabNHXWlUHmPr9A/9p+ZxNa\nkWFXkbjHxqT0tSB2ll5mcoDNSq95NsbTJSMdZiJTPbyNJOE09qg5DUnWjnwg\nf8gA\r\n=Twzy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13-alpha.0": {"name": "@inquirer/select", "version": "0.0.13-alpha.0", "dependencies": {"chalk": "^4.1.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.13-alpha.0"}, "dist": {"shasum": "853f8fa7fac09c81341c2cf0ea9be1f310bc1c30", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.13-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-Ne<PERSON>rCNUOXotVhXm478C7smRTuOrAZEGXtOhRfpnDSnjjDHQLnBXJKXN8CVioG3Imbi7OKZu3DwAxjfJFV7KOxQ==", "signatures": [{"sig": "MEQCIAGrswh4+DbnZKzAkrmZBKtOXZYdhm0m9Rhu8uuVRV+xAiAzXVzQsKCiT3gP1jQM7lZNT4hNls6UNJiohHVHLRvtYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGPbLCRA9TVsSAnZWagAA2kAP+QAmFI3+z7DIzu6PkLbZ\nNYbDLm5Q/HcTDmfNO4Rmq4iN4HT3Xp8K9RZ2GXZ0Xg1goN+cwNS/ComZIINv\njkqQFnWOsjRzZxnMWWfrCnHL63ybYBxdP1MCjrLwNzoq8F7+k5lHL9RH8eTU\nl5VEi3L+KpXJDvC7FJjXp7D4kUiTgfRlPX6eJtVGZD5EDLcv4FSXT+/lkMxj\nEOc6h+Jzwwt7I69cYdt1PNsu2myPaKJCKkRpIfTDifqCY3U2qRRLV4o/FtqT\nhmi6k5E458jcpW8ChQumwRwNIXVws43etiNRxU2atCXqz4f7OZoD4Bo7kQ83\nnDaUmIaLBYL87jvW4Y/mNpgghOZ0uTbj7yDX0MF2gq2qcHySaG8ghU73Xpji\nVF+LZtDTF4eeBDAuCmLIsb1zOALVEbsdMXJSy92M07D1E7rcV3FlOfg33crc\nTB8Nodo/G1Lgj63JD23nG4lHB3l9fCTaAKbJhOzYI7bHFNOKF67f7BdasF8u\nDe+JXTWuWcakAjQ33WIwUpH2IzFp21qkDcOMp7XooyOQRBSe0wezWItlH5/8\npYbxSdDCq5iSbauF73HEixunqJY/59C7ElEqMy7dxNTBztgPXFURBHsh3gzy\nHodDjU8HCfz2mAXZUrxTFPoRAluWoEizMUA47E3RhMQCX5uIaJAVHnujBR4P\nl78y\r\n=aUST\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14-alpha.0": {"name": "@inquirer/select", "version": "0.0.14-alpha.0", "dependencies": {"chalk": "^4.1.0", "figures": "^3.0.0", "@inquirer/core": "^0.0.14-alpha.0"}, "dist": {"shasum": "7110c02123c48711af08ecf721b8708038066024", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.14-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-CcoIXKQywZek//XwZQdxtAmAILOYafOSNSl3T4hmyMYnOttWzgTbZoDAXijFVyGcod+IS+zgQ9tVZS25WtWnNg==", "signatures": [{"sig": "MEYCIQCb3ZOz1GVQ6EXttyyTpeP3CCK0B4O5vOF8rjTDNNSnRwIhALhCKl475OyO83UVZjeom52Iu7C63yEEMJHopP0y9+am", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgODZTCRA9TVsSAnZWagAA37cP/24zPPIdjJgoerMyVYYM\n3n4Shrd1cD/ZSXwRSt1FmnDhHssZRppjfx+oOoMi8RHeVaWTqwtjPCkKDNd8\nJBHpgt+SH55VTTEpuH5JzPzdlA/6I6D8FvJ9xiJfYOSWwUgzMv+4RF2ZDKuE\n2V0vvokNbTTHjwB/Dg6fp03azb43sOaVpapftTTzQtdM1wMaPKVze2e75ZbC\nwe4eBuWMp3+w0rw92eixDRAPFfVCtPkUguCVLyJ0rNekediZzsR8ixbfFIZL\nq/FS1q9H4akafqJwSij/cmFFB5OvARJr3srBGlHGybpmWhlbgQojKBEUvcf+\nBPeUp6SdFu5Cl4OdkmIFZsipevVVgHxEDrr1UvRXZRDVnL4oHRv/5AfMM0qT\nIbpiq0caaaLq76iqRCHAiYeyQu7hnNT8Idy8QyQr2KaRQOdaSboySRJEbuc9\nb6gn1eTjv+dvQTMJWU5u2gghYQrFiC445vI7JEickcIlJMVmsKnND28PjTOP\nTZirrszf1qtYsl54qIgOpMKn6fuQdDRg8YBIfFNJmL4W4vDErCoW5laBpDsq\ni72jC6aY7+GDG5WtttwDI/3d5fkhzQYRTPmkgQL0rOzazNtuntQE1tL+dukO\nIMmAmvqZ2aEU/pCW+Jo2ztfwimghZYrf5nzLVzsMcr0xlvuruFBTRF8qhb3p\nWEC6\r\n=AAQx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15-alpha.0": {"name": "@inquirer/select", "version": "0.0.15-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.15-alpha.0"}, "dist": {"shasum": "62f6c140f46cc5112b4efcb190afe6867d5e5d7e", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.15-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-ZRAt1ZbM1urwrFKJ1v3dECf+hC3aHVxTYK9fkew5sV1ZtLw0WDyl8bECFZWsnJVls1ha2S55ZN7oO4nwv7OWig==", "signatures": [{"sig": "MEUCICDi+N3RZxb9Rz323V3PiBXm1858+oV7W3mEhC1JELlpAiEA5K3h3L1I9flOtmyg3w/yxYMilMnkb10v8X3Rczlae0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqBNlCRA9TVsSAnZWagAAhlQP/RR7UW8P5O3uPeBjk/Ih\n/sQ52l2SZ2ZhvAUw/t/64UQyz51r3UQWhPSAaxV7stjR7sTfyWkNzr1O/RDv\nEX+LTDEyYRU0/ZTwXFQMUvhCJn9Q1WsFLQl0j+ndQkhX8gSddr9ISM/mwULa\nQKigscgAgnnZzvMw/a6yBKLU09H/uLhDKeMsROkJVNxTnwZLHHORh+vKoAZw\nSRZQVYoa25EbKqz8rVJmSz5M9QuwKHekiPP2rjAys3V8WIEvbN6L97DgPWXP\n7n9mN/jNFEcsAuDyLagjJmQEMhXf3/J+EJuerjhugJLWaNl9qVIPPSRxBUhS\n20whzRJKL7xC639NFyOB25wDFoDFBGK5U7V2rzwth9lVOKZ4FOVhHaH0XJif\njOnv9+56cO7ev9WW+IbG0cYbZPlb2WAaBsxeeQ6YY5S3/gzsJgW0RH6C+wck\nmbrtoJ1jN7xBvORYwBvayfcrEaOVTxFSrYFrL6atI9KhlJA6fFPjyUF0sbHb\ncLnZSQCmV6SEsIhfjs9+YPoEr1nVaxVP8k54Aqs/EuC2zaALmOP5LpftvL/P\nJ0OXdpdyzixtc4u/234TK3kTlvsrHzqExg5UTR6g3OU/n47rfn/Ws2rxiaOh\nvHw6la7J7Y+XGvs33xAOKeGCdd+zMX1dH9yDPUAx5P11ItE1skfUqqoeERku\nfsnn\r\n=ysma\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16-alpha.0": {"name": "@inquirer/select", "version": "0.0.16-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.16-alpha.0"}, "dist": {"shasum": "cae16dd780f5ab76e2246e094c0570faaeb7327a", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.16-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-au5uBCkdcWKam5rUPnFiYJMFT7L+XJx88tazKubKW5cibNG8NCkoh/u4NtO4ZcKMqx0+NYFL5z5/uPc+ckBdXg==", "signatures": [{"sig": "MEQCIA3F0gVefb1fK8ihqKUFHRVhFyNTxBS45jnmP3edOtf0AiB52k6UzDR2RmiqPg6RJGOZKdwWMFJUZNmhI52WIcE4ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7wIPCRA9TVsSAnZWagAA7YsP/0ksiSWYwaXuv/nbb1m/\nm1xn2X4EUDxUQ+94bT3jA5AOyeUOHwQ9ack6FBHfgiPfmqq97qpCqWkmXuQ6\nWGy8LZOOLifiQgJjqvlbuig4cA1wZaTpC8Mar/+9cbuuKi5wznp2la23iepe\n473Pv1/vIfhRMAuXGcqEyxntFs39VR6UAhMpkS1Jyb+Ql6fom4lHFe48Yz4/\nJr/Me/2Qpuim0Q2CB/x3T8k5eWLDKnUjJp+1qzOqg24SmKWGySk8LWhU0aBA\nz6NGJkcByrfXYLTXvtGEpzlpXu7Udv1+zOXnaqkdN6ZRrxOsyplXNpE0hzYv\nMLezPHLvzlSTNkemv05Tw2u0tqu8bOroLCia7/AJZuvcrYkb8uigYE38TGT6\nP88Jev8xm4jd+0/nxw0hNAa/sklti41Vadl/Gf39GlTpTrPSB2yEDoLBME9I\n/qJy9uqlKpAHlCocQQzkYUjMhNGW035XvIXlA43GGfJfhxfF5ZBrQUQqHkoi\nS1JdCcdq7fy8QlD/gVfsgSINFQrJMU9pKQWiPXNhRx55htBIEZwp61lRuk9G\n477Yz3vnVvoJWs1YsHdkF78rbEPJqEPs57ONqChbq/EjbNrRVHeTRVZCfyhs\n/uN4bNI7nNC5Pe1RmwWoxt7Umbz9K2Rk3SLdyXMfywXZGFmo/FNWtMRhQlio\nv/UR\r\n=+FF7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17-alpha.0": {"name": "@inquirer/select", "version": "0.0.17-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.17-alpha.0"}, "dist": {"shasum": "6dd539ddb2b3f754f82e0e2865da38011a2156f4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.17-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-OsitqzoWaADmf/IV7q+J/aUfQ1R/rEclWQSOMc+j+oZO1A1YYZIweBL0SIpstfOPVit+dZbG45jz6XMHaWpkzg==", "signatures": [{"sig": "MEUCIH6CiIR8rrTyx98gQsVWfVrI5XzxHTh5B5IFRpbh0+YYAiEA5nATeXzpG6O+w33gsd5TUTEe/dBic+rG8n/8+aa+zpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP5iaCRA9TVsSAnZWagAAhV0P/1HdPW7+Vpfs/IC44y5U\n0s554W6Dab0/WyW4fXiVbZkSR0NKfbHtF8PostA518/ZXrc5yEMxomoExRRH\nEEC76BXz57wHsMdBEL9XcvbsPXC/UGcoXBeGTWNclFrC8V276B82t5kyoK7T\nSkdYOFyCRI26dyjKl4klHGWM04JyCrFu6QIeuRSUit5jGJrTDN7pqJmIfY8X\nwXrALnW2IJOfHULrVTKLuNSB2wwFXLOitKubDbjdmcr3k4932fmx+Uu7mtO1\no3gKgXbYjDAJ6hpZV95CAh1RqyQWjJ3KE2+D8aF+dHTR4k6rT6pc5ypV1OyT\nqEA7k+jqQTd35K89Vdw3UUZftmQZM/Iqcp/7upMfdwG+uBiDjdeGG6UGYYbz\nOjazghcUbmL3S5rk2VYaQZL3k9IjVwF2+ldngkQ27jwlH1nCk+pNrNzsdsGS\nUFAymSWpTmAAMYKHzUCDNuioxM4uL+OeYqKOB9hIvtu3jA2Z8xjWUAcOyy1h\nYFMxOh0KAtehPXFQHS9QKFUj/HxK0jy3AkOshEPbmrVAw7nOTSs7MLgtaqf9\nmgJyTBd3sII+CO9KpdlPuCYHpnsNy+pii7dyjt3NK7/+W2Lr0E/wL0Bi73WV\nFb93RC+xRBXeEN9E8dQ6d0Lj8t7kPAT5E4u8SETsETdGBTCfqlIho1kBmIxN\nbTnQ\r\n=+Mmu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18-alpha.0": {"name": "@inquirer/select", "version": "0.0.18-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.18-alpha.0"}, "dist": {"shasum": "9cb6a1150fd7e203b60d0ce0bb4a0fe31ace5b0d", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.18-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-NIjgoHx7YQTbsrVm0hWtdljFGwIKgenzmeeynvudpivbwh2ak1GzcOZ7STd+MJ1u3IAgC8F0FKx9PwwalcE/8Q==", "signatures": [{"sig": "MEUCIQCxmamQmsTIwFUD2p/Ra/PkqfeondlVYZdHR13RTbssVgIgaz90B9BL/pSb+sFcKUAH4RBDkKi3oJfIBarZ3gqk/MQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiKAGFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBDA//Xhy8RvFSle49FstnSnNKMamNqmJ7o1eSFoasyq7RYLPUofAu\r\nyJQpJ74XjBpeVGr1CrIoz1fOadn5vtPuesAWZqsWr9wIIMQyVrByR4ddYYxC\r\nQogCDnph4C0M6LPK9nnzS2hSgU/EEIgWIKP1nddbq8i+wCvlFkrmKkmjsBbr\r\nEdfZVoJwuENBR3aokrndub4FKJeCpZbgSOD6MS1FsED5g/mqfdK0YM6zQVCv\r\n+4GakUl8VbqPVL+60MQT0l6cp4RcNb6pTn5tDFXraPHeO+cerL8THt/ES766\r\nmYi+MCsXTQDhzdGfLpYdWIUOfICjfou8dN/stsxDEYhGIlpOYPKKWCHrWTNd\r\nuV+WR6j1wbJtuD6bLFBydeviI1jmq42a29jnLAx7Cx6lAbjADKFCtdXZ3oYH\r\n1DOFiHWsCvoO9Pqm7yXs+qnKCnbWnkf0GXLkhbEwNUwFgUj0OkleRQbUJUuI\r\nqCakzcPH0kocwxo0qGJ1JVDMaRX7SkptiTmJCzb50qBRlbtku4/hwxtQ5Z6F\r\nbgI23se52MwWIdkjVY1o6HUoK2feX5iSo9ZbOixQzaqGQEbG9raBw/M8al7n\r\nF7cV77ad45Ke8r/Wea5yJ9JsbgRAMttDv+R6VY1COVvuBzoV0nqFB4SDA8Tv\r\nxDfmlvqXqJMKOQMd6AXDFp+GffkhRvcpxiU=\r\n=Ip2D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.19-alpha.0": {"name": "@inquirer/select", "version": "0.0.19-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.19-alpha.0"}, "dist": {"shasum": "33bc02da204b0e6d1222b6189d087821b1fd9903", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.19-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-PXn5puj6ff9wa7886p4PCM1/onNAoyRUk6LHDJQ9VE/G9/nmIfyA4JpXQ4nCGnwp623PmRP9J7r3M7pqZX/1+A==", "signatures": [{"sig": "MEUCIC2ahVr1syapb/xvR6p2UFx9dPCkJ2uwoI2/2p1ICBcgAiEAoGv2TARv1xIUSMpp4poSQKLNWTvECQbHLnRkworP7dU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaEfoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn0w//R83wUP1TqeiHLKgVgyZZlxBGGm3rHO3R/27oBsPljG6I4Kch\r\n8NLD+ETsfQ8VcBETbYID4tHNqJvobBwqproBQADKD3Qmfag3fNpO3xF2u7IU\r\n3dZctRVe8p2A0DQBI7nhEEuwIl5CFX2eIzJK33GQwCfmRCTTrE4ip90BNWW0\r\nf9c3Zl5oJ94E9cvlz1f/wnvD4ZNl5Q5CQgOSqpI3Z4+6z2HLH4f1gvYhWfFX\r\noWzlZ2UFRiQnkOVmxV2XDr2kPumwMak4zDbRYxZH4TUtDb61LPpjT/CKQRAc\r\nLL7o4jL2UrEQ6T5q1lX7C6Hwry+MdGz1BRAE/Kg7HI0WYTHutQ0QRH7q45Gq\r\nZZWapU+5+W/rXp4IxNkvUtj6zRo9dg6CTkRLEmovTgGAIPs7cy5OJr6yT6O+\r\nsC1UHAPAmiMnJyDQUFea1QWDtBbhWWe+nPpxDPmenchqUymwGcK/y6yI50B3\r\nEhvOUYTUw8/civySM8imn+M/wX8W0YecFYjAW/lha+jFOzsOjDZ1muUvR+JO\r\nk0QoY6WGA3bIHLqWP+nzPxQ0122HvHqoM6y4GQyF2j6Tufn1qqQaNmPmyqP1\r\nb1ZD3sRUa/94AaLCPRZoRthamolbpyqkVeY8w0+Eesh+N86uH/AxMjahB9+7\r\nMHhriC33d3NCGkQZw3PQM4owmuhUfCu6vvE=\r\n=zzYd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.20-alpha.0": {"name": "@inquirer/select", "version": "0.0.20-alpha.0", "dependencies": {"chalk": "^4.1.1", "figures": "^3.0.0", "@inquirer/core": "^0.0.20-alpha.0"}, "dist": {"shasum": "42de50944f41043dd1bc5f09bbbad4b9b8b3b92e", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.20-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-/bVoQOEihVOmw3S2LJlOgEdqsPypVMHr/Jm3tRAsDfamlIybcdD4N9fLG/d9/Gh8fZmVvT5QCzIWT6nPNojWsA==", "signatures": [{"sig": "MEUCIHPyBN9Lp3dO7Kd9HfK3t6FACChmcDeyK8dzPlcZpilXAiEApMtyqwnKc/Hkqvy7PizRhUYCzXZ4f8Lsi4f67otN/sI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7023, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJialmWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqbcg/+I3OepFn0PpX0blrJesWhEOnt/seSyN74XLQQLsLHZPq7i8Z7\r\nCfzLBnAC7Toughdrij2CVepdtY6hehMRSSr3vPO1Xgs3GIqz+mgOayyPghXs\r\nQplq8lk1sJ5ABWvOtDnIKKDCZtHXy0HOalfzdFhw53/Y+f34IfLqs/aQSJrG\r\n/PO1nniWRSOYcOuP8B5BOjMEAuTCLgRaK0K7HDfxTDQv/KdLiXoBfE4VPKV1\r\n6/lRGzLEi5RWIyYuuPE/FrlBDDNDZsB2K8MmNGKr0J43Zszo84DKOerbugZ0\r\n7H3K/0mqJRzv43PLiGuNIKQdbfTd6NhWLB89aA1dJ3JNaNmyM7xya19S08eh\r\n95XXjp6dh5ovXQeb3rbgUm/AGo0xNVw3m3ImAO1aaDau+dEym2R5Ge2eSe3x\r\nYeEFeB53+7pgYa7zCI3hSBbqrCYQ/W8rrcTKElk4+ow0CUZICDdPxQbnF9/E\r\n0Seg70GGu4GxLZ0XX5i5Rdr+Ii+yZ68nCn0zvLu7A3C3jUaqjosxKgqsNaG0\r\nLx+u/CNtBhmom88hIelBpZBQ2GL0xYJ/RM6EctCDexiDMFJ9igAe1IirfQyg\r\nk0JiterPUY5DnI3woan44dTQXaIWF6WIoGf+VKuJfIqjmI2stlta24Vfni21\r\n0DBbhe2tr7+UC4zkdBvFQe9T5J03hOyk1WI=\r\n=S0FB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.21-alpha.0": {"name": "@inquirer/select", "version": "0.0.21-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^4.0.1", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.21-alpha.0"}, "dist": {"shasum": "c8d9f420a2cc1ac054b21539b1092b353d4284f3", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.21-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-6UShUZjhB2wOqC1oVVHgJUOwe03vdCdOwfShV7CqLtB730TtWfkZdIKYNhDpeQaWZEVkYbF+u9vWT4r8iKmBXA==", "signatures": [{"sig": "MEUCICS6Ad+FxNMi3ZCxwUy3va5+LfNKGFHyWjwnDVCyVo0YAiEAs1dpV1ZfIu/G7+d2jlGveXgUtytTCu8nnkkt4qxEuys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJirhB2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDIxAAiOmWkKrFBNYLwFYHzrKBFgPcFFx7EuJDCZQsb2wwqNbVf1i7\r\nQQDCD3D57K2w0ENG3xTD4lW9J4NsKan2VvmLNlrhTBbMW/BoD7BQzozPzZRu\r\nYmZ6LMAWRKXkniIK1U0R6esuxn+qsc9T8/sBSONVwQzt9nLni5HK31Td/FIR\r\nbPhZ7rrz50LActOvBfENhbeKno5gTUYzOAYyAvojMgTzU7niNfoda3xijt3y\r\nKuxjQrpsnxGOuVc7BWjYrgo7dVGVEdtVnydsrVWWCNsOW80qd4a+L7DJFOqa\r\ny3QnmjPE3XS5xdtCWXeDfmgirU+nn5XdZDeczynoQSsFEt3Yb0zJ4nnM0WVw\r\nCHDP33taqJuBscEQlAZF6vnOLmbhVtA8vxym+WTjPhjf6EbcDOyxo1E3+iHr\r\n2uig6s1EfNaVyRgZvYE/crBDkMTqUCYcYc1FlWDX3GDy27m8KkAk6XDnUsOf\r\n24+H5r7pT0dk/4WeSse3PG04B07zVEm6xBYhzbip8a08tHxrWdNRn8UHz8Hm\r\nnXOvPQqIXBeMjyONqHQU0Te8CgNy41KB+w9Di90bZ0O1MpXl50n82AHww/vq\r\njpSBUBnjLORIJwdUPC8PwIc1JG3/cgOI3OfOdlH51inmjgeHDPiC+h+kjIdl\r\nhuTJoiD0D/jziRBegATtHds+o/Mw184BRAE=\r\n=TTTH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.22-alpha.0": {"name": "@inquirer/select", "version": "0.0.22-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^4.0.1", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.22-alpha.0"}, "dist": {"shasum": "2ae314570705fb36eebf1aa1f0a683fbcf70a6cb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.22-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-7L+S4AzXXdssIW2a2WjRzveeHFDf8oOwFCm3sY4+QThm3n80iIlK2DLKAg7h8FYnOoIOX0ZCGj5NskXWBolPtA==", "signatures": [{"sig": "MEYCIQDzZz9JZmWkoOzc8W30FzGY0o+yaytbLJA4AM5MC0bP/wIhAKtgHvyeSALjoeOygG1gXYBcUuMMOP2AJD9W06QLlZeh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizzDpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOsg/+LNKKvQGY/88+wgYpONsHX/GdSpjnm51661pGHhxkx2EdgmSU\r\nPPqY4bawII1YXJWIrPTG5qBi+gofyzHKIzVrr52zxeN5Ps3+NclnRsvLOj4w\r\nBtRsfEtX089nbvLeBrLjE5I/yZdEzzo27wRyN5fnjAAQcjj5EI2KMVCwArkv\r\nVLmlRWiXDkpZNTh4vaXu6ZxlhKtR8JTIYrJQzScPyzSLpOzvFrWVi4oxrryZ\r\nwffb6msU5v0+8HSRzLiZy+EThGvMDJZK5HUV0iiBoYlSM0xqn1GXNdOiKymK\r\nXubq8zjB2BL1tAexCp7FF3DaNGe3jEbL1ZBgBERyeeLMwrLgjaiKlAyfepxF\r\nCfc/Dz0FrI/8U4+CqCrQKxqohLKNCtIB4MDRM1/JY9R6zAQ9QWjvo1EYKZ2s\r\n3XtsasSoXh//AZm+MREV1m9Y4Gi5P61y4G/1x9UL8yWlLzzeHmQwohNaLPb/\r\n0ZxOybzmU3s+u4vk+69cr4S/gODOpeQXFPkhLyUXVsQR0hVmH2U1QZISOjg2\r\n9uWlojNSsxm5wsCi0Lc/RNXCVFjNP/QWEpEZnUmL5GHIt3m7pCN0jUei4QGs\r\n6IcSHZGmfCrreZTZNjK8aRpvNbS+73LLdQZNI8DPfsNka3Exdyz5wGbY2uWP\r\nKAs+3oDM48d80JF8nF190wCs4ath9uzoDIQ=\r\n=Ia4A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.23-alpha.0": {"name": "@inquirer/select", "version": "0.0.23-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^4.0.1", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.23-alpha.0"}, "dist": {"shasum": "72ff36dbafc1bceabe23f86872ae14ee03853c42", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.23-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-q0AKsQr7B25Y0wYPSgZoqiP5cRQ1tSH5SpBMASejOt9eG8Q7tcGlzDzHHgwzk+pfPwCSFPxn5BNUtxg1KYqB/Q==", "signatures": [{"sig": "MEUCIQC4lnyz3fu3lXgcdo4IS9kXQtfbqjueeEJfZ9tZrVddbwIgNP0v7J2ENld/KXY/3nqMfQusHE+Hzoj78MRBORylE0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi67L2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOrw//fOA7DHnXkttorLfJ/FOwB0EGMmfR3Of2DjzIExE3QeFrEz+E\r\nhuPP+DR5bYm3UjW23g0KvbE9mK2xsjqoC/rPLgfnIAGDTYqFRg8YkfY7806F\r\nDBbDX8LxDw+4LDLn3Ve/wHIjQFMfmmgiUGkKfNHIPY/LT7W5d+3q04O0NUwM\r\nudLAPto99ukWUp9a6PJyiR/TsiQE41FMoN1hjB338rN5UNVlKDnlGj44uz7y\r\nEJ5MFmeKXZY07WkvDKbQPOmn6IvJmUNDSROG8qqBaBqaYD+jfqgUpMhOcaRT\r\nFNdSMTx1AxHIaenmasopIOuRKJiJpcPTsIxIMiIebkwj7DmtF9/JZELXhOo/\r\nd/nEAiHG4yvsf/uNPJNfR3oZmybDRNS9naOhrX3w1OTs3QVOdZtb39STtiDE\r\npuKkg6xFceDaI2JMBh1GSGQvFtQuUAN8tNiSfydqGUOV3qkYkoOnTy0kcVDz\r\niMMGfOz7sO5NWnQWBrTKcNDNaeoBi+tCDD8zUQkXdTwTbmapKnpcSSM/v7J3\r\nytPb0wunWNp8lIVpkLH94s/PeiWHPtlHXc4K+vmwrP3gnb0C7IhEHmMgt873\r\nuLpBkfFFPwydEJkKIbyBtpOrJd0eAiFiKgTyB7qJqP6nfzXO2sor8J2Q6Zhb\r\ncM5s1WOshw4KU67YJQoOcVp0iOUwixmPaz0=\r\n=MC6W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.24-alpha.0": {"name": "@inquirer/select", "version": "0.0.24-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^4.0.1", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.24-alpha.0"}, "dist": {"shasum": "b43d6662baf0b9f47a74eb512af7ddc875459548", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.24-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-wX2p/V6ezd0qp34G70APBuL7UHm5e4wF2cPP//z0G41aM+J4L+O8FesusoashVTa4De7/gVdBVbFdHuawMgvCQ==", "signatures": [{"sig": "MEQCIHLEgsizVtGBsfEcoZ8zyVmFrXLYX5rsLkiC9f3T/L25AiANzUDMXF+tYu9AJ7Dh5aDWS9t/C0zFt2GPwNlEjOLnLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/49+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpPQ/5Aa3y6Wq1etECexsNGTJXn6Egh+9uP2x/t0YGvLvtCYH4pEdx\r\nTIwAQcIgQlQpIWjl6yu3MvwHUG7sBB/9cBtP/uPYzfJAU/Cl60EI/U4CwDpR\r\nzp3RgY/+GJd4aVCTQRfaNRIC5V+CaVGP2+jLbc64D9OLE/bfjYG9UcYFwuTF\r\nvnupFRNF1GeWyEIeS9GGPGWX9fiey77ftZYCaRYYCcMgJj6sjSuS135Xkaxy\r\nVfUxmU9/8bDAum5HYUEWxefpDPFLE47MbBd/5aA6mYtw3CZnRzPY3zR3EOw9\r\nnwxQza3kWPbCh4Q0ux2vhSrWZg/tmyo5R+crbvAFsSc7IiWYJ82U0A+g8ZNz\r\nIWnou/S5NdVKp7bdEdaJj1kVFg7hN86gxtTopllDADQpLWCu7XQDoBg3TvN0\r\nyN5bFKT8Su5FPR0Qi3SG6lzHz0UBkrFI5w02bAxyAmvZl1AHg/YRpisigllj\r\nkH4hDdgCr/jzkOS+zBORLGZfEN1RlHgmR2Qwx7EkVk0nPtil+J6F35bm1maK\r\n+K/D41liP3rfNcyGUkkZzt/onF9cFVWcBheHpdY4P/QtsxnGT91avsgYV7rk\r\n1PMyqZvfKZ1GyAb00T4wWRD2+71S6+NteNjl1cxcR1WSvMwFWUPPVmeg8ICe\r\n+CPBwft7BQzwpvczItP/n/u52Jz5RS3tRcY=\r\n=pgO5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.26-alpha.0": {"name": "@inquirer/select", "version": "0.0.26-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^5.0.0", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.26-alpha.0"}, "dist": {"shasum": "f0f26e403e26b48d4c3968f6ca62ed14b0094b93", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.26-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-w2O36d9BL8ahXZ0qjjJnyxogVg1KfnxnPGCCaAsQgIkTWWQ5Rgl1htJoZIQrrFmWRpUkXQYLbrJIfJXx2lqmkw==", "signatures": [{"sig": "MEYCIQDhF738KA8o07gd/N9g44FW3IZbQes/z9EGrqGEQOn0ggIhANa9Xb9MaVsdY5wP8231fr6sKFNQKuM6IODBbqSm4Bec", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD6m5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMTA/+LqQwF7ujGhZaR9wEpMxeFVOCITAFpzd1fVGNcQKGZ3jRUu0h\r\nCVjr4EavFxPIkydaB6DotvV8fo/aIwUuosnZb1PyP3fKbqd6As2ftGvO1PlI\r\nHgB2vfdOzDEEL9Uw5viYK95RoWbmcMr8BMLFFe4ruVImAxxv5AnbABYQmHv3\r\nwvjXqUOqCtpyw0eT1BhPwRlCu0Ue0vKMYb/q+fZGAXqVyhS3COlMVv7xdolG\r\nwyw3Iy37p6oYkRlmWgvryhxjPQEFcaJXw6UjkEtObxaejm95tN4tstgxDKFk\r\nxBDUrTeNyv18DdSd1wh1oahAfOtvkMqGVdoXzf9ebS0/eXADAbAYy7EFji4K\r\njEjgljbj0rV7KWLmId9+O88m5fO3n+aO3FrNxJ9NEr4YEqm1d8F+qdNHrfi8\r\n9LQSxh+MpKxtfQYMxaS8F8k5vIl84h5tv8b4XRB6Ihrzb3SXFAg7rl/NDwDA\r\no+9AZw/KF8Nba+8wsz1f3K/JXewX9Ik9kOcZU07eb7OocfY+fflNMAJrj1XJ\r\n5YH4u3OnyDLQ/9FGkz9JrDc9vNs6JKCfCFAZgca4OnStmIayz69iSIOCt+Nc\r\ncpnDDb5shkPCnnx3qZlp3EYil+xjr4aqIIuUZp3qd3OfNKEyimXenjuI12kk\r\nN626U2SwuzkY8+PgPNH6yF+F+DwdFFynrzs=\r\n=3IKR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.27-alpha.0": {"name": "@inquirer/select", "version": "0.0.27-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^5.0.0", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.26-alpha.0"}, "dist": {"shasum": "ff1016dee2ac7bc474949a2103e826931230be58", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.27-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-iV7EHL1ohNIjhaLoS9XW/A4RjTMoL+yXO0+V6iZlgZyat7Yu8bGYAJJhtKKat5XxIStYcs06YQc1+A/1qNZRiA==", "signatures": [{"sig": "MEYCIQCyiuApqfdlRGW+zQBNiYrpqgU810GK16CeauUTLzCq6QIhAO0CB1Lar0uX+u81CIBSRsAeygA0lbrqX+dq3MB30NU1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ2MxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSkg//dESiwTApO2wAv3pJRxqB1wmZT2Ch7y9rQPqWz+3IyZP3t3E1\r\n+MwVZ2QgJz6X/USikVBkMWj6zWEsAh94yYaSMVBAVLDRbAIVTX+QTmcADVfy\r\n0jWeamAW1IktydnMUwa5KqJ311VS0wOnB755xhZKLJGJoUNcnG6/w42VVtsW\r\n0DmhYA2JbUu5xJCY/bYnO578s8BPmLm7MHqpY/u3bU0mNuqCahy3Uy0tYpOE\r\ns8aiNJjSZpkRiWYDZw24PCAgp0oOveXYFtbcRrFS96MeRoNc7gwm3++JB8X6\r\nVoW67in5SMkbCRuCH6Ur8ngyBAgZSKCfba3Ftqh+3zfu9Hc99V7Hgf0/f5Hi\r\nz/i+5pqNZOwbX9lNqR53lgp8A7aoF55rwvlhfR5Mc88au/3qEeYqAsigq0iD\r\nHBlReIFL+9JWPdxUNSSlLGwWRt5G8n2lctCeQr1IXkvh4tszgxit1R7iXQue\r\nH1Yv+XEzvRSMf6BYN0AkAH+ov+aqJefsigAufSvTTCQNnz2xbtkiJK7qZ/Pk\r\nee32bWGECK5fedgDrzPyuFeTTzsYvRaaO9xbfh2/MRMfQ0lCmmZZXL8a7WtJ\r\nF3pvfZfUAh5G9YZay+5EC9lqvSXtUJt7FLhsL4/BrUybK63xtSmsbfGF243D\r\nq/ZLAuX1H48Si3T80+QjQKfO+M8Od/9uSRM=\r\n=TehI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.28-alpha.0": {"name": "@inquirer/select", "version": "0.0.28-alpha.0", "dependencies": {"chalk": "^5.0.1", "figures": "^5.0.0", "ansi-escapes": "^5.0.0", "@inquirer/core": "^0.0.26-alpha.0"}, "dist": {"shasum": "33a5afc8e3b2f3dae8542bdb16ce9c99ef28155b", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.28-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-Q6JBPPi7mE+Kp0qE1/qc9LGXncRaWipjhYgtWlQxDdTwHJN5ZhQ1cVK/GaRjbTGQm3WdYNAnVgTMXh8PPIbUOA==", "signatures": [{"sig": "MEUCIBhdLQlPVH5WPk/+E/3qd7kci9qWFilXYZW3J456aqOQAiEAoySMGxkNutaYMwxX8Y+fb04vrSFxAqSmVXDiJj0s01Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjP0CPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7qA/+K2zLNeLx4ZVQVi9uPshcv3aw0w/eHzKm1Q8VtC9j1YHQeYFY\r\nHcI5N+hBjQwsL0aMkqx9VVYcfJbHeOBL45jXZmFKMrovnObKbn38lKOHUVtI\r\nttJd81jegc7LUTnUFxwG5G3fT+W6MahGhz4MzMqghJR8/UXO99mpG3dZevTm\r\ntIDc02DqhXwBQoDC3kbXAwi9r7wJ+HcwPSvdzLjUV232nQCMEP51KuY9Rpca\r\n0Ol88u2q3iVOaUd6rmkDsqvQuhk4sR6E24IKq7Hn8tJevlmsSixIk+jRxgSr\r\n5LLETByXTPz9LpX2YljLGQ2XFzBFeXULVJsQW8uJDTnOyz9skrUyYqdIYLBo\r\nMGB/TW7whnrpm9RqQPf2fePEBWHqtgjbs5daZiInwH1vC/jDC1GCVnZC4YlF\r\neJWqQKURmxP63jHcFWVFQC2N9haebkFNPQiIvuPdh8zY3OuGEvDROSSSxf34\r\n05tQX/UKHp6F02+boQpPwpDheOX2uczD7xEM+URe9c4nk0/OYSrEXrLsUIYJ\r\nLopY7t7YjQHC5WBt0h02ig3RbjUhfZqElPvb5BkVlZYP/dtMSO48eYlTIszb\r\nYWuZMjkZ9S8oETK9AALNqS5GWBj33+WRWFonlnPVramK2c7Lzzxw0ULV82pu\r\nIHdX3JY+1bjiUGpq7lmarsuASphf4ZoiKdY=\r\n=joTu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.29-alpha.0": {"name": "@inquirer/select", "version": "0.0.29-alpha.0", "dependencies": {"chalk": "^5.1.2", "figures": "^5.0.0", "ansi-escapes": "^6.0.0", "@inquirer/core": "^0.0.30-alpha.0"}, "dist": {"shasum": "cd7541017fdbf1c55acda0cbd7bbf1ed535c59e6", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.0.29-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-xfrWfKRPiu6ynzA57IivSS5sSPW9qW+qOhRJkffj+pXM//D6iczsPqYolv1Gyni27nDIvWk6HZ0pDheLq+0jzQ==", "signatures": [{"sig": "MEUCIQDKxU2IisKWxNP5A5aZ83jevdIQnIEd87jFeZDxZBb9xQIgNifST0kcm2lVSvYL7EMLE4HqFeRbF1foxdlWXKjRoic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTcH6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9SQ//WWZAqjPc6XrYsbosIxcRxfq6tx4ITGCIYviKCjY3PBwge/uC\r\nOL+zjWue7JHAvbTyUJnCybO26JC2zyvZybZYLutYeBbtc1/FGaOVCiO0065F\r\n3e3kUyd1rVZ0uvzLqW4U7F9cPq3BTtElzBRJfwMK8sXgzqnGDCnTzGS6fMXe\r\nqGMDn1FXLSLYd2c4PXRvRr0TvtFs8LMg713XxlySPvNlCJtLSUpR6R6RxRj1\r\njDxFvoJP5eevMH6ZLM4ooADw/OM6EmZgc0WB8MOUp4CsDBqh7DFIeF5tIsIu\r\nQ2XcbdcHni0G+qW8dC8PF3o9yAMCZoqbBVcSbpucF40r5QOx2QE0GKFLnsj7\r\nTRPIfw7NE8UQJxfv5YjJGshLiVaADZtKOWZpdt15VWu/F601fCR5bnfGutRz\r\nLN+2qdXGHccDsMliftCGfLfRqLvczIZ3TkoMRgsGqHWBZDX+Zt6LgtGEuxoE\r\nt+Pw6/V1kfPVOPixA/6tZrf9JA5J6S7TnWiFiznBleqDUV2NZmVL7qPxr6vy\r\nXdMcxVepiu7ZNGm387fnQQoPJPyljxv/P9DsgrQjg94dAziwMufnBRospkuk\r\nnfV4yAVMstiOlh5Ftd9cZynUnSG6Ujn+Cli2uawQOCvP8Jza4t9d7mpdv5zh\r\nluBzTzSooGMyLNdv5tTqZaVCnW8KHY91HOs=\r\n=9Woi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@inquirer/select", "version": "0.1.0", "dependencies": {"chalk": "^5.2.0", "figures": "^5.0.0", "ansi-escapes": "^6.0.0", "@inquirer/core": "^1.0.0", "@inquirer/type": "^0.1.0"}, "dist": {"shasum": "7654b7927d3b14542110d10253c318a224d51c5a", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-loQqLx9wT9YQl9exqHz3IvVSPg4TNohe4+vGtMRpPHcZC/KgNNq4UKd/IeHz4pAdM75NXYWblRi/PqbOmOxQ9g==", "signatures": [{"sig": "MEYCIQDCK3wjuYTrqu1naAkWbgDBkG7k26+eT4Ivu1b5lkdHxQIhAMWh9MbPfqz+9s67EN/6Y3JAxJ8OV23kpMWj51ak+Jnd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFe5JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK2w/6A50cmNZOmBO/u4/fhJZmaEgfnyMIt+qo7e2SNdXl34OiLObi\r\npV08whbamDYkE+Gf4bxGCLyEXcEIV8oKspLPOa5aWsacM2kvCWfoTkVUxvfW\r\nVQ8sxSth82hka55KUjlb6N5Nsa+yUskTCdxyfY60FaBKdwQayfJR1zG+qEVR\r\nTmBsn65YiNEfrwxlnZHGycNH1Xurg0ZLravsVDpAPmyNPoatm+Y0Vw1P4ng+\r\nHZI8Gvuhg5LAfQYUJDd2eWujAvPk5nyITCwp79rd+SCG2m9TjfBK3vRD4I14\r\nRTrH6GxpOO7iDivGj8+14hy3aeozZY6dwMMcM13aIDo5HYl7Wdka2zL/eMFJ\r\nvlmqSMrzXoAaRNzbhkcQMfx9mbjVwP2f9ZvqL9fGPdJlh9k2/JhLychJbKU5\r\nx3viPjKFrZcgsU8og4Kj+kxWJUBZVkHMznogliDbCVcz3iIf7L54nkiY1o3Q\r\n9hHL6YzQ8sAQdHY6iV085lXI/wkCdnYd5yQUNNDBoo7QBO/ikOj8hztc4TQu\r\nkiJ2Adnpsdsy/qHbD9nDYu0PPYNelhbuItvEnk6qVm1wfYlxoZ7oIFAeFHQi\r\n0qmHX1pH9PCvl3CKm1KUcBBXF+eu/D82jYAZ0Z7czbCr6QwdZuECaPY4VIp0\r\nRIPV4EFVSLdIL934jHho8K2OQv4Siqp0bL4=\r\n=rRUR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.0": {"name": "@inquirer/select", "version": "1.0.0", "dependencies": {"chalk": "^5.2.0", "figures": "^5.0.0", "ansi-escapes": "^6.0.0", "@inquirer/core": "^1.0.1", "@inquirer/type": "^1.0.0"}, "dist": {"shasum": "b933b4b17419a0cbf3720dad2edf52f7617dfeae", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-mSXqBGgpkL3VM2capK/OYSkGyBYGMkUuCVx98wi3v6QbMWb38yQB+szq3FtIfRy6BzP2ImtS0wsb2h2Mv591Aw==", "signatures": [{"sig": "MEYCIQDIiqS8UNRuViHz0WprGMpG3siwdCTDWOEKHFa4Y+KYCgIhAK67kXMhfgqMa118Sgxge1/Qv+QSINSrHjqrVCFhGcxl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRZ/TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZJQ//b7MKqNsyPQTOuYRBRANkZH0AeSB1KVN4l5qNJX6GTM3f/CCt\r\nI/morlduVwhHvORLtBAEdAx+/j7M63w+zJEYupxlLqJtb8/+lpypt2rYpbB0\r\nyriSrRdNtjQTyTxGDkHz4fvBt0I+h9to3uxrU7xh7jEJEI/jviWAvzlNnVr5\r\nflIAVMwDyNAH9hKOQ0971F3AKQxn512fdyaTzicTBhAE92F8qaxbtokZ1eha\r\n8yOFCuNqvmsgY2R8SQFVGs1LJtkV8gjL8+q+IY3Fl7Rb+unfwStrZs0lgruV\r\nD6Oo3yQqMrqPSDCQXiNcPYg5jRjOrfLo5EFvDN4NNt8jTfa7fBS3HmN7EOnB\r\nwYBCEglB6zL8jriUaqLfShheXKqVBq3wAPUdHKxb10gfZq/dNELBWoRij71I\r\nGrQHE6yt94mftAYkruGuzdQRkv+/5PSqrdVCUNJLoCXKiCl2Buv1FUvOEGpK\r\n/10Vgk1gXGZZI1+tmozKQTRWBUiP8BU2Vx5kLAELdiQA2JVQGsswXrd78CxJ\r\nH8aA5/2xJDBH5f2vlUq3AjlWBXK/lFytLtmHH3eTXNrsVFuYJUOyPogHdais\r\nCs+yZN7AD7lITGVwS8P48oAL3hj7fncFMUECtx3j0n0gGr0Ej6jiY/PGq8IU\r\nVJfnuoqOOAH0f2X7OVOo3yHukNLJr9eYBr4=\r\n=Kr/t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.1": {"name": "@inquirer/select", "version": "1.0.1", "dependencies": {"chalk": "^5.2.0", "figures": "^5.0.0", "ansi-escapes": "^6.2.0", "@inquirer/core": "^1.0.2", "@inquirer/type": "^1.0.1"}, "dist": {"shasum": "649e89b51a0ea69dca92abc03ee58b8384d2cf88", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.0.1.tgz", "fileCount": 7, "integrity": "sha512-rDI5MbHstTppgiDkV8kB6mBpyD6LZmnMKrSuIUDcYAKDJXSo3JwTMnBV0wFTwi79CSXKlRF1LZnv/i35oa1hTQ==", "signatures": [{"sig": "MEQCIFUXib3dG7Eko9SMeCJyCm1/FdzAQod8jXX+CRn/QNOcAiB/LPZapleh/A35KSBzGS7AfXwHVCsxAUTeU5BDaS8+qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8LqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqEGg//fOqTWylLPnXKTvGWezuaLrhqHA/cYcPTa2JfN17ZXdY8JGpn\r\nR9R/oR1zIZ07HEbLfANuGqgZqIdW8pkkIZslSaXy1qUIE50+U2pQUP0oCuN1\r\nJq3U8j49JDC8u7dYzVILX/pCPuymabC6JnBn9lThsSiVcPHRY5Xg/HktKvpT\r\nZpndUgoRNT2KxSjO1dhbh/jD4Vei1bIiqlJ3tZG7W6S3/+ftfph7QXXudH9N\r\n/IR06/AtNxU5RhNSov98ZjR4YlDRlOpEbi1BX5gL1QO20Upf4RM6xcqjSaad\r\nQF3LJHIlwbSq3I9dirx0+Ss5WB/njFe/Rbo1Ja3KVU7OCwy1QnuVaHRha1sa\r\n5DEPduVlz79gSTk+vdzF5y4asAQD+q7p+icDC6xqqXH8wPPJViVgeFm6H0+p\r\nXAniG4/XmVqgUkcdv52Zt3SriIM3qgWnL+VioK7Hwr8/EfKv3Lufa6KAk0Z3\r\nP+uoOcHjskHOdB6ZJLHkMuSM/Cfs/S2X6jUnznHNP0OvHioeOwlz4TV9a8hI\r\ngLvZOjAgpS3VjfT4DuxnuepXOaqssN0FlG/aSpH3Ve9HcCns8xCqsILQ/28R\r\n45lNfSadFgYrHb4zB53AVBbrTglUMI623zzc1VALK+OKYSqTqW+tzoWLuXAo\r\nl8hxBYDN/6BF+fLsgH9C7PixWgBRH44BXSw=\r\n=xYVM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.18.0"}}, "1.0.2": {"name": "@inquirer/select", "version": "1.0.2", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.0.3", "@inquirer/type": "^1.0.2"}, "dist": {"shasum": "9092bfcb8ac88bc18cddee50239f9f04d85cf6eb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.0.2.tgz", "fileCount": 7, "integrity": "sha512-JBUdSpL/vzJyCK7VDGbPCpMoFtORx7gJYVXcfhksGcd2l8zlKz45Rj6cnAJkzqQ4cQUNYEuHBGtWbQN8zf/w7w==", "signatures": [{"sig": "MEQCIFl+t5pWXnDNS9XeF5D5/jydRzBTkm29cndDNBb25it+AiBYBLaUarzlaAMnq2PNaYSYmrc6Yd8vHfkJG1WaUVfZOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12725}, "engines": {"node": ">=14.18.0"}}, "1.0.3": {"name": "@inquirer/select", "version": "1.0.3", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.0.4", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "10f8dffb8d44228c08ad6515ecfd21047b30805b", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.0.3.tgz", "fileCount": 7, "integrity": "sha512-RCU8jFKtXmgBsNqHfy5pp3NsVhouBmOGWTY2IXiwxllFkbD/prKEs/2B11/A0Db3d/kOZ2Oe/4vgFgH7vOh5kw==", "signatures": [{"sig": "MEUCIQCHwnhIw74RSr9zDcCVcP0DgT69jX9HHFKnpXSA+AeLbwIgfH1UwI+unwy33XpoYWNw67O2lPy/Uf5YvwHu15gvIG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12725}, "engines": {"node": ">=14.18.0"}}, "1.1.0": {"name": "@inquirer/select", "version": "1.1.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.1.0", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "a4956fd8be490e9f1c3c7fb6ee435873f7bb15f3", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.0.tgz", "fileCount": 7, "integrity": "sha512-CRO35I8s515EWrlDtuMX0nQtY31KlAT9pAmaW+f5q/e6gPQDY51rFAq3FVrzYsBjgETvQVPhSm9XooRboNev0A==", "signatures": [{"sig": "MEUCIBgZN8+NW2SzFicD1kX9vUWjN1gmNbSWG1LpzR2rrAOTAiEAueC11yQw6B9KLInd7L78Ei5ZqtyloXo3wfXShWJRwVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14257}, "engines": {"node": ">=14.18.0"}}, "1.1.2": {"name": "@inquirer/select", "version": "1.1.2", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.1.2", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "84d364582813b1d1bdad80892c82fc2df2209ea7", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.2.tgz", "fileCount": 7, "integrity": "sha512-ZYiAONxCAkbbcH1qUDAYCmZszvvUCrkz5xypXhxB5orQ66oRc+jjCuvnNr7o/97pmjyhKH2gDr3eeKBJlIDx8g==", "signatures": [{"sig": "MEQCIGPAWcNODoFDxYkvehP+/tpejMJwJNY/y6IdzAf4haCQAiA50IzJi5Umi0qUo8YdNNAtgi+dmDyLZb8eN5HVd0Y9FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14257}, "engines": {"node": ">=14.18.0"}}, "1.1.3": {"name": "@inquirer/select", "version": "1.1.3", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.1.3", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "7974d1beff6b87c981a9e25e1ef4cb237f03c1b2", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-R3ypgFRq7QKz9l8uqlEPRkK4dhkkVFy6gzuMPWzkHFTiWBDIjHTwD8b41eQwQW8SRAVls6VyTdtA/Sf0te/bVw==", "signatures": [{"sig": "MEYCIQChW3sVNb+aeID7DCr+d4KIkQyk/JTYLQbNugSCGp1lRwIhAJgXAjMK4CtB19JgLRD0vpg89cCC3JJKUcTXkLIPpu/w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17986}, "engines": {"node": ">=14.18.0"}}, "1.1.4": {"name": "@inquirer/select", "version": "1.1.4", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.2.0", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "5f64c4b6954c414df56a85e13d8b324881749e68", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-Rr1g5Vv9LpGabnNSbqyJ1ON6DqJAnse9DzQfDeqnwdcegawltWHkwPWfwD30xctOQjT0i9YkQTLR0Q2FIdjclg==", "signatures": [{"sig": "MEUCIQDXaFJYDXMoXS8prFBmf2WOxjhJb/c+QhVvhBLGo5txiAIgPCd4NtzliDkhU7xzqdN646nF2CJq9zToWn5MyF3XAF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18109}, "engines": {"node": ">=14.18.0"}}, "1.1.5": {"name": "@inquirer/select", "version": "1.1.5", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.2.1", "@inquirer/type": "^1.0.3"}, "dist": {"shasum": "91e86af361e323a4f607e2cbf71126edab835c81", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.5.tgz", "fileCount": 8, "integrity": "sha512-j2jSrOxwCWvRbgevi0dKlx6rS4MadSK2aBAfdjR/Q3A19yjfHpo7nfi8n+EXVCl7fjBHCIhWunkSTiOoDXB1NA==", "signatures": [{"sig": "MEUCIQDQJNWmzFOmyGnWugFeKCBonHwoZLpsV0bz3L+dB6sjCwIgFLRzrMCpyKLtRgUQ7fH7os+GgnioXYnCeq4f7lS9Um4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18109}, "engines": {"node": ">=14.18.0"}}, "1.1.6": {"name": "@inquirer/select", "version": "1.1.6", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.2.2", "@inquirer/type": "^1.0.4"}, "dist": {"shasum": "7b86b7a4a35d72a8534644759333d293d5b0b580", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.6.tgz", "fileCount": 8, "integrity": "sha512-yF6q9ftqXLgm+vvKNOtYl4poUeWZrGIy6VXGIweMLWCQDhy3lg5J6u0jVZY5LEYLTwRg7/mWJ5pOjk+7hEQcYA==", "signatures": [{"sig": "MEQCIF43bV/RLgn+lqzCk/pPLxyQqBuBUhnw9HVLnhvb19LGAiA5c8I83MWehOvtvQiAkeftmwEx0qY9qZZmOfAvLE7f6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18109}, "engines": {"node": ">=14.18.0"}}, "1.1.7": {"name": "@inquirer/select", "version": "1.1.7", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^1.3.0", "@inquirer/type": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^1.0.6"}, "dist": {"shasum": "1ad35a032d0eafbc3ec8f531ac330324a50baaf4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.1.7.tgz", "fileCount": 8, "integrity": "sha512-3Ym0WOoVduu/AG5GwIxa+fNz8Eop7S1zADbUmMsllrubdYu7qMe9HaTHCb5JOjaVNSoFJuYPH6TizFzGVFVrCQ==", "signatures": [{"sig": "MEUCIQD9LT6g8kITFPjR2f4SfrZX+AAaNImuVDwwgU5sfY2PTgIgHiK069MYYrkQf07JXy+B9hFtJHLGBQZ1nmFG9eiMVvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18164}, "engines": {"node": ">=14.18.0"}}, "1.2.0": {"name": "@inquirer/select", "version": "1.2.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^2.0.0", "@inquirer/type": "^1.1.0"}, "devDependencies": {"@inquirer/testing": "^2.0.0"}, "dist": {"shasum": "f8e054bbc4ad6a53ba3d8500c93d842b779d078d", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-2CqhtE40GFmRXDFzJeMvSowKcO2/yvIzgSpL44+Hl/SAO/1FJgmHNAFGBuqX0RbohYPnSpF8eftgiy16fA3RJw==", "signatures": [{"sig": "MEYCIQDlxjt3Wk6T0qSf0BIhFOeZF6yZZqG0Y0VjQg9A2m1wzgIhAKhEaWvLD5XNWqIIm/MJypLuQJW5UFIaIUOzUKZ7YGfG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18234}, "engines": {"node": ">=14.18.0"}}, "1.2.1": {"name": "@inquirer/select", "version": "1.2.1", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^2.1.0", "@inquirer/type": "^1.1.0"}, "devDependencies": {"@inquirer/testing": "^2.0.0"}, "dist": {"shasum": "51c68ae116ab1e220fedacc225d7e7a7fd017b42", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-13JDLtlwFoqQUYRdMzz5wP3a4DWccJfNA/8M8MDUhhZ8HeKZ3MPaTMlpxwY+Q0Jgbmt56nf7xUuck0XXPce8Xw==", "signatures": [{"sig": "MEYCIQCghaPaFCPFUbG+yhdDZuTKVzo0GN53O9Vk2O7JBAeGqAIhAPIqcuLsSwm+/GiVrteOz8mT+kZd3tXnJz5shirwDHFa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18234}, "engines": {"node": ">=14.18.0"}}, "1.2.2": {"name": "@inquirer/select", "version": "1.2.2", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^2.2.0", "@inquirer/type": "^1.1.0"}, "devDependencies": {"@inquirer/testing": "^2.0.0"}, "dist": {"shasum": "4b86a31214f0f9ff75669539ff9555a95834ae5e", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-asiP4Ej4AR0uWsQt8/ajAtF5IjBuTZ/YQgn/Xk7kviWN/wuFfUBo0dYntr0/rSvhNyt0jY6/yDhMM6mSPFLHRg==", "signatures": [{"sig": "MEQCIAlC+Eq50rVGrdW1aOF2843tjUpJjrUONVHFwIJtFVHyAiAKaoM6vlfbGcLJgO52zMXuw2PTZpPxxMARa3CDghlnMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18234}, "engines": {"node": ">=14.18.0"}}, "1.2.3": {"name": "@inquirer/select", "version": "1.2.3", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^2.3.0", "@inquirer/type": "^1.1.0"}, "devDependencies": {"@inquirer/testing": "^2.1.0"}, "dist": {"shasum": "95062d783478cefa089f7050ec82c3f09f4316f2", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-kipYkf5iVok9i22YSLJiwf4m0Ek6S67tJm20jJr/kjuSmbnbpO0mJGFuhgbrGS4uDqkeEOB3tQ81mqb7cVIVbA==", "signatures": [{"sig": "MEYCIQDVxiSWT7yK1XcqZHCrtwepTRaqWesbt7wWzXonaF/lXAIhAMenfwvmeBklKbnn4cMudj2rxfIfdM4NhKyZt0HzMxtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18234}, "engines": {"node": ">=14.18.0"}}, "1.2.4": {"name": "@inquirer/select", "version": "1.2.4", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^2.3.1", "@inquirer/type": "^1.1.1"}, "devDependencies": {"@inquirer/testing": "^2.1.1"}, "dist": {"shasum": "389cdbe5e8b28bab2ee31c194e620b4818166591", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-mgEH0V6dEhVZuOlOiCRAjHW4FnrBlsLlq3QTRPeaKFw+KV2ygZzzDdOrtkN9gPE1nNeJyl6Bjq4CLveZbPqEOg==", "signatures": [{"sig": "MEUCIGqzaHggr7AUj939e7gu0zUKYCW3yb0Kf1zo0CM+H3HiAiEAhB7JMvuyM7SrpIiNjPIsEPkMWiH2X5HkMfx+l8fSK/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18232}, "engines": {"node": ">=14.18.0"}}, "1.2.5": {"name": "@inquirer/select", "version": "1.2.5", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^3.0.0", "@inquirer/type": "^1.1.1"}, "devDependencies": {"@inquirer/testing": "^2.1.1"}, "dist": {"shasum": "858e65f9b25b60a7741bc33c843322b2371cc831", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.5.tgz", "fileCount": 8, "integrity": "sha512-MPoqecOtxLMGyWQNBmjmVIHQPkTpIJcdAo+K7kvowCymb8dnuuTu+fzYZoRolnszsj4C1mMezirDo3yhCpj40Q==", "signatures": [{"sig": "MEUCIQDQqsuSFPFRKA76uQYSy00+WFHEslWG9rKRWzOjhLx1vAIgfLQPs3qqAKsZSjcPLlN1d3rNRLTr7MKgbpbwi9Rp4jI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18171}, "engines": {"node": ">=14.18.0"}}, "1.2.6": {"name": "@inquirer/select", "version": "1.2.6", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^3.1.0", "@inquirer/type": "^1.1.1"}, "devDependencies": {"@inquirer/testing": "^2.1.1"}, "dist": {"shasum": "30f2681c127088a1563b6a33d9b9d26c524e480c", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.6.tgz", "fileCount": 8, "integrity": "sha512-FE2UQ9sfabDMzu8ynz83nnFoTmUjeoh52AmEwCmiMo61ulN785B5N4t0w8R8ezm6hQtngIGXkP0/FCuGTbsR6g==", "signatures": [{"sig": "MEUCIQDQQ7PY/Y0PLQOYjyMa7Rdi8SzFaD7XYG6dfVcHazWXtgIgchBKRK1QdKA3H3os9g3ALKL7qxrjk5ppYHkPd0OtLPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18171}, "engines": {"node": ">=14.18.0"}}, "1.2.7": {"name": "@inquirer/select", "version": "1.2.7", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^3.1.1", "@inquirer/type": "^1.1.1"}, "devDependencies": {"@inquirer/testing": "^2.1.2"}, "dist": {"shasum": "2e53e7b9299f9f2ff9d6856dd62e3d596cac5ab6", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.7.tgz", "fileCount": 8, "integrity": "sha512-8QJXGEh8s3WYW+TjdO5S0VTyTGWOw+7Ox8hZ0ME/jM89hi0LQxvO7YTwgUpu/8PQ0VV7kMZvWVL4HK3Eh4HJ/g==", "signatures": [{"sig": "MEUCIFGgro/Bc90xyDrA2LbkSsIc8/Ng5+3XkVBxBLGfz5G4AiEA0dbzLusqOKwqPJ/R2QZRbDgE6olqFYLe1ezrggciXHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18171}, "engines": {"node": ">=14.18.0"}}, "1.2.8": {"name": "@inquirer/select", "version": "1.2.8", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^3.1.2", "@inquirer/type": "^1.1.2"}, "devDependencies": {"@inquirer/testing": "^2.1.3"}, "dist": {"shasum": "ce1b529a8d208afcb22819beb6d22f0ce2121742", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.8.tgz", "fileCount": 8, "integrity": "sha512-XX0QMcqvXJwFYDZpx38cSSmJG4wNdpRUJZddY2zjDvzLfpbFRv488cl0vSVJCmRdluzzEwWFc+0tPOQGQ+dqLA==", "signatures": [{"sig": "MEUCIQCELWN7fbzDo/ouNxKUQAjpM7aAhrXP2yEqIn7pOefveQIgR/7v0AjylCv+8pTp7+90G6LmKds12hG8JX9gF9DqdqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18081}, "engines": {"node": ">=14.18.0"}}, "1.2.9": {"name": "@inquirer/select", "version": "1.2.9", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^4.0.0", "@inquirer/type": "^1.1.2"}, "devDependencies": {"@inquirer/testing": "^2.1.4"}, "dist": {"shasum": "0a4a0392e6de8b3b9ce888c782d34625ae3b662d", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.9.tgz", "fileCount": 8, "integrity": "sha512-WXMQfUGzxrxdbDCU50xKqYCMoz+SOZnyV8sOeEJ8Ei5AjANz1fap3xA7EF8aZLJ9K1//m4OnyS/XDoi31Tqn+g==", "signatures": [{"sig": "MEYCIQDFzLtgCv/83Ugi4MzkDi08Zf/pj5U8rdtzR2RfQQ82qwIhAJE4DLDZNAAxPMyKVb6w+WU8Nm9NS13LxQM/smJNPDq+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18081}, "engines": {"node": ">=14.18.0"}}, "1.2.10": {"name": "@inquirer/select", "version": "1.2.10", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^4.1.0", "@inquirer/type": "^1.1.3"}, "devDependencies": {"@inquirer/testing": "^2.1.5"}, "dist": {"shasum": "d3689b36fa86724a21f2727beeeffd7150c91137", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.10.tgz", "fileCount": 8, "integrity": "sha512-3gH8nglC+x/8DFXIQMGmfMwEO+Lv0UIxyRfPmUnqBVO49/uXhuvJFg7Qc9JYVVa62wm0PoPUwm/TGoBEtAIozQ==", "signatures": [{"sig": "MEUCIFHHaWc02TBJa5bKbXchF5GtnTRFwonstR2K1HsoZ98JAiEArfSHZN5Q61/Yem9qkO1j6d8nHEYen+6HjiGiZEJIMe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17886}, "engines": {"node": ">=14.18.0"}}, "1.2.11": {"name": "@inquirer/select", "version": "1.2.11", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^5.0.0", "@inquirer/type": "^1.1.4"}, "devDependencies": {"@inquirer/testing": "^2.1.6"}, "dist": {"shasum": "4a99edadd23f398bf51838b89383d3d0cfd80a05", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.11.tgz", "fileCount": 8, "integrity": "sha512-LH2wzAsWfu/+wcapeht07zTDefuvGTpv0+9dCAQ68QigF+4gHzpWq5+AbBLbxzuH2Wz4WlHcti85nFUPPM1t3A==", "signatures": [{"sig": "MEYCIQD9lRTgRYUjL6A3NIZOkn8ykZRBi8HW4iRax9jjO5lr8gIhANSSUyEcMGRH9Cpm8pnvlsq6qoq+1kvqHiQDJwKzxqLc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18318}, "engines": {"node": ">=14.18.0"}}, "1.2.12": {"name": "@inquirer/select", "version": "1.2.12", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^5.0.1", "@inquirer/type": "^1.1.5"}, "devDependencies": {"@inquirer/testing": "^2.1.7"}, "dist": {"shasum": "a9b9a8828a9387a8e6eaa6f2466ec7e70b077547", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.2.12.tgz", "fileCount": 8, "integrity": "sha512-+Ke<PERSON>bxjJ/qa1yUxwaD38fhhi1nzL2qGigdT/E/GwXLtoWLJ8Rl8hCe37xRB2gP8yK3vhhv6ll8C4bgJtu2hIWA==", "signatures": [{"sig": "MEYCIQCZ8yvLxL/R6UdXgZkqgOcdoO5TbTMJH5G3lAtwN6waYAIhANtM71mfEetS2IOp6g7cqCycn1VSB36CMLw58rfaZFsE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17166}, "engines": {"node": ">=14.18.0"}}, "1.3.0": {"name": "@inquirer/select", "version": "1.3.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^5.1.0", "@inquirer/type": "^1.1.5"}, "devDependencies": {"@inquirer/testing": "^2.1.8"}, "dist": {"shasum": "00dfa5068bea85bffeb7aa7c402407bb590c8cd4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-3sL5odCDYI+i+piAFqFa5ULDUKEpc0U1zEY4Wm6gjP6nMAHWM8r1UzMlpQXCyHny91Tz+oeSLeKinAde0z6R7w==", "signatures": [{"sig": "MEUCIQCQNvUf2I6F8eZS27AanRhG7r0JPAoiwkTblVVtjTRvoQIgL7wwkICh1fjwyHgGXBxnGiX7FPPRaA/5MRkSvxEW9Yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18258}, "engines": {"node": ">=14.18.0"}}, "1.3.1": {"name": "@inquirer/select", "version": "1.3.1", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^5.1.1", "@inquirer/type": "^1.1.5"}, "devDependencies": {"@inquirer/testing": "^2.1.9"}, "dist": {"shasum": "b10bb8d4ba72f08eb887b3d948eb734d680897c6", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.3.1.tgz", "fileCount": 8, "integrity": "sha512-EgOPHv7XOHEqiBwBJTyiMg9r57ySyW4oyYCumGp+pGyOaXQaLb2kTnccWI6NFd9HSi5kDJhF7YjA+3RfMQJ2JQ==", "signatures": [{"sig": "MEUCIC1dwVfD02dltoLezqGpLRjcJC1GnzxhVyXH+gZZVRYpAiEAl34OltFEBOEkIBD6DV1sn8eFZBgOgMGnqZ04tqRY6Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19998}, "engines": {"node": ">=14.18.0"}}, "1.3.2": {"name": "@inquirer/select", "version": "1.3.2", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^5.1.2", "@inquirer/type": "^1.1.6"}, "devDependencies": {"@inquirer/testing": "^2.1.10"}, "dist": {"shasum": "452518328c971fa360582538e10db2554eae089e", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.3.2.tgz", "fileCount": 8, "integrity": "sha512-sFKGEHX3dYZNhfBXcrmN691eZJnLjHPF6DXPGJWaBjkeBEUvDcngqpAden+U8cR7HOwvP9hb7LHn4hr9VbDpSQ==", "signatures": [{"sig": "MEUCIC6rP7X00B/QndNhI7cVlicNApino//0CcyA4+M9K0JLAiEAoj9RmWPDVmZYzyd+JNAUH9ShPwo/slX8R0yr7/Ni0Vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19999}, "engines": {"node": ">=14.18.0"}}, "1.3.3": {"name": "@inquirer/select", "version": "1.3.3", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^6.0.0", "@inquirer/type": "^1.1.6"}, "devDependencies": {"@inquirer/testing": "^2.1.10"}, "dist": {"shasum": "7d832ee603c15b706148e47cda29cdf6634cd94b", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-1.3.3.tgz", "fileCount": 8, "integrity": "sha512-RzlRISXWqIKEf83FDC9ZtJ3JvuK1l7aGpretf41BCWYrvla2wU8W8MTRNMiPrPJ+1SIqrRC1nZdZ60hD9hRXLg==", "signatures": [{"sig": "MEQCIATwMic22vnmBkEtrG9HUNSOusig2M5DrmxMw5prrtD/AiA8RpXyc8TDwVkNy+1VHx9dBftPD4/smQdJ5IwDEdBCDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20251}, "engines": {"node": ">=14.18.0"}}, "2.0.0": {"name": "@inquirer/select", "version": "2.0.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.0.0", "@inquirer/type": "^1.2.0"}, "devDependencies": {"@inquirer/testing": "^2.1.11"}, "dist": {"shasum": "c518562bcf6a7c54ed8037f4ff8f5a351e7a3d75", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-ZxWP1gHbReAH6HdoNQRV/9W/UjgKSeiiQX2DxJ6w3GDiQeC3fRAL+lukuMM+QGteGqaTjWwIEWhPLvgbGIrRgg==", "signatures": [{"sig": "MEUCIQCEcdsIJYfhDCwRHLcj/GSKJDPynyK472xsxBx7vv5nMAIgSLIdGW3iTOcQqjLQr1nPy2b1FktpzsxHl3cznFjTSkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23776}, "engines": {"node": ">=18"}}, "2.1.0": {"name": "@inquirer/select", "version": "2.1.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.0.1", "@inquirer/type": "^1.2.0"}, "devDependencies": {"@inquirer/testing": "^2.1.12"}, "dist": {"shasum": "e9a87140383697d2ea446147e8c7050db14b5fbd", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-4t/8FUS9qRCfkVT/yuTunA/j51Y2TehJVRexbTvILwckwBBJVGhOuxLwWwt8F9CO6CvKHW7U3nyibBifa5LUzA==", "signatures": [{"sig": "MEYCIQDKazVjE9C2S6J3Z7liziBOKgd6HhKEp9A4U5tC12xiIwIhANxyQ3ATL3mzqf93SYsTpmzcZR9flYC2itXFoTiP6JEf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26449}, "engines": {"node": ">=18"}}, "2.1.1": {"name": "@inquirer/select", "version": "2.1.1", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.0.2", "@inquirer/type": "^1.2.0"}, "devDependencies": {"@inquirer/testing": "^2.1.12"}, "dist": {"shasum": "1b28fe286a22f2e00214b408cd50aa8955161842", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.1.1.tgz", "fileCount": 8, "integrity": "sha512-CvUvTGwgaQBUzPVynxxgwUXNhr0zXp44gjqAQ9yvW9RGRH4kMEpcoSId4aLH0ao2HsBzAdE8DlefdojsjGfNNw==", "signatures": [{"sig": "MEQCID5g+mpm04dq7F8Y3ma1VGUaWY01oSN+oMbMQtRXmzHSAiAfAElZG6RGiJepHqKQgRxdAgqCgb5YQog1e87sn3X3xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26449}, "engines": {"node": ">=18"}}, "2.2.0": {"name": "@inquirer/select", "version": "2.2.0", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.1.0", "@inquirer/type": "^1.2.1"}, "devDependencies": {"@inquirer/testing": "^2.1.13"}, "dist": {"shasum": "f0a6c523f24a7eefd3b912a2a473a2dc82e561d9", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-Pml3DhVM1LnfqasUMIzaBtw+s5UjM5k0bzDeWrWOgqAMWe16AOg0DcAhXHf+SYbnj2CFBeP/TvkvedL4aAEWww==", "signatures": [{"sig": "MEUCIQDgzapdNm/TTNyFwOV06tpzfQ4FIIQxqoXBsW42gGW15wIgFhA/IVjox65SUgzGtGU8Og7pLkNQ0mtAqv08gnvWitA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26537}, "engines": {"node": ">=18"}}, "2.2.1": {"name": "@inquirer/select", "version": "2.2.1", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.1.1", "@inquirer/type": "^1.2.1"}, "devDependencies": {"@inquirer/testing": "^2.1.14"}, "dist": {"shasum": "cd1f8b7869a74ff7f409a01f27998d06e234ea98", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.2.1.tgz", "fileCount": 8, "integrity": "sha512-JR4FeHvuxPSPWQy8DzkIvoIsJ4SWtSFb4xVLvLto84dL+jkv12lm8ILtuax4bMHvg5MBj3wYUF6Tk9izJ07gdw==", "signatures": [{"sig": "MEYCIQDE8hQVFVluCf7eB4hJHFvvz7cRM8yQVNQisCprn4+wZwIhAIE9ERJSg/7NnVMGVB+VnXAucy5N9wX8Y0T4AV7Kkfn3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26537}, "engines": {"node": ">=18"}}, "2.2.2": {"name": "@inquirer/select", "version": "2.2.2", "dependencies": {"chalk": "^4.1.2", "figures": "^3.2.0", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.1.2", "@inquirer/type": "^1.2.1"}, "devDependencies": {"@inquirer/testing": "^2.1.15"}, "dist": {"shasum": "6a81ce041745725343afacaf38e1b03ff9adee3a", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.2.2.tgz", "fileCount": 8, "integrity": "sha512-WaoleV3O/7iDAHFC0GArOkl7Yg/7wQ/UptxEkfM+bG67h65v0troAjkNASBbNiz9vvoNZxOGhVrug0LNDftCoQ==", "signatures": [{"sig": "MEUCIErmqbDM/mOddjdBmk9PdKnKkT+9bsOdLaAgknEm8fVdAiEAlHCoLPxgJiaVWMgn57dQZrLXiOUt9GVH1pr7tvPTYuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26537}, "engines": {"node": ">=18"}}, "2.2.3": {"name": "@inquirer/select", "version": "2.2.3", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^7.1.3", "@inquirer/type": "^1.2.2", "@inquirer/figures": "^1.0.0"}, "devDependencies": {"@inquirer/testing": "^2.1.16"}, "dist": {"shasum": "b6ef131734b0b29e782873838e24d5e3fff8e1d4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.2.3.tgz", "fileCount": 8, "integrity": "sha512-jwR8JpCOtg9Q+H5RDvquNKb6at3C+yuTPbu/tLuJgHkvq1WJYllWOmtxBDgzSw/xO78bsWlgN2cDmSiWyxtWow==", "signatures": [{"sig": "MEUCIFqyrp7H0SR49kggFW1cNv6Jz7FWgaHRBkVz/cSJ+vAzAiEA1kkiRE2CsyTagi/qT8tcGpi3ahEhmblHheh7kzEnHBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26322}, "engines": {"node": ">=18"}}, "2.3.0": {"name": "@inquirer/select", "version": "2.3.0", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.0.0", "@inquirer/type": "^1.3.0", "@inquirer/figures": "^1.0.0"}, "devDependencies": {"@inquirer/testing": "^2.1.17"}, "dist": {"shasum": "7edc17f6502b46a0d320091ae4d9aabed990d662", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.0.tgz", "fileCount": 8, "integrity": "sha512-FHZkDUIfGfENxzH/M4tskSWUgRnszKUXb/qlrqbvjwUeFFFSOaWztMkAg4sLwnw2nbT+bdi+WlBn98C/j0NOlQ==", "signatures": [{"sig": "MEQCIENACx3xvBllXJcnJBfMQj4RcmYMbOlSO4BjeXpJV2NQAiAwpeTph7ZDrXYrf4xpYVQa1ukKOW/WM9dxpH7QDsa7xA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27518}, "engines": {"node": ">=18"}}, "2.3.1": {"name": "@inquirer/select", "version": "2.3.1", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.0.1", "@inquirer/type": "^1.3.0", "@inquirer/figures": "^1.0.1"}, "devDependencies": {"@inquirer/testing": "^2.1.17"}, "dist": {"shasum": "dea077284bc29217d712e44672fd906be42d91af", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.1.tgz", "fileCount": 8, "integrity": "sha512-UagbSdmSjeoukHLXqkDQi2ewiGEogUyxaOeKeH34Ngmc/2z+S8u4JsJWToMJNKIHjEtoTFdlYpFrxCxapp06nQ==", "signatures": [{"sig": "MEQCIA1fe6CgkgcKoTWO8YMUxkptevcBEmoAkIusVpnhJc/KAiAAxyYBd+TG9MwlLtRif5L2xm9Af82HsWLMCRIRnEYOMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27518}, "engines": {"node": ">=18"}}, "2.3.2": {"name": "@inquirer/select", "version": "2.3.2", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.1.0", "@inquirer/type": "^1.3.1", "@inquirer/figures": "^1.0.1"}, "devDependencies": {"@inquirer/testing": "^2.1.18"}, "dist": {"shasum": "3c4768a223a38aea57b694cfd7d1c283df320570", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.2.tgz", "fileCount": 8, "integrity": "sha512-VzLHVpaobBpI3o/CWSG2sCDqrjHZEYAfT1bowbR8Q72fEi0WfBO3Fnh595QqBit9kQhI1uJbVHaaovg1I7eE7Q==", "signatures": [{"sig": "MEYCIQCAsdB0JE/ZcNbA5unXVo/YUNAL883nvPRfr1RmICcQRgIhAJ8NFnnUgXOsQxVd5uzuN2lAxkBAbhDAffJLVTzH2/N2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27637}, "engines": {"node": ">=18"}}, "2.3.3": {"name": "@inquirer/select", "version": "2.3.3", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.2.0", "@inquirer/type": "^1.3.1", "@inquirer/figures": "^1.0.1"}, "devDependencies": {"@inquirer/testing": "^2.1.19"}, "dist": {"shasum": "771ef76fbb0846df3d1954b3b3fd2b684247b2ee", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.3.tgz", "fileCount": 8, "integrity": "sha512-0ptHMogTnyTNKIJVEfCl4fFDQSzIR2/SjgBoD1MLXDszP3UbkYroZ9ii3e6x7dMCWrPGkGWZPyxpy3Rs55vWLw==", "signatures": [{"sig": "MEUCIQCFhtWAhe3Mbgm2wk4Bd8v5RgQyl7EZaZCaeKNQig5snAIgRVt3ihiUO7OttSmuT+HfcLbe3bj4cNdqqS8MHPlm5Ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27637}, "engines": {"node": ">=18"}}, "2.3.4": {"name": "@inquirer/select", "version": "2.3.4", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.2.1", "@inquirer/type": "^1.3.2", "@inquirer/figures": "^1.0.2"}, "devDependencies": {"@inquirer/testing": "^2.1.20"}, "dist": {"shasum": "f1040402ceb51ca4eb7a08d457fba540f01ec6a8", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.4.tgz", "fileCount": 8, "integrity": "sha512-y9HGzHfPPh60QciH7WiKtjtWjgU24jrIsfJnq4Zqmu8k4HVVQPBXxiKKzwzJyJWwdWZcWATm4VDDWGFEjVHvGA==", "signatures": [{"sig": "MEYCIQDEw1lnvaPKJci0NZQmPt9AuJ1OsE+S67LDOqTzqgthHQIhAIJHo2+cYdHvMC9zkUVOuZMR4LJPqOdyvD0Z3ibu3mtY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27682}, "engines": {"node": ">=18"}}, "2.3.5": {"name": "@inquirer/select", "version": "2.3.5", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.2.2", "@inquirer/type": "^1.3.3", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.21"}, "dist": {"shasum": "db4ff1ba482ed87f2cdd33e03e64c7b430acdd7d", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.5.tgz", "fileCount": 8, "integrity": "sha512-IyBj8oEtmdF2Gx4FJTPtEya37MD6s0KATKsHqgmls0lK7EQbhYSq9GQlcFq6cBsYe/cgQ0Fg2cCqYYPi/d/fxQ==", "signatures": [{"sig": "MEYCIQC6Z9ERTPy7iPIEOQWEPR0E+VsFFJOfSonsSJmDjIdstgIhALv06bZuAiKVMfIyYoUCZgwg6F8mGsis9KebHG+M0HvV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27706}, "engines": {"node": ">=18"}}, "2.3.6": {"name": "@inquirer/select", "version": "2.3.6", "dependencies": {"chalk": "^4.1.2", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.2.3", "@inquirer/type": "^1.3.3", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.22"}, "dist": {"shasum": "2b1d09f48ec52f1a66c59082ef214ce61a7315b3", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.6.tgz", "fileCount": 8, "integrity": "sha512-eLqlZXre69Jenmar5s+3018xF3lpaGfxVZLHkCzkrhtuTuFjpYtb0YpiYeZNKZm9pa+ih3s9acN/zRt+dDh+qA==", "signatures": [{"sig": "MEUCIQC+DVivCTD0j/kx20Pn/uETS42lbspxEDpXRAzLsoL7BQIgZNCYq/oUc3BirgUjyUNlhmYCdCpspbwAuatjpKq2YZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27706}, "engines": {"node": ">=18"}}, "2.3.7": {"name": "@inquirer/select", "version": "2.3.7", "dependencies": {"picocolors": "^1.0.1", "ansi-escapes": "^4.3.2", "@inquirer/core": "^8.2.4", "@inquirer/type": "^1.3.3", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.23"}, "dist": {"shasum": "6cfb2f986e1507d6604a9464b97f4edbc11ece54", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.7.tgz", "fileCount": 7, "integrity": "sha512-11+4klLDhnhvT8FgpFkq3+CUvWVRqk8N8f3TnM84Orzt6UmdynRRzT9glcc5JD7u9uN8rvUJNyskf4QKlR+flA==", "signatures": [{"sig": "MEUCIES0J0Xso+Ro5WuPlqLtuW80SWjEUbj/enGj852gzjI4AiEAs3JS8BfqbEmRXxYAnXfNQUZxg/V/mG+QTXNivUPy84Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21087}, "engines": {"node": ">=18"}}, "2.3.8": {"name": "@inquirer/select", "version": "2.3.8", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.0", "@inquirer/type": "^1.4.0", "yoctocolors-cjs": "^2.1.1", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.24"}, "dist": {"shasum": "484d749e4f5e0232f2c3b5c293394aaa6378f990", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.8.tgz", "fileCount": 7, "integrity": "sha512-x4gJq9OVw8i0K03V3tJwTt2svz4J7ghqEhXaFBprRTnmbYJwQZRnnLf3PFBg+fWOti7b7fD0Oc4SLXqGeQLByg==", "signatures": [{"sig": "MEUCIGk9ZJrRs6hjHr7PQxu+4exENa2VDcmHL08x1vyxyNXDAiEA2rzlZVYiHN2ydXD5xFQqfcscOLbIquv4crI0NyU5ZZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21120}, "engines": {"node": ">=18"}}, "2.3.9": {"name": "@inquirer/select", "version": "2.3.9", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.1", "@inquirer/type": "^1.4.0", "yoctocolors-cjs": "^2.1.1", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "51cff8288da4d0c7ba0dce081ac98ee7651aa8eb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.9.tgz", "fileCount": 7, "integrity": "sha512-5kgs8L0Xo62iD047gpxpACxRqwl+iK/NNhvXdkt1qjdADdEd1rszVqzIWHYaigz8sBG+F50wMAREulJFt/Xozw==", "signatures": [{"sig": "MEUCICrgsYC0HhSNVI80Z0BtUK17DGE8i8OY+e+UuNNNYT1VAiEAnmOmr5ZNS29xigLtmKNiN13l2ya+a1EADmkC7Pp3buc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21225}, "engines": {"node": ">=18"}}, "2.3.10": {"name": "@inquirer/select", "version": "2.3.10", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.2", "@inquirer/type": "^1.4.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.3"}, "devDependencies": {"@inquirer/testing": "^2.1.25"}, "dist": {"shasum": "4491805435984726c75f89e8f810ddb1fe503123", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.10.tgz", "fileCount": 8, "integrity": "sha512-rr7iR0Zj1YFfgM8IUGimPD9Yukd+n/U63CnYT9kdum6DbRXtMxR45rrreP+EA9ixCnShr+W4xj7suRxC1+8t9g==", "signatures": [{"sig": "MEUCIGK9d3I7Czp1qQYDlJ7c5x96FqQqYVJEkcAXKSHawOKbAiEAif8RHJa7w0GIfDeaR2XsJ/kuOHwTHcoQ/rducAvNZLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22044}, "engines": {"node": ">=18"}}, "2.3.11": {"name": "@inquirer/select", "version": "2.3.11", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.3", "@inquirer/type": "^1.5.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.4"}, "devDependencies": {"@inquirer/testing": "^2.1.26"}, "dist": {"shasum": "e17fc324758571557beb373c7d3b95dd136d090e", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.3.11.tgz", "fileCount": 8, "integrity": "sha512-DebGErUSCyzwIP2zx3hs1X4TAzxSl/yNHzuYGE6KFkHq3ubg+5dJZacFxN1C1eBkJvQ0XBWGpY6MTzHsJbxkpw==", "signatures": [{"sig": "MEYCIQCBxi61yc0vToQkfcf4LwiB2zUXuDW3iV9Kvifc6IXy0gIhAOthGc8HHBD//+HoKTN9IT28vib/hYBTm6pHH7NHdWym", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22050}, "engines": {"node": ">=18"}}, "2.4.0": {"name": "@inquirer/select", "version": "2.4.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.3", "@inquirer/type": "^1.5.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.4"}, "devDependencies": {"@inquirer/testing": "^2.1.26"}, "dist": {"shasum": "88e6063c55c6c7c487a414564ee6e956c550a8f7", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.0.tgz", "fileCount": 8, "integrity": "sha512-iU1eRkHirVNs43zWk6anMIMKc7tCXlJ+I5DcpIV7JzMe+45TuPPOGGCgeGIkZ4xYJ3cXdFoh7GJpm84PNC8veg==", "signatures": [{"sig": "MEQCIHsdyQtVT57gVNpk+8zD5QzGeNClR2S8OKJkGFwg3q3MAiA/cETSIEeJ4RbVozcSGc1WWXMI6PSWfEZr5hVEk9J0Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22888}, "engines": {"node": ">=18"}}, "2.4.1": {"name": "@inquirer/select", "version": "2.4.1", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.4", "@inquirer/type": "^1.5.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.4"}, "devDependencies": {"@inquirer/testing": "^2.1.27"}, "dist": {"shasum": "385cf2c62a402303d9ea214c358f60364f0ad12c", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.1.tgz", "fileCount": 8, "integrity": "sha512-m15ZwV2E2QDy0VbO/BRkVZ6TX6chYU+7K7//R47c3/Xai1d2AESHy4U88G7uq2mR1atl/p4HvMClKASNJvUDRg==", "signatures": [{"sig": "MEYCIQCgymPPQKwjjoReFk5DpNKLAqK29Tms7TAFl4ioxRfiuwIhAM3eyTHy5sYEa5jEwvGiytbORPIjB0+fMkktoC9d/cIw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22886}, "engines": {"node": ">=18"}}, "2.4.2": {"name": "@inquirer/select", "version": "2.4.2", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.5", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.28"}, "dist": {"shasum": "d76a7a4ced94ddf195942133cc40e63f92d97035", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.2.tgz", "fileCount": 8, "integrity": "sha512-r78JlgShqRxyAtBDeBHSDtfrOhSQwm2ecWGGaxe7kD9JwgL3UN563G1ncVRYdsWD7/tigflcskfipVeoDLhLJg==", "signatures": [{"sig": "MEUCIB/+9dfyGyiL52ZMRa7UhM2z++ANsYIcYn9T7sjLKgFcAiEAi3Lx+hPCpaQZKawsbHVCehsWzZVR/FsUUZ7oz2epgG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21775}, "engines": {"node": ">=18"}}, "2.4.3": {"name": "@inquirer/select", "version": "2.4.3", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.6", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.29"}, "dist": {"shasum": "e9c641da77e7dd2514ffa10b576455f1735d684c", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.3.tgz", "fileCount": 8, "integrity": "sha512-JKKZKFtN+E6aY8p9eHHDilTqAJ/taQeSzDUE08T3AddwZVj3bgQgQ5CR4Yi2/XfVv1xfZH/ENNQop7eZ8sEqfQ==", "signatures": [{"sig": "MEYCIQC8sc6i7VsumyMGcouLb8NYW8L35s1/VDIjyAT2UnxlwQIhAOCHu5ATqSr0jStlfrkiUaZJ6JZnm5XuvIn7bbxPdCtL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21847}, "engines": {"node": ">=18"}}, "2.4.4": {"name": "@inquirer/select", "version": "2.4.4", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.7", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "4ea80f0c51bcf28c8c80e7432e320a2b940b27b0", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.4.tgz", "fileCount": 8, "integrity": "sha512-TfC3M/sUesQnDUMSjCpp631e+Sl3LMzVV5Eyx0z3OoPd44ttF0QNUe/gE4XCX3ofg1LxUHOl/qYUukdfLUxXGw==", "signatures": [{"sig": "MEYCIQClb3N7pt7DgcSsUyweTF1++9no++ERdUaZbeENCtWrvgIhAMfSl2I8vdvw3gZt0kAqjr8HjwsnXACmmQYxp5U82ZyM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21847}, "engines": {"node": ">=18"}}, "2.4.5": {"name": "@inquirer/select", "version": "2.4.5", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.8", "@inquirer/type": "^1.5.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.30"}, "dist": {"shasum": "365341ee5c2f80ed48b8ed5cf2bdd6c456067ab8", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.5.tgz", "fileCount": 8, "integrity": "sha512-DbCthH3l7vrrK+Ewll3bgzxC3dzMle8xkWYta4if31p9NOmFNhZKhSfdYMjaOtGFBCUEwo4D5LMgN6sPKgUWIw==", "signatures": [{"sig": "MEYCIQC4boM4GYM4RdH0qFZAunVuQU9+5TFK1GKJ+iqNixqkWwIhAI2OAXj0G8RFyTNxEIm3WFtz3sw8PKsYtYzCB1qeXtRn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22046}, "engines": {"node": ">=18"}}, "2.4.6": {"name": "@inquirer/select", "version": "2.4.6", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.9", "@inquirer/type": "^1.5.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "b920be057ac2993a6b036d4165f7946db5049e1a", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.6.tgz", "fileCount": 8, "integrity": "sha512-fjcZQGyIviUBQ0Msoyf92vCmmN7Xv99vzPoybhLGBCM4cCz+l/U3p2++F7/xDJwvH70YHcvWrk8aN7STmrHMsQ==", "signatures": [{"sig": "MEUCIQC7IqiBVOWD5Ab649M0Txd1szJ1xgeCgS56Iu/0SI3mdQIgK7/Yt1S5zPQ99WRcm3h2z6oC32g7T+bRJeB6++gbjAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22062}, "engines": {"node": ">=18"}}, "2.4.7": {"name": "@inquirer/select", "version": "2.4.7", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.0.10", "@inquirer/type": "^1.5.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.31"}, "dist": {"shasum": "6a23742b4f76d228186dfd42571d973def378ffa", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.4.7.tgz", "fileCount": 8, "integrity": "sha512-JH7XqPEkBpNWp3gPCqWqY8ECbyMoFcCZANlL6pV9hf59qK6dGmkOlx1ydyhY+KZ0c5X74+W6Mtp+nm2QX0/MAQ==", "signatures": [{"sig": "MEUCIQCLIe8GHn3ZEKWGU+FqgdvypKEovJgfCwU7DFnEOJBLzQIgK26fnjdwrttu3X90g8iEmKeQZQdVIvfMDU3YG+1fO6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22063}, "engines": {"node": ">=18"}}, "2.5.0": {"name": "@inquirer/select", "version": "2.5.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.1.0", "@inquirer/type": "^1.5.3", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.32"}, "dist": {"shasum": "345c6908ecfaeef3d84ddd2f9feb2f487c558efb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-2.5.0.tgz", "fileCount": 8, "integrity": "sha512-YmDobTItPP3WcEI86GvPo+T2sRHkxxOq/kXmsBjHS5BVXUgvgZ5AfJjkvQvZr03T81NnI3KrrRuMzeuYUQRFOA==", "signatures": [{"sig": "MEUCIGsGXL1+rAPy59wQQRA83o+EqdfgdhsXicZN33iYAutBAiEAuoNEfdJA/B5Y3d7kQqf0E+gLTEF47TNMRF0EojhRqpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23084}, "engines": {"node": ">=18"}}, "3.0.0": {"name": "@inquirer/select", "version": "3.0.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.2.0", "@inquirer/type": "^1.5.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.5"}, "devDependencies": {"@inquirer/testing": "^2.1.33"}, "dist": {"shasum": "55e00ae25511a53b473f428c2a538716b8146a43", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-mraI4Mb7zAXqtQydxoQk9k2ByezfXSWjiLU2X0IVfZ36DDtlphziCGUi1KTkSzH79yYp8wwHPh58dQcSl4/KWg==", "signatures": [{"sig": "MEUCIAykOzm9F24GScCZm5hrHY/jbgOT1wOhrR+yT9I6p88cAiEAlPEaAqJfyXkWktipTECGMrw3BWQohipvnfMHdFE3xq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23170}, "engines": {"node": ">=18"}}, "3.0.1": {"name": "@inquirer/select", "version": "3.0.1", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^9.2.1", "@inquirer/type": "^2.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.6"}, "devDependencies": {"@inquirer/testing": "^2.1.34"}, "dist": {"shasum": "1df9ed27fb85a5f526d559ac5ce7cc4e9dc4e7ec", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-lUDGUxPhdWMkN/fHy1Lk7pF3nK1fh/gqeyWXmctefhxLYxlDsc7vsPBEpxrfVGDsVdyYJsiJoD4bJ1b623cV1Q==", "signatures": [{"sig": "MEYCIQDfZqmpt5zC+xrnBQQ4FTyVY3gpD7ZSzYWhb5/nIqsmqgIhAOkXPuSfzPadsG57/imm6rRtb8S173SCQs/z3m1vmpoC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23170}, "engines": {"node": ">=18"}}, "4.0.0": {"name": "@inquirer/select", "version": "4.0.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.0.0", "@inquirer/type": "^3.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.7"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.35", "@arethetypeswrong/cli": "^0.16.4"}, "dist": {"shasum": "6b795e288319d9f3e3c471ac79b13f44592c3257", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.0.tgz", "fileCount": 9, "integrity": "sha512-XTN4AIFusWbNCBU1Xm2YDxbtH94e/FOrC27U3QargSsoDT1mRm+aLfqE+oOZnUuxwtTnInRT8UHRU3MVOu52wg==", "signatures": [{"sig": "MEUCIQC5kyqJfeYZXU2lACPgguiBBFxU/zg7/GNA2GCehZOb7wIgXHPQ2dyaGWjN3VANBJJYREfGVQqE4a5igRS1+U2KgIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22434}, "engines": {"node": ">=18"}}, "4.0.1": {"name": "@inquirer/select", "version": "4.0.1", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.0.1", "@inquirer/type": "^3.0.0", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.7"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.36", "@arethetypeswrong/cli": "^0.16.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "fb651f0e0fb7da1256cc75a399dc2ac72a7f7df4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.1.tgz", "fileCount": 9, "integrity": "sha512-tVRatFRGU49bxFCKi/3P+C0E13KZduNFbWuHWRx0L2+jbiyKRpXgHp9qiRHWRk/KarhYBXzH/di6w3VQ5aJd5w==", "signatures": [{"sig": "MEYCIQDQuSR+g4c/US1ouiRY04wk433/TLg4betfI70d3fyhBQIhAK25lcTWHIizTrSZqGeZ8iUt15vpT1yM5BtusZ5oxl/t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22489}, "engines": {"node": ">=18"}}, "4.0.2": {"name": "@inquirer/select", "version": "4.0.2", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.0", "@inquirer/type": "^3.0.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.8"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.37", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "c38ef154524a6859de4a1af11a90ad3f9638c9f2", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.2.tgz", "fileCount": 9, "integrity": "sha512-uSWUzaSYAEj0hlzxa1mUB6VqrKaYx0QxGBLZzU4xWFxaSyGaXxsSE4OSOwdU24j0xl8OajgayqFXW0l2bkl2kg==", "signatures": [{"sig": "MEQCIGed4VbflgMoM9dWwMLEdjmPi1iZunV8Et1fuFm14pjIAiAigsiV83kbwZwg7hKEOQ0AoZieiPC0fMm6fQVK2i7Gpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22489}, "engines": {"node": ">=18"}}, "4.0.3": {"name": "@inquirer/select", "version": "4.0.3", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.1", "@inquirer/type": "^3.0.1", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.8"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.38", "@arethetypeswrong/cli": "^0.17.0"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "24a9d744685608ff26262fccb41fa93b4dac615f", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.3.tgz", "fileCount": 9, "integrity": "sha512-OZfKDtDE8+J54JYAFTUGZwvKNfC7W/gFCjDkcsO7HnTH/wljsZo9y/FJquOxMy++DY0+9l9o/MOZ8s5s1j5wmw==", "signatures": [{"sig": "MEUCIEx64LjPtrtg/vKJq1xDPV7YR+Exw+BAoI7zi+3ZTBYEAiEAvolicMLyJ8yMoTtRdo7odO4vsW7pMGKXnl26o+BPsMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22489}, "engines": {"node": ">=18"}}, "4.0.4": {"name": "@inquirer/select", "version": "4.0.4", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.2", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.39", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "026ada15754def1cd3fbc01efc56eae45ccc7de4", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.4.tgz", "fileCount": 9, "integrity": "sha512-ZzYLuLoUzTIW9EJm++jBpRiTshGqS3Q1o5qOEQqgzaBlmdsjQr6pA4TUNkwu6OBYgM2mIRbCz6mUhFDfl/GF+w==", "signatures": [{"sig": "MEUCIQCi3DkmftoO45OuUy7eCHpYoqGlMjvEVt2X4B/flVPGRwIgDl9fve7ERNSaan1yAZCxM3vXgNzp0wkSSo+KYBz7PZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22489}, "engines": {"node": ">=18"}}, "4.0.5": {"name": "@inquirer/select", "version": "4.0.5", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.3", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.40", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "f7f59e19f085d9b0b62e659b54a81d54b5740cdb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.5.tgz", "fileCount": 9, "integrity": "sha512-5UAnpWqs0G316MwJdSdgaRcWPIuUPllHa8pdHVi/w9KE/Ff/GzWhPwUn9ETtq/n8GEiWDUrP/LdJN8FJxf7JbA==", "signatures": [{"sig": "MEQCIGv7tGTNTKvQTWheJo8cl4HBhfgEBtYPiwIfbXZFZnQaAiB65+sTyqZ+zDk+hkU3siCVqHBJiMB22vgf6dpwG2v+kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22955}, "engines": {"node": ">=18"}}, "4.0.6": {"name": "@inquirer/select", "version": "4.0.6", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.4", "@inquirer/type": "^3.0.2", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.9"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.41", "@arethetypeswrong/cli": "^0.17.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "3062c02c82f7bbe238972672def6d8394732bb2b", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.6.tgz", "fileCount": 9, "integrity": "sha512-yANzIiNZ8fhMm4NORm+a74+KFYHmf7BZphSOBovIzYPVLquseTGEkU5l2UTnBOf5k0VLmTgPighNDLE9QtbViQ==", "signatures": [{"sig": "MEUCIQDnAV07I830fI0TRofoowTMevgpmrHYvjYW6HgMreoPSQIgbFuuCVVvE2c+T+L9stT3Z10FZ1y2paQtJyjxRjCyjd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23097}, "engines": {"node": ">=18"}}, "4.0.7": {"name": "@inquirer/select", "version": "4.0.7", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.5", "@inquirer/type": "^3.0.3", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.42", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "cea50dc7a00e749386d23ac42487dd62f7379d84", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.7.tgz", "fileCount": 9, "integrity": "sha512-ejGBMDSD+Iqk60u5t0Zf2UQhGlJWDM78Ep70XpNufIfc+f4VOTeybYKXu9pDjz87FkRzLiVsGpQG2SzuGlhaJw==", "signatures": [{"sig": "MEUCIQCKlePG1mt9WRCo0cslvAUZAHx3mlnWgSndENCoSLDhHAIgOuUoowdlYuSKClfxcmp09im5aNPQpfk6U4n6XAtsm0g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23102}, "engines": {"node": ">=18"}}, "4.0.8": {"name": "@inquirer/select", "version": "4.0.8", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.6", "@inquirer/type": "^3.0.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.43", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "dde85e10bc4e650c51542de533a91b6bc63498b7", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.8.tgz", "fileCount": 9, "integrity": "sha512-Io2prxFyN2jOCcu4qJbVoilo19caiD3kqkD3WR0q3yDA5HUCo83v4LrRtg55ZwniYACW64z36eV7gyVbOfORjA==", "signatures": [{"sig": "MEUCIQCigagdAMvF/YZe/Nst0ZLpEbYKHbG7VemwNmpfFzRJQQIgW5/QLhVYx0dhs8EgLtvTCy8d9OrYDkLcciEs/UMfbC8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23185}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.0.9": {"name": "@inquirer/select", "version": "4.0.9", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.7", "@inquirer/type": "^3.0.4", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.10"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.44", "@arethetypeswrong/cli": "^0.17.3"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "28a4c7b9a406798a9ea365d67dbad5e427c3febe", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.9.tgz", "fileCount": 9, "integrity": "sha512-BpJyJe7Dkhv2kz7yG7bPSbJLQuu/rqyNlF1CfiiFeFwouegfH+zh13KDyt6+d9DwucKo7hqM3wKLLyJxZMO+Xg==", "signatures": [{"sig": "MEQCIH21mhHbwyuPV2iQEuD1m/jiMr9fG+UAZdY5LGA8DGTUAiBcXmC01yZ/AsRZYmacDA7Z2IZI7ZkgLjCNeK3nn5UNYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22573}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.0.10": {"name": "@inquirer/select", "version": "4.0.10", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.8", "@inquirer/type": "^3.0.5", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "f14b9c18804ae2aef80c00195fbe811b5fd85364", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.0.10.tgz", "fileCount": 9, "integrity": "sha512-Tg8S9nESnCfISu5tCZSuXpXq0wHuDVimj7xyHstABgR34zcJnLdq/VbjB2mdZvNAMAehYBnNzSjxB06UE8LLAA==", "signatures": [{"sig": "MEUCIQDHXA6sk8dipsgfZccbX+GiEjSJcswIpzkUDwEjBPRnlQIgcb+EtiVzpgYNHN4aSFGRKxz0bu8+HvYMYANyjuN4LI0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22574}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.1.0": {"name": "@inquirer/select", "version": "4.1.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.9", "@inquirer/type": "^3.0.5", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.45", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "e99f483cb004c0247ced597f2c6015f084223dfb", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.1.0.tgz", "fileCount": 9, "integrity": "sha512-z0a2fmgTSRN+YBuiK1ROfJ2Nvrpij5lVN3gPDkQGhavdvIVGHGW29LwYZfM/j42Ai2hUghTI/uoBuTbrJk42bA==", "signatures": [{"sig": "MEUCIQDtZsdz6P9G+Eba/wgwTN7zJFPG5ePMBctg/xHbtCXpJQIgNoS7Z1XQONs3A8sAfTkURALEBjzbRbhvvEbvw3cfxFk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 23389}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.1.1": {"name": "@inquirer/select", "version": "4.1.1", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.10", "@inquirer/type": "^3.0.6", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "0496b913514149171cf6351f0acb6d4243a39fdf", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.1.1.tgz", "fileCount": 9, "integrity": "sha512-IUXzzTKVdiVNMA+2yUvPxWsSgOG4kfX93jOM4Zb5FgujeInotv5SPIJVeXQ+fO4xu7tW8VowFhdG5JRmmCyQ1Q==", "signatures": [{"sig": "MEYCIQCSKRWNyBIo7IhHHbx+Zng8+8yCY7Z7YWC4ESU/IaYE4wIhAKzMujbZUqIaTmjK7mYIhuojCZyfXdG0e2ZQOM3bCKoh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24056}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.0": {"name": "@inquirer/select", "version": "4.2.0", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.10", "@inquirer/type": "^3.0.6", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "42c66977c8992bd025f5d2f4124bee390cb16a98", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.2.0.tgz", "fileCount": 9, "integrity": "sha512-KkXQ4aSySWimpV4V/TUJWdB3tdfENZUU765GjOIZ0uPwdbGIG6jrxD4dDf1w68uP+DVtfNhr1A92B+0mbTZ8FA==", "signatures": [{"sig": "MEYCIQDAsu0G99nQwD1IIb4YJu9KQpypYGqRfYwBw6RF+yAhVAIhAOwgwULZaVLYM/1AJwMfLQyzIci6vX43VFcTk/AssWCw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24769}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.1": {"name": "@inquirer/select", "version": "4.2.1", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.11", "@inquirer/type": "^3.0.6", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.11"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.46", "@arethetypeswrong/cli": "^0.17.4"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "1be785ef4cd7dccd67fa4b77ff9dc8460cbc554b", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.2.1.tgz", "fileCount": 9, "integrity": "sha512-gt1Kd5XZm+/ddemcT3m23IP8aD8rC9drRckWoP/1f7OL46Yy2FGi8DSmNjEjQKtPl6SV96Kmjbl6p713KXJ/Jg==", "signatures": [{"sig": "MEQCIBs5xxf4eOIACVOOCV7mPCmsm3eqjrkdWzRBtUv+dYNoAiBpOJy9o11thuxXdzvQxAjMuuBOW2edS7omzvOOAuLMZA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24732}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.2": {"name": "@inquirer/select", "version": "4.2.2", "dependencies": {"ansi-escapes": "^4.3.2", "@inquirer/core": "^10.1.12", "@inquirer/type": "^3.0.7", "yoctocolors-cjs": "^2.1.2", "@inquirer/figures": "^1.0.12"}, "devDependencies": {"tshy": "^3.0.2", "@repo/tsconfig": "workspace:*", "@inquirer/testing": "^2.1.47", "@arethetypeswrong/cli": "^0.18.1"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"shasum": "9766ed3c9017c278bae942b77a225cefcb7f7660", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.2.2.tgz", "fileCount": 9, "integrity": "sha512-3X8AAPE1WPUwY3IawT19BapD0kKpAUP7SVUu5mxmRjnl/f4q0MQz8CU8ToCC6Im0SzyOTWmSauE3GBgyOv1rBw==", "signatures": [{"sig": "MEQCIEh0nwDwo5yJwfY9WJRk+QnN1dr2dVYhZmg3pSs6yNYVAiBjrVRFtgLo1guMidWmAi+Mg1PYCfAC/6IlmgBMAie2lw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 24732}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "4.2.3": {"name": "@inquirer/select", "version": "4.2.3", "dependencies": {"@inquirer/core": "^10.1.13", "@inquirer/figures": "^1.0.12", "@inquirer/type": "^3.0.7", "ansi-escapes": "^4.3.2", "yoctocolors-cjs": "^2.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@inquirer/testing": "^2.1.47", "@repo/tsconfig": "workspace:*", "tshy": "^3.0.2"}, "peerDependencies": {"@types/node": ">=18"}, "dist": {"integrity": "sha512-OAGhXU0Cvh0PhLz9xTF/kx6g6x+sP+PcyTiLvCrewI99P3BBeexD+VbuwkNDvqGkk3y2h5ZiWLeRP7BFlhkUDg==", "shasum": "3e31b56aff7bce9b46a0e2c8428118a25fe51c32", "tarball": "https://registry.npmjs.org/@inquirer/select/-/select-4.2.3.tgz", "fileCount": 9, "unpackedSize": 24732, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBMaeFfu7e0eP9gKkw1XZLa9lHHKg4Js/mUOezioW5FKAiB4ZhNM6kKpm4D8nsGpWnKbtD0s/25MW3+pY5pulLIgdA=="}]}, "engines": {"node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}}, "modified": "2025-05-25T20:55:52.073Z", "cachedAt": 1748373705422}