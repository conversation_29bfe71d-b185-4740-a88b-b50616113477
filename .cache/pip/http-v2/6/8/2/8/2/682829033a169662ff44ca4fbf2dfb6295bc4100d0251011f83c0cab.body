Metadata-Version: 2.1
Name: python-docx
Version: 1.1.2
Summary: Create, read, and update Microsoft Word .docx files.
Author-email: <PERSON> <<EMAIL>>
License: MIT
Project-URL: Changelog, https://github.com/python-openxml/python-docx/blob/master/HISTORY.rst
Project-URL: Documentation, https://python-docx.readthedocs.org/en/latest/
Project-URL: Homepage, https://github.com/python-openxml/python-docx
Project-URL: Repository, https://github.com/python-openxml/python-docx
Keywords: docx,office,openxml,word
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Office/Business :: Office Suites
Classifier: Topic :: Software Development :: Libraries
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: lxml >=3.1.0
Requires-Dist: typing-extensions >=4.9.0

# python-docx

*python-docx* is a Python library for reading, creating, and updating Microsoft Word 2007+ (.docx) files.

## Installation

```
pip install python-docx
```

## Example

```python
>>> from docx import Document

>>> document = Document()
>>> document.add_paragraph("It was a dark and stormy night.")
<docx.text.paragraph.Paragraph object at 0x10f19e760>
>>> document.save("dark-and-stormy.docx")

>>> document = Document("dark-and-stormy.docx")
>>> document.paragraphs[0].text
'It was a dark and stormy night.'
```

More information is available in the [python-docx documentation](https://python-docx.readthedocs.org/en/latest/)
