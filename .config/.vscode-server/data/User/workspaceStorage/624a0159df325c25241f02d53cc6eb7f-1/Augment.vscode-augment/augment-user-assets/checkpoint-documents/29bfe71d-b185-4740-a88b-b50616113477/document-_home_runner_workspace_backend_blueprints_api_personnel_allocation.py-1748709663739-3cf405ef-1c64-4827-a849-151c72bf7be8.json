{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}, "originalCode": "\"\"\"\nAPI per l'analisi dell'allocazione delle risorse del personale.\nFornisce viste aggregate temporali con confronto pianificato vs effettivo.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import func, and_, or_\nfrom datetime import datetime, timedelta, date\nimport calendar\nfrom models import User, Project, ProjectResource, Task, Timesheet\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS\nfrom extensions import db\n\n# Crea il blueprint per l'analisi allocazione personale\napi_personnel_allocation = Blueprint('api_personnel_allocation', __name__, url_prefix='/personnel')\n\n@api_personnel_allocation.route('/allocation-analysis', methods=['GET'])\n@login_required\ndef get_allocation_analysis():\n    \"\"\"\n    Analizza l'allocazione delle risorse con confronto temporale.\n    ---\n    tags:\n      - personnel-allocation\n    parameters:\n      - name: period\n        in: query\n        description: Periodo di analisi\n        schema:\n          type: string\n          enum: [current-month, current-quarter, current-year, next-quarter]\n          default: current-month\n    responses:\n      200:\n        description: Analisi dell'allocazione\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    summary:\n                      type: array\n                      description: Riepilogo per persona\n                    detailed:\n                      type: array\n                      description: Vista dettagliata per allocazione\n                    period_info:\n                      type: object\n                      description: Informazioni sul periodo analizzato\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            return api_response(\n                message=\"Non hai i permessi per visualizzare l'analisi dell'allocazione\",\n                status_code=403\n            )\n\n        # Ottieni parametri\n        period = request.args.get('period', 'current-month')\n\n        # Calcola date del periodo\n        period_info = calculate_period_dates(period)\n        start_date = period_info['start_date']\n        end_date = period_info['end_date']\n\n        # Ottieni tutti gli utenti attivi\n        users = User.query.filter(User.is_active == True).all()\n\n        summary_data = []\n        detailed_data = []\n\n        for user in users:\n            # Calcola capacità lavorativa per il periodo\n            capacity_hours = calculate_user_capacity(user, start_date, end_date)\n\n            # Ottieni allocazioni per il periodo\n            allocations = get_user_allocations(user.id, start_date, end_date)\n\n            # Calcola ore effettive dai timesheet\n            actual_hours = calculate_actual_hours(user.id, start_date, end_date)\n\n            # Calcola ore allocate totali\n            total_allocated_hours = sum(alloc['allocated_hours'] for alloc in allocations)\n\n            # Calcola utilizzo\n            utilization_percentage = (actual_hours / capacity_hours * 100) if capacity_hours > 0 else 0\n\n            # Dati summary per persona\n            user_summary = {\n                'user_id': user.id,\n                'user_name': f\"{user.first_name} {user.last_name}\",\n                'role': user.role,\n                'capacity_hours': capacity_hours,\n                'allocated_hours': total_allocated_hours,\n                'actual_hours': actual_hours,\n                'utilization_percentage': utilization_percentage,\n                'projects': allocations\n            }\n            summary_data.append(user_summary)\n\n            # Dati detailed per ogni allocazione\n            for allocation in allocations:\n                project_actual_hours = calculate_project_actual_hours(\n                    user.id, allocation['project_id'], start_date, end_date\n                )\n\n                variance = project_actual_hours - allocation['allocated_hours']\n\n                detailed_allocation = {\n                    'user_id': user.id,\n                    'user_name': f\"{user.first_name} {user.last_name}\",\n                    'project_id': allocation['project_id'],\n                    'project_name': allocation['project_name'],\n                    'project_period': f\"{allocation['project_start']} - {allocation['project_end']}\",\n                    'role': allocation['role'],\n                    'allocation_percentage': allocation['allocation_percentage'],\n                    'planned_hours': allocation['allocated_hours'],\n                    'actual_hours': project_actual_hours,\n                    'variance': variance\n                }\n                detailed_data.append(detailed_allocation)\n\n        return api_response(data={\n            'summary': summary_data,\n            'detailed': detailed_data,\n            'period_info': period_info\n        })\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_allocation_analysis: {str(e)}\")\n        return handle_api_error(e)\n\ndef calculate_period_dates(period):\n    \"\"\"Calcola le date di inizio e fine per il periodo specificato.\"\"\"\n    today = date.today()\n\n    if period == 'current-month':\n        start_date = today.replace(day=1)\n        # Ultimo giorno del mese\n        _, last_day = calendar.monthrange(today.year, today.month)\n        end_date = today.replace(day=last_day)\n        description = f\"Mese corrente ({start_date.strftime('%B %Y')})\"\n\n    elif period == 'current-quarter':\n        quarter = (today.month - 1) // 3 + 1\n        start_date = date(today.year, (quarter - 1) * 3 + 1, 1)\n        # Ultimo mese del trimestre\n        end_month = quarter * 3\n        _, last_day = calendar.monthrange(today.year, end_month)\n        end_date = date(today.year, end_month, last_day)\n        description = f\"Q{quarter} {today.year}\"\n\n    elif period == 'current-year':\n        start_date = date(today.year, 1, 1)\n        end_date = date(today.year, 12, 31)\n        description = f\"Anno {today.year}\"\n\n    elif period == 'next-quarter':\n        quarter = (today.month - 1) // 3 + 2\n        if quarter > 4:\n            quarter = 1\n            year = today.year + 1\n        else:\n            year = today.year\n        start_date = date(year, (quarter - 1) * 3 + 1, 1)\n        # Ultimo mese del trimestre\n        end_month = quarter * 3\n        _, last_day = calendar.monthrange(year, end_month)\n        end_date = date(year, end_month, last_day)\n        description = f\"Q{quarter} {year}\"\n\n    else:\n        # Default a mese corrente\n        start_date = today.replace(day=1)\n        _, last_day = calendar.monthrange(today.year, today.month)\n        end_date = today.replace(day=last_day)\n        description = f\"Mese corrente ({start_date.strftime('%B %Y')})\"\n\n    return {\n        'start_date': start_date,\n        'end_date': end_date,\n        'description': description,\n        'period': period\n    }\n\ndef calculate_user_capacity(user, start_date, end_date):\n    \"\"\"Calcola la capacità lavorativa di un utente per il periodo.\"\"\"\n    # Calcola giorni lavorativi (assumendo 5 giorni/settimana, 8 ore/giorno)\n    total_days = (end_date - start_date).days + 1\n\n    # Stima approssimativa: 5/7 dei giorni sono lavorativi\n    working_days = int(total_days * 5 / 7)\n\n    # 8 ore per giorno lavorativo (questo potrebbe essere configurabile per utente)\n    hours_per_day = 8\n\n    return working_days * hours_per_day\n\ndef get_user_allocations(user_id, start_date, end_date):\n    \"\"\"Ottieni le allocazioni di un utente per il periodo.\"\"\"\n    allocations = db.session.query(\n        ProjectResource.project_id,\n        ProjectResource.allocation_percentage,\n        ProjectResource.role,\n        Project.name.label('project_name'),\n        Project.start_date.label('project_start'),\n        Project.end_date.label('project_end')\n    ).join(\n        Project, ProjectResource.project_id == Project.id\n    ).filter(\n        ProjectResource.user_id == user_id,\n        or_(\n            Project.end_date.is_(None),  # Progetti senza data fine\n            Project.end_date >= start_date  # Progetti che finiscono dopo l'inizio del periodo\n        ),\n        or_(\n            Project.start_date.is_(None),  # Progetti senza data inizio\n            Project.start_date <= end_date  # Progetti che iniziano prima della fine del periodo\n        )\n    ).all()\n\n    result = []\n    for alloc in allocations:\n        # Calcola ore allocate per il periodo\n        project_start = alloc.project_start or start_date\n        project_end = alloc.project_end or end_date\n\n        # Intersezione del periodo progetto con il periodo di analisi\n        period_start = max(start_date, project_start)\n        period_end = min(end_date, project_end)\n\n        if period_start <= period_end:\n            # Calcola giorni lavorativi nel periodo\n            period_days = (period_end - period_start).days + 1\n            working_days = int(period_days * 5 / 7)\n\n            # Calcola ore allocate\n            allocated_hours = working_days * 8 * (alloc.allocation_percentage / 100)\n\n            result.append({\n                'project_id': alloc.project_id,\n                'project_name': alloc.project_name,\n                'project_start': project_start.strftime('%Y-%m-%d') if project_start else None,\n                'project_end': project_end.strftime('%Y-%m-%d') if project_end else None,\n                'allocation_percentage': alloc.allocation_percentage,\n                'role': alloc.role,\n                'allocated_hours': round(allocated_hours, 1)\n            })\n\n    return result\n\ndef calculate_actual_hours(user_id, start_date, end_date):\n    \"\"\"Calcola le ore effettive lavorate da un utente nel periodo.\"\"\"\n    total_hours = db.session.query(\n        func.sum(Timesheet.hours)\n    ).filter(\n        Timesheet.user_id == user_id,\n        Timesheet.date >= start_date,\n        Timesheet.date <= end_date,\n        Timesheet.status == 'approved'  # Solo timesheet approvati\n    ).scalar()\n\n    return float(total_hours or 0)\n\ndef calculate_project_actual_hours(user_id, project_id, start_date, end_date):\n    \"\"\"Calcola le ore effettive lavorate da un utente su un progetto specifico.\"\"\"\n    total_hours = db.session.query(\n        func.sum(Timesheet.hours)\n    ).filter(\n        Timesheet.user_id == user_id,\n        Timesheet.project_id == project_id,\n        Timesheet.date >= start_date,\n        Timesheet.date <= end_date,\n        Timesheet.status == 'approved'\n    ).scalar()\n\n    return float(total_hours or 0)\n", "modifiedCode": "\"\"\"\nAPI per l'analisi dell'allocazione delle risorse del personale.\nFornisce viste aggregate temporali con confronto pianificato vs effettivo.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import func, and_, or_\nfrom datetime import datetime, timedelta, date\nimport calendar\nfrom models import User, Project, ProjectResource, Task, Timesheet\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS\nfrom extensions import db\n\n# Crea il blueprint per l'analisi allocazione personale\napi_personnel_allocation = Blueprint('api_personnel_allocation', __name__, url_prefix='/personnel')\n\n@api_personnel_allocation.route('/test', methods=['GET'])\ndef test_endpoint():\n    \"\"\"Test endpoint per verificare che il blueprint funzioni\"\"\"\n    return api_response(data={'message': 'Blueprint personnel allocation funziona!'})\n\n@api_personnel_allocation.route('/allocation-analysis', methods=['GET'])\n@login_required\ndef get_allocation_analysis():\n    \"\"\"\n    Analizza l'allocazione delle risorse con confronto temporale.\n    ---\n    tags:\n      - personnel-allocation\n    parameters:\n      - name: period\n        in: query\n        description: Periodo di analisi\n        schema:\n          type: string\n          enum: [current-month, current-quarter, current-year, next-quarter]\n          default: current-month\n    responses:\n      200:\n        description: Analisi dell'allocazione\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    summary:\n                      type: array\n                      description: Riepilogo per persona\n                    detailed:\n                      type: array\n                      description: Vista dettagliata per allocazione\n                    period_info:\n                      type: object\n                      description: Informazioni sul periodo analizzato\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            return api_response(\n                message=\"Non hai i permessi per visualizzare l'analisi dell'allocazione\",\n                status_code=403\n            )\n\n        # Ottieni parametri\n        period = request.args.get('period', 'current-month')\n\n        # Calcola date del periodo\n        period_info = calculate_period_dates(period)\n        start_date = period_info['start_date']\n        end_date = period_info['end_date']\n\n        # Ottieni tutti gli utenti attivi\n        users = User.query.filter(User.is_active == True).all()\n\n        summary_data = []\n        detailed_data = []\n\n        for user in users:\n            # Calcola capacità lavorativa per il periodo\n            capacity_hours = calculate_user_capacity(user, start_date, end_date)\n\n            # Ottieni allocazioni per il periodo\n            allocations = get_user_allocations(user.id, start_date, end_date)\n\n            # Calcola ore effettive dai timesheet\n            actual_hours = calculate_actual_hours(user.id, start_date, end_date)\n\n            # Calcola ore allocate totali\n            total_allocated_hours = sum(alloc['allocated_hours'] for alloc in allocations)\n\n            # Calcola utilizzo\n            utilization_percentage = (actual_hours / capacity_hours * 100) if capacity_hours > 0 else 0\n\n            # Dati summary per persona\n            user_summary = {\n                'user_id': user.id,\n                'user_name': f\"{user.first_name} {user.last_name}\",\n                'role': user.role,\n                'capacity_hours': capacity_hours,\n                'allocated_hours': total_allocated_hours,\n                'actual_hours': actual_hours,\n                'utilization_percentage': utilization_percentage,\n                'projects': allocations\n            }\n            summary_data.append(user_summary)\n\n            # Dati detailed per ogni allocazione\n            for allocation in allocations:\n                project_actual_hours = calculate_project_actual_hours(\n                    user.id, allocation['project_id'], start_date, end_date\n                )\n\n                variance = project_actual_hours - allocation['allocated_hours']\n\n                detailed_allocation = {\n                    'user_id': user.id,\n                    'user_name': f\"{user.first_name} {user.last_name}\",\n                    'project_id': allocation['project_id'],\n                    'project_name': allocation['project_name'],\n                    'project_period': f\"{allocation['project_start']} - {allocation['project_end']}\",\n                    'role': allocation['role'],\n                    'allocation_percentage': allocation['allocation_percentage'],\n                    'planned_hours': allocation['allocated_hours'],\n                    'actual_hours': project_actual_hours,\n                    'variance': variance\n                }\n                detailed_data.append(detailed_allocation)\n\n        return api_response(data={\n            'summary': summary_data,\n            'detailed': detailed_data,\n            'period_info': period_info\n        })\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_allocation_analysis: {str(e)}\")\n        return handle_api_error(e)\n\ndef calculate_period_dates(period):\n    \"\"\"Calcola le date di inizio e fine per il periodo specificato.\"\"\"\n    today = date.today()\n\n    if period == 'current-month':\n        start_date = today.replace(day=1)\n        # Ultimo giorno del mese\n        _, last_day = calendar.monthrange(today.year, today.month)\n        end_date = today.replace(day=last_day)\n        description = f\"Mese corrente ({start_date.strftime('%B %Y')})\"\n\n    elif period == 'current-quarter':\n        quarter = (today.month - 1) // 3 + 1\n        start_date = date(today.year, (quarter - 1) * 3 + 1, 1)\n        # Ultimo mese del trimestre\n        end_month = quarter * 3\n        _, last_day = calendar.monthrange(today.year, end_month)\n        end_date = date(today.year, end_month, last_day)\n        description = f\"Q{quarter} {today.year}\"\n\n    elif period == 'current-year':\n        start_date = date(today.year, 1, 1)\n        end_date = date(today.year, 12, 31)\n        description = f\"Anno {today.year}\"\n\n    elif period == 'next-quarter':\n        quarter = (today.month - 1) // 3 + 2\n        if quarter > 4:\n            quarter = 1\n            year = today.year + 1\n        else:\n            year = today.year\n        start_date = date(year, (quarter - 1) * 3 + 1, 1)\n        # Ultimo mese del trimestre\n        end_month = quarter * 3\n        _, last_day = calendar.monthrange(year, end_month)\n        end_date = date(year, end_month, last_day)\n        description = f\"Q{quarter} {year}\"\n\n    else:\n        # Default a mese corrente\n        start_date = today.replace(day=1)\n        _, last_day = calendar.monthrange(today.year, today.month)\n        end_date = today.replace(day=last_day)\n        description = f\"Mese corrente ({start_date.strftime('%B %Y')})\"\n\n    return {\n        'start_date': start_date,\n        'end_date': end_date,\n        'description': description,\n        'period': period\n    }\n\ndef calculate_user_capacity(user, start_date, end_date):\n    \"\"\"Calcola la capacità lavorativa di un utente per il periodo.\"\"\"\n    # Calcola giorni lavorativi (assumendo 5 giorni/settimana, 8 ore/giorno)\n    total_days = (end_date - start_date).days + 1\n\n    # Stima approssimativa: 5/7 dei giorni sono lavorativi\n    working_days = int(total_days * 5 / 7)\n\n    # 8 ore per giorno lavorativo (questo potrebbe essere configurabile per utente)\n    hours_per_day = 8\n\n    return working_days * hours_per_day\n\ndef get_user_allocations(user_id, start_date, end_date):\n    \"\"\"Ottieni le allocazioni di un utente per il periodo.\"\"\"\n    allocations = db.session.query(\n        ProjectResource.project_id,\n        ProjectResource.allocation_percentage,\n        ProjectResource.role,\n        Project.name.label('project_name'),\n        Project.start_date.label('project_start'),\n        Project.end_date.label('project_end')\n    ).join(\n        Project, ProjectResource.project_id == Project.id\n    ).filter(\n        ProjectResource.user_id == user_id,\n        or_(\n            Project.end_date.is_(None),  # Progetti senza data fine\n            Project.end_date >= start_date  # Progetti che finiscono dopo l'inizio del periodo\n        ),\n        or_(\n            Project.start_date.is_(None),  # Progetti senza data inizio\n            Project.start_date <= end_date  # Progetti che iniziano prima della fine del periodo\n        )\n    ).all()\n\n    result = []\n    for alloc in allocations:\n        # Calcola ore allocate per il periodo\n        project_start = alloc.project_start or start_date\n        project_end = alloc.project_end or end_date\n\n        # Intersezione del periodo progetto con il periodo di analisi\n        period_start = max(start_date, project_start)\n        period_end = min(end_date, project_end)\n\n        if period_start <= period_end:\n            # Calcola giorni lavorativi nel periodo\n            period_days = (period_end - period_start).days + 1\n            working_days = int(period_days * 5 / 7)\n\n            # Calcola ore allocate\n            allocated_hours = working_days * 8 * (alloc.allocation_percentage / 100)\n\n            result.append({\n                'project_id': alloc.project_id,\n                'project_name': alloc.project_name,\n                'project_start': project_start.strftime('%Y-%m-%d') if project_start else None,\n                'project_end': project_end.strftime('%Y-%m-%d') if project_end else None,\n                'allocation_percentage': alloc.allocation_percentage,\n                'role': alloc.role,\n                'allocated_hours': round(allocated_hours, 1)\n            })\n\n    return result\n\ndef calculate_actual_hours(user_id, start_date, end_date):\n    \"\"\"Calcola le ore effettive lavorate da un utente nel periodo.\"\"\"\n    total_hours = db.session.query(\n        func.sum(Timesheet.hours)\n    ).filter(\n        Timesheet.user_id == user_id,\n        Timesheet.date >= start_date,\n        Timesheet.date <= end_date,\n        Timesheet.status == 'approved'  # Solo timesheet approvati\n    ).scalar()\n\n    return float(total_hours or 0)\n\ndef calculate_project_actual_hours(user_id, project_id, start_date, end_date):\n    \"\"\"Calcola le ore effettive lavorate da un utente su un progetto specifico.\"\"\"\n    total_hours = db.session.query(\n        func.sum(Timesheet.hours)\n    ).filter(\n        Timesheet.user_id == user_id,\n        Timesheet.project_id == project_id,\n        Timesheet.date >= start_date,\n        Timesheet.date <= end_date,\n        Timesheet.status == 'approved'\n    ).scalar()\n\n    return float(total_hours or 0)\n"}