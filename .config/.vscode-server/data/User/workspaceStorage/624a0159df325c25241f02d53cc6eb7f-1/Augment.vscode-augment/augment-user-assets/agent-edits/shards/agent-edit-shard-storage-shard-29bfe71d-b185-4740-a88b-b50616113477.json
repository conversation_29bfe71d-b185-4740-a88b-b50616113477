{"id": "shard-29bfe71d-b185-4740-a88b-b50616113477", "checkpoints": {"29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py": [{"sourceToolCallRequestId": "7d01d519-0fe9-4e57-9de3-f30eb1d2542b", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "3cf405ef-1c64-4827-a849-151c72bf7be8", "timestamp": 1748709663739, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "c689e295-2368-46c1-a40e-d1c8488e7ef9", "timestamp": 1748709838137, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py": [{"sourceToolCallRequestId": "049745ef-aaab-46c1-aea9-a942fb6cb4df", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}, {"sourceToolCallRequestId": "e52e5c67-7d3e-470b-92e4-54adbc5b5064", "timestamp": 1748709820332, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAllocation.vue": [{"sourceToolCallRequestId": "920343c3-49f5-4f0a-a4d5-c3d29079317b", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "e71b422b-7a08-4c43-ae12-67ae28f4da26", "timestamp": 1748709852697, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}]}, "metadata": {"checkpointDocumentIds": ["29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAllocation.vue"], "size": 177513, "checkpointCount": 7, "lastModified": 1748709854215}}