{"id": "shard-29bfe71d-b185-4740-a88b-b50616113477", "checkpoints": {"29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py": [{"sourceToolCallRequestId": "7d01d519-0fe9-4e57-9de3-f30eb1d2542b", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "3cf405ef-1c64-4827-a849-151c72bf7be8", "timestamp": 1748709663739, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "c689e295-2368-46c1-a40e-d1c8488e7ef9", "timestamp": 1748709838137, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "6d26d317-ccfa-4b3b-883e-cc63d91b8e7b", "timestamp": 1748710494526, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "bd8df52f-d172-4d97-91d5-1a4f4f44ff4e", "timestamp": 1748710508029, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "85140a03-fc7c-429e-99f7-499870564993", "timestamp": 1748710526968, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "62d169b7-b909-4a4f-bd1c-d164a31fe04e", "timestamp": 1748710610459, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}, {"sourceToolCallRequestId": "612b7695-812a-4bb1-a2d7-7f7a27511bb2", "timestamp": 1748710937456, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel_allocation.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py": [{"sourceToolCallRequestId": "049745ef-aaab-46c1-aea9-a942fb6cb4df", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}, {"sourceToolCallRequestId": "e52e5c67-7d3e-470b-92e4-54adbc5b5064", "timestamp": 1748709820332, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAllocation.vue": [{"sourceToolCallRequestId": "920343c3-49f5-4f0a-a4d5-c3d29079317b", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "e71b422b-7a08-4c43-ae12-67ae28f4da26", "timestamp": 1748709852697, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "e23554d0-36ba-4c22-ae79-ddf2da0bb61f", "timestamp": 1748710086839, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "641d926a-bc96-4ca7-97f7-2b43bb6ffe4c", "timestamp": 1748710548987, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "3cbc26b6-b35f-404d-b819-97d36f0cfb71", "timestamp": 1748710563121, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "a3cdccae-d546-4941-abe1-a54e19554d57", "timestamp": 1748710580909, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "6c3b0e0c-ad42-4561-9083-aab3c143cf54", "timestamp": 1748710595194, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "ab825a98-f302-4a7d-86ed-294183a6db2a", "timestamp": 1748710793529, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}, {"sourceToolCallRequestId": "87da9ff4-1e6a-4fce-a8c1-dd04828c5558", "timestamp": 1748710924937, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}}}]}, "metadata": {"checkpointDocumentIds": ["29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/personnel_allocation.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAllocation.vue"], "size": 585754, "checkpointCount": 19, "lastModified": 1748710939009}}