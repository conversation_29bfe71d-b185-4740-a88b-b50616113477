{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAllocation.vue"}, "originalCode": "<template>\n  <div class=\"personnel-allocation\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div class=\"flex items-center\">\n        <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n        </svg>\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Allocazione Risorse</h1>\n          <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Analisi temporale e confronto pianificato vs effettivo</p>\n        </div>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"flex items-center space-x-4\">\n        <!-- Time Period Selector -->\n        <select v-model=\"selectedPeriod\" @change=\"loadAllocationData\"\n                class=\"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">\n          <option value=\"current-month\">Mese Corrente</option>\n          <option value=\"current-quarter\">Trimestre Corrente</option>\n          <option value=\"current-year\">Anno Corrente</option>\n          <option value=\"next-quarter\">Prossimo Trimestre</option>\n        </select>\n\n        <!-- View Mode -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'summary'\"\n                  :class=\"viewMode === 'summary' ? 'bg-white dark:bg-gray-600 shadow' : ''\"\n                  class=\"px-3 py-1 text-sm rounded-md transition-colors\">\n            Riepilogo\n          </button>\n          <button @click=\"viewMode = 'detailed'\"\n                  :class=\"viewMode === 'detailed' ? 'bg-white dark:bg-gray-600 shadow' : ''\"\n                  class=\"px-3 py-1 text-sm rounded-md transition-colors\">\n            Dettagliato\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\n      </div>\n    </div>\n\n    <!-- Summary Cards -->\n    <div v-if=\"!loading && !error\" class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Capacità Totale</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalCapacity }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Allocato</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalAllocated }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-yellow-600 dark:text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Effettivo</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalActual }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2\" :class=\"summary.utilizationClass\">\n            <svg class=\"w-6 h-6\" :class=\"summary.utilizationIconClass\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Utilizzo</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.utilizationPercentage }}%</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div v-if=\"!loading && !error\" class=\"space-y-6\">\n      <!-- Summary View -->\n      <div v-if=\"viewMode === 'summary'\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Riepilogo per Persona</h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"person in allocationData\" :key=\"person.user_id\"\n                 class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n              <div class=\"flex items-center justify-between mb-4\">\n                <div class=\"flex items-center\">\n                  <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                    <span class=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\n                      {{ person.user_name.split(' ').map(n => n[0]).join('') }}\n                    </span>\n                  </div>\n                  <div class=\"ml-3\">\n                    <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ person.user_name }}</h4>\n                    <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ person.role || 'Nessun ruolo' }}</p>\n                  </div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm text-gray-500 dark:text-gray-400\">Utilizzo</div>\n                  <div class=\"text-lg font-semibold\" :class=\"getUtilizationTextClass(person.utilization_percentage)\">\n                    {{ Math.round(person.utilization_percentage) }}%\n                  </div>\n                </div>\n              </div>\n\n              <!-- Progress Bars -->\n              <div class=\"space-y-3\">\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Capacità</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.capacity_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-gray-400 h-2 rounded-full\" style=\"width: 100%\"></div>\n                  </div>\n                </div>\n\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Allocato</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.allocated_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-blue-500 h-2 rounded-full\"\n                         :style=\"{ width: Math.min((person.allocated_hours / person.capacity_hours) * 100, 100) + '%' }\"></div>\n                  </div>\n                </div>\n\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Effettivo</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.actual_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-green-500 h-2 rounded-full\"\n                         :style=\"{ width: Math.min((person.actual_hours / person.capacity_hours) * 100, 100) + '%' }\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Projects List -->\n              <div v-if=\"person.projects && person.projects.length > 0\" class=\"mt-4\">\n                <h5 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Progetti Attivi</h5>\n                <div class=\"space-y-2\">\n                  <div v-for=\"project in person.projects\" :key=\"project.project_id\"\n                       class=\"flex justify-between items-center text-sm\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">{{ project.project_name }}</span>\n                    <div class=\"flex items-center space-x-2\">\n                      <span class=\"text-gray-900 dark:text-white\">{{ project.allocation_percentage }}%</span>\n                      <span class=\"text-gray-500 dark:text-gray-400\">({{ project.allocated_hours }}h)</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Detailed View -->\n      <div v-else class=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Vista Dettagliata</h3>\n        </div>\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Persona\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Progetto\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ruolo\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Allocazione\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ore Pianificate\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ore Effettive\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Varianza\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"allocation in detailedAllocations\" :key=\"`${allocation.user_id}-${allocation.project_id}`\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                      <span class=\"text-xs font-medium text-blue-600 dark:text-blue-400\">\n                        {{ allocation.user_name.split(' ').map(n => n[0]).join('') }}\n                      </span>\n                    </div>\n                    <div class=\"ml-3\">\n                      <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ allocation.user_name }}</div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">{{ allocation.project_name }}</div>\n                  <div class=\"text-sm text-gray-500 dark:text-gray-400\">{{ allocation.project_period }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                  {{ allocation.role }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ allocation.allocation_percentage }}%</div>\n                    <div class=\"ml-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div class=\"bg-blue-500 h-2 rounded-full\"\n                           :style=\"{ width: allocation.allocation_percentage + '%' }\"></div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ allocation.planned_hours }}h\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ allocation.actual_hours }}h\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span class=\"text-sm font-medium\" :class=\"getVarianceClass(allocation.variance)\">\n                    {{ allocation.variance > 0 ? '+' : '' }}{{ allocation.variance }}h\n                  </span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\n// Stores\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst error = ref(null)\nconst selectedPeriod = ref('current-month')\nconst viewMode = ref('summary')\nconst allocationData = ref([])\nconst detailedAllocations = ref([])\n\n// Computed\nconst summary = computed(() => {\n  if (!allocationData.value.length) {\n    return {\n      totalCapacity: 0,\n      totalAllocated: 0,\n      totalActual: 0,\n      utilizationPercentage: 0,\n      utilizationClass: 'bg-gray-100 dark:bg-gray-700',\n      utilizationIconClass: 'text-gray-600 dark:text-gray-400'\n    }\n  }\n\n  const totalCapacity = allocationData.value.reduce((sum, person) => sum + person.capacity_hours, 0)\n  const totalAllocated = allocationData.value.reduce((sum, person) => sum + person.allocated_hours, 0)\n  const totalActual = allocationData.value.reduce((sum, person) => sum + person.actual_hours, 0)\n  const utilizationPercentage = totalCapacity > 0 ? Math.round((totalActual / totalCapacity) * 100) : 0\n\n  let utilizationClass = 'bg-gray-100 dark:bg-gray-700'\n  let utilizationIconClass = 'text-gray-600 dark:text-gray-400'\n\n  if (utilizationPercentage > 100) {\n    utilizationClass = 'bg-red-100 dark:bg-red-900'\n    utilizationIconClass = 'text-red-600 dark:text-red-400'\n  } else if (utilizationPercentage >= 90) {\n    utilizationClass = 'bg-yellow-100 dark:bg-yellow-900'\n    utilizationIconClass = 'text-yellow-600 dark:text-yellow-400'\n  } else if (utilizationPercentage >= 70) {\n    utilizationClass = 'bg-green-100 dark:bg-green-900'\n    utilizationIconClass = 'text-green-600 dark:text-green-400'\n  }\n\n  return {\n    totalCapacity,\n    totalAllocated,\n    totalActual,\n    utilizationPercentage,\n    utilizationClass,\n    utilizationIconClass\n  }\n})\n\n// Methods\nconst loadAllocationData = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    console.log('Calling API:', `/api/personnel/allocation-analysis?period=${selectedPeriod.value}`)\n    const response = await fetch(`/api/personnel/allocation-analysis?period=${selectedPeriod.value}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento dei dati di allocazione')\n    }\n\n    const result = await response.json()\n    allocationData.value = result.data?.summary || []\n    detailedAllocations.value = result.data?.detailed || []\n  } catch (err) {\n    error.value = err.message\n    console.error('Error loading allocation data:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getUtilizationTextClass = (percentage) => {\n  if (percentage > 100) return 'text-red-600 dark:text-red-400'\n  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'\n  if (percentage >= 70) return 'text-green-600 dark:text-green-400'\n  return 'text-gray-600 dark:text-gray-400'\n}\n\nconst getVarianceClass = (variance) => {\n  if (variance > 0) return 'text-red-600 dark:text-red-400'\n  if (variance < 0) return 'text-green-600 dark:text-green-400'\n  return 'text-gray-600 dark:text-gray-400'\n}\n\n// Lifecycle\nonMounted(() => {\n  loadAllocationData()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"personnel-allocation\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div class=\"flex items-center\">\n        <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n        </svg>\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Allocazione Risorse</h1>\n          <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Analisi temporale e confronto pianificato vs effettivo</p>\n        </div>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"flex items-center space-x-4\">\n        <!-- Time Period Selector -->\n        <select v-model=\"selectedPeriod\" @change=\"loadAllocationData\"\n                class=\"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">\n          <option value=\"current-month\">Mese Corrente</option>\n          <option value=\"current-quarter\">Trimestre Corrente</option>\n          <option value=\"current-year\">Anno Corrente</option>\n          <option value=\"next-quarter\">Prossimo Trimestre</option>\n        </select>\n\n        <!-- View Mode -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'summary'\"\n                  :class=\"viewMode === 'summary' ? 'bg-white dark:bg-gray-600 shadow' : ''\"\n                  class=\"px-3 py-1 text-sm rounded-md transition-colors\">\n            Riepilogo\n          </button>\n          <button @click=\"viewMode = 'detailed'\"\n                  :class=\"viewMode === 'detailed' ? 'bg-white dark:bg-gray-600 shadow' : ''\"\n                  class=\"px-3 py-1 text-sm rounded-md transition-colors\">\n            Dettagliato\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <p class=\"text-red-800 dark:text-red-200\">{{ error }}</p>\n      </div>\n    </div>\n\n    <!-- Summary Cards -->\n    <div v-if=\"!loading && !error\" class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Capacità Totale</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalCapacity }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Allocato</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalAllocated }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\n            <svg class=\"w-6 h-6 text-yellow-600 dark:text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Effettivo</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.totalActual }}h</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"p-2\" :class=\"summary.utilizationClass\">\n            <svg class=\"w-6 h-6\" :class=\"summary.utilizationIconClass\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Utilizzo</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ summary.utilizationPercentage }}%</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Main Content -->\n    <div v-if=\"!loading && !error\" class=\"space-y-6\">\n      <!-- Summary View -->\n      <div v-if=\"viewMode === 'summary'\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Riepilogo per Persona</h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"person in allocationData\" :key=\"person.user_id\"\n                 class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n              <div class=\"flex items-center justify-between mb-4\">\n                <div class=\"flex items-center\">\n                  <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                    <span class=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\n                      {{ person.user_name.split(' ').map(n => n[0]).join('') }}\n                    </span>\n                  </div>\n                  <div class=\"ml-3\">\n                    <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ person.user_name }}</h4>\n                    <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ person.role || 'Nessun ruolo' }}</p>\n                  </div>\n                </div>\n                <div class=\"text-right\">\n                  <div class=\"text-sm text-gray-500 dark:text-gray-400\">Utilizzo</div>\n                  <div class=\"text-lg font-semibold\" :class=\"getUtilizationTextClass(person.utilization_percentage)\">\n                    {{ Math.round(person.utilization_percentage) }}%\n                  </div>\n                </div>\n              </div>\n\n              <!-- Progress Bars -->\n              <div class=\"space-y-3\">\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Capacità</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.capacity_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-gray-400 h-2 rounded-full\" style=\"width: 100%\"></div>\n                  </div>\n                </div>\n\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Allocato</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.allocated_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-blue-500 h-2 rounded-full\"\n                         :style=\"{ width: Math.min((person.allocated_hours / person.capacity_hours) * 100, 100) + '%' }\"></div>\n                  </div>\n                </div>\n\n                <div>\n                  <div class=\"flex justify-between text-sm mb-1\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">Effettivo</span>\n                    <span class=\"text-gray-900 dark:text-white\">{{ person.actual_hours }}h</span>\n                  </div>\n                  <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div class=\"bg-green-500 h-2 rounded-full\"\n                         :style=\"{ width: Math.min((person.actual_hours / person.capacity_hours) * 100, 100) + '%' }\"></div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Projects List -->\n              <div v-if=\"person.projects && person.projects.length > 0\" class=\"mt-4\">\n                <h5 class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Progetti Attivi</h5>\n                <div class=\"space-y-2\">\n                  <div v-for=\"project in person.projects\" :key=\"project.project_id\"\n                       class=\"flex justify-between items-center text-sm\">\n                    <span class=\"text-gray-600 dark:text-gray-400\">{{ project.project_name }}</span>\n                    <div class=\"flex items-center space-x-2\">\n                      <span class=\"text-gray-900 dark:text-white\">{{ project.allocation_percentage }}%</span>\n                      <span class=\"text-gray-500 dark:text-gray-400\">({{ project.allocated_hours }}h)</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Detailed View -->\n      <div v-else class=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Vista Dettagliata</h3>\n        </div>\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Persona\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Progetto\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ruolo\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Allocazione\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ore Pianificate\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Ore Effettive\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Varianza\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"allocation in detailedAllocations\" :key=\"`${allocation.user_id}-${allocation.project_id}`\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                      <span class=\"text-xs font-medium text-blue-600 dark:text-blue-400\">\n                        {{ allocation.user_name.split(' ').map(n => n[0]).join('') }}\n                      </span>\n                    </div>\n                    <div class=\"ml-3\">\n                      <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ allocation.user_name }}</div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">{{ allocation.project_name }}</div>\n                  <div class=\"text-sm text-gray-500 dark:text-gray-400\">{{ allocation.project_period }}</div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                  {{ allocation.role }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ allocation.allocation_percentage }}%</div>\n                    <div class=\"ml-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div class=\"bg-blue-500 h-2 rounded-full\"\n                           :style=\"{ width: allocation.allocation_percentage + '%' }\"></div>\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ allocation.planned_hours }}h\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ allocation.actual_hours }}h\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span class=\"text-sm font-medium\" :class=\"getVarianceClass(allocation.variance)\">\n                    {{ allocation.variance > 0 ? '+' : '' }}{{ allocation.variance }}h\n                  </span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\n// Stores\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst error = ref(null)\nconst selectedPeriod = ref('current-month')\nconst viewMode = ref('summary')\nconst allocationData = ref([])\nconst detailedAllocations = ref([])\n\n// Computed\nconst summary = computed(() => {\n  if (!allocationData.value.length) {\n    return {\n      totalCapacity: 0,\n      totalAllocated: 0,\n      totalActual: 0,\n      utilizationPercentage: 0,\n      utilizationClass: 'bg-gray-100 dark:bg-gray-700',\n      utilizationIconClass: 'text-gray-600 dark:text-gray-400'\n    }\n  }\n\n  const totalCapacity = allocationData.value.reduce((sum, person) => sum + person.capacity_hours, 0)\n  const totalAllocated = allocationData.value.reduce((sum, person) => sum + person.allocated_hours, 0)\n  const totalActual = allocationData.value.reduce((sum, person) => sum + person.actual_hours, 0)\n  const utilizationPercentage = totalCapacity > 0 ? Math.round((totalActual / totalCapacity) * 100) : 0\n\n  let utilizationClass = 'bg-gray-100 dark:bg-gray-700'\n  let utilizationIconClass = 'text-gray-600 dark:text-gray-400'\n\n  if (utilizationPercentage > 100) {\n    utilizationClass = 'bg-red-100 dark:bg-red-900'\n    utilizationIconClass = 'text-red-600 dark:text-red-400'\n  } else if (utilizationPercentage >= 90) {\n    utilizationClass = 'bg-yellow-100 dark:bg-yellow-900'\n    utilizationIconClass = 'text-yellow-600 dark:text-yellow-400'\n  } else if (utilizationPercentage >= 70) {\n    utilizationClass = 'bg-green-100 dark:bg-green-900'\n    utilizationIconClass = 'text-green-600 dark:text-green-400'\n  }\n\n  return {\n    totalCapacity,\n    totalAllocated,\n    totalActual,\n    utilizationPercentage,\n    utilizationClass,\n    utilizationIconClass\n  }\n})\n\n// Methods\nconst loadAllocationData = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    console.log('Calling API:', `/api/allocation/allocation-analysis?period=${selectedPeriod.value}`)\n    const response = await fetch(`/api/allocation/allocation-analysis?period=${selectedPeriod.value}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento dei dati di allocazione')\n    }\n\n    const result = await response.json()\n    allocationData.value = result.data?.summary || []\n    detailedAllocations.value = result.data?.detailed || []\n  } catch (err) {\n    error.value = err.message\n    console.error('Error loading allocation data:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getUtilizationTextClass = (percentage) => {\n  if (percentage > 100) return 'text-red-600 dark:text-red-400'\n  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'\n  if (percentage >= 70) return 'text-green-600 dark:text-green-400'\n  return 'text-gray-600 dark:text-gray-400'\n}\n\nconst getVarianceClass = (variance) => {\n  if (variance > 0) return 'text-red-600 dark:text-red-400'\n  if (variance < 0) return 'text-green-600 dark:text-green-400'\n  return 'text-gray-600 dark:text-gray-400'\n}\n\n// Lifecycle\nonMounted(() => {\n  loadAllocationData()\n})\n</script>\n"}