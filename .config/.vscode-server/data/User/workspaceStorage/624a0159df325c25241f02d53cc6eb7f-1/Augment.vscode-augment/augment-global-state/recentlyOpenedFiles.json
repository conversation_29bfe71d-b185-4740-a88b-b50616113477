[["/home/<USER>/workspace/.cursor/rules/cursor_rules.mdc", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/cursor_rules.mdc"}}], ["/home/<USER>/workspace/config.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "config.py"}}], ["/home/<USER>/workspace/.cursor/mcp.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/mcp.json"}}], ["/home/<USER>/workspace/specs/chat_history_1.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/chat_history_1.md"}}], ["/home/<USER>/workspace/blueprints/api.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api.py"}}], ["/home/<USER>/workspace/blueprints/projects.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/projects.py"}}], ["/home/<USER>/workspace/static/swagger/swagger.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/swagger/swagger.json"}}], ["/home/<USER>/workspace/blueprints/api/projects.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/api/projects.py"}}], ["/home/<USER>/workspace/tests/conftest.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/conftest.py"}}], ["/home/<USER>/workspace/backend/ai_services.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/ai_services.py"}}]]