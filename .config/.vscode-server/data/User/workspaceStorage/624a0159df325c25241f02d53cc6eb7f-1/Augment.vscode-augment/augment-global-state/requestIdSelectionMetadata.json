[["8077383b-4fb7-48ae-be14-c7b905759d18", {"value": {"selectedCode": "", "prefix": "from flask import Blueprint, jsonify, request\n", "suffix": "from flask_login import login_required, current_user\nfrom models import Notification\nfrom app import db\n\napi_bp = Blueprint('api', __name__, url_prefix='/api')\n\n@api_bp.route('/notifications')\n@login_required\ndef get_notifications():\n    \"\"\"Endpoint per ottenere le notifiche dell'utente corrente.\"\"\"\n    # Ottieni le ultime 10 notifiche per l'utente\n    notifications = Notification.query.filter_by(\n        user_id=current_user.id, \n        is_read=False\n    ).order_by(Notification.created_at.desc()).limit(10).all()\n    \n    # Calcola il numero totale di notifiche non lette\n    unread_count = Notification.query.filter_by(\n        user_id=current_user.id, \n        is_read=False\n    ).count()\n    \n    # Formatta le notifiche per il frontend\n    formatted_notifications = []\n    for notification in notifications:\n        formatted_notifications.append({\n            'id': notification.id,\n            'text': notification.message,\n            'created_at': notification.created_at.isoformat(),\n            'link': notification.link or '#',\n            'type': notification.type,\n            'is_read': notification.is_read\n        })\n    \n    return jsonify({\n        'notifications': formatted_notifications,\n        'unread_count': unread_count\n    })\n\n@api_bp.route('/notifications/mark-read/<int:notification_id>', methods=['POST'])\n@login_required\ndef mark_notification_read(notification_id):\n    \"\"\"Segna una notifica come letta.\"\"\"\n    notification = Notification.query.get_or_404(notification_id)\n    \n    # Verifica che la notifica appartenga all'utente corrente\n    if notification.user_id != current_user.id:\n        return jsonify({'error': 'Non autorizzato'}), 403\n    \n    notification.is_read = True\n    db.session.commit()\n    \n    return jsonify({'success': True})\n\n@api_bp.route('/notifications/mark-all-read', methods=['POST'])\n@login_required\ndef mark_all_notifications_read():\n    \"\"\"Segna tutte le notifiche dell'utente come lette.\"\"\"\n    Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).update({'is_read': True})\n    \n    db.session.commit()\n    \n    return jsonify({'success': True}) ", "path": "blueprints/api.py", "language": "python", "prefixBegin": 0, "suffixEnd": 38}}], ["3cf405ef-1c64-4827-a849-151c72bf7be8", {"value": {"selectedCode": "# API Keys\nOPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")\nPERPLEXITY_API_KEY = os.environ.get(\"PERPLEXITY_API_KEY\")", "prefix": "import os\nimport json\nimport requests\nimport logging\nfrom openai import OpenAI\n\nlogger = logging.getLogger(__name__)\n\n", "suffix": "\n\n# Initialize OpenAI client\nopenai_client = OpenAI(api_key=OPENAI_API_KEY)\n\ndef analyze_text_with_openai(text, prompt=\"\", model=\"gpt-4o\"):\n    \"\"\"\n    Analyze text using OpenAI GPT models\n    \"\"\"\n    try:\n        # the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024.\n        # do not change this unless explicitly requested by the user\n        if not prompt:\n            prompt = \"Analyze the following text and provide insights:\"\n\n        complete_prompt = f\"{prompt}\\n\\n{text}\"\n\n        response = openai_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": complete_prompt}],\n            temperature=0.2,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error analyzing text: {str(e)}\"\n\ndef generate_summary_with_openai(text, max_length=200):\n    \"\"\"\n    Generate a concise summary of the provided text\n    \"\"\"\n    try:\n        prompt = f\"Summarize the following text in about {max_length} words or less:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error generating summary: {str(e)}\"\n\ndef extract_insights_with_openai(text, context=\"business\"):\n    \"\"\"\n    Extract key insights from text with a specific business context\n    \"\"\"\n    try:\n        prompt = f\"Extract key {context} insights and actionable points from the following text:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return {\"error\": str(e), \"insights\": []}\n\ndef analyze_with_perplexity(text, question=\"\", model=\"llama-3.1-sonar-small-128k-online\"):\n    \"\"\"\n    Analyze text using Perplexity API\n    \"\"\"\n    if not PERPLEXITY_API_KEY:\n        logger.error(\"Perplexity API key not found\")\n        return \"Error: Perplexity API key not configured\"\n\n    try:\n        if not question:\n            question = \"Analyze this text and provide insights:\"\n\n        headers = {\n            \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n            \"Content-Type\": \"application/json\"\n        }\n\n        data = {\n            \"model\": model,\n            \"messages\": [\n                {\n                    \"role\": \"system\",\n                    \"content\": \"You are an AI assistant for business analytics. Be precise and concise.\"\n                },\n                {\n                    \"role\": \"user\",\n                    \"content\": f\"{question}\\n\\n{text}\"\n                }\n            ],\n            \"temperature\": 0.2,\n            \"max_tokens\": 500,\n            \"stream\": False\n        }\n\n        response = requests.post(\n            \"https://api.perplexity.ai/chat/completions\",\n            headers=headers,\n            json=data\n        )\n\n        if response.status_code == 200:\n            response_data = response.json()\n            return {\n                \"content\": response_data[\"choices\"][0][\"message\"][\"content\"],\n                \"citations\": response_data.get(\"citations\", [])\n            }\n        else:\n            logger.error(f\"Perplexity API error: {response.status_code} - {response.text}\")\n            return {\"error\": f\"API Error: {response.status_code}\", \"content\": \"\"}\n    except Exception as e:\n        logger.error(f\"Perplexity API call error: {str(e)}\")\n        return {\"error\": str(e), \"content\": \"\"}\n\ndef analyze_project_requirements(requirements_text):\n    \"\"\"\n    Analyze project requirements and extract key components\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analyze these project requirements and extract the following information in JSON format:\n        1. Key deliverables\n        2. Potential risks\n        3. Resources needed\n        4. Estimated timeline\n        5. Success criteria\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{requirements_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Project requirements analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"key_deliverables\": [],\n            \"potential_risks\": [],\n            \"resources_needed\": [],\n            \"estimated_timeline\": \"Unknown\",\n            \"success_criteria\": []\n        }\n\ndef generate_funding_recommendations(company_profile):\n    \"\"\"\n    Generate funding recommendations based on company profile\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Based on this company profile, provide recommendations for:\n        1. Suitable funding opportunities\n        2. Grant programs to consider\n        3. Application strategy suggestions\n        4. Key points to highlight\n\n        Respond in JSON format.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{company_profile}\"}],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Funding recommendations error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"funding_opportunities\": [],\n            \"grant_programs\": [],\n            \"application_strategy\": [],\n            \"key_highlights\": []\n        }\n\ndef extract_skills_from_cv(cv_text):\n    \"\"\"\n    Estrae competenze da un CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza questo CV e estrai tutte le competenze tecniche e professionali.\n        Categorizza le competenze in:\n        - Linguaggi di programmazione\n        - Framework e librerie\n        - Database\n        - Cloud e DevOps\n        - Soft skills\n        - Certificazioni\n        - Strumenti e software\n        - Metodologie\n\n        Restituisci il risultato in formato JSON con questa struttura:\n        {\n            \"skills\": [\n                {\n                    \"name\": \"nome competenza\",\n                    \"category\": \"categoria\",\n                    \"level\": \"beginner|intermediate|advanced|expert\"\n                }\n            ],\n            \"summary\": \"breve riassunto del profilo professionale\",\n            \"experience_years\": numero_totale_anni_esperienza\n        }\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\nCV:\\n{cv_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI CV analysis error: {str(e)}\")\n        return {\"error\": str(e), \"skills\": [], \"summary\": \"\", \"experience_years\": 0}\n\ndef generate_cv_html(user_data, skills_data):\n    \"\"\"\n    Genera HTML per CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = f\"\"\"\n        Genera un CV professionale in HTML per questo profilo:\n\n        Dati utente: {json.dumps(user_data, indent=2)}\n        Competenze: {json.dumps(skills_data, indent=2)}\n\n        Crea un CV completo con:\n        - Intestazione con dati personali\n        - Profilo professionale (summary)\n        - Competenze tecniche organizzate per categoria\n        - Layout professionale con CSS inline\n        - Stile moderno e pulito\n\n        Restituisci solo l'HTML completo del CV.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI CV generation error: {str(e)}\")\n        return f\"<p>Errore nella generazione del CV: {str(e)}</p>\"\n\n# ============================================================================\n# RESOURCE ALLOCATION AI SERVICES\n# ============================================================================\n\ndef analyze_resource_allocation(project_data, available_resources, current_allocations=None):\n    \"\"\"\n    Analizza l'allocazione delle risorse per un progetto e fornisce suggerimenti AI\n    \"\"\"\n    try:\n        # Prepara i dati per l'analisi\n        context = {\n            \"project\": {\n                \"name\": project_data.get(\"name\", \"\"),\n                \"description\": project_data.get(\"description\", \"\"),\n                \"project_type\": project_data.get(\"project_type\", \"\"),\n                \"start_date\": project_data.get(\"start_date\", \"\"),\n                \"end_date\": project_data.get(\"end_date\", \"\"),\n                \"budget\": project_data.get(\"budget\", 0),\n                \"estimated_hours\": project_data.get(\"estimated_hours\", 0),\n                \"required_skills\": project_data.get(\"required_skills\", [])\n            },\n            \"available_resources\": available_resources,\n            \"current_allocations\": current_allocations or []\n        }\n\n        prompt = \"\"\"\n        Analizza questa situazione di allocazione risorse e fornisci suggerimenti intelligenti.\n\n        IMPORTANTE: Usa SOLO i nomi reali degli utenti forniti nei dati available_resources.\n        NON usare placeholder generici come \"employee\" o \"admin\".\n\n        Considera:\n        1. Competenze richieste vs competenze disponibili\n        2. Carico di lavoro attuale delle risorse\n        3. Disponibilità temporale\n        4. Costi e budget\n        5. Efficienza del team\n\n        Fornisci suggerimenti in formato JSON con:\n        - recommended_allocations: array di allocazioni suggerite con user_id, user_name (nome reale), role, allocation (percentuale numerica)\n        - optimization_insights: insights per ottimizzazione usando i nomi reali\n        - potential_conflicts: conflitti potenziali identificati\n        - efficiency_score: punteggio efficienza (0-100)\n        - cost_analysis: analisi costi\n\n        Esempio formato recommended_allocations:\n        [\n          {\n            \"user_id\": 1,\n            \"user_name\": \"Mario Rossi\",\n            \"role\": \"Senior Developer\",\n            \"allocation\": 80\n          }\n        ]\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un esperto AI in resource management e project planning. Analizza i dati e fornisci suggerimenti pratici e attuabili.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource allocation analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"recommended_allocations\": [],\n            \"optimization_insights\": [],\n            \"potential_conflicts\": [],\n            \"efficiency_score\": 0,\n            \"cost_analysis\": {}\n        }\n\ndef predict_resource_conflicts(allocations_data, timeline_data):\n    \"\"\"\n    Predice conflitti di risorse basandosi su dati storici e allocazioni attuali\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza queste allocazioni di risorse e predici potenziali conflitti.\n\n        Identifica:\n        1. Sovrallocazioni (>100% capacità)\n        2. Conflitti temporali\n        3. Competenze mancanti\n        4. Rischi di burnout\n        5. Dipendenze critiche\n\n        Fornisci risultato in JSON con:\n        - conflicts: array di conflitti identificati\n        - risk_level: livello rischio (low/medium/high)\n        - recommendations: raccomandazioni per risoluzione\n        - timeline_impact: impatto su timeline progetto\n        \"\"\"\n\n        context = {\n            \"allocations\": allocations_data,\n            \"timeline\": timeline_data\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI specialist in project risk management. Identifica proattivamente i rischi nelle allocazioni di risorse.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.1,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource conflict prediction error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"conflicts\": [],\n            \"risk_level\": \"unknown\",\n            \"recommendations\": [],\n            \"timeline_impact\": \"unknown\"\n        }\n\ndef optimize_team_composition(project_requirements, candidate_resources):\n    \"\"\"\n    Ottimizza la composizione del team per un progetto specifico\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Ottimizza la composizione del team per questo progetto.\n\n        Considera:\n        1. Skill match con requisiti progetto\n        2. Sinergie tra membri del team\n        3. Bilanciamento senior/junior\n        4. Copertura competenze critiche\n        5. Dinamiche di team\n\n        Fornisci in JSON:\n        - optimal_team: composizione team ottimale\n        - skill_coverage: copertura competenze (%)\n        - team_synergy_score: punteggio sinergia (0-100)\n        - alternative_options: opzioni alternative\n        - training_needs: necessità formazione\n        \"\"\"\n\n        context = {\n            \"project_requirements\": project_requirements,\n            \"candidate_resources\": candidate_resources\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI expert in team building e resource optimization. Crea team ad alta performance.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Team composition optimization error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"optimal_team\": [],\n            \"skill_coverage\": 0,\n            \"team_synergy_score\": 0,\n            \"alternative_options\": [],\n            \"training_needs\": []\n        }\n", "path": "backend/ai_services.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["5bbcc239-4efc-4fd1-8b02-ee82bcc80dbd", {"value": {"selectedCode": "", "prefix": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash\nfrom flask_login import logout_user, current_user\nfrom flask_cors import CORS\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'static',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'tenants_api.api_tenant_config',  # API per configurazione tenant\n    'api_auth.login', 'api_auth.logout', 'api_auth.debug',  # API di autenticazione Vue.js\n    'swagger_json.swagger_json',  # Swagger JSON\n    'swagger_ui.show',  # Swagger UI\n    'spa'  # SPA catch-all route - Vue.js gestisce tutto\n]\n\n# Fix MIME types for ES6 modules GLOBALLY\nimport mimetypes\nmimetypes.add_type('application/javascript', '.js')\nmimetypes.add_type('application/javascript', '.mjs')\nmimetypes.add_type('application/javascript', '.vue')\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    # login_manager.login_view = None  # Non serve più con Vue.js SPA\n    migrate.init_app(app, db)\n\n    # Configure CORS to allow credentials (cookies) for Vue.js SPA\n    CORS(app,\n         origins=['http://localhost:3000', 'http://localhost:5000', 'http://127.0.0.1:5000', 'http://127.0.0.1:3000'],\n", "suffix": "         supports_credentials=True,\n         allow_headers=['Content-Type', 'X-CSRFToken', 'Authorization'],\n         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints for Vue.js SPA\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints - SOLO API per Vue.js SPA\n        app.register_blueprint(public_api_bp)\n\n        # Auth API\n        from blueprints.api.auth import api_auth\n        app.register_blueprint(api_auth, url_prefix='/api/auth')\n\n        # Dashboard API\n        from blueprints.api.dashboard import api_dashboard\n        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')\n\n        # Projects API\n        from blueprints.api.projects import api_projects\n        app.register_blueprint(api_projects, url_prefix='/api/projects')\n\n        # Tasks API\n        from blueprints.api.tasks import api_tasks\n        app.register_blueprint(api_tasks, url_prefix='/api/tasks')\n\n        # Timesheets API\n        from blueprints.api.timesheets import api_timesheets\n        app.register_blueprint(api_timesheets, url_prefix='/api/timesheets')\n\n        # Personnel API\n        from blueprints.api.personnel import api_personnel\n        app.register_blueprint(api_personnel, url_prefix='/api/personnel')\n\n        # Personnel Allocation API (registrato prima per evitare conflitti)\n        from blueprints.api.personnel_allocation import api_personnel_allocation\n        app.register_blueprint(api_personnel_allocation, url_prefix='/api')\n\n        # Expenses API\n        from blueprints.api.expenses import api_expenses\n        app.register_blueprint(api_expenses)\n\n        # KPIs API\n        from blueprints.api.kpis import api_kpis\n        app.register_blueprint(api_kpis, url_prefix='/api/kpis')\n\n        # Project KPIs API\n        from blueprints.api.project_kpis import api_project_kpis\n        app.register_blueprint(api_project_kpis, url_prefix='/api/project-kpis')\n\n        # Resources API\n        from blueprints.api.resources import api_resources\n        app.register_blueprint(api_resources, url_prefix='/api/resources')\n\n        # Task Dependencies API\n        from blueprints.api.task_dependencies import api_task_dependencies\n        app.register_blueprint(api_task_dependencies, url_prefix='/api/task-dependencies')\n\n        # Tenant API (senza prefix per mantenere /api/config/tenant)\n        from blueprints.api.tenants import tenants_api\n        app.register_blueprint(tenants_api, url_prefix='/api')\n\n        # Admin API\n        from blueprints.api.admin import api_admin\n        app.register_blueprint(api_admin, url_prefix='/api/admin')\n\n        # AI Resources API\n        from blueprints.api.ai_resources import api_ai_resources\n        app.register_blueprint(api_ai_resources, url_prefix='/api/ai-resources')\n\n\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # Configure static file serving with correct MIME types\n        @app.after_request\n        def after_request(response):\n            # Fix MIME type for JavaScript modules and Vue files\n            if request.path.endswith('.js') or request.path.endswith('.mjs'):\n                response.content_type = 'application/javascript'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            elif request.path.endswith('.vue'):\n                response.content_type = 'application/javascript'\n            elif request.path.endswith('.css'):\n                response.content_type = 'text/css'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            return response\n\n\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files - let Flask serve them directly\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):\n                from flask import abort\n                abort(404)\n\n            # Serve the Vue.js SPA template for all other routes\n            from flask import render_template\n            return render_template('vue_app.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        db.create_all()\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            # Simplified session management for Vue.js SPA\n            if current_user.is_authenticated:\n                session['last_activity'] = time.time()\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n\n            # Skip enforcement per endpoint pubblici\n            if not endpoint or endpoint.startswith('static') or endpoint in PUBLIC_ENDPOINTS:\n                return\n\n            # Enforcement autenticazione globale solo per endpoint protetti\n            if not current_user.is_authenticated:\n                logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                # Per le API, restituisci JSON 401\n                if endpoint and (endpoint.startswith('api_') or endpoint.startswith('api.')):\n                    from flask import jsonify\n                    return jsonify({\n                        'success': False,\n                        'message': 'Autenticazione richiesta'\n                    }), 401\n\n                # Per le pagine web/SPA, restituisci JSON 401 (Vue.js gestirà il redirect)\n                from flask import jsonify\n                return jsonify({'error': 'Authentication required'}), 401\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect('/')\n\n        # Registra i filtri personalizzati\n        from utils.filters import register_filters\n        register_filters(app)\n\n    return app\n", "path": "backend/app.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}]]