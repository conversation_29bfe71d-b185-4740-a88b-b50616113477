{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_rbac.py"}, "originalCode": "import pytest\nfrom flask import url_for\nfrom models import User # Assicurati che User sia importato\n\ndef test_admin_route_forbidden_for_non_admin(client, db_session, test_user_id, new_user_data):\n    # Recupera l'utente normale dal DB\n    user = db_session.get(User, test_user_id)\n    assert user is not None, f\"Test user with id {test_user_id} not found.\"\n    assert user.role != 'admin', \"Test user should not have admin role for this test.\"\n\n    # Login come utente normale\n    client.post(url_for('auth.login'), data={\n        'username': user.username,\n        'password': new_user_data['password'], # Password da new_user_data\n    }, follow_redirects=True)\n\n    # Prova ad accedere a una route admin\n    try:\n        # Assicurati che la route esista e sia protetta da permessi admin\n        admin_url = url_for('personnel.admin')\n        response = client.get(admin_url, follow_redirects=False)\n\n        # Verifica accesso negato\n        if response.status_code == 302:  # Redirect\n            response_after_redirect = client.get(response.location, follow_redirects=True)\n            # Controlla messaggi di errore comuni\n            permission_messages = [\n                b'Accesso negato',\n                b'Non hai i permessi necessari',\n                b'Unauthorized',\n                b'Permission denied',\n                b'Forbidden'\n            ]\n            assert any(msg in response_after_redirect.data for msg in permission_messages) or \\\n                   response_after_redirect.status_code in [403, 401]\n        elif response.status_code in [403, 401]:  # Risposta diretta di errore\n            pass\n        else:\n            # Se non è chiaro che l'accesso sia stato negato, fallisce il test\n            assert False, f\"Expected access denied, got status code {response.status_code}\"\n    except Exception as e:\n        # La route potrebbe non esistere, in tal caso modifica il test\n        # per usare una route admin appropriata\n        print(f\"Exception during test: {e}\")\n        # Skip the test instead of failing it\n        pytest.skip(f\"Route personnel.admin might not exist or other error: {e}\")\n\n    # Conferma che all'utente non admin è stato negato l'accesso\n    # e rimane un utente normale (role non è cambiato)\n    db_session.refresh(user)\n    assert user.role != 'admin', \"User role should not have changed to admin.\"", "modifiedCode": "import pytest\nimport json\nfrom models import User\n\ndef test_admin_api_forbidden_for_non_admin(client, db_session, test_user_id, new_user_data):\n    \"\"\"Test that non-admin users cannot access admin-only API endpoints.\"\"\"\n    # Recupera l'utente normale dal DB\n    user = db_session.get(User, test_user_id)\n    assert user is not None, f\"Test user with id {test_user_id} not found.\"\n\n    # Assicura che l'utente non sia admin\n    user.role = 'employee'\n    db_session.commit()\n    assert user.role != 'admin', \"Test user should not have admin role for this test.\"\n\n    # Login come utente normale via API\n    login_response = client.post('/api/auth/login',\n                                json={\n                                    'username': user.username,\n                                    'password': new_user_data['password']\n                                },\n                                headers={'Content-Type': 'application/json'})\n    assert login_response.status_code == 200\n\n    # Test accesso negato a API admin-only\n    admin_endpoints = [\n        '/api/personnel/users',\n        '/api/personnel/export',\n        '/api/dashboard/stats'\n    ]\n\n    for endpoint in admin_endpoints:\n        response = client.get(endpoint)\n        # Should return 401 (Unauthorized) or 403 (Forbidden)\n        assert response.status_code in [401, 403], f\"Endpoint {endpoint} should deny access to non-admin\"\n\ndef test_role_based_permissions_api(client, auth, sample_users):\n    \"\"\"Test that different roles have appropriate permissions via API.\"\"\"\n    # Login as admin\n    auth.login()\n\n    # Get current user info\n    response = client.get('/api/auth/me')\n    assert response.status_code == 200\n\n    data = json.loads(response.data)\n    user = data['data']['user']\n    permissions = user['permissions']\n\n    # Test role-based permissions\n    if user['role'] == 'admin':\n        # Admin should have extensive permissions\n        assert len(permissions) > 5\n        assert any('admin' in perm or 'all' in perm for perm in permissions)\n    elif user['role'] == 'manager':\n        # Manager should have moderate permissions\n        assert len(permissions) > 2\n        assert 'view_projects' in permissions or 'view_dashboard' in permissions\n    elif user['role'] == 'employee':\n        # Employee should have basic permissions\n        assert len(permissions) >= 1\n        assert 'view_dashboard' in permissions or 'view_own_timesheet' in permissions\n\ndef test_permission_escalation_prevention_api(client, db_session, created_user, new_user_data):\n    \"\"\"Test that users cannot escalate their own permissions.\"\"\"\n    # Get regular user\n    user = db_session.get(User, created_user)\n    assert user is not None\n\n    # Make sure user is employee\n    user.role = 'employee'\n    db_session.commit()\n\n    # Login as employee\n    login_response = client.post('/api/auth/login',\n                                json={\n                                    'username': user.username,\n                                    'password': new_user_data['password']\n                                },\n                                headers={'Content-Type': 'application/json'})\n    assert login_response.status_code == 200\n\n    # Try to access user management (should fail)\n    response = client.get('/api/personnel/users')\n    assert response.status_code in [401, 403]\n\n    # Try to access admin dashboard (should fail)\n    response = client.get('/api/dashboard/stats')\n    assert response.status_code in [401, 403]\n\ndef test_session_role_consistency_api(client, db_session, admin_user_id):\n    \"\"\"Test that user role in session matches database.\"\"\"\n    # Get admin user\n    admin = db_session.get(User, admin_user_id)\n    assert admin is not None\n    assert admin.role == 'admin'\n\n    # Login as admin\n    login_response = client.post('/api/auth/login',\n                                json={\n                                    'username': admin.username,\n                                    'password': 'adminpassword'\n                                },\n                                headers={'Content-Type': 'application/json'})\n    assert login_response.status_code == 200\n\n    # Check that API returns correct role\n    me_response = client.get('/api/auth/me')\n    assert me_response.status_code == 200\n\n    data = json.loads(me_response.data)\n    assert data['data']['user']['role'] == 'admin'\n    assert data['data']['user']['id'] == admin.id"}