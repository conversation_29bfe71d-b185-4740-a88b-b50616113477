{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/ai_services.py"}, "originalCode": "import os\nimport json\nimport requests\nimport logging\nfrom openai import OpenAI\n\nlogger = logging.getLogger(__name__)\n\n# API Keys\nOPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")\nPERPLEXITY_API_KEY = os.environ.get(\"PERPLEXITY_API_KEY\")\n\n# Initialize OpenAI client\nopenai_client = OpenAI(api_key=OPENAI_API_KEY)\n\ndef analyze_text_with_openai(text, prompt=\"\", model=\"gpt-4o\"):\n    \"\"\"\n    Analyze text using OpenAI GPT models\n    \"\"\"\n    try:\n        # the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024.\n        # do not change this unless explicitly requested by the user\n        if not prompt:\n            prompt = \"Analyze the following text and provide insights:\"\n\n        complete_prompt = f\"{prompt}\\n\\n{text}\"\n\n        response = openai_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": complete_prompt}],\n            temperature=0.2,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error analyzing text: {str(e)}\"\n\ndef generate_summary_with_openai(text, max_length=200):\n    \"\"\"\n    Generate a concise summary of the provided text\n    \"\"\"\n    try:\n        prompt = f\"Summarize the following text in about {max_length} words or less:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error generating summary: {str(e)}\"\n\ndef extract_insights_with_openai(text, context=\"business\"):\n    \"\"\"\n    Extract key insights from text with a specific business context\n    \"\"\"\n    try:\n        prompt = f\"Extract key {context} insights and actionable points from the following text:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return {\"error\": str(e), \"insights\": []}\n\ndef analyze_with_perplexity(text, question=\"\", model=\"llama-3.1-sonar-small-128k-online\"):\n    \"\"\"\n    Analyze text using Perplexity API\n    \"\"\"\n    if not PERPLEXITY_API_KEY:\n        logger.error(\"Perplexity API key not found\")\n        return \"Error: Perplexity API key not configured\"\n\n    try:\n        if not question:\n            question = \"Analyze this text and provide insights:\"\n\n        headers = {\n            \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n            \"Content-Type\": \"application/json\"\n        }\n\n        data = {\n            \"model\": model,\n            \"messages\": [\n                {\n                    \"role\": \"system\",\n                    \"content\": \"You are an AI assistant for business analytics. Be precise and concise.\"\n                },\n                {\n                    \"role\": \"user\",\n                    \"content\": f\"{question}\\n\\n{text}\"\n                }\n            ],\n            \"temperature\": 0.2,\n            \"max_tokens\": 500,\n            \"stream\": False\n        }\n\n        response = requests.post(\n            \"https://api.perplexity.ai/chat/completions\",\n            headers=headers,\n            json=data\n        )\n\n        if response.status_code == 200:\n            response_data = response.json()\n            return {\n                \"content\": response_data[\"choices\"][0][\"message\"][\"content\"],\n                \"citations\": response_data.get(\"citations\", [])\n            }\n        else:\n            logger.error(f\"Perplexity API error: {response.status_code} - {response.text}\")\n            return {\"error\": f\"API Error: {response.status_code}\", \"content\": \"\"}\n    except Exception as e:\n        logger.error(f\"Perplexity API call error: {str(e)}\")\n        return {\"error\": str(e), \"content\": \"\"}\n\ndef analyze_project_requirements(requirements_text):\n    \"\"\"\n    Analyze project requirements and extract key components\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analyze these project requirements and extract the following information in JSON format:\n        1. Key deliverables\n        2. Potential risks\n        3. Resources needed\n        4. Estimated timeline\n        5. Success criteria\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{requirements_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Project requirements analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"key_deliverables\": [],\n            \"potential_risks\": [],\n            \"resources_needed\": [],\n            \"estimated_timeline\": \"Unknown\",\n            \"success_criteria\": []\n        }\n\ndef generate_funding_recommendations(company_profile):\n    \"\"\"\n    Generate funding recommendations based on company profile\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Based on this company profile, provide recommendations for:\n        1. Suitable funding opportunities\n        2. Grant programs to consider\n        3. Application strategy suggestions\n        4. Key points to highlight\n\n        Respond in JSON format.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{company_profile}\"}],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Funding recommendations error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"funding_opportunities\": [],\n            \"grant_programs\": [],\n            \"application_strategy\": [],\n            \"key_highlights\": []\n        }\n\ndef extract_skills_from_cv(cv_text):\n    \"\"\"\n    Estrae competenze da un CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza questo CV e estrai tutte le competenze tecniche e professionali.\n        Categorizza le competenze in:\n        - Linguaggi di programmazione\n        - Framework e librerie\n        - Database\n        - Cloud e DevOps\n        - Soft skills\n        - Certificazioni\n        - Strumenti e software\n        - Metodologie\n\n        Restituisci il risultato in formato JSON con questa struttura:\n        {\n            \"skills\": [\n                {\n                    \"name\": \"nome competenza\",\n                    \"category\": \"categoria\",\n                    \"level\": \"beginner|intermediate|advanced|expert\"\n                }\n            ],\n            \"summary\": \"breve riassunto del profilo professionale\",\n            \"experience_years\": numero_totale_anni_esperienza\n        }\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\nCV:\\n{cv_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI CV analysis error: {str(e)}\")\n        return {\"error\": str(e), \"skills\": [], \"summary\": \"\", \"experience_years\": 0}\n\ndef generate_cv_html(user_data, skills_data):\n    \"\"\"\n    Genera HTML per CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = f\"\"\"\n        Genera un CV professionale in HTML per questo profilo:\n\n        Dati utente: {json.dumps(user_data, indent=2)}\n        Competenze: {json.dumps(skills_data, indent=2)}\n\n        Crea un CV completo con:\n        - Intestazione con dati personali\n        - Profilo professionale (summary)\n        - Competenze tecniche organizzate per categoria\n        - Layout professionale con CSS inline\n        - Stile moderno e pulito\n\n        Restituisci solo l'HTML completo del CV.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI CV generation error: {str(e)}\")\n        return f\"<p>Errore nella generazione del CV: {str(e)}</p>\"\n", "modifiedCode": "import os\nimport json\nimport requests\nimport logging\nfrom openai import OpenAI\n\nlogger = logging.getLogger(__name__)\n\n# API Keys\nOPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")\nPERPLEXITY_API_KEY = os.environ.get(\"PERPLEXITY_API_KEY\")\n\n# Initialize OpenAI client\nopenai_client = OpenAI(api_key=OPENAI_API_KEY)\n\ndef analyze_text_with_openai(text, prompt=\"\", model=\"gpt-4o\"):\n    \"\"\"\n    Analyze text using OpenAI GPT models\n    \"\"\"\n    try:\n        # the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024.\n        # do not change this unless explicitly requested by the user\n        if not prompt:\n            prompt = \"Analyze the following text and provide insights:\"\n\n        complete_prompt = f\"{prompt}\\n\\n{text}\"\n\n        response = openai_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": complete_prompt}],\n            temperature=0.2,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error analyzing text: {str(e)}\"\n\ndef generate_summary_with_openai(text, max_length=200):\n    \"\"\"\n    Generate a concise summary of the provided text\n    \"\"\"\n    try:\n        prompt = f\"Summarize the following text in about {max_length} words or less:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error generating summary: {str(e)}\"\n\ndef extract_insights_with_openai(text, context=\"business\"):\n    \"\"\"\n    Extract key insights from text with a specific business context\n    \"\"\"\n    try:\n        prompt = f\"Extract key {context} insights and actionable points from the following text:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return {\"error\": str(e), \"insights\": []}\n\ndef analyze_with_perplexity(text, question=\"\", model=\"llama-3.1-sonar-small-128k-online\"):\n    \"\"\"\n    Analyze text using Perplexity API\n    \"\"\"\n    if not PERPLEXITY_API_KEY:\n        logger.error(\"Perplexity API key not found\")\n        return \"Error: Perplexity API key not configured\"\n\n    try:\n        if not question:\n            question = \"Analyze this text and provide insights:\"\n\n        headers = {\n            \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n            \"Content-Type\": \"application/json\"\n        }\n\n        data = {\n            \"model\": model,\n            \"messages\": [\n                {\n                    \"role\": \"system\",\n                    \"content\": \"You are an AI assistant for business analytics. Be precise and concise.\"\n                },\n                {\n                    \"role\": \"user\",\n                    \"content\": f\"{question}\\n\\n{text}\"\n                }\n            ],\n            \"temperature\": 0.2,\n            \"max_tokens\": 500,\n            \"stream\": False\n        }\n\n        response = requests.post(\n            \"https://api.perplexity.ai/chat/completions\",\n            headers=headers,\n            json=data\n        )\n\n        if response.status_code == 200:\n            response_data = response.json()\n            return {\n                \"content\": response_data[\"choices\"][0][\"message\"][\"content\"],\n                \"citations\": response_data.get(\"citations\", [])\n            }\n        else:\n            logger.error(f\"Perplexity API error: {response.status_code} - {response.text}\")\n            return {\"error\": f\"API Error: {response.status_code}\", \"content\": \"\"}\n    except Exception as e:\n        logger.error(f\"Perplexity API call error: {str(e)}\")\n        return {\"error\": str(e), \"content\": \"\"}\n\ndef analyze_project_requirements(requirements_text):\n    \"\"\"\n    Analyze project requirements and extract key components\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analyze these project requirements and extract the following information in JSON format:\n        1. Key deliverables\n        2. Potential risks\n        3. Resources needed\n        4. Estimated timeline\n        5. Success criteria\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{requirements_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Project requirements analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"key_deliverables\": [],\n            \"potential_risks\": [],\n            \"resources_needed\": [],\n            \"estimated_timeline\": \"Unknown\",\n            \"success_criteria\": []\n        }\n\ndef generate_funding_recommendations(company_profile):\n    \"\"\"\n    Generate funding recommendations based on company profile\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Based on this company profile, provide recommendations for:\n        1. Suitable funding opportunities\n        2. Grant programs to consider\n        3. Application strategy suggestions\n        4. Key points to highlight\n\n        Respond in JSON format.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{company_profile}\"}],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Funding recommendations error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"funding_opportunities\": [],\n            \"grant_programs\": [],\n            \"application_strategy\": [],\n            \"key_highlights\": []\n        }\n\ndef extract_skills_from_cv(cv_text):\n    \"\"\"\n    Estrae competenze da un CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza questo CV e estrai tutte le competenze tecniche e professionali.\n        Categorizza le competenze in:\n        - Linguaggi di programmazione\n        - Framework e librerie\n        - Database\n        - Cloud e DevOps\n        - Soft skills\n        - Certificazioni\n        - Strumenti e software\n        - Metodologie\n\n        Restituisci il risultato in formato JSON con questa struttura:\n        {\n            \"skills\": [\n                {\n                    \"name\": \"nome competenza\",\n                    \"category\": \"categoria\",\n                    \"level\": \"beginner|intermediate|advanced|expert\"\n                }\n            ],\n            \"summary\": \"breve riassunto del profilo professionale\",\n            \"experience_years\": numero_totale_anni_esperienza\n        }\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\nCV:\\n{cv_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI CV analysis error: {str(e)}\")\n        return {\"error\": str(e), \"skills\": [], \"summary\": \"\", \"experience_years\": 0}\n\ndef generate_cv_html(user_data, skills_data):\n    \"\"\"\n    Genera HTML per CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = f\"\"\"\n        Genera un CV professionale in HTML per questo profilo:\n\n        Dati utente: {json.dumps(user_data, indent=2)}\n        Competenze: {json.dumps(skills_data, indent=2)}\n\n        Crea un CV completo con:\n        - Intestazione con dati personali\n        - Profilo professionale (summary)\n        - Competenze tecniche organizzate per categoria\n        - Layout professionale con CSS inline\n        - Stile moderno e pulito\n\n        Restituisci solo l'HTML completo del CV.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI CV generation error: {str(e)}\")\n        return f\"<p>Errore nella generazione del CV: {str(e)}</p>\"\n\n# ============================================================================\n# RESOURCE ALLOCATION AI SERVICES\n# ============================================================================\n\ndef analyze_resource_allocation(project_data, available_resources, current_allocations=None):\n    \"\"\"\n    Analizza l'allocazione delle risorse per un progetto e fornisce suggerimenti AI\n    \"\"\"\n    try:\n        # Prepara i dati per l'analisi\n        context = {\n            \"project\": {\n                \"name\": project_data.get(\"name\", \"\"),\n                \"description\": project_data.get(\"description\", \"\"),\n                \"project_type\": project_data.get(\"project_type\", \"\"),\n                \"start_date\": project_data.get(\"start_date\", \"\"),\n                \"end_date\": project_data.get(\"end_date\", \"\"),\n                \"budget\": project_data.get(\"budget\", 0),\n                \"estimated_hours\": project_data.get(\"estimated_hours\", 0),\n                \"required_skills\": project_data.get(\"required_skills\", [])\n            },\n            \"available_resources\": available_resources,\n            \"current_allocations\": current_allocations or []\n        }\n\n        prompt = \"\"\"\n        Analizza questa situazione di allocazione risorse e fornisci suggerimenti intelligenti.\n\n        Considera:\n        1. Competenze richieste vs competenze disponibili\n        2. Carico di lavoro attuale delle risorse\n        3. Disponibilità temporale\n        4. Costi e budget\n        5. Efficienza del team\n\n        Fornisci suggerimenti in formato JSON con:\n        - recommended_allocations: array di allocazioni suggerite\n        - optimization_insights: insights per ottimizzazione\n        - potential_conflicts: conflitti potenziali identificati\n        - efficiency_score: punteggio efficienza (0-100)\n        - cost_analysis: analisi costi\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un esperto AI in resource management e project planning. Analizza i dati e fornisci suggerimenti pratici e attuabili.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource allocation analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"recommended_allocations\": [],\n            \"optimization_insights\": [],\n            \"potential_conflicts\": [],\n            \"efficiency_score\": 0,\n            \"cost_analysis\": {}\n        }\n\ndef predict_resource_conflicts(allocations_data, timeline_data):\n    \"\"\"\n    Predice conflitti di risorse basandosi su dati storici e allocazioni attuali\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza queste allocazioni di risorse e predici potenziali conflitti.\n\n        Identifica:\n        1. Sovrallocazioni (>100% capacità)\n        2. Conflitti temporali\n        3. Competenze mancanti\n        4. Rischi di burnout\n        5. Dipendenze critiche\n\n        Fornisci risultato in JSON con:\n        - conflicts: array di conflitti identificati\n        - risk_level: livello rischio (low/medium/high)\n        - recommendations: raccomandazioni per risoluzione\n        - timeline_impact: impatto su timeline progetto\n        \"\"\"\n\n        context = {\n            \"allocations\": allocations_data,\n            \"timeline\": timeline_data\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI specialist in project risk management. Identifica proattivamente i rischi nelle allocazioni di risorse.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.1,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource conflict prediction error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"conflicts\": [],\n            \"risk_level\": \"unknown\",\n            \"recommendations\": [],\n            \"timeline_impact\": \"unknown\"\n        }\n\ndef optimize_team_composition(project_requirements, candidate_resources):\n    \"\"\"\n    Ottimizza la composizione del team per un progetto specifico\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Ottimizza la composizione del team per questo progetto.\n\n        Considera:\n        1. Skill match con requisiti progetto\n        2. Sinergie tra membri del team\n        3. Bilanciamento senior/junior\n        4. Copertura competenze critiche\n        5. Dinamiche di team\n\n        Fornisci in JSON:\n        - optimal_team: composizione team ottimale\n        - skill_coverage: copertura competenze (%)\n        - team_synergy_score: punteggio sinergia (0-100)\n        - alternative_options: opzioni alternative\n        - training_needs: necessità formazione\n        \"\"\"\n\n        context = {\n            \"project_requirements\": project_requirements,\n            \"candidate_resources\": candidate_resources\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI expert in team building e resource optimization. Crea team ad alta performance.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Team composition optimization error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"optimal_team\": [],\n            \"skill_coverage\": 0,\n            \"team_synergy_score\": 0,\n            \"alternative_options\": [],\n            \"training_needs\": []\n        }\n"}