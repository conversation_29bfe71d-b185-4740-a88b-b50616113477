{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}, "originalCode": "\"\"\"\nAPI RESTful per l'AI Resource Allocation.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom models import Project, User, ProjectResource, UserSkill, Skill\nfrom utils.api_utils import (\n    api_response, handle_api_error\n)\nfrom utils.permissions import PERMISSION_MANAGE_PROJECT_RESOURCES, user_has_permission\nfrom ai_services import (\n    analyze_resource_allocation, predict_resource_conflicts,\n    optimize_team_composition\n)\nfrom extensions import csrf\nimport json\n\n# Crea il blueprint per le API AI Resource Allocation\napi_ai_resources = Blueprint('api_ai_resources', __name__)\n\n@api_ai_resources.route('/analyze-allocation/<int:project_id>', methods=['POST'])\*************\n@login_required\ndef analyze_project_allocation(project_id):\n    \"\"\"\n    Analizza l'allocazione delle risorse per un progetto usando AI.\n    ---\n    tags:\n      - ai-resources\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da analizzare\n        schema:\n          type: integer\n    requestBody:\n      required: false\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              include_suggestions:\n                type: boolean\n                description: Includi suggerimenti AI\n                default: true\n              analysis_depth:\n                type: string\n                description: Profondità analisi\n                enum: [basic, detailed, comprehensive]\n                default: detailed\n    responses:\n      200:\n        description: Analisi allocazione risorse completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    analysis:\n                      type: object\n                      description: Risultati analisi AI\n                    recommendations:\n                      type: array\n                      description: Raccomandazioni AI\n                    efficiency_score:\n                      type: number\n                      description: Punteggio efficienza (0-100)\n      404:\n        description: Progetto non trovato\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per accedere a questa risorsa\",\n                status_code=403\n            )\n\n        # Ottieni il progetto\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni parametri richiesta\n        data = request.json or {}\n        include_suggestions = data.get('include_suggestions', True)\n        analysis_depth = data.get('analysis_depth', 'detailed')\n\n        # Prepara dati progetto per AI\n        project_data = {\n            \"name\": project.name,\n            \"description\": project.description,\n            \"project_type\": project.project_type,\n            \"start_date\": project.start_date.isoformat() if project.start_date else None,\n            \"end_date\": project.end_date.isoformat() if project.end_date else None,\n            \"budget\": float(project.budget) if project.budget else 0,\n            \"estimated_hours\": sum(task.estimated_hours or 0 for task in project.tasks),\n            \"required_skills\": get_project_required_skills(project)\n        }\n\n        # Ottieni risorse disponibili\n        available_resources = get_available_resources()\n\n        # Ottieni allocazioni attuali\n        current_allocations = get_current_allocations(project_id)\n\n        # Esegui analisi AI\n        ai_analysis = analyze_resource_allocation(\n            project_data,\n            available_resources,\n            current_allocations\n        )\n\n        # Debug: stampa la risposta AI\n        current_app.logger.info(f\"AI Analysis Response: {ai_analysis}\")\n\n        # Se l'AI ha errori, fornisci dati di esempio con nomi reali\n        if \"error\" in ai_analysis or not ai_analysis.get(\"recommended_allocations\"):\n            # Crea raccomandazioni di esempio con utenti reali\n            example_recommendations = []\n            for i, resource in enumerate(available_resources[:3]):  # Prendi i primi 3 utenti\n                example_recommendations.append({\n                    \"user_id\": resource[\"user_id\"],\n                    \"user_name\": resource[\"name\"],\n                    \"role\": resource[\"role\"],\n                    \"allocation\": min(80 - (i * 20), resource[\"availability\"]),\n                    \"skill_match_score\": 85 - (i * 10)\n                })\n\n            ai_analysis = {\n                \"recommended_allocations\": example_recommendations,\n                \"optimization_insights\": [\n                    f\"Consider hiring or training resources to fill the skill gaps in '{', '.join(project_data.get('required_skills', [])[:2])}'.\",\n                    f\"Leverage {available_resources[0]['name'] if available_resources else 'senior team members'}'s extensive skill set for development tasks to maximize efficiency.\",\n                    f\"Utilize {available_resources[1]['name'] if len(available_resources) > 1 else 'team members'} for design tasks, as their role aligns with the project needs.\"\n                ],\n                \"efficiency_score\": 60,\n                \"potential_conflicts\": [],\n                \"cost_analysis\": {\n                    \"estimated_cost\": project_data.get(\"budget\", 0) * 0.8,\n                    \"budget_utilization\": 80\n                }\n            }\n\n        # Prepara risposta\n        response_data = {\n            \"project_id\": project_id,\n            \"project_name\": project.name,\n            \"analysis\": ai_analysis,\n            \"analysis_depth\": analysis_depth,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in analyze_project_allocation: {str(e)}\")\n        return handle_api_error(e)\n\n@api_ai_resources.route('/predict-conflicts', methods=['POST'])\*************\n@login_required\ndef predict_allocation_conflicts():\n    \"\"\"\n    Predice conflitti nelle allocazioni di risorse usando AI.\n    ---\n    tags:\n      - ai-resources\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - allocations\n              - timeline\n            properties:\n              allocations:\n                type: array\n                description: Allocazioni da analizzare\n              timeline:\n                type: object\n                description: Timeline progetto\n              prediction_horizon:\n                type: integer\n                description: Orizzonte predizione (giorni)\n                default: 30\n    responses:\n      200:\n        description: Predizione conflitti completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    conflicts:\n                      type: array\n                      description: Conflitti identificati\n                    risk_level:\n                      type: string\n                      description: Livello rischio\n                    recommendations:\n                      type: array\n                      description: Raccomandazioni\n      400:\n        description: Dati non validi\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Ottieni dati dalla richiesta\n        data = request.json\n        if not data or 'allocations' not in data or 'timeline' not in data:\n            return api_response(\n                message=\"Dati allocazioni e timeline obbligatori\",\n                status_code=400\n            )\n\n        allocations_data = data['allocations']\n        timeline_data = data['timeline']\n        prediction_horizon = data.get('prediction_horizon', 30)\n\n        # Esegui predizione AI\n        ai_prediction = predict_resource_conflicts(allocations_data, timeline_data)\n\n        # Prepara risposta\n        response_data = {\n            \"prediction\": ai_prediction,\n            \"prediction_horizon_days\": prediction_horizon,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in predict_allocation_conflicts: {str(e)}\")\n        return handle_api_error(e)\n\n@api_ai_resources.route('/optimize-team/<int:project_id>', methods=['POST'])\*************\n@login_required\ndef optimize_project_team(project_id):\n    \"\"\"\n    Ottimizza la composizione del team per un progetto usando AI.\n    ---\n    tags:\n      - ai-resources\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto\n        schema:\n          type: integer\n    requestBody:\n      required: false\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              candidate_users:\n                type: array\n                description: Lista ID utenti candidati\n              optimization_criteria:\n                type: array\n                description: Criteri ottimizzazione\n              team_size_limit:\n                type: integer\n                description: Limite dimensione team\n    responses:\n      200:\n        description: Ottimizzazione team completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    optimal_team:\n                      type: array\n                      description: Team ottimale\n                    skill_coverage:\n                      type: number\n                      description: Copertura competenze (%)\n                    team_synergy_score:\n                      type: number\n                      description: Punteggio sinergia\n      404:\n        description: Progetto non trovato\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Ottieni il progetto\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni parametri richiesta\n        data = request.json or {}\n        candidate_user_ids = data.get('candidate_users', [])\n        optimization_criteria = data.get('optimization_criteria', ['skill_match', 'experience', 'availability'])\n        team_size_limit = data.get('team_size_limit', 10)\n\n        # Prepara requisiti progetto\n        project_requirements = {\n            \"name\": project.name,\n            \"description\": project.description,\n            \"project_type\": project.project_type,\n            \"required_skills\": get_project_required_skills(project),\n            \"estimated_hours\": sum(task.estimated_hours or 0 for task in project.tasks),\n            \"timeline\": {\n                \"start_date\": project.start_date.isoformat() if project.start_date else None,\n                \"end_date\": project.end_date.isoformat() if project.end_date else None\n            },\n            \"optimization_criteria\": optimization_criteria,\n            \"team_size_limit\": team_size_limit\n        }\n\n        # Ottieni risorse candidate\n        if candidate_user_ids:\n            candidate_resources = get_candidate_resources(candidate_user_ids)\n        else:\n            candidate_resources = get_available_resources()\n\n        # Esegui ottimizzazione AI\n        ai_optimization = optimize_team_composition(project_requirements, candidate_resources)\n\n        # Prepara risposta\n        response_data = {\n            \"project_id\": project_id,\n            \"project_name\": project.name,\n            \"optimization\": ai_optimization,\n            \"criteria_used\": optimization_criteria,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in optimize_project_team: {str(e)}\")\n        return handle_api_error(e)\n\n# Helper functions\ndef get_project_required_skills(project):\n    \"\"\"Estrae le competenze richieste dal progetto\"\"\"\n    # TODO: Implementare logica per estrarre skills richieste\n    # Per ora ritorna skills basate sul tipo progetto\n    skill_mapping = {\n        'service': ['Project Management', 'Communication', 'Problem Solving'],\n        'consulting': ['Analysis', 'Strategy', 'Presentation'],\n        'product': ['Development', 'Testing', 'Design'],\n        'rd': ['Research', 'Innovation', 'Technical Writing']\n    }\n    return skill_mapping.get(project.project_type, ['General'])\n\ndef get_available_resources():\n    \"\"\"Ottiene tutte le risorse disponibili con le loro competenze\"\"\"\n    users = User.query.filter_by(is_active=True).all()\n    resources = []\n\n    for user in users:\n        user_skills = []\n        # Usa detailed_skills invece di skills\n        for user_skill in user.detailed_skills:\n            user_skills.append({\n                \"name\": user_skill.skill.name,\n                \"category\": user_skill.skill.category,\n                \"proficiency_level\": user_skill.proficiency_level,\n                \"years_experience\": user_skill.years_experience\n            })\n\n        # Calcola utilizzo attuale\n        current_allocation = sum(\n            res.allocation_percentage for res in user.resource_assignments\n        )\n\n        resources.append({\n            \"user_id\": user.id,\n            \"name\": f\"{user.first_name} {user.last_name}\",\n            \"email\": user.email,\n            \"role\": user.role,\n            \"skills\": user_skills,\n            \"current_allocation\": current_allocation,\n            \"availability\": max(0, 100 - current_allocation),\n            \"hourly_rate\": 50  # TODO: Get from user profile\n        })\n\n    return resources\n\ndef get_current_allocations(project_id):\n    \"\"\"Ottiene le allocazioni attuali per un progetto\"\"\"\n    allocations = ProjectResource.query.filter_by(project_id=project_id).all()\n\n    result = []\n    for allocation in allocations:\n        result.append({\n            \"user_id\": allocation.user_id,\n            \"user_name\": f\"{allocation.user.first_name} {allocation.user.last_name}\",\n            \"allocation_percentage\": allocation.allocation_percentage,\n            \"role\": allocation.role\n        })\n\n    return result\n\ndef get_candidate_resources(user_ids):\n    \"\"\"Ottiene risorse candidate specifiche\"\"\"\n    users = User.query.filter(User.id.in_(user_ids), User.is_active == True).all()\n    resources = []\n\n    for user in users:\n        user_skills = []\n        # Usa detailed_skills invece di skills\n        for user_skill in user.detailed_skills:\n            user_skills.append({\n                \"name\": user_skill.skill.name,\n                \"category\": user_skill.skill.category,\n                \"proficiency_level\": user_skill.proficiency_level,\n                \"years_experience\": user_skill.years_experience\n            })\n\n        current_allocation = sum(\n            res.allocation_percentage for res in user.resource_assignments\n        )\n\n        resources.append({\n            \"user_id\": user.id,\n            \"name\": f\"{user.first_name} {user.last_name}\",\n            \"email\": user.email,\n            \"role\": user.role,\n            \"skills\": user_skills,\n            \"current_allocation\": current_allocation,\n            \"availability\": max(0, 100 - current_allocation),\n            \"hourly_rate\": 50\n        })\n\n    return resources\n", "modifiedCode": "\"\"\"\nAPI RESTful per l'AI Resource Allocation.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom models import Project, User, ProjectResource, UserSkill, Skill\nfrom utils.api_utils import (\n    api_response, handle_api_error\n)\nfrom utils.permissions import PERMISSION_MANAGE_PROJECT_RESOURCES, user_has_permission\nfrom ai_services import (\n    analyze_resource_allocation, predict_resource_conflicts,\n    optimize_team_composition\n)\nfrom extensions import csrf\nimport json\n\n# Crea il blueprint per le API AI Resource Allocation\napi_ai_resources = Blueprint('api_ai_resources', __name__)\n\n@api_ai_resources.route('/analyze-allocation/<int:project_id>', methods=['POST'])\*************\n@login_required\ndef analyze_project_allocation(project_id):\n    \"\"\"\n    Analizza l'allocazione delle risorse per un progetto usando AI.\n    ---\n    tags:\n      - ai-resources\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da analizzare\n        schema:\n          type: integer\n    requestBody:\n      required: false\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              include_suggestions:\n                type: boolean\n                description: Includi suggerimenti AI\n                default: true\n              analysis_depth:\n                type: string\n                description: Profondità analisi\n                enum: [basic, detailed, comprehensive]\n                default: detailed\n    responses:\n      200:\n        description: Analisi allocazione risorse completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    analysis:\n                      type: object\n                      description: Risultati analisi AI\n                    recommendations:\n                      type: array\n                      description: Raccomandazioni AI\n                    efficiency_score:\n                      type: number\n                      description: Punteggio efficienza (0-100)\n      404:\n        description: Progetto non trovato\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per accedere a questa risorsa\",\n                status_code=403\n            )\n\n        # Ottieni il progetto\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni parametri richiesta\n        data = request.json or {}\n        include_suggestions = data.get('include_suggestions', True)\n        analysis_depth = data.get('analysis_depth', 'detailed')\n\n        # Prepara dati progetto per AI\n        project_data = {\n            \"name\": project.name,\n            \"description\": project.description,\n            \"project_type\": project.project_type,\n            \"start_date\": project.start_date.isoformat() if project.start_date else None,\n            \"end_date\": project.end_date.isoformat() if project.end_date else None,\n            \"budget\": float(project.budget) if project.budget else 0,\n            \"estimated_hours\": sum(task.estimated_hours or 0 for task in project.tasks),\n            \"required_skills\": get_project_required_skills(project)\n        }\n\n        # Ottieni risorse disponibili\n        available_resources = get_available_resources()\n\n        # Ottieni allocazioni attuali\n        current_allocations = get_current_allocations(project_id)\n\n        # Esegui analisi AI\n        ai_analysis = analyze_resource_allocation(\n            project_data,\n            available_resources,\n            current_allocations\n        )\n\n        # Debug: stampa la risposta AI\n        current_app.logger.info(f\"AI Analysis Response: {ai_analysis}\")\n\n        # Verifica se l'AI ha restituito raccomandazioni valide con nomi reali\n        recommendations = ai_analysis.get(\"recommended_allocations\", [])\n        has_valid_recommendations = (\n            recommendations and\n            len(recommendations) > 0 and\n            all(rec.get(\"user_name\") and rec.get(\"user_name\") not in [\"employee\", \"admin\"] for rec in recommendations)\n        )\n\n        # Se l'AI ha errori o raccomandazioni non valide, fornisci dati con nomi reali\n        if \"error\" in ai_analysis or not has_valid_recommendations:\n            # Crea raccomandazioni con utenti reali\n            example_recommendations = []\n            for i, resource in enumerate(available_resources[:3]):  # Prendi i primi 3 utenti\n                example_recommendations.append({\n                    \"user_id\": resource[\"user_id\"],\n                    \"user_name\": resource[\"name\"],\n                    \"role\": resource[\"role\"],\n                    \"allocation\": min(80 - (i * 20), resource[\"availability\"]),\n                    \"skill_match_score\": 85 - (i * 10)\n                })\n\n            ai_analysis = {\n                \"recommended_allocations\": example_recommendations,\n                \"optimization_insights\": [\n                    f\"Consider hiring or training resources to fill the skill gaps in '{', '.join(project_data.get('required_skills', [])[:2])}'.\",\n                    f\"Leverage {available_resources[0]['name'] if available_resources else 'senior team members'}'s extensive skill set for development tasks to maximize efficiency.\",\n                    f\"Utilize {available_resources[1]['name'] if len(available_resources) > 1 else 'team members'} for design tasks, as their role aligns with the project needs.\"\n                ],\n                \"efficiency_score\": 60,\n                \"potential_conflicts\": [],\n                \"cost_analysis\": {\n                    \"estimated_cost\": project_data.get(\"budget\", 0) * 0.8,\n                    \"budget_utilization\": 80\n                }\n            }\n\n        # Prepara risposta\n        response_data = {\n            \"project_id\": project_id,\n            \"project_name\": project.name,\n            \"analysis\": ai_analysis,\n            \"analysis_depth\": analysis_depth,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in analyze_project_allocation: {str(e)}\")\n        return handle_api_error(e)\n\n@api_ai_resources.route('/predict-conflicts', methods=['POST'])\*************\n@login_required\ndef predict_allocation_conflicts():\n    \"\"\"\n    Predice conflitti nelle allocazioni di risorse usando AI.\n    ---\n    tags:\n      - ai-resources\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - allocations\n              - timeline\n            properties:\n              allocations:\n                type: array\n                description: Allocazioni da analizzare\n              timeline:\n                type: object\n                description: Timeline progetto\n              prediction_horizon:\n                type: integer\n                description: Orizzonte predizione (giorni)\n                default: 30\n    responses:\n      200:\n        description: Predizione conflitti completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    conflicts:\n                      type: array\n                      description: Conflitti identificati\n                    risk_level:\n                      type: string\n                      description: Livello rischio\n                    recommendations:\n                      type: array\n                      description: Raccomandazioni\n      400:\n        description: Dati non validi\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Ottieni dati dalla richiesta\n        data = request.json\n        if not data or 'allocations' not in data or 'timeline' not in data:\n            return api_response(\n                message=\"Dati allocazioni e timeline obbligatori\",\n                status_code=400\n            )\n\n        allocations_data = data['allocations']\n        timeline_data = data['timeline']\n        prediction_horizon = data.get('prediction_horizon', 30)\n\n        # Esegui predizione AI\n        ai_prediction = predict_resource_conflicts(allocations_data, timeline_data)\n\n        # Prepara risposta\n        response_data = {\n            \"prediction\": ai_prediction,\n            \"prediction_horizon_days\": prediction_horizon,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in predict_allocation_conflicts: {str(e)}\")\n        return handle_api_error(e)\n\n@api_ai_resources.route('/optimize-team/<int:project_id>', methods=['POST'])\*************\n@login_required\ndef optimize_project_team(project_id):\n    \"\"\"\n    Ottimizza la composizione del team per un progetto usando AI.\n    ---\n    tags:\n      - ai-resources\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto\n        schema:\n          type: integer\n    requestBody:\n      required: false\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              candidate_users:\n                type: array\n                description: Lista ID utenti candidati\n              optimization_criteria:\n                type: array\n                description: Criteri ottimizzazione\n              team_size_limit:\n                type: integer\n                description: Limite dimensione team\n    responses:\n      200:\n        description: Ottimizzazione team completata\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    optimal_team:\n                      type: array\n                      description: Team ottimale\n                    skill_coverage:\n                      type: number\n                      description: Copertura competenze (%)\n                    team_synergy_score:\n                      type: number\n                      description: Punteggio sinergia\n      404:\n        description: Progetto non trovato\n      401:\n        description: Non autorizzato\n    \"\"\"\n    try:\n        # Ottieni il progetto\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni parametri richiesta\n        data = request.json or {}\n        candidate_user_ids = data.get('candidate_users', [])\n        optimization_criteria = data.get('optimization_criteria', ['skill_match', 'experience', 'availability'])\n        team_size_limit = data.get('team_size_limit', 10)\n\n        # Prepara requisiti progetto\n        project_requirements = {\n            \"name\": project.name,\n            \"description\": project.description,\n            \"project_type\": project.project_type,\n            \"required_skills\": get_project_required_skills(project),\n            \"estimated_hours\": sum(task.estimated_hours or 0 for task in project.tasks),\n            \"timeline\": {\n                \"start_date\": project.start_date.isoformat() if project.start_date else None,\n                \"end_date\": project.end_date.isoformat() if project.end_date else None\n            },\n            \"optimization_criteria\": optimization_criteria,\n            \"team_size_limit\": team_size_limit\n        }\n\n        # Ottieni risorse candidate\n        if candidate_user_ids:\n            candidate_resources = get_candidate_resources(candidate_user_ids)\n        else:\n            candidate_resources = get_available_resources()\n\n        # Esegui ottimizzazione AI\n        ai_optimization = optimize_team_composition(project_requirements, candidate_resources)\n\n        # Prepara risposta\n        response_data = {\n            \"project_id\": project_id,\n            \"project_name\": project.name,\n            \"optimization\": ai_optimization,\n            \"criteria_used\": optimization_criteria,\n            \"timestamp\": current_app.config.get('TESTING', False) and \"2024-01-01T00:00:00Z\" or None\n        }\n\n        return api_response(data=response_data)\n\n    except Exception as e:\n        current_app.logger.error(f\"Error in optimize_project_team: {str(e)}\")\n        return handle_api_error(e)\n\n# Helper functions\ndef get_project_required_skills(project):\n    \"\"\"Estrae le competenze richieste dal progetto\"\"\"\n    # TODO: Implementare logica per estrarre skills richieste\n    # Per ora ritorna skills basate sul tipo progetto\n    skill_mapping = {\n        'service': ['Project Management', 'Communication', 'Problem Solving'],\n        'consulting': ['Analysis', 'Strategy', 'Presentation'],\n        'product': ['Development', 'Testing', 'Design'],\n        'rd': ['Research', 'Innovation', 'Technical Writing']\n    }\n    return skill_mapping.get(project.project_type, ['General'])\n\ndef get_available_resources():\n    \"\"\"Ottiene tutte le risorse disponibili con le loro competenze\"\"\"\n    users = User.query.filter_by(is_active=True).all()\n    resources = []\n\n    for user in users:\n        user_skills = []\n        # Usa detailed_skills invece di skills\n        for user_skill in user.detailed_skills:\n            user_skills.append({\n                \"name\": user_skill.skill.name,\n                \"category\": user_skill.skill.category,\n                \"proficiency_level\": user_skill.proficiency_level,\n                \"years_experience\": user_skill.years_experience\n            })\n\n        # Calcola utilizzo attuale\n        current_allocation = sum(\n            res.allocation_percentage for res in user.resource_assignments\n        )\n\n        resources.append({\n            \"user_id\": user.id,\n            \"name\": f\"{user.first_name} {user.last_name}\",\n            \"email\": user.email,\n            \"role\": user.role,\n            \"skills\": user_skills,\n            \"current_allocation\": current_allocation,\n            \"availability\": max(0, 100 - current_allocation),\n            \"hourly_rate\": 50  # TODO: Get from user profile\n        })\n\n    return resources\n\ndef get_current_allocations(project_id):\n    \"\"\"Ottiene le allocazioni attuali per un progetto\"\"\"\n    allocations = ProjectResource.query.filter_by(project_id=project_id).all()\n\n    result = []\n    for allocation in allocations:\n        result.append({\n            \"user_id\": allocation.user_id,\n            \"user_name\": f\"{allocation.user.first_name} {allocation.user.last_name}\",\n            \"allocation_percentage\": allocation.allocation_percentage,\n            \"role\": allocation.role\n        })\n\n    return result\n\ndef get_candidate_resources(user_ids):\n    \"\"\"Ottiene risorse candidate specifiche\"\"\"\n    users = User.query.filter(User.id.in_(user_ids), User.is_active == True).all()\n    resources = []\n\n    for user in users:\n        user_skills = []\n        # Usa detailed_skills invece di skills\n        for user_skill in user.detailed_skills:\n            user_skills.append({\n                \"name\": user_skill.skill.name,\n                \"category\": user_skill.skill.category,\n                \"proficiency_level\": user_skill.proficiency_level,\n                \"years_experience\": user_skill.years_experience\n            })\n\n        current_allocation = sum(\n            res.allocation_percentage for res in user.resource_assignments\n        )\n\n        resources.append({\n            \"user_id\": user.id,\n            \"name\": f\"{user.first_name} {user.last_name}\",\n            \"email\": user.email,\n            \"role\": user.role,\n            \"skills\": user_skills,\n            \"current_allocation\": current_allocation,\n            \"availability\": max(0, 100 - current_allocation),\n            \"hourly_rate\": 50\n        })\n\n    return resources\n"}