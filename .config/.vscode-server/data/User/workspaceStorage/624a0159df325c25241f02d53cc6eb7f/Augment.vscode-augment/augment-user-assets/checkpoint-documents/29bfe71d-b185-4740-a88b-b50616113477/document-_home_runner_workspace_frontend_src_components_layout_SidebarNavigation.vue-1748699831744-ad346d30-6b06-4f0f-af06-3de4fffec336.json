{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/SidebarNavigation.vue"}, "originalCode": "<template>\n  <div class=\"mt-5 flex-grow flex flex-col overflow-hidden\">\n    <nav class=\"flex-1 px-2 space-y-1\">\n      <!-- Dashboard -->\n      <SidebarNavItem\n        v-if=\"showDashboard\"\n        :item=\"{\n          name: 'Dashboard',\n          path: '/app/dashboard',\n          icon: 'dashboard'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Personale con submenu -->\n      <SidebarNavItemCollapsible\n        v-if=\"showPersonnel\"\n        :item=\"{\n          name: 'Personale',\n          icon: 'users',\n          children: [\n            { name: 'Directory', path: '/app/personnel', icon: 'directory' },\n            { name: 'Organigram<PERSON>', path: '/app/personnel/orgchart', icon: 'orgchart' },\n            { name: 'Competenze', path: '/app/personnel/skills', icon: 'skills' },\n            { name: 'Dipartimenti', path: '/app/personnel/departments', icon: 'departments', admin: true },\n            { name: 'Amministrazione', path: '/app/personnel/admin', icon: 'admin', admin: true }\n          ]\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Progetti -->\n      <SidebarNavItem\n        v-if=\"showProjects\"\n        :item=\"{\n          name: 'Progetti',\n          path: '/app/projects',\n          icon: 'projects'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- CRM -->\n      <SidebarNavItem\n        v-if=\"showCRM\"\n        :item=\"{\n          name: 'CRM',\n          path: '#',\n          icon: 'clients'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Prodotti e Servizi -->\n      <SidebarNavItem\n        v-if=\"showProducts\"\n        :item=\"{\n          name: 'Prodotti',\n          path: '#',\n          icon: 'products'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Performance -->\n      <SidebarNavItem\n        v-if=\"showPerformance\"\n        :item=\"{\n          name: 'Performance',\n          path: '#',\n          icon: 'reports'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Comunicazione -->\n      <SidebarNavItem\n        v-if=\"showCommunications\"\n        :item=\"{\n          name: 'Comunicazione',\n          path: '#',\n          icon: 'communications'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Bandi e Finanziamenti -->\n      <SidebarNavItem\n        v-if=\"showFunding\"\n        :item=\"{\n          name: 'Finanziamenti',\n          path: '#',\n          icon: 'funding'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Rendicontazione -->\n      <SidebarNavItem\n        v-if=\"showReports\"\n        :item=\"{\n          name: 'Rendicontazione',\n          path: '#',\n          icon: 'reporting'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Amministrazione con submenu -->\n      <SidebarNavItemCollapsible\n        v-if=\"showAdminSection\"\n        :item=\"{\n          name: 'Amministrazione',\n          icon: 'settings',\n          children: [\n            { name: 'Gestione Utenti', path: '/app/admin/users', icon: 'user-management' },\n            { name: 'Template KPI', path: '/app/admin/kpi-templates', icon: 'reports' }\n          ]\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n    </nav>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { usePermissions } from '@/composables/usePermissions'\nimport SidebarNavItem from './SidebarNavItem.vue'\nimport SidebarNavItemCollapsible from './SidebarNavItemCollapsible.vue'\n\nconst props = defineProps({\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['item-click'])\n\nconst { hasPermission, isAdmin } = usePermissions()\n\n// Controlli di visibilità basati sui permessi\nconst showDashboard = computed(() => hasPermission.value('view_dashboard'))\nconst showPersonnel = computed(() => hasPermission.value('view_personnel_data'))\nconst showProjects = computed(() => hasPermission.value('view_all_projects'))\nconst showCRM = computed(() => hasPermission.value('view_crm'))\nconst showProducts = computed(() => hasPermission.value('view_products'))\nconst showPerformance = computed(() => hasPermission.value('view_performance'))\nconst showCommunications = computed(() => hasPermission.value('view_communications'))\nconst showFunding = computed(() => hasPermission.value('view_funding'))\nconst showReports = computed(() => hasPermission.value('view_reports'))\nconst showAdminSection = computed(() => hasPermission.value('admin_access'))\n</script>", "modifiedCode": "<template>\n  <div class=\"mt-5 flex-grow flex flex-col overflow-hidden\">\n    <nav class=\"flex-1 px-2 space-y-1\">\n      <!-- Dashboard -->\n      <SidebarNavItem\n        v-if=\"showDashboard\"\n        :item=\"{\n          name: 'Dashboard',\n          path: '/app/dashboard',\n          icon: 'dashboard'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Personale con submenu -->\n      <SidebarNavItemCollapsible\n        v-if=\"showPersonnel\"\n        :item=\"{\n          name: 'Personale',\n          icon: 'users',\n          children: [\n            { name: 'Directory', path: '/app/personnel', icon: 'directory' },\n            { name: 'Organigramma', path: '/app/personnel/orgchart', icon: 'orgchart' },\n            { name: 'Competenze', path: '/app/personnel/skills', icon: 'skills' },\n            { name: 'Allocazione Risorse', path: '/app/personnel/allocation', icon: 'allocation' },\n            { name: 'Dipartimenti', path: '/app/personnel/departments', icon: 'departments', admin: true },\n            { name: 'Amministrazione', path: '/app/personnel/admin', icon: 'admin', admin: true }\n          ]\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Progetti -->\n      <SidebarNavItem\n        v-if=\"showProjects\"\n        :item=\"{\n          name: 'Progetti',\n          path: '/app/projects',\n          icon: 'projects'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- CRM -->\n      <SidebarNavItem\n        v-if=\"showCRM\"\n        :item=\"{\n          name: 'CRM',\n          path: '#',\n          icon: 'clients'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Prodotti e Servizi -->\n      <SidebarNavItem\n        v-if=\"showProducts\"\n        :item=\"{\n          name: 'Prodotti',\n          path: '#',\n          icon: 'products'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Performance -->\n      <SidebarNavItem\n        v-if=\"showPerformance\"\n        :item=\"{\n          name: 'Performance',\n          path: '#',\n          icon: 'reports'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Comunicazione -->\n      <SidebarNavItem\n        v-if=\"showCommunications\"\n        :item=\"{\n          name: 'Comunicazione',\n          path: '#',\n          icon: 'communications'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Bandi e Finanziamenti -->\n      <SidebarNavItem\n        v-if=\"showFunding\"\n        :item=\"{\n          name: 'Finanziamenti',\n          path: '#',\n          icon: 'funding'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Rendicontazione -->\n      <SidebarNavItem\n        v-if=\"showReports\"\n        :item=\"{\n          name: 'Rendicontazione',\n          path: '#',\n          icon: 'reporting'\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n\n      <!-- Amministrazione con submenu -->\n      <SidebarNavItemCollapsible\n        v-if=\"showAdminSection\"\n        :item=\"{\n          name: 'Amministrazione',\n          icon: 'settings',\n          children: [\n            { name: 'Gestione Utenti', path: '/app/admin/users', icon: 'user-management' },\n            { name: 'Template KPI', path: '/app/admin/kpi-templates', icon: 'reports' }\n          ]\n        }\"\n        :is-collapsed=\"isCollapsed\"\n        @click=\"$emit('item-click')\"\n      />\n    </nav>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\nimport { usePermissions } from '@/composables/usePermissions'\nimport SidebarNavItem from './SidebarNavItem.vue'\nimport SidebarNavItemCollapsible from './SidebarNavItemCollapsible.vue'\n\nconst props = defineProps({\n  isCollapsed: {\n    type: Boolean,\n    default: false\n  }\n})\n\ndefineEmits(['item-click'])\n\nconst { hasPermission, isAdmin } = usePermissions()\n\n// Controlli di visibilità basati sui permessi\nconst showDashboard = computed(() => hasPermission.value('view_dashboard'))\nconst showPersonnel = computed(() => hasPermission.value('view_personnel_data'))\nconst showProjects = computed(() => hasPermission.value('view_all_projects'))\nconst showCRM = computed(() => hasPermission.value('view_crm'))\nconst showProducts = computed(() => hasPermission.value('view_products'))\nconst showPerformance = computed(() => hasPermission.value('view_performance'))\nconst showCommunications = computed(() => hasPermission.value('view_communications'))\nconst showFunding = computed(() => hasPermission.value('view_funding'))\nconst showReports = computed(() => hasPermission.value('view_reports'))\nconst showAdminSection = computed(() => hasPermission.value('admin_access'))\n</script>"}