{"id": "shard-29bfe71d-b185-4740-a88b-b50616113477", "checkpoints": {"29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue": [{"sourceToolCallRequestId": "0fa60db4-ad9d-43e6-97de-b8d77bbda92f", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "fa80cdaa-ee4b-4bfa-a2d8-56f0915fde54", "timestamp": 1748696218527, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "e1e3c0eb-5b20-4de9-95f1-f1356e5a61cc", "timestamp": 1748696236557, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/projects.py": [{"sourceToolCallRequestId": "53ba9b2f-3424-45f3-af8e-256ec1c6b754", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}, {"sourceToolCallRequestId": "3d3012ba-07a9-4fb9-be54-7125335b75dd", "timestamp": 1748696265884, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}, {"sourceToolCallRequestId": "be256abf-59a0-4594-8390-de7a27d87314", "timestamp": 1748696282782, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/ai_services.py": [{"sourceToolCallRequestId": "09e9669e-35ac-4f6e-8cc6-016b3ebf0815", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/ai_services.py"}}}, {"sourceToolCallRequestId": "4f26eb9b-f85f-4dd7-aa97-25b3ecc3df52", "timestamp": 1748696518433, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/ai_services.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/ai_resources.py": [{"sourceToolCallRequestId": "a5755d45-d367-4dc0-9374-9dc3eb0e1a3d", "timestamp": 1748696562606, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}, {"sourceToolCallRequestId": "d3860d93-5cda-4e71-acec-c788f317db7b", "timestamp": 1748697014054, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}, {"sourceToolCallRequestId": "18aa9f74-4415-47b4-8d8f-376e42e6273c", "timestamp": 1748697140547, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}, {"sourceToolCallRequestId": "0a4ae073-b1d5-4358-bcbc-99f0af250d98", "timestamp": 1748697154495, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}, {"sourceToolCallRequestId": "80604f93-de51-470d-892e-4a23ba6b87d0", "timestamp": 1748697733709, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}, {"sourceToolCallRequestId": "7eecdda3-ffd5-48af-bc83-c2eba7ad04c2", "timestamp": 1748697747374, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/ai_resources.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py": [{"sourceToolCallRequestId": "b714cea8-4d27-4b57-9c8a-e1120c86224d", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}, {"sourceToolCallRequestId": "bd163b86-a6cf-4af6-acf4-15184e6e7b74", "timestamp": 1748696582438, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}, {"sourceToolCallRequestId": "8da62335-7343-4fdb-8395-a510414856a5", "timestamp": 1748697025669, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectResourceAllocation.vue": [{"sourceToolCallRequestId": "ba7941b3-fa1b-46aa-b06d-2469019cf02b", "timestamp": 1748696650322, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectResourceAllocation.vue"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/ProjectView.vue": [{"sourceToolCallRequestId": "4517489c-2496-490b-b074-78116130c7b2", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}}}, {"sourceToolCallRequestId": "0e4c0363-5a3a-4b31-9509-80012f1b44b5", "timestamp": 1748696659066, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}}}, {"sourceToolCallRequestId": "eb233234-35c4-401f-88ba-5ebc47365bf8", "timestamp": 1748696672928, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}}}, {"sourceToolCallRequestId": "c2a0dab6-fd19-47e5-9ace-d374a1530286", "timestamp": 1748696685365, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}}}, {"sourceToolCallRequestId": "9f11b63b-17cc-4a17-84d0-765986186115", "timestamp": 1748696700825, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/tasks/tasks.json": [{"sourceToolCallRequestId": "384a6110-343e-4c30-9060-c49aecc81256", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tasks/tasks.json"}}}, {"sourceToolCallRequestId": "6af86c1a-4120-4099-a61d-e0a3ab4cbb5c", "timestamp": 1748696716447, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tasks/tasks.json"}}}, {"sourceToolCallRequestId": "96659895-b6ed-4373-a74e-51819a3072c9", "timestamp": 1748696988238, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tasks/tasks.json"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/tests/test_ai_resources.py": [{"sourceToolCallRequestId": "e64f0664-3b08-4aa3-b2ce-6b180836382b", "timestamp": 1748696750793, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/test_ai_resources.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/docs/task_2.4_ai_resource_allocation.md": [{"sourceToolCallRequestId": "66791f1b-3666-43a3-9c25-35afb415acc5", "timestamp": 1748696779214, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task_2.4_ai_resource_allocation.md"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/models.py": [{"sourceToolCallRequestId": "6fff21f3-301d-4498-910a-2c39dd99566b", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}}}, {"sourceToolCallRequestId": "958b7c99-64a1-4512-9c91-ebe32c31331d", "timestamp": 1748697059582, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}}}, {"sourceToolCallRequestId": "2cc2956c-9bfb-4526-9180-cbc82f28aa2b", "timestamp": 1748697078849, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}}}, {"sourceToolCallRequestId": "5e151ea5-0b2b-4ebd-a769-658cea2a9384", "timestamp": 1748697127280, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/base.py": [{"sourceToolCallRequestId": "0003cad2-f17a-460d-9a62-ca044dd4fdc2", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}}}, {"sourceToolCallRequestId": "bfc31999-de79-4116-8110-696061b605eb", "timestamp": 1748697790781, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}}}, {"sourceToolCallRequestId": "cd82e202-90c8-4786-8fa9-189938da90c3", "timestamp": 1748697863712, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}}}, {"sourceToolCallRequestId": "dc867e8f-e80e-4e47-816a-4493d52ef003", "timestamp": 1748697877689, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/auth.py": [{"sourceToolCallRequestId": "226ea7db-3aac-49cf-9f1c-9293fb60a2fd", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/auth.py"}}}, {"sourceToolCallRequestId": "3a056ae6-f05c-4475-9c55-5e97d03274cc", "timestamp": 1748697849643, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/auth.py"}}}]}, "metadata": {"checkpointDocumentIds": ["29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/projects.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/ai_services.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/ai_resources.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/app.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/components/ProjectResourceAllocation.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/ProjectView.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/tasks/tasks.json", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/tests/test_ai_resources.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/docs/task_2.4_ai_resource_allocation.md", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/models.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/base.py", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/auth.py"], "size": 1633324, "checkpointCount": 38, "lastModified": 1748697878577}}