{"id": "shard-29bfe71d-b185-4740-a88b-b50616113477", "checkpoints": {"29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue": [{"sourceToolCallRequestId": "0fa60db4-ad9d-43e6-97de-b8d77bbda92f", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "fa80cdaa-ee4b-4bfa-a2d8-56f0915fde54", "timestamp": 1748696218527, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}, {"sourceToolCallRequestId": "e1e3c0eb-5b20-4de9-95f1-f1356e5a61cc", "timestamp": 1748696236557, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}}], "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/projects.py": [{"sourceToolCallRequestId": "53ba9b2f-3424-45f3-af8e-256ec1c6b754", "timestamp": 0, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}, {"sourceToolCallRequestId": "3d3012ba-07a9-4fb9-be54-7125335b75dd", "timestamp": 1748696265884, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}, {"sourceToolCallRequestId": "be256abf-59a0-4594-8390-de7a27d87314", "timestamp": 1748696282782, "conversationId": "29bfe71d-b185-4740-a88b-b50616113477", "documentMetadata": {"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}}]}, "metadata": {"checkpointDocumentIds": ["29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", "29bfe71d-b185-4740-a88b-b50616113477:/home/<USER>/workspace/backend/blueprints/api/projects.py"], "size": 309288, "checkpointCount": 6, "lastModified": 1748696284153}}