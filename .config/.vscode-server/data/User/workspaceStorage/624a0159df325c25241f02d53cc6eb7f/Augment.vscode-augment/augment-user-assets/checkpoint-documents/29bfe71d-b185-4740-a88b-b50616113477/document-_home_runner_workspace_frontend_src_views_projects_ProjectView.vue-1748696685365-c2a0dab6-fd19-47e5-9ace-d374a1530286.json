{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/ProjectView.vue"}, "originalCode": "<template>\n  <div class=\"project-view\">\n    <!-- Project Header -->\n    <ProjectHeader\n      :project=\"project\"\n      :loading=\"loading\"\n      @edit=\"handleEdit\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- Project Tabs -->\n    <TabNavigation\n      v-model=\"activeTab\"\n      :tabs=\"availableTabs\"\n      class=\"mb-6\"\n    />\n\n    <!-- Tab Content -->\n    <div class=\"tab-content\">\n      <keep-alive>\n        <component\n          :is=\"currentTabComponent\"\n          :project=\"project\"\n          :loading=\"loading\"\n        />\n      </keep-alive>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useProjectsStore } from '@/stores/projects'\nimport { useAuthStore } from '@/stores/auth'\n\n// Components\nimport ProjectHeader from './components/ProjectHeader.vue'\nimport TabNavigation from '@/components/ui/TabNavigation.vue'\nimport ProjectOverview from './components/ProjectOverview.vue'\nimport ProjectTasks from './components/ProjectTasks.vue'\nimport ProjectTeam from './components/ProjectTeam.vue'\nimport ProjectExpenses from './components/ProjectExpenses.vue'\nimport ProjectKPI from './components/ProjectKPI.vue'\nimport ProjectGantt from './components/ProjectGantt.vue'\nimport ProjectTimesheet from './components/ProjectTimesheet.vue'\nimport ProjectResourceAllocation from './components/ProjectResourceAllocation.vue'\n\n// Stores\nconst projectsStore = useProjectsStore()\nconst authStore = useAuthStore()\nconst route = useRoute()\nconst router = useRouter()\n\n// State\nconst loading = ref(true)\nconst activeTab = ref('overview')\n\n// Computed\nconst project = computed(() => projectsStore.currentProject)\n\nconst availableTabs = computed(() => {\n  const tabs = [\n    { id: 'overview', label: 'Panoramica', icon: 'chart-bar' },\n    { id: 'tasks', label: 'Task', icon: 'clipboard-list' },\n    { id: 'team', label: 'Team', icon: 'users' },\n    { id: 'resources', label: 'Allocazione Risorse', icon: 'user-group' },\n    { id: 'gantt', label: 'Gantt', icon: 'calendar' },\n    { id: 'timesheet', label: 'Timesheet', icon: 'clock' },\n    { id: 'expenses', label: 'Spese', icon: 'credit-card' },\n    { id: 'kpi', label: 'KPI & Analytics', icon: 'trending-up' }\n  ]\n\n  // Filter tabs based on permissions (keep all for now, add permission checks later if needed)\n  return tabs.filter(tab => {\n    // Basic tabs always visible\n    if (['overview', 'tasks', 'gantt', 'team', 'timesheet'].includes(tab.id)) {\n      return true\n    }\n\n    // Advanced tabs based on permissions\n    if (tab.id === 'kpi' && authStore.hasPermission('view_reports')) {\n      return true\n    }\n\n    if (tab.id === 'expenses' && authStore.hasPermission('manage_expenses')) {\n      return true\n    }\n\n    return false\n  })\n})\n\nconst currentTabComponent = computed(() => {\n  const components = {\n    overview: ProjectOverview,\n    tasks: ProjectTasks,\n    team: ProjectTeam,\n    expenses: ProjectExpenses,\n    kpi: ProjectKPI,\n    gantt: ProjectGantt,\n    timesheet: ProjectTimesheet\n  }\n  return components[activeTab.value] || ProjectOverview\n})\n\n// Methods\nconst loadProject = async () => {\n  loading.value = true\n  try {\n    const projectId = route.params.id\n    await projectsStore.fetchProject(projectId)\n  } catch (error) {\n    console.error('Error loading project:', error)\n    // Handle error (show toast, redirect, etc.)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst handleEdit = () => {\n  router.push(`/projects/${route.params.id}/edit`)\n}\n\nconst handleDelete = async () => {\n  if (confirm('Sei sicuro di voler eliminare questo progetto?')) {\n    try {\n      await projectsStore.deleteProject(route.params.id)\n      router.push('/projects')\n    } catch (error) {\n      console.error('Error deleting project:', error)\n    }\n  }\n}\n\n// Watchers\nwatch(() => route.params.id, (newId, oldId) => {\n  if (newId && newId !== oldId) {\n    loadProject()\n  }\n})\n\nwatch(() => route.hash, (newHash) => {\n  if (newHash) {\n    const tab = newHash.replace('#', '')\n    if (availableTabs.value.find(t => t.id === tab) && activeTab.value !== tab) {\n      activeTab.value = tab\n    }\n  }\n}, { immediate: true })\n\nwatch(activeTab, (newTab) => {\n  // Update URL hash without triggering navigation\n  const newHash = `#${newTab}`\n  if (route.hash !== newHash) {\n    router.replace({ ...route, hash: newHash })\n  }\n})\n\n// Lifecycle\nonMounted(() => {\n  // Set initial tab from URL hash\n  if (route.hash) {\n    const tab = route.hash.replace('#', '')\n    if (availableTabs.value.find(t => t.id === tab)) {\n      activeTab.value = tab\n    }\n  }\n\n  loadProject()\n})\n</script>\n\n<style scoped>\n.project-view {\n  @apply container mx-auto px-4 py-6;\n}\n\n.tab-content {\n  @apply min-h-96;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"project-view\">\n    <!-- Project Header -->\n    <ProjectHeader\n      :project=\"project\"\n      :loading=\"loading\"\n      @edit=\"handleEdit\"\n      @delete=\"handleDelete\"\n    />\n\n    <!-- Project Tabs -->\n    <TabNavigation\n      v-model=\"activeTab\"\n      :tabs=\"availableTabs\"\n      class=\"mb-6\"\n    />\n\n    <!-- Tab Content -->\n    <div class=\"tab-content\">\n      <keep-alive>\n        <component\n          :is=\"currentTabComponent\"\n          :project=\"project\"\n          :loading=\"loading\"\n        />\n      </keep-alive>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { useProjectsStore } from '@/stores/projects'\nimport { useAuthStore } from '@/stores/auth'\n\n// Components\nimport ProjectHeader from './components/ProjectHeader.vue'\nimport TabNavigation from '@/components/ui/TabNavigation.vue'\nimport ProjectOverview from './components/ProjectOverview.vue'\nimport ProjectTasks from './components/ProjectTasks.vue'\nimport ProjectTeam from './components/ProjectTeam.vue'\nimport ProjectExpenses from './components/ProjectExpenses.vue'\nimport ProjectKPI from './components/ProjectKPI.vue'\nimport ProjectGantt from './components/ProjectGantt.vue'\nimport ProjectTimesheet from './components/ProjectTimesheet.vue'\nimport ProjectResourceAllocation from './components/ProjectResourceAllocation.vue'\n\n// Stores\nconst projectsStore = useProjectsStore()\nconst authStore = useAuthStore()\nconst route = useRoute()\nconst router = useRouter()\n\n// State\nconst loading = ref(true)\nconst activeTab = ref('overview')\n\n// Computed\nconst project = computed(() => projectsStore.currentProject)\n\nconst availableTabs = computed(() => {\n  const tabs = [\n    { id: 'overview', label: 'Panoramica', icon: 'chart-bar' },\n    { id: 'tasks', label: 'Task', icon: 'clipboard-list' },\n    { id: 'team', label: 'Team', icon: 'users' },\n    { id: 'resources', label: 'Allocazione Risorse', icon: 'user-group' },\n    { id: 'gantt', label: 'Gantt', icon: 'calendar' },\n    { id: 'timesheet', label: 'Timesheet', icon: 'clock' },\n    { id: 'expenses', label: 'Spese', icon: 'credit-card' },\n    { id: 'kpi', label: 'KPI & Analytics', icon: 'trending-up' }\n  ]\n\n  // Filter tabs based on permissions (keep all for now, add permission checks later if needed)\n  return tabs.filter(tab => {\n    // Basic tabs always visible\n    if (['overview', 'tasks', 'gantt', 'team', 'timesheet', 'resources'].includes(tab.id)) {\n      return true\n    }\n\n    // Advanced tabs based on permissions\n    if (tab.id === 'kpi' && authStore.hasPermission('view_reports')) {\n      return true\n    }\n\n    if (tab.id === 'expenses' && authStore.hasPermission('manage_expenses')) {\n      return true\n    }\n\n    return false\n  })\n})\n\nconst currentTabComponent = computed(() => {\n  const components = {\n    overview: ProjectOverview,\n    tasks: ProjectTasks,\n    team: ProjectTeam,\n    expenses: ProjectExpenses,\n    kpi: ProjectKPI,\n    gantt: ProjectGantt,\n    timesheet: ProjectTimesheet\n  }\n  return components[activeTab.value] || ProjectOverview\n})\n\n// Methods\nconst loadProject = async () => {\n  loading.value = true\n  try {\n    const projectId = route.params.id\n    await projectsStore.fetchProject(projectId)\n  } catch (error) {\n    console.error('Error loading project:', error)\n    // Handle error (show toast, redirect, etc.)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst handleEdit = () => {\n  router.push(`/projects/${route.params.id}/edit`)\n}\n\nconst handleDelete = async () => {\n  if (confirm('Sei sicuro di voler eliminare questo progetto?')) {\n    try {\n      await projectsStore.deleteProject(route.params.id)\n      router.push('/projects')\n    } catch (error) {\n      console.error('Error deleting project:', error)\n    }\n  }\n}\n\n// Watchers\nwatch(() => route.params.id, (newId, oldId) => {\n  if (newId && newId !== oldId) {\n    loadProject()\n  }\n})\n\nwatch(() => route.hash, (newHash) => {\n  if (newHash) {\n    const tab = newHash.replace('#', '')\n    if (availableTabs.value.find(t => t.id === tab) && activeTab.value !== tab) {\n      activeTab.value = tab\n    }\n  }\n}, { immediate: true })\n\nwatch(activeTab, (newTab) => {\n  // Update URL hash without triggering navigation\n  const newHash = `#${newTab}`\n  if (route.hash !== newHash) {\n    router.replace({ ...route, hash: newHash })\n  }\n})\n\n// Lifecycle\nonMounted(() => {\n  // Set initial tab from URL hash\n  if (route.hash) {\n    const tab = route.hash.replace('#', '')\n    if (availableTabs.value.find(t => t.id === tab)) {\n      activeTab.value = tab\n    }\n  }\n\n  loadProject()\n})\n</script>\n\n<style scoped>\n.project-view {\n  @apply container mx-auto px-4 py-6;\n}\n\n.tab-content {\n  @apply min-h-96;\n}\n</style>\n"}