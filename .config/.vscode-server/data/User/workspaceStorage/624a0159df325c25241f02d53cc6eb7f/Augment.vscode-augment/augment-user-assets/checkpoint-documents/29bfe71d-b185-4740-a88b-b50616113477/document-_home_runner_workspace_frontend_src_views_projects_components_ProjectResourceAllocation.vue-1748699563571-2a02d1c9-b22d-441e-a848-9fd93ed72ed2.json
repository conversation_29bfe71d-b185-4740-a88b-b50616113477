{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectResourceAllocation.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header con AI Assistant -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Allocazione Risorse\n            </h3>\n            <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n              Gestisci l'allocazione delle risorse con assistenza AI\n            </p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <!-- AI Analysis Button -->\n            <button\n              @click=\"runAIAnalysis\"\n              :disabled=\"analyzingWithAI\"\n              class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50\"\n            >\n              <svg v-if=\"!analyzingWithAI\" class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n              </svg>\n              <svg v-else class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              {{ analyzingWithAI ? 'Analizzando...' : 'Analisi AI' }}\n            </button>\n\n            <!-- Add Resource Button -->\n            <button\n              @click=\"showAddResourceModal = true\"\n              class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n            >\n              <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              Aggiungi Risorsa\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Insights Panel -->\n      <div v-if=\"aiInsights\" class=\"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700\">\n        <div class=\"flex items-start space-x-3\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n            </svg>\n          </div>\n          <div class=\"flex-1\">\n            <h4 class=\"text-sm font-medium text-purple-900 dark:text-purple-100\">\n              Insights AI - Efficienza: {{ aiInsights.efficiency_score }}%\n            </h4>\n            <div class=\"mt-2 space-y-2\">\n              <div v-for=\"insight in aiInsights.optimization_insights\" :key=\"insight\" class=\"text-sm text-purple-700 dark:text-purple-300\">\n                • {{ insight }}\n              </div>\n            </div>\n\n            <!-- AI Recommendations -->\n            <div v-if=\"aiInsights.recommended_allocations?.length\" class=\"mt-3\">\n              <h5 class=\"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2\">\n                Raccomandazioni AI:\n              </h5>\n              <div class=\"space-y-2\">\n                <div v-for=\"rec in aiInsights.recommended_allocations\" :key=\"rec.user_id\"\n                     class=\"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3\">\n                  <div class=\"flex items-center space-x-3\">\n                    <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center\">\n                      <span class=\"text-xs font-medium text-purple-600 dark:text-purple-300\">\n                        {{ rec.user_name?.charAt(0) }}\n                      </span>\n                    </div>\n                    <div>\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ rec.user_name }}</p>\n                      <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ rec.role }} - {{ rec.allocation }}%</p>\n                    </div>\n                  </div>\n                  <button\n                    @click=\"applyAIRecommendation(rec)\"\n                    class=\"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700\"\n                  >\n                    Applica\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <button @click=\"aiInsights = null\" class=\"flex-shrink-0 text-purple-400 hover:text-purple-600\">\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Current Allocations -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Allocazioni Attuali\n        </h4>\n      </div>\n\n      <div v-if=\"loading\" class=\"p-6\">\n        <div class=\"animate-pulse space-y-4\">\n          <div v-for=\"i in 3\" :key=\"i\" class=\"flex items-center space-x-4\">\n            <div class=\"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full\"></div>\n            <div class=\"flex-1 space-y-2\">\n              <div class=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"></div>\n              <div class=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n            </div>\n            <div class=\"w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n          </div>\n        </div>\n      </div>\n\n      <div v-else-if=\"!allocations.length\" class=\"p-6 text-center\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna risorsa allocata</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti.\n        </p>\n      </div>\n\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"allocation in allocations\" :key=\"allocation.id\"\n             class=\"p-6 hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center\">\n                <span class=\"text-sm font-medium text-primary-600 dark:text-primary-300\">\n                  {{ allocation.user_name?.charAt(0) }}\n                </span>\n              </div>\n              <div>\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ allocation.user_name }}\n                </h4>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ allocation.role || 'Team Member' }}\n                </p>\n              </div>\n            </div>\n\n            <div class=\"flex items-center space-x-4\">\n              <!-- Allocation Percentage -->\n              <div class=\"text-right\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ allocation.allocation_percentage }}%\n                </div>\n                <div class=\"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n                  <div\n                    class=\"h-2 rounded-full\"\n                    :class=\"getAllocationClass(allocation.allocation_percentage)\"\n                    :style=\"{ width: allocation.allocation_percentage + '%' }\"\n                  ></div>\n                </div>\n              </div>\n\n              <!-- Actions -->\n              <div class=\"flex items-center space-x-2\">\n                <button\n                  @click=\"editAllocation(allocation)\"\n                  class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </button>\n                <button\n                  @click=\"removeAllocation(allocation)\"\n                  class=\"text-red-400 hover:text-red-600\"\n                >\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Resource Utilization Chart -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Utilizzo Risorse\n        </h4>\n      </div>\n      <div class=\"p-6\">\n        <div class=\"space-y-4\">\n          <div v-for=\"resource in resourceUtilization\" :key=\"resource.user_id\" class=\"flex items-center\">\n            <div class=\"w-32 text-sm text-gray-600 dark:text-gray-400\">\n              {{ resource.user_name }}\n            </div>\n            <div class=\"flex-1 mx-4\">\n              <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                <div\n                  class=\"h-3 rounded-full transition-all duration-300\"\n                  :class=\"getUtilizationClass(resource.total_allocation)\"\n                  :style=\"{ width: Math.min(resource.total_allocation, 100) + '%' }\"\n                ></div>\n              </div>\n            </div>\n            <div class=\"w-16 text-sm text-right font-medium\"\n                 :class=\"getUtilizationTextClass(resource.total_allocation)\">\n              {{ resource.total_allocation }}%\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add Resource Modal -->\n    <div v-if=\"showAddResourceModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Risorsa\n          </h3>\n\n          <form @submit.prevent=\"addResource\">\n            <div class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Utente\n                </label>\n                <select v-model=\"newAllocation.user_id\" required\n                        class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Seleziona utente...</option>\n                  <option v-for=\"user in availableUsers\" :key=\"user.id\" :value=\"user.id\">\n                    {{ user.full_name }} ({{ user.role }})\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Ruolo nel Progetto\n                </label>\n                <input v-model=\"newAllocation.role\" type=\"text\"\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                       placeholder=\"es. Developer, Designer, PM\">\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Allocazione (%)\n                </label>\n                <input v-model.number=\"newAllocation.allocation_percentage\" type=\"number\" min=\"1\" max=\"100\" required\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button type=\"button\" @click=\"showAddResourceModal = false\"\n                      class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500\">\n                Annulla\n              </button>\n              <button type=\"submit\" :disabled=\"saving\"\n                      class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50\">\n                {{ saving ? 'Salvando...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, required: true }\n})\n\n// Stores\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(true)\nconst saving = ref(false)\nconst analyzingWithAI = ref(false)\nconst allocations = ref([])\nconst availableUsers = ref([])\nconst resourceUtilization = ref([])\nconst aiInsights = ref(null)\nconst showAddResourceModal = ref(false)\nconst showEditResourceModal = ref(false)\n\nconst newAllocation = ref({\n  user_id: '',\n  role: '',\n  allocation_percentage: 100\n})\n\nconst editingAllocation = ref({\n  id: null,\n  role: '',\n  allocation_percentage: 100\n})\n\n// Computed\nconst projectId = computed(() => props.project?.id)\n\n// Methods\nconst loadAllocations = async () => {\n  if (!projectId.value) return\n\n  loading.value = true\n  try {\n    const response = await fetch(`/api/resources?project_id=${projectId.value}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nel caricamento allocazioni')\n\n    const result = await response.json()\n    allocations.value = result.data?.resources || []\n\n    // Load resource utilization\n    await loadResourceUtilization()\n  } catch (error) {\n    console.error('Error loading allocations:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadAvailableUsers = async () => {\n  try {\n    const response = await fetch('/api/personnel', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nel caricamento utenti')\n\n    const result = await response.json()\n    availableUsers.value = result.data?.users || []\n  } catch (error) {\n    console.error('Error loading users:', error)\n  }\n}\n\nconst loadResourceUtilization = async () => {\n  // Mock data for now - in real implementation, this would come from API\n  resourceUtilization.value = allocations.value.map(allocation => ({\n    user_id: allocation.user_id,\n    user_name: allocation.user_name,\n    total_allocation: allocation.allocation_percentage + Math.floor(Math.random() * 30) // Mock other project allocations\n  }))\n}\n\nconst runAIAnalysis = async () => {\n  if (!projectId.value) return\n\n  analyzingWithAI.value = true\n  try {\n    const response = await fetch(`/api/ai-resources/analyze-allocation/${projectId.value}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        include_suggestions: true,\n        analysis_depth: 'detailed'\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'analisi AI')\n\n    const result = await response.json()\n    aiInsights.value = result.data?.analysis || null\n  } catch (error) {\n    console.error('Error in AI analysis:', error)\n    alert('Errore nell\\'analisi AI: ' + error.message)\n  } finally {\n    analyzingWithAI.value = false\n  }\n}\n\nconst addResource = async () => {\n  saving.value = true\n  try {\n    const response = await fetch('/api/resources', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        project_id: projectId.value,\n        ...newAllocation.value\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'aggiunta risorsa')\n\n    await loadAllocations()\n    showAddResourceModal.value = false\n    newAllocation.value = { user_id: '', role: '', allocation_percentage: 100 }\n  } catch (error) {\n    console.error('Error adding resource:', error)\n    alert('Errore nell\\'aggiunta risorsa: ' + error.message)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editAllocation = (allocation) => {\n  editingAllocation.value = {\n    id: allocation.id,\n    role: allocation.role,\n    allocation_percentage: allocation.allocation_percentage\n  }\n  showEditResourceModal.value = true\n}\n\nconst updateAllocation = async () => {\n  saving.value = true\n  try {\n    const response = await fetch(`/api/resources/${editingAllocation.value.id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        role: editingAllocation.value.role,\n        allocation_percentage: editingAllocation.value.allocation_percentage\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'aggiornamento allocazione')\n\n    await loadAllocations()\n    showEditResourceModal.value = false\n    editingAllocation.value = { id: null, role: '', allocation_percentage: 100 }\n  } catch (error) {\n    console.error('Error updating allocation:', error)\n    alert('Errore nell\\'aggiornamento: ' + error.message)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst removeAllocation = async (allocation) => {\n  if (!confirm('Sei sicuro di voler rimuovere questa allocazione?')) return\n\n  try {\n    const response = await fetch(`/api/resources/${allocation.id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nella rimozione')\n\n    await loadAllocations()\n  } catch (error) {\n    console.error('Error removing allocation:', error)\n    alert('Errore nella rimozione: ' + error.message)\n  }\n}\n\nconst applyAIRecommendation = async (recommendation) => {\n  try {\n    await fetch('/api/resources', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        project_id: projectId.value,\n        user_id: recommendation.user_id,\n        role: recommendation.role,\n        allocation_percentage: recommendation.allocation\n      })\n    })\n\n    await loadAllocations()\n  } catch (error) {\n    console.error('Error applying AI recommendation:', error)\n  }\n}\n\n// Utility functions\nconst getAllocationClass = (percentage) => {\n  if (percentage >= 80) return 'bg-red-500'\n  if (percentage >= 60) return 'bg-yellow-500'\n  return 'bg-green-500'\n}\n\nconst getUtilizationClass = (percentage) => {\n  if (percentage > 100) return 'bg-red-500'\n  if (percentage >= 90) return 'bg-yellow-500'\n  return 'bg-green-500'\n}\n\nconst getUtilizationTextClass = (percentage) => {\n  if (percentage > 100) return 'text-red-600 dark:text-red-400'\n  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'\n  return 'text-green-600 dark:text-green-400'\n}\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadAllocations()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  loadAvailableUsers()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header con AI Assistant -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <div>\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Allocazione Risorse\n            </h3>\n            <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n              Gestisci l'allocazione delle risorse con assistenza AI\n            </p>\n          </div>\n          <div class=\"flex items-center space-x-3\">\n            <!-- AI Analysis Button -->\n            <button\n              @click=\"runAIAnalysis\"\n              :disabled=\"analyzingWithAI\"\n              class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50\"\n            >\n              <svg v-if=\"!analyzingWithAI\" class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n              </svg>\n              <svg v-else class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              {{ analyzingWithAI ? 'Analizzando...' : 'Analisi AI' }}\n            </button>\n\n            <!-- Add Resource Button -->\n            <button\n              @click=\"showAddResourceModal = true\"\n              class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n            >\n              <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n              </svg>\n              Aggiungi Risorsa\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Insights Panel -->\n      <div v-if=\"aiInsights\" class=\"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700\">\n        <div class=\"flex items-start space-x-3\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n            </svg>\n          </div>\n          <div class=\"flex-1\">\n            <h4 class=\"text-sm font-medium text-purple-900 dark:text-purple-100\">\n              Insights AI - Efficienza: {{ aiInsights.efficiency_score }}%\n            </h4>\n            <div class=\"mt-2 space-y-2\">\n              <div v-for=\"insight in aiInsights.optimization_insights\" :key=\"insight\" class=\"text-sm text-purple-700 dark:text-purple-300\">\n                • {{ insight }}\n              </div>\n            </div>\n\n            <!-- AI Recommendations -->\n            <div v-if=\"aiInsights.recommended_allocations?.length\" class=\"mt-3\">\n              <h5 class=\"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2\">\n                Raccomandazioni AI:\n              </h5>\n              <div class=\"space-y-2\">\n                <div v-for=\"rec in aiInsights.recommended_allocations\" :key=\"rec.user_id\"\n                     class=\"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3\">\n                  <div class=\"flex items-center space-x-3\">\n                    <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center\">\n                      <span class=\"text-xs font-medium text-purple-600 dark:text-purple-300\">\n                        {{ rec.user_name?.charAt(0) }}\n                      </span>\n                    </div>\n                    <div>\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ rec.user_name }}</p>\n                      <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ rec.role }} - {{ rec.allocation }}%</p>\n                    </div>\n                  </div>\n                  <button\n                    @click=\"applyAIRecommendation(rec)\"\n                    class=\"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700\"\n                  >\n                    Applica\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n          <button @click=\"aiInsights = null\" class=\"flex-shrink-0 text-purple-400 hover:text-purple-600\">\n            <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Current Allocations -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Allocazioni Attuali\n        </h4>\n      </div>\n\n      <div v-if=\"loading\" class=\"p-6\">\n        <div class=\"animate-pulse space-y-4\">\n          <div v-for=\"i in 3\" :key=\"i\" class=\"flex items-center space-x-4\">\n            <div class=\"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full\"></div>\n            <div class=\"flex-1 space-y-2\">\n              <div class=\"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4\"></div>\n              <div class=\"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2\"></div>\n            </div>\n            <div class=\"w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n          </div>\n        </div>\n      </div>\n\n      <div v-else-if=\"!allocations.length\" class=\"p-6 text-center\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna risorsa allocata</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti.\n        </p>\n      </div>\n\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"allocation in allocations\" :key=\"allocation.id\"\n             class=\"p-6 hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center\">\n                <span class=\"text-sm font-medium text-primary-600 dark:text-primary-300\">\n                  {{ allocation.user_name?.charAt(0) }}\n                </span>\n              </div>\n              <div>\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ allocation.user_name }}\n                </h4>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ allocation.role || 'Team Member' }}\n                </p>\n              </div>\n            </div>\n\n            <div class=\"flex items-center space-x-4\">\n              <!-- Allocation Percentage -->\n              <div class=\"text-right\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ allocation.allocation_percentage }}%\n                </div>\n                <div class=\"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n                  <div\n                    class=\"h-2 rounded-full\"\n                    :class=\"getAllocationClass(allocation.allocation_percentage)\"\n                    :style=\"{ width: allocation.allocation_percentage + '%' }\"\n                  ></div>\n                </div>\n              </div>\n\n              <!-- Actions -->\n              <div class=\"flex items-center space-x-2\">\n                <button\n                  @click=\"editAllocation(allocation)\"\n                  class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                  </svg>\n                </button>\n                <button\n                  @click=\"removeAllocation(allocation)\"\n                  class=\"text-red-400 hover:text-red-600\"\n                >\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Resource Utilization Chart -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Utilizzo Risorse\n        </h4>\n      </div>\n      <div class=\"p-6\">\n        <div class=\"space-y-4\">\n          <div v-for=\"resource in resourceUtilization\" :key=\"resource.user_id\" class=\"flex items-center\">\n            <div class=\"w-32 text-sm text-gray-600 dark:text-gray-400\">\n              {{ resource.user_name }}\n            </div>\n            <div class=\"flex-1 mx-4\">\n              <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                <div\n                  class=\"h-3 rounded-full transition-all duration-300\"\n                  :class=\"getUtilizationClass(resource.total_allocation)\"\n                  :style=\"{ width: Math.min(resource.total_allocation, 100) + '%' }\"\n                ></div>\n              </div>\n            </div>\n            <div class=\"w-16 text-sm text-right font-medium\"\n                 :class=\"getUtilizationTextClass(resource.total_allocation)\">\n              {{ resource.total_allocation }}%\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Add Resource Modal -->\n    <div v-if=\"showAddResourceModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Risorsa\n          </h3>\n\n          <form @submit.prevent=\"addResource\">\n            <div class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Utente\n                </label>\n                <select v-model=\"newAllocation.user_id\" required\n                        class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Seleziona utente...</option>\n                  <option v-for=\"user in availableUsers\" :key=\"user.id\" :value=\"user.id\">\n                    {{ user.full_name }} ({{ user.role }})\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Ruolo nel Progetto\n                </label>\n                <input v-model=\"newAllocation.role\" type=\"text\"\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                       placeholder=\"es. Developer, Designer, PM\">\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Allocazione (%)\n                </label>\n                <input v-model.number=\"newAllocation.allocation_percentage\" type=\"number\" min=\"1\" max=\"100\" required\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button type=\"button\" @click=\"showAddResourceModal = false\"\n                      class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500\">\n                Annulla\n              </button>\n              <button type=\"submit\" :disabled=\"saving\"\n                      class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50\">\n                {{ saving ? 'Salvando...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit Resource Modal -->\n    <div v-if=\"showEditResourceModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Modifica Allocazione\n          </h3>\n\n          <form @submit.prevent=\"updateAllocation\">\n            <div class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Ruolo nel Progetto\n                </label>\n                <input v-model=\"editingAllocation.role\" type=\"text\" required\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n                       placeholder=\"es. Developer, Designer, PM\">\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Allocazione (%)\n                </label>\n                <input v-model.number=\"editingAllocation.allocation_percentage\" type=\"number\" min=\"1\" max=\"100\" required\n                       class=\"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button type=\"button\" @click=\"showEditResourceModal = false\"\n                      class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500\">\n                Annulla\n              </button>\n              <button type=\"submit\" :disabled=\"saving\"\n                      class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50\">\n                {{ saving ? 'Salvando...' : 'Aggiorna' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\n// Props\nconst props = defineProps({\n  project: { type: Object, required: true }\n})\n\n// Stores\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(true)\nconst saving = ref(false)\nconst analyzingWithAI = ref(false)\nconst allocations = ref([])\nconst availableUsers = ref([])\nconst resourceUtilization = ref([])\nconst aiInsights = ref(null)\nconst showAddResourceModal = ref(false)\nconst showEditResourceModal = ref(false)\n\nconst newAllocation = ref({\n  user_id: '',\n  role: '',\n  allocation_percentage: 100\n})\n\nconst editingAllocation = ref({\n  id: null,\n  role: '',\n  allocation_percentage: 100\n})\n\n// Computed\nconst projectId = computed(() => props.project?.id)\n\n// Methods\nconst loadAllocations = async () => {\n  if (!projectId.value) return\n\n  loading.value = true\n  try {\n    const response = await fetch(`/api/resources?project_id=${projectId.value}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nel caricamento allocazioni')\n\n    const result = await response.json()\n    allocations.value = result.data?.resources || []\n\n    // Load resource utilization\n    await loadResourceUtilization()\n  } catch (error) {\n    console.error('Error loading allocations:', error)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadAvailableUsers = async () => {\n  try {\n    const response = await fetch('/api/personnel', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nel caricamento utenti')\n\n    const result = await response.json()\n    availableUsers.value = result.data?.users || []\n  } catch (error) {\n    console.error('Error loading users:', error)\n  }\n}\n\nconst loadResourceUtilization = async () => {\n  // Mock data for now - in real implementation, this would come from API\n  resourceUtilization.value = allocations.value.map(allocation => ({\n    user_id: allocation.user_id,\n    user_name: allocation.user_name,\n    total_allocation: allocation.allocation_percentage + Math.floor(Math.random() * 30) // Mock other project allocations\n  }))\n}\n\nconst runAIAnalysis = async () => {\n  if (!projectId.value) return\n\n  analyzingWithAI.value = true\n  try {\n    const response = await fetch(`/api/ai-resources/analyze-allocation/${projectId.value}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        include_suggestions: true,\n        analysis_depth: 'detailed'\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'analisi AI')\n\n    const result = await response.json()\n    aiInsights.value = result.data?.analysis || null\n  } catch (error) {\n    console.error('Error in AI analysis:', error)\n    alert('Errore nell\\'analisi AI: ' + error.message)\n  } finally {\n    analyzingWithAI.value = false\n  }\n}\n\nconst addResource = async () => {\n  saving.value = true\n  try {\n    const response = await fetch('/api/resources', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        project_id: projectId.value,\n        ...newAllocation.value\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'aggiunta risorsa')\n\n    await loadAllocations()\n    showAddResourceModal.value = false\n    newAllocation.value = { user_id: '', role: '', allocation_percentage: 100 }\n  } catch (error) {\n    console.error('Error adding resource:', error)\n    alert('Errore nell\\'aggiunta risorsa: ' + error.message)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editAllocation = (allocation) => {\n  editingAllocation.value = {\n    id: allocation.id,\n    role: allocation.role,\n    allocation_percentage: allocation.allocation_percentage\n  }\n  showEditResourceModal.value = true\n}\n\nconst updateAllocation = async () => {\n  saving.value = true\n  try {\n    const response = await fetch(`/api/resources/${editingAllocation.value.id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        role: editingAllocation.value.role,\n        allocation_percentage: editingAllocation.value.allocation_percentage\n      })\n    })\n\n    if (!response.ok) throw new Error('Errore nell\\'aggiornamento allocazione')\n\n    await loadAllocations()\n    showEditResourceModal.value = false\n    editingAllocation.value = { id: null, role: '', allocation_percentage: 100 }\n  } catch (error) {\n    console.error('Error updating allocation:', error)\n    alert('Errore nell\\'aggiornamento: ' + error.message)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst removeAllocation = async (allocation) => {\n  if (!confirm('Sei sicuro di voler rimuovere questa allocazione?')) return\n\n  try {\n    const response = await fetch(`/api/resources/${allocation.id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) throw new Error('Errore nella rimozione')\n\n    await loadAllocations()\n  } catch (error) {\n    console.error('Error removing allocation:', error)\n    alert('Errore nella rimozione: ' + error.message)\n  }\n}\n\nconst applyAIRecommendation = async (recommendation) => {\n  try {\n    await fetch('/api/resources', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        project_id: projectId.value,\n        user_id: recommendation.user_id,\n        role: recommendation.role,\n        allocation_percentage: recommendation.allocation\n      })\n    })\n\n    await loadAllocations()\n  } catch (error) {\n    console.error('Error applying AI recommendation:', error)\n  }\n}\n\n// Utility functions\nconst getAllocationClass = (percentage) => {\n  if (percentage >= 80) return 'bg-red-500'\n  if (percentage >= 60) return 'bg-yellow-500'\n  return 'bg-green-500'\n}\n\nconst getUtilizationClass = (percentage) => {\n  if (percentage > 100) return 'bg-red-500'\n  if (percentage >= 90) return 'bg-yellow-500'\n  return 'bg-green-500'\n}\n\nconst getUtilizationTextClass = (percentage) => {\n  if (percentage > 100) return 'text-red-600 dark:text-red-400'\n  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'\n  return 'text-green-600 dark:text-green-400'\n}\n\n// Watchers\nwatch(() => props.project, (newProject) => {\n  if (newProject) {\n    loadAllocations()\n  }\n}, { immediate: true })\n\n// Lifecycle\nonMounted(() => {\n  loadAvailableUsers()\n})\n</script>\n"}