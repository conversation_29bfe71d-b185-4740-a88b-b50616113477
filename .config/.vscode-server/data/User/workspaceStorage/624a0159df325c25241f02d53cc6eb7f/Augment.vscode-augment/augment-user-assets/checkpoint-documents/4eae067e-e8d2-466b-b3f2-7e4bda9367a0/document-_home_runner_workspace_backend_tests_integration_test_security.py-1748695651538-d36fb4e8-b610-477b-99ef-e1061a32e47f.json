{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/integration/test_security.py"}, "originalCode": "import pytest\nimport json\nfrom models import User\n\ndef test_permission_escalation_forbidden_api(client, db_session, test_user_id, new_user_data):\n    \"\"\"Test that users cannot escalate permissions via API.\"\"\"\n    # Recupera l'utente normale dal DB\n    user = db_session.get(User, test_user_id)\n    assert user is not None, f\"Test user with id {test_user_id} not found.\"\n\n    # Assicura che l'utente non sia admin\n    original_role = user.role\n    user.role = 'employee'\n    db_session.commit()\n    assert user.role != 'admin', \"Test user should not have admin role for this test.\"\n\n    # Login come utente normale via API\n    login_response = client.post('/api/auth/login',\n                                json={\n                                    'username': user.username,\n                                    'password': new_user_data['password']\n                                },\n                                headers={'Content-Type': 'application/json'})\n    assert login_response.status_code == 200\n\n    # Test che l'utente non possa accedere a funzioni admin\n    admin_endpoints = [\n        '/api/personnel/users',\n        '/api/personnel/export',\n        '/api/dashboard/stats'\n    ]\n\n    for endpoint in admin_endpoints:\n        response = client.get(endpoint)\n        assert response.status_code in [401, 403], f\"Endpoint {endpoint} should deny access\"\n\n    # Verifica che il ruolo non sia cambiato\n    db_session.refresh(user)\n    assert user.role == 'employee', \"User role should not have changed\"\n\ndef test_api_authentication_required(client):\n    \"\"\"Test that protected API endpoints require authentication.\"\"\"\n    protected_endpoints = [\n        '/api/auth/me',\n        '/api/dashboard/stats',\n        '/api/projects/',\n        '/api/personnel/users'\n    ]\n\n    for endpoint in protected_endpoints:\n        response = client.get(endpoint)\n        assert response.status_code == 401, f\"Endpoint {endpoint} should require authentication\"\n\ndef test_invalid_session_handling_api(client, auth):\n    \"\"\"Test handling of invalid or expired sessions.\"\"\"\n    # Login first\n    auth.login()\n\n    # Verify we're logged in\n    response = client.get('/api/auth/me')\n    assert response.status_code == 200\n\n    # Logout\n    auth.logout()\n\n    # Try to access protected endpoint after logout\n    response = client.get('/api/auth/me')\n    assert response.status_code == 401\n\ndef test_concurrent_session_security_api(client, db_session, created_user, new_user_data):\n    \"\"\"Test security with multiple sessions.\"\"\"\n    user = db_session.get(User, created_user)\n    assert user is not None\n\n    # Login with first client\n    login_response1 = client.post('/api/auth/login',\n                                 json={\n                                     'username': user.username,\n                                     'password': new_user_data['password']\n                                 },\n                                 headers={'Content-Type': 'application/json'})\n    assert login_response1.status_code == 200\n\n    # Verify access works\n    me_response = client.get('/api/auth/me')\n    assert me_response.status_code == 200\n\ndef test_sql_injection_protection_api(client):\n    \"\"\"Test that API endpoints are protected against SQL injection.\"\"\"\n    # Try SQL injection in login\n    response = client.post('/api/auth/login',\n                          json={\n                              'username': \"admin'; DROP TABLE users; --\",\n                              'password': 'password'\n                          },\n                          headers={'Content-Type': 'application/json'})\n\n    # Should return 401 (invalid credentials) not 500 (server error)\n    assert response.status_code == 401\n\n    data = json.loads(response.data)\n    assert data['success'] is False", "modifiedCode": "import pytest\nimport json\nfrom models import User\n\ndef test_permission_escalation_forbidden_api(client, db_session, test_user_id, new_user_data):\n    \"\"\"Test that users cannot escalate permissions via API.\"\"\"\n    # Recupera l'utente normale dal DB\n    user = db_session.get(User, test_user_id)\n    assert user is not None, f\"Test user with id {test_user_id} not found.\"\n\n    # Assicura che l'utente non sia admin\n    original_role = user.role\n    user.role = 'employee'\n    db_session.commit()\n    assert user.role != 'admin', \"Test user should not have admin role for this test.\"\n\n    # Login come utente normale via API\n    login_response = client.post('/api/auth/login',\n                                json={\n                                    'username': user.username,\n                                    'password': new_user_data['password']\n                                },\n                                headers={'Content-Type': 'application/json'})\n    assert login_response.status_code == 200\n\n    # Test che l'utente non possa accedere a funzioni admin-only\n    admin_only_endpoints = [\n        '/api/personnel/users',\n        '/api/personnel/export'\n    ]\n\n    for endpoint in admin_only_endpoints:\n        response = client.get(endpoint)\n        assert response.status_code in [401, 403], f\"Endpoint {endpoint} should deny access\"\n\n    # Dashboard stats is accessible to employees\n    response = client.get('/api/dashboard/stats')\n    assert response.status_code == 200, \"Dashboard stats should be accessible to employees\"\n\n    # Verifica che il ruolo non sia cambiato\n    db_session.refresh(user)\n    assert user.role == 'employee', \"User role should not have changed\"\n\ndef test_api_authentication_required(client):\n    \"\"\"Test that protected API endpoints require authentication.\"\"\"\n    protected_endpoints = [\n        '/api/auth/me',\n        '/api/dashboard/stats',\n        '/api/projects/',\n        '/api/personnel/users'\n    ]\n\n    for endpoint in protected_endpoints:\n        response = client.get(endpoint)\n        assert response.status_code == 401, f\"Endpoint {endpoint} should require authentication\"\n\ndef test_invalid_session_handling_api(client, auth):\n    \"\"\"Test handling of invalid or expired sessions.\"\"\"\n    # Login first\n    auth.login()\n\n    # Verify we're logged in\n    response = client.get('/api/auth/me')\n    assert response.status_code == 200\n\n    # Logout\n    auth.logout()\n\n    # Try to access protected endpoint after logout\n    response = client.get('/api/auth/me')\n    assert response.status_code == 401\n\ndef test_concurrent_session_security_api(client, db_session, created_user, new_user_data):\n    \"\"\"Test security with multiple sessions.\"\"\"\n    user = db_session.get(User, created_user)\n    assert user is not None\n\n    # Login with first client\n    login_response1 = client.post('/api/auth/login',\n                                 json={\n                                     'username': user.username,\n                                     'password': new_user_data['password']\n                                 },\n                                 headers={'Content-Type': 'application/json'})\n    assert login_response1.status_code == 200\n\n    # Verify access works\n    me_response = client.get('/api/auth/me')\n    assert me_response.status_code == 200\n\ndef test_sql_injection_protection_api(client):\n    \"\"\"Test that API endpoints are protected against SQL injection.\"\"\"\n    # Try SQL injection in login\n    response = client.post('/api/auth/login',\n                          json={\n                              'username': \"admin'; DROP TABLE users; --\",\n                              'password': 'password'\n                          },\n                          headers={'Content-Type': 'application/json'})\n\n    # Should return 401 (invalid credentials) not 500 (server error)\n    assert response.status_code == 401\n\n    data = json.loads(response.data)\n    assert data['success'] is False"}