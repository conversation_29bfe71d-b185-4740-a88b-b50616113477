{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}, "originalCode": "\"\"\"\nBlueprint principale per le API RESTful.\n\"\"\"\nfrom flask import Blueprint, jsonify, request\nfrom flask_login import login_required, current_user\nfrom models import Notification\nfrom utils.db import db\n\n# Crea il blueprint principale per le API\napi_bp = Blueprint('api', __name__, url_prefix='/api')\n\n# Importa i blueprint delle API\nfrom blueprints.api.projects import api_projects\nfrom blueprints.api.tasks import api_tasks\nfrom blueprints.api.resources import api_resources\nfrom blueprints.api.task_dependencies import api_task_dependencies\nfrom blueprints.api.kpis import api_kpis\nfrom blueprints.api.project_kpis import api_project_kpis\nfrom blueprints.api.personnel import api_personnel\nfrom blueprints.api.dashboard import api_dashboard\nfrom blueprints.api.auth import api_auth\nfrom blueprints.api.clients import api_clients\n\n# Registra i blueprint delle API\napi_bp.register_blueprint(api_projects)\napi_bp.register_blueprint(api_tasks)\napi_bp.register_blueprint(api_resources)\napi_bp.register_blueprint(api_task_dependencies)\napi_bp.register_blueprint(api_kpis)\napi_bp.register_blueprint(api_project_kpis)\napi_bp.register_blueprint(api_personnel)\napi_bp.register_blueprint(api_dashboard)\napi_bp.register_blueprint(api_auth)\napi_bp.register_blueprint(api_clients)\n\n@api_bp.route('/notifications')\n@login_required\ndef get_notifications():\n    \"\"\"Endpoint per ottenere le notifiche dell'utente corrente.\"\"\"\n    # Ottieni le ultime 10 notifiche per l'utente\n    notifications = Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).order_by(Notification.created_at.desc()).limit(10).all()\n\n    # Calcola il numero totale di notifiche non lette\n    unread_count = Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).count()\n\n    # Formatta le notifiche per il frontend\n    formatted_notifications = []\n    for notification in notifications:\n        formatted_notifications.append({\n            'id': notification.id,\n            'text': notification.message,\n            'created_at': notification.created_at.isoformat(),\n            'link': notification.link or '#',\n            'type': notification.type,\n            'is_read': notification.is_read\n        })\n\n    return jsonify({\n        'notifications': formatted_notifications,\n        'unread_count': unread_count\n    })\n\n@api_bp.route('/notifications/mark-read/<int:notification_id>', methods=['POST'])\n@login_required\ndef mark_notification_read(notification_id):\n    \"\"\"Segna una notifica come letta.\"\"\"\n    notification = Notification.query.get_or_404(notification_id)\n\n    # Verifica che la notifica appartenga all'utente corrente\n    if notification.user_id != current_user.id:\n        return jsonify({'error': 'Non autorizzato'}), 403\n\n    notification.is_read = True\n    db.session.commit()\n\n    return jsonify({'success': True})\n\n@api_bp.route('/notifications/mark-all-read', methods=['POST'])\n@login_required\ndef mark_all_notifications_read():\n    \"\"\"Segna tutte le notifiche dell'utente come lette.\"\"\"\n    Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).update({'is_read': True})\n\n    db.session.commit()\n\n    return jsonify({'success': True})\n", "modifiedCode": "\"\"\"\nBlueprint principale per le API RESTful.\n\"\"\"\nfrom flask import Blueprint, jsonify, request\nfrom flask_login import login_required, current_user\nfrom models import Notification\nfrom extensions import db\n\n# Crea il blueprint principale per le API\napi_bp = Blueprint('api', __name__, url_prefix='/api')\n\n# Importa i blueprint delle API\nfrom blueprints.api.projects import api_projects\nfrom blueprints.api.tasks import api_tasks\nfrom blueprints.api.resources import api_resources\nfrom blueprints.api.task_dependencies import api_task_dependencies\nfrom blueprints.api.kpis import api_kpis\nfrom blueprints.api.project_kpis import api_project_kpis\nfrom blueprints.api.personnel import api_personnel\nfrom blueprints.api.dashboard import api_dashboard\nfrom blueprints.api.auth import api_auth\nfrom blueprints.api.clients import api_clients\n\n# Registra i blueprint delle API\napi_bp.register_blueprint(api_projects)\napi_bp.register_blueprint(api_tasks)\napi_bp.register_blueprint(api_resources)\napi_bp.register_blueprint(api_task_dependencies)\napi_bp.register_blueprint(api_kpis)\napi_bp.register_blueprint(api_project_kpis)\napi_bp.register_blueprint(api_personnel)\napi_bp.register_blueprint(api_dashboard)\napi_bp.register_blueprint(api_auth)\napi_bp.register_blueprint(api_clients)\n\n@api_bp.route('/notifications')\n@login_required\ndef get_notifications():\n    \"\"\"Endpoint per ottenere le notifiche dell'utente corrente.\"\"\"\n    # Ottieni le ultime 10 notifiche per l'utente\n    notifications = Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).order_by(Notification.created_at.desc()).limit(10).all()\n\n    # Calcola il numero totale di notifiche non lette\n    unread_count = Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).count()\n\n    # Formatta le notifiche per il frontend\n    formatted_notifications = []\n    for notification in notifications:\n        formatted_notifications.append({\n            'id': notification.id,\n            'text': notification.message,\n            'created_at': notification.created_at.isoformat(),\n            'link': notification.link or '#',\n            'type': notification.type,\n            'is_read': notification.is_read\n        })\n\n    return jsonify({\n        'notifications': formatted_notifications,\n        'unread_count': unread_count\n    })\n\n@api_bp.route('/notifications/mark-read/<int:notification_id>', methods=['POST'])\n@login_required\ndef mark_notification_read(notification_id):\n    \"\"\"Segna una notifica come letta.\"\"\"\n    notification = Notification.query.get_or_404(notification_id)\n\n    # Verifica che la notifica appartenga all'utente corrente\n    if notification.user_id != current_user.id:\n        return jsonify({'error': 'Non autorizzato'}), 403\n\n    notification.is_read = True\n    db.session.commit()\n\n    return jsonify({'success': True})\n\n@api_bp.route('/notifications/mark-all-read', methods=['POST'])\n@login_required\ndef mark_all_notifications_read():\n    \"\"\"Segna tutte le notifiche dell'utente come lette.\"\"\"\n    Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).update({'is_read': True})\n\n    db.session.commit()\n\n    return jsonify({'success': True})\n"}