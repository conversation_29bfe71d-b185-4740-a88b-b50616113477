{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/resources.py"}, "originalCode": "\"\"\"\nAPI RESTful per la gestione delle risorse dei progetti.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import desc\nfrom models import ProjectResource, Project, User\nfrom utils.api_utils import (\n    api_response, handle_api_error, get_pagination_params,\n    format_pagination\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_RESOURCES,\n    user_has_permission\n)\nfrom extensions import db, csrf\n\n# Crea il blueprint per le API delle risorse\napi_resources = Blueprint('api_resources', __name__, url_prefix='/resources')\n\n@api_resources.route('/', methods=['GET'])\n@login_required\ndef get_resources():\n    \"\"\"\n    Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.\n    ---\n    tags:\n      - resources\n    parameters:\n      - $ref: '#/components/parameters/pageParam'\n      - $ref: '#/components/parameters/perPageParam'\n      - name: project_id\n        in: query\n        description: Filtra per ID progetto\n        schema:\n          type: integer\n      - name: user_id\n        in: query\n        description: Filtra per ID utente\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Lista di risorse\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resources:\n                      type: array\n                      items:\n                        $ref: '#/components/schemas/ProjectResource'\n                pagination:\n                  $ref: '#/components/schemas/Pagination'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        # Ottieni parametri di paginazione\n        page, per_page = get_pagination_params()\n\n        # Inizia la query\n        query = ProjectResource.query\n\n        # Applica filtri\n        project_id = request.args.get('project_id', type=int)\n        if project_id:\n            query = query.filter(ProjectResource.project_id == project_id)\n\n        user_id = request.args.get('user_id', type=int)\n        if user_id:\n            query = query.filter(ProjectResource.user_id == user_id)\n\n        # Filtra per permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            # Se l'utente non ha il permesso di vedere tutti i progetti,\n            # mostra solo le risorse dei progetti a cui è assegnato\n            query = query.filter(\n                (ProjectResource.user_id == current_user.id) |\n                (ProjectResource.project_id.in_(\n                    db.session.query(Project.id).join(\n                        Project.team_members\n                    ).filter(User.id == current_user.id)\n                ))\n            )\n\n        # Applica ordinamento\n        query = query.order_by(desc(ProjectResource.project_id))\n\n        # Esegui query con paginazione\n        pagination = query.paginate(page=page, per_page=per_page)\n\n        # Prepara i dati delle risorse\n        resources_data = []\n        for resource in pagination.items:\n            resources_data.append({\n                'id': resource.id,\n                'project_id': resource.project_id,\n                'user_id': resource.user_id,\n                'allocation_percentage': resource.allocation_percentage,\n                'role': resource.role,\n                'project_name': resource.project.name if resource.project else None,\n                'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n            })\n\n        # Restituisci risposta\n        return api_response(\n            data={'resources': resources_data},\n            pagination=format_pagination(pagination)\n        )\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_resources: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['GET'])\n@login_required\ndef get_resource(resource_id):\n    \"\"\"\n    Ottiene i dettagli di una risorsa specifica.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Dettagli della risorsa\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            # Verifica se l'utente è la risorsa o è assegnato al progetto\n            is_resource = resource.user_id == current_user.id\n            is_in_project = current_user in resource.project.team_members if resource.project else False\n\n            if not (is_resource or is_in_project):\n                return api_response(\n                    message=\"Non hai i permessi per visualizzare questa risorsa\",\n                    status_code=403\n                )\n\n        # Prepara i dati della risorsa\n        resource_data = {\n            'id': resource.id,\n            'project_id': resource.project_id,\n            'user_id': resource.user_id,\n            'allocation_percentage': resource.allocation_percentage,\n            'role': resource.role,\n            'project_name': resource.project.name if resource.project else None,\n            'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n        }\n\n        return api_response(data={'resource': resource_data})\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/', methods=['POST'])\*************\n@login_required\ndef create_resource():\n    \"\"\"\n    Assegna una nuova risorsa a un progetto.\n    ---\n    tags:\n      - resources\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - project_id\n              - user_id\n            properties:\n              project_id:\n                type: integer\n                description: ID del progetto\n              user_id:\n                type: integer\n                description: ID dell'utente da assegnare\n              allocation_percentage:\n                type: integer\n                description: Percentuale di allocazione (1-100)\n                default: 100\n              role:\n                type: string\n                description: Ruolo dell'utente nel progetto\n    responses:\n      201:\n        description: Risorsa assegnata con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n                    message:\n                      type: string\n                      example: Risorsa assegnata con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: ID progetto e ID utente obbligatori\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per gestire le risorse dei progetti\",\n                status_code=403\n            )\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or not data.get('project_id') or not data.get('user_id'):\n            return api_response(\n                message=\"ID progetto e ID utente obbligatori\",\n                status_code=400\n            )\n\n        # Verifica che il progetto esista\n        project = Project.query.get(data.get('project_id'))\n        if not project:\n            return api_response(\n                message=\"Progetto non trovato\",\n                status_code=404\n            )\n\n        # Verifica che l'utente esista\n        user = User.query.get(data.get('user_id'))\n        if not user:\n            return api_response(\n                message=\"Utente non trovato\",\n                status_code=404\n            )\n\n        # Verifica che l'utente non sia già assegnato al progetto con lo stesso ruolo\n        existing_resource = ProjectResource.query.filter_by(\n            project_id=data.get('project_id'),\n            user_id=data.get('user_id'),\n            role=data.get('role')\n        ).first()\n\n        if existing_resource:\n            return api_response(\n                message=\"L'utente è già assegnato a questo progetto con lo stesso ruolo\",\n                status_code=400\n            )\n\n        # Crea la nuova risorsa\n        new_resource = ProjectResource(\n            project_id=data.get('project_id'),\n            user_id=data.get('user_id'),\n            allocation_percentage=data.get('allocation_percentage', 100),\n            role=data.get('role')\n        )\n\n        # Aggiungi la risorsa al database\n        db.session.add(new_resource)\n\n        # Aggiungi l'utente al team del progetto se non è già incluso\n        if user not in project.team_members:\n            project.team_members.append(user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Prepara i dati della risorsa per la risposta\n        resource_data = {\n            'id': new_resource.id,\n            'project_id': new_resource.project_id,\n            'user_id': new_resource.user_id,\n            'allocation_percentage': new_resource.allocation_percentage,\n            'role': new_resource.role,\n            'project_name': project.name,\n            'user_name': f\"{user.first_name} {user.last_name}\"\n        }\n\n        return api_response(\n            data={'resource': resource_data},\n            message=\"Risorsa assegnata con successo\",\n            status_code=201\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in create_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['PUT'])\*************\n@login_required\ndef update_resource(resource_id):\n    \"\"\"\n    Aggiorna un'assegnazione di risorsa esistente.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa da aggiornare\n        schema:\n          type: integer\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              allocation_percentage:\n                type: integer\n                description: Percentuale di allocazione (1-100)\n              role:\n                type: string\n                description: Ruolo dell'utente nel progetto\n    responses:\n      200:\n        description: Risorsa aggiornata con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n                    message:\n                      type: string\n                      example: Risorsa aggiornata con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per gestire le risorse dei progetti\",\n                status_code=403\n            )\n\n        # Ottieni la risorsa dal database\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n        if not data:\n            return api_response(\n                message=\"Nessun dato fornito per l'aggiornamento\",\n                status_code=400\n            )\n\n        # Aggiorna i campi della risorsa\n        if 'allocation_percentage' in data:\n            resource.allocation_percentage = data['allocation_percentage']\n        if 'role' in data:\n            resource.role = data['role']\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Prepara i dati della risorsa per la risposta\n        resource_data = {\n            'id': resource.id,\n            'project_id': resource.project_id,\n            'user_id': resource.user_id,\n            'allocation_percentage': resource.allocation_percentage,\n            'role': resource.role,\n            'project_name': resource.project.name if resource.project else None,\n            'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n        }\n\n        return api_response(\n            data={'resource': resource_data},\n            message=\"Risorsa aggiornata con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in update_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_resource(resource_id):\n    \"\"\"\n    Rimuove un'assegnazione di risorsa.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa da rimuovere\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Risorsa rimossa con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                message:\n                  type: string\n                  example: Risorsa rimossa con successo\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni la risorsa dal database\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Salva i dati della risorsa per il messaggio di risposta\n        project_name = resource.project.name if resource.project else \"Progetto sconosciuto\"\n        user_name = f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else \"Utente sconosciuto\"\n\n        # Elimina la risorsa\n        db.session.delete(resource)\n        db.session.commit()\n\n        return api_response(\n            message=f\"Risorsa rimossa con successo: {user_name} dal progetto '{project_name}'\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in delete_resource: {str(e)}\")\n        return handle_api_error(e)", "modifiedCode": "\"\"\"\nAPI RESTful per la gestione delle risorse dei progetti.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import desc\nfrom models import ProjectResource, Project, User\nfrom utils.api_utils import (\n    api_response, handle_api_error, get_pagination_params,\n    format_pagination\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_RESOURCES,\n    user_has_permission\n)\nfrom extensions import db, csrf\n\n# Crea il blueprint per le API delle risorse\napi_resources = Blueprint('api_resources', __name__, url_prefix='/resources')\n\n@api_resources.route('/', methods=['GET'])\n@login_required\ndef get_resources():\n    \"\"\"\n    Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.\n    ---\n    tags:\n      - resources\n    parameters:\n      - $ref: '#/components/parameters/pageParam'\n      - $ref: '#/components/parameters/perPageParam'\n      - name: project_id\n        in: query\n        description: Filtra per ID progetto\n        schema:\n          type: integer\n      - name: user_id\n        in: query\n        description: Filtra per ID utente\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Lista di risorse\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resources:\n                      type: array\n                      items:\n                        $ref: '#/components/schemas/ProjectResource'\n                pagination:\n                  $ref: '#/components/schemas/Pagination'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        # Ottieni parametri di paginazione\n        page, per_page = get_pagination_params()\n\n        # Inizia la query\n        query = ProjectResource.query\n\n        # Applica filtri\n        project_id = request.args.get('project_id', type=int)\n        if project_id:\n            query = query.filter(ProjectResource.project_id == project_id)\n\n        user_id = request.args.get('user_id', type=int)\n        if user_id:\n            query = query.filter(ProjectResource.user_id == user_id)\n\n        # Filtra per permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            # Se l'utente non ha il permesso di vedere tutti i progetti,\n            # mostra solo le risorse dei progetti a cui è assegnato\n            query = query.filter(\n                (ProjectResource.user_id == current_user.id) |\n                (ProjectResource.project_id.in_(\n                    db.session.query(Project.id).join(\n                        Project.team_members\n                    ).filter(User.id == current_user.id)\n                ))\n            )\n\n        # Applica ordinamento\n        query = query.order_by(desc(ProjectResource.project_id))\n\n        # Esegui query con paginazione\n        pagination = query.paginate(page=page, per_page=per_page)\n\n        # Prepara i dati delle risorse\n        resources_data = []\n        for resource in pagination.items:\n            resources_data.append({\n                'id': resource.id,\n                'project_id': resource.project_id,\n                'user_id': resource.user_id,\n                'allocation_percentage': resource.allocation_percentage,\n                'role': resource.role,\n                'project_name': resource.project.name if resource.project else None,\n                'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n            })\n\n        # Restituisci risposta\n        return api_response(\n            data={'resources': resources_data},\n            pagination=format_pagination(pagination)\n        )\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_resources: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['GET'])\n@login_required\ndef get_resource(resource_id):\n    \"\"\"\n    Ottiene i dettagli di una risorsa specifica.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Dettagli della risorsa\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            # Verifica se l'utente è la risorsa o è assegnato al progetto\n            is_resource = resource.user_id == current_user.id\n            is_in_project = current_user in resource.project.team_members if resource.project else False\n\n            if not (is_resource or is_in_project):\n                return api_response(\n                    message=\"Non hai i permessi per visualizzare questa risorsa\",\n                    status_code=403\n                )\n\n        # Prepara i dati della risorsa\n        resource_data = {\n            'id': resource.id,\n            'project_id': resource.project_id,\n            'user_id': resource.user_id,\n            'allocation_percentage': resource.allocation_percentage,\n            'role': resource.role,\n            'project_name': resource.project.name if resource.project else None,\n            'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n        }\n\n        return api_response(data={'resource': resource_data})\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/', methods=['POST'])\*************\n@login_required\ndef create_resource():\n    \"\"\"\n    Assegna una nuova risorsa a un progetto.\n    ---\n    tags:\n      - resources\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - project_id\n              - user_id\n            properties:\n              project_id:\n                type: integer\n                description: ID del progetto\n              user_id:\n                type: integer\n                description: ID dell'utente da assegnare\n              allocation_percentage:\n                type: integer\n                description: Percentuale di allocazione (1-100)\n                default: 100\n              role:\n                type: string\n                description: Ruolo dell'utente nel progetto\n    responses:\n      201:\n        description: Risorsa assegnata con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n                    message:\n                      type: string\n                      example: Risorsa assegnata con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: ID progetto e ID utente obbligatori\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per gestire le risorse dei progetti\",\n                status_code=403\n            )\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or not data.get('project_id') or not data.get('user_id'):\n            return api_response(\n                message=\"ID progetto e ID utente obbligatori\",\n                status_code=400\n            )\n\n        # Verifica che il progetto esista\n        project = Project.query.get(data.get('project_id'))\n        if not project:\n            return api_response(\n                message=\"Progetto non trovato\",\n                status_code=404\n            )\n\n        # Verifica che l'utente esista\n        user = User.query.get(data.get('user_id'))\n        if not user:\n            return api_response(\n                message=\"Utente non trovato\",\n                status_code=404\n            )\n\n        # Verifica che l'utente non sia già assegnato al progetto con lo stesso ruolo\n        existing_resource = ProjectResource.query.filter_by(\n            project_id=data.get('project_id'),\n            user_id=data.get('user_id'),\n            role=data.get('role')\n        ).first()\n\n        if existing_resource:\n            return api_response(\n                message=\"L'utente è già assegnato a questo progetto con lo stesso ruolo\",\n                status_code=400\n            )\n\n        # Crea la nuova risorsa\n        new_resource = ProjectResource(\n            project_id=data.get('project_id'),\n            user_id=data.get('user_id'),\n            allocation_percentage=data.get('allocation_percentage', 100),\n            role=data.get('role')\n        )\n\n        # Aggiungi la risorsa al database\n        db.session.add(new_resource)\n\n        # Aggiungi l'utente al team del progetto se non è già incluso\n        if user not in project.team_members:\n            project.team_members.append(user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Prepara i dati della risorsa per la risposta\n        resource_data = {\n            'id': new_resource.id,\n            'project_id': new_resource.project_id,\n            'user_id': new_resource.user_id,\n            'allocation_percentage': new_resource.allocation_percentage,\n            'role': new_resource.role,\n            'project_name': project.name,\n            'user_name': f\"{user.first_name} {user.last_name}\"\n        }\n\n        return api_response(\n            data={'resource': resource_data},\n            message=\"Risorsa assegnata con successo\",\n            status_code=201\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in create_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['PUT'])\*************\n@login_required\ndef update_resource(resource_id):\n    \"\"\"\n    Aggiorna un'assegnazione di risorsa esistente.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa da aggiornare\n        schema:\n          type: integer\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              allocation_percentage:\n                type: integer\n                description: Percentuale di allocazione (1-100)\n              role:\n                type: string\n                description: Ruolo dell'utente nel progetto\n    responses:\n      200:\n        description: Risorsa aggiornata con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    resource:\n                      $ref: '#/components/schemas/ProjectResource'\n                    message:\n                      type: string\n                      example: Risorsa aggiornata con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per gestire le risorse dei progetti\",\n                status_code=403\n            )\n\n        # Ottieni la risorsa dal database\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n        if not data:\n            return api_response(\n                message=\"Nessun dato fornito per l'aggiornamento\",\n                status_code=400\n            )\n\n        # Aggiorna i campi della risorsa\n        if 'allocation_percentage' in data:\n            resource.allocation_percentage = data['allocation_percentage']\n        if 'role' in data:\n            resource.role = data['role']\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Prepara i dati della risorsa per la risposta\n        resource_data = {\n            'id': resource.id,\n            'project_id': resource.project_id,\n            'user_id': resource.user_id,\n            'allocation_percentage': resource.allocation_percentage,\n            'role': resource.role,\n            'project_name': resource.project.name if resource.project else None,\n            'user_name': f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else None\n        }\n\n        return api_response(\n            data={'resource': resource_data},\n            message=\"Risorsa aggiornata con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in update_resource: {str(e)}\")\n        return handle_api_error(e)\n\n@api_resources.route('/<int:resource_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_resource(resource_id):\n    \"\"\"\n    Rimuove un'assegnazione di risorsa.\n    ---\n    tags:\n      - resources\n    parameters:\n      - name: resource_id\n        in: path\n        required: true\n        description: ID della risorsa da rimuovere\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Risorsa rimossa con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                message:\n                  type: string\n                  example: Risorsa rimossa con successo\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Verifica permessi\n        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):\n            return api_response(\n                message=\"Non hai i permessi necessari per gestire le risorse dei progetti\",\n                status_code=403\n            )\n\n        # Ottieni la risorsa dal database\n        resource = ProjectResource.query.get_or_404(resource_id)\n\n        # Salva i dati della risorsa per il messaggio di risposta\n        project_name = resource.project.name if resource.project else \"Progetto sconosciuto\"\n        user_name = f\"{resource.user.first_name} {resource.user.last_name}\" if resource.user else \"Utente sconosciuto\"\n\n        # Elimina la risorsa\n        db.session.delete(resource)\n        db.session.commit()\n\n        return api_response(\n            message=f\"Risorsa rimossa con successo: {user_name} dal progetto '{project_name}'\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in delete_resource: {str(e)}\")\n        return handle_api_error(e)"}