{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/test_ai_resources.py"}, "modifiedCode": "\"\"\"\nTest per AI Resource Allocation API\n\"\"\"\nimport pytest\nimport json\nfrom unittest.mock import patch, MagicMock\nfrom app import create_app\nfrom models import User, Project, ProjectResource\nfrom extensions import db\n\****************\ndef app():\n    \"\"\"Create test app\"\"\"\n    app = create_app()\n    app.config['TESTING'] = True\n    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'\n    \n    with app.app_context():\n        db.create_all()\n        yield app\n        db.drop_all()\n\****************\ndef client(app):\n    \"\"\"Create test client\"\"\"\n    return app.test_client()\n\****************\ndef auth_user(app):\n    \"\"\"Create authenticated user\"\"\"\n    with app.app_context():\n        user = User(\n            username='testuser',\n            email='<EMAIL>',\n            first_name='Test',\n            last_name='User',\n            role='admin'\n        )\n        user.set_password('password')\n        db.session.add(user)\n        db.session.commit()\n        return user\n\****************\ndef test_project(app, auth_user):\n    \"\"\"Create test project\"\"\"\n    with app.app_context():\n        project = Project(\n            name='Test Project',\n            description='Test project for AI resource allocation',\n            project_type='service',\n            budget=10000.0\n        )\n        db.session.add(project)\n        db.session.commit()\n        return project\n\nclass TestAIResourceAllocation:\n    \"\"\"Test AI Resource Allocation functionality\"\"\"\n    \n    def test_analyze_allocation_endpoint(self, client, auth_user, test_project):\n        \"\"\"Test AI allocation analysis endpoint\"\"\"\n        with client.session_transaction() as sess:\n            sess['_user_id'] = str(auth_user.id)\n            sess['_fresh'] = True\n        \n        # Mock AI service response\n        mock_ai_response = {\n            \"recommended_allocations\": [\n                {\n                    \"user_id\": auth_user.id,\n                    \"user_name\": \"Test User\",\n                    \"role\": \"Developer\",\n                    \"allocation\": 80\n                }\n            ],\n            \"optimization_insights\": [\n                \"Team has good skill coverage for this project type\",\n                \"Consider adding a senior developer for mentoring\"\n            ],\n            \"efficiency_score\": 85,\n            \"potential_conflicts\": [],\n            \"cost_analysis\": {\n                \"estimated_cost\": 8000,\n                \"budget_utilization\": 80\n            }\n        }\n        \n        with patch('ai_services.analyze_resource_allocation', return_value=mock_ai_response):\n            response = client.post(\n                f'/api/ai-resources/analyze-allocation/{test_project.id}',\n                json={\n                    \"include_suggestions\": True,\n                    \"analysis_depth\": \"detailed\"\n                },\n                headers={'Content-Type': 'application/json'}\n            )\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'analysis' in data['data']\n        assert data['data']['project_id'] == test_project.id\n    \n    def test_predict_conflicts_endpoint(self, client, auth_user):\n        \"\"\"Test AI conflict prediction endpoint\"\"\"\n        with client.session_transaction() as sess:\n            sess['_user_id'] = str(auth_user.id)\n            sess['_fresh'] = True\n        \n        # Mock AI service response\n        mock_prediction = {\n            \"conflicts\": [\n                {\n                    \"type\": \"overallocation\",\n                    \"user_id\": auth_user.id,\n                    \"user_name\": \"Test User\",\n                    \"current_allocation\": 120,\n                    \"severity\": \"high\"\n                }\n            ],\n            \"risk_level\": \"medium\",\n            \"recommendations\": [\n                \"Reduce allocation for Test User to 100%\",\n                \"Consider hiring additional resources\"\n            ],\n            \"timeline_impact\": \"Potential 2-week delay if not addressed\"\n        }\n        \n        test_data = {\n            \"allocations\": [\n                {\n                    \"user_id\": auth_user.id,\n                    \"allocation_percentage\": 120,\n                    \"project_id\": 1\n                }\n            ],\n            \"timeline\": {\n                \"start_date\": \"2024-01-01\",\n                \"end_date\": \"2024-03-01\"\n            }\n        }\n        \n        with patch('ai_services.predict_resource_conflicts', return_value=mock_prediction):\n            response = client.post(\n                '/api/ai-resources/predict-conflicts',\n                json=test_data,\n                headers={'Content-Type': 'application/json'}\n            )\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'prediction' in data['data']\n        assert data['data']['prediction']['risk_level'] == 'medium'\n    \n    def test_optimize_team_endpoint(self, client, auth_user, test_project):\n        \"\"\"Test AI team optimization endpoint\"\"\"\n        with client.session_transaction() as sess:\n            sess['_user_id'] = str(auth_user.id)\n            sess['_fresh'] = True\n        \n        # Mock AI service response\n        mock_optimization = {\n            \"optimal_team\": [\n                {\n                    \"user_id\": auth_user.id,\n                    \"user_name\": \"Test User\",\n                    \"role\": \"Lead Developer\",\n                    \"allocation_percentage\": 80,\n                    \"skill_match_score\": 95\n                }\n            ],\n            \"skill_coverage\": 85,\n            \"team_synergy_score\": 78,\n            \"alternative_options\": [],\n            \"training_needs\": [\n                \"Advanced project management training for Test User\"\n            ]\n        }\n        \n        with patch('ai_services.optimize_team_composition', return_value=mock_optimization):\n            response = client.post(\n                f'/api/ai-resources/optimize-team/{test_project.id}',\n                json={\n                    \"candidate_users\": [auth_user.id],\n                    \"optimization_criteria\": [\"skill_match\", \"experience\"],\n                    \"team_size_limit\": 5\n                },\n                headers={'Content-Type': 'application/json'}\n            )\n        \n        assert response.status_code == 200\n        data = json.loads(response.data)\n        assert data['success'] is True\n        assert 'optimization' in data['data']\n        assert data['data']['optimization']['skill_coverage'] == 85\n    \n    def test_unauthorized_access(self, client, test_project):\n        \"\"\"Test unauthorized access to AI endpoints\"\"\"\n        response = client.post(f'/api/ai-resources/analyze-allocation/{test_project.id}')\n        assert response.status_code == 401\n        \n        response = client.post('/api/ai-resources/predict-conflicts')\n        assert response.status_code == 401\n        \n        response = client.post(f'/api/ai-resources/optimize-team/{test_project.id}')\n        assert response.status_code == 401\n    \n    def test_invalid_project_id(self, client, auth_user):\n        \"\"\"Test with invalid project ID\"\"\"\n        with client.session_transaction() as sess:\n            sess['_user_id'] = str(auth_user.id)\n            sess['_fresh'] = True\n        \n        response = client.post('/api/ai-resources/analyze-allocation/99999')\n        assert response.status_code == 404\n    \n    def test_missing_required_data(self, client, auth_user):\n        \"\"\"Test with missing required data\"\"\"\n        with client.session_transaction() as sess:\n            sess['_user_id'] = str(auth_user.id)\n            sess['_fresh'] = True\n        \n        # Test predict conflicts without required data\n        response = client.post(\n            '/api/ai-resources/predict-conflicts',\n            json={},\n            headers={'Content-Type': 'application/json'}\n        )\n        assert response.status_code == 400\n        \n        data = json.loads(response.data)\n        assert 'allocazioni e timeline obbligatori' in data['message']\n\nclass TestAIServiceIntegration:\n    \"\"\"Test AI service integration\"\"\"\n    \n    @patch('ai_services.openai_client')\n    def test_ai_service_mock_response(self, mock_openai):\n        \"\"\"Test AI service with mocked OpenAI response\"\"\"\n        from ai_services import analyze_resource_allocation\n        \n        # Mock OpenAI response\n        mock_response = MagicMock()\n        mock_response.choices[0].message.content = json.dumps({\n            \"recommended_allocations\": [],\n            \"optimization_insights\": [\"Test insight\"],\n            \"efficiency_score\": 75,\n            \"potential_conflicts\": [],\n            \"cost_analysis\": {}\n        })\n        mock_openai.chat.completions.create.return_value = mock_response\n        \n        # Test data\n        project_data = {\n            \"name\": \"Test Project\",\n            \"description\": \"Test description\",\n            \"project_type\": \"service\"\n        }\n        available_resources = [\n            {\n                \"user_id\": 1,\n                \"name\": \"Test User\",\n                \"skills\": [\"Python\", \"JavaScript\"]\n            }\n        ]\n        \n        result = analyze_resource_allocation(project_data, available_resources)\n        \n        assert \"efficiency_score\" in result\n        assert result[\"efficiency_score\"] == 75\n        assert \"optimization_insights\" in result\n        assert len(result[\"optimization_insights\"]) > 0\n\nif __name__ == '__main__':\n    pytest.main([__file__])\n"}