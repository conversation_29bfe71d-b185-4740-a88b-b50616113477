"""
Blueprint principale per le API RESTful.
"""
from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import Notification
from extensions import db

# Crea il blueprint principale per le API
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Importa i blueprint delle API
from blueprints.api.projects import api_projects
from blueprints.api.tasks import api_tasks
from blueprints.api.resources import api_resources
from blueprints.api.task_dependencies import api_task_dependencies
from blueprints.api.kpis import api_kpis
from blueprints.api.project_kpis import api_project_kpis
from blueprints.api.personnel import api_personnel
from blueprints.api.dashboard import api_dashboard
from blueprints.api.auth import api_auth
from blueprints.api.clients import api_clients

# Registra i blueprint delle API
api_bp.register_blueprint(api_projects)
api_bp.register_blueprint(api_tasks)
api_bp.register_blueprint(api_resources)
api_bp.register_blueprint(api_task_dependencies)
api_bp.register_blueprint(api_kpis)
api_bp.register_blueprint(api_project_kpis)
api_bp.register_blueprint(api_personnel)
api_bp.register_blueprint(api_dashboard)
api_bp.register_blueprint(api_auth)
api_bp.register_blueprint(api_clients)

@api_bp.route('/notifications')
@login_required
def get_notifications():
    """Endpoint per ottenere le notifiche dell'utente corrente."""
    # Ottieni le ultime 10 notifiche per l'utente
    notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).order_by(Notification.created_at.desc()).limit(10).all()

    # Calcola il numero totale di notifiche non lette
    unread_count = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).count()

    # Formatta le notifiche per il frontend
    formatted_notifications = []
    for notification in notifications:
        formatted_notifications.append({
            'id': notification.id,
            'text': notification.message,
            'created_at': notification.created_at.isoformat(),
            'link': notification.link or '#',
            'type': notification.type,
            'is_read': notification.is_read
        })

    return jsonify({
        'notifications': formatted_notifications,
        'unread_count': unread_count
    })

@api_bp.route('/notifications/mark-read/<int:notification_id>', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Segna una notifica come letta."""
    notification = Notification.query.get_or_404(notification_id)

    # Verifica che la notifica appartenga all'utente corrente
    if notification.user_id != current_user.id:
        return jsonify({'error': 'Non autorizzato'}), 403

    notification.is_read = True
    db.session.commit()

    return jsonify({'success': True})

@api_bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Segna tutte le notifiche dell'utente come lette."""
    Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).update({'is_read': True})

    db.session.commit()

    return jsonify({'success': True})
