import pytest
import json
from models import User

def test_permission_escalation_forbidden_api(client, db_session, test_user_id, new_user_data):
    """Test that users cannot escalate permissions via API."""
    # Recupera l'utente normale dal DB
    user = db_session.get(User, test_user_id)
    assert user is not None, f"Test user with id {test_user_id} not found."

    # Assicura che l'utente non sia admin
    original_role = user.role
    user.role = 'employee'
    db_session.commit()
    assert user.role != 'admin', "Test user should not have admin role for this test."

    # Login come utente normale via API
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': user.username,
                                    'password': new_user_data['password']
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Test che l'utente non possa accedere a funzioni admin-only
    admin_only_endpoints = [
        '/api/personnel/users',
        '/api/personnel/export'
    ]

    for endpoint in admin_only_endpoints:
        response = client.get(endpoint)
        assert response.status_code in [401, 403], f"Endpoint {endpoint} should deny access"

    # Dashboard stats is accessible to employees
    response = client.get('/api/dashboard/stats')
    assert response.status_code == 200, "Dashboard stats should be accessible to employees"

    # Verifica che il ruolo non sia cambiato
    db_session.refresh(user)
    assert user.role == 'employee', "User role should not have changed"

def test_api_authentication_required(client, auth):
    """Test that protected API endpoints require authentication."""
    # Make sure we're logged out first
    auth.logout()

    protected_endpoints = [
        '/api/auth/me',
        '/api/personnel/users'
    ]

    for endpoint in protected_endpoints:
        response = client.get(endpoint)
        assert response.status_code == 401, f"Endpoint {endpoint} should require authentication"

def test_invalid_session_handling_api(client, auth):
    """Test handling of invalid or expired sessions."""
    # Login first
    auth.login()

    # Verify we're logged in
    response = client.get('/api/auth/me')
    assert response.status_code == 200

    # Logout
    auth.logout()

    # Try to access protected endpoint after logout
    response = client.get('/api/auth/me')
    assert response.status_code == 401

def test_concurrent_session_security_api(client, db_session, created_user, new_user_data):
    """Test security with multiple sessions."""
    user = db_session.get(User, created_user)
    assert user is not None

    # Login with first client
    login_response1 = client.post('/api/auth/login',
                                 json={
                                     'username': user.username,
                                     'password': new_user_data['password']
                                 },
                                 headers={'Content-Type': 'application/json'})
    assert login_response1.status_code == 200

    # Verify access works
    me_response = client.get('/api/auth/me')
    assert me_response.status_code == 200

def test_sql_injection_protection_api(client):
    """Test that API endpoints are protected against SQL injection."""
    # Try SQL injection in login
    response = client.post('/api/auth/login',
                          json={
                              'username': "admin'; DROP TABLE users; --",
                              'password': 'password'
                          },
                          headers={'Content-Type': 'application/json'})

    # Should return 401 (invalid credentials) not 500 (server error)
    assert response.status_code == 401

    data = json.loads(response.data)
    assert data['success'] is False