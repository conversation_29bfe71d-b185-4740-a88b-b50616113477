<template>
  <div>
    <!-- Header -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON><PERSON></h1>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Gestisci e monitora tutti i progetti aziendali
          </p>
        </div>
        <div class="mt-4 sm:mt-0">
          <button
            @click="createProject"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Nuovo Progetto
          </button>
        </div>
      </div>
    </div>

    <!-- Filtri e Ricerca -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stato</label>
          <select
            v-model="filters.status"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti gli stati</option>
            <option value="planning">Pianificazione</option>
            <option value="active">Attivo</option>
            <option value="completed">Completato</option>
            <option value="on-hold">In Pausa</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Cliente</label>
          <select
            v-model="filters.client"
            @change="applyFilters"
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">Tutti i clienti</option>
            <option v-for="client in clients" :key="client.id" :value="client.id">
              {{ client.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ricerca</label>
          <input
            v-model="searchQuery"
            @input="search"
            type="text"
            placeholder="Cerca progetti..."
            class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Reset Filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Projects List -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Progetti ({{ filteredProjects.length }})
        </h3>
      </div>

      <div v-if="isLoading" class="p-6 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"></div>
        <p class="mt-2 text-gray-600 dark:text-gray-400">Caricamento progetti...</p>
      </div>

      <div v-else-if="filteredProjects.length === 0" class="p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun progetto</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Inizia creando il tuo primo progetto.</p>
      </div>

      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div
          v-for="project in filteredProjects"
          :key="project.id"
          class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
          @click="viewProject(project.id)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ project.name }}
                </h4>
                <span
                  :class="getStatusClass(project.status)"
                  class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                >
                  {{ getStatusLabel(project.status) }}
                </span>
              </div>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {{ project.description }}
              </p>
              <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                <span v-if="project.client">Cliente: {{ project.client.name }}</span>
                <span v-if="project.client" class="mx-2">•</span>
                <span v-if="project.end_date">Scadenza: {{ formatDate(project.end_date) }}</span>
                <span v-if="project.end_date && project.budget" class="mx-2">•</span>
                <span v-if="project.budget">Budget: {{ formatCurrency(project.budget) }}</span>
              </div>

              <!-- KPI Indicators -->
              <div v-if="project.kpis" class="mt-3 flex items-center space-x-4">
                <div v-if="project.kpis.budget_usage !== undefined" class="flex items-center space-x-1">
                  <div class="w-3 h-3 rounded-full" :class="getBudgetStatusClass(project.kpis.budget_usage)"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">
                    Budget: {{ project.kpis.budget_usage }}%
                  </span>
                </div>
                <div v-if="project.kpis.time_usage !== undefined" class="flex items-center space-x-1">
                  <div class="w-3 h-3 rounded-full" :class="getTimeStatusClass(project.kpis.time_usage)"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">
                    Tempo: {{ project.kpis.time_usage }}%
                  </span>
                </div>
                <div v-if="project.kpis.margin !== undefined" class="flex items-center space-x-1">
                  <div class="w-3 h-3 rounded-full" :class="getMarginStatusClass(project.kpis.margin)"></div>
                  <span class="text-xs text-gray-600 dark:text-gray-400">
                    Margine: {{ project.kpis.margin }}%
                  </span>
                </div>
              </div>
            </div>
            <div class="ml-4 flex items-center space-x-4">
              <!-- Progress Bar -->
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ getProjectProgress(project) }}%
                </div>
                <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1">
                  <div
                    class="bg-primary-600 h-2 rounded-full"
                    :style="{ width: getProjectProgress(project) + '%' }"
                  ></div>
                </div>
              </div>

              <!-- KPI Summary -->
              <div v-if="project.kpis" class="text-right">
                <div class="text-xs text-gray-500 dark:text-gray-400">KPI Status</div>
                <div class="flex items-center space-x-1 mt-1">
                  <div class="w-2 h-2 rounded-full" :class="getOverallKPIStatus(project.kpis)"></div>
                  <span class="text-xs font-medium" :class="getOverallKPITextClass(project.kpis)">
                    {{ getOverallKPILabel(project.kpis) }}
                  </span>
                </div>
              </div>
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'

const router = useRouter()
const projectsStore = useProjectsStore()

// Reactive data
const isLoading = ref(true)
const searchQuery = ref('')
const filters = ref({
  status: '',
  client: ''
})

// Use store data
const projects = computed(() => projectsStore.projects)
const clients = ref([])



// Computed
const filteredProjects = computed(() => {
  let filtered = projects.value

  if (filters.value.status) {
    filtered = filtered.filter(p => p.status === filters.value.status)
  }

  if (filters.value.client) {
    filtered = filtered.filter(p => p.client_id == filters.value.client)
  }

  if (searchQuery.value) {
    const search = searchQuery.value.toLowerCase()
    filtered = filtered.filter(p =>
      p.name.toLowerCase().includes(search) ||
      (p.description && p.description.toLowerCase().includes(search)) ||
      (p.client && p.client.name && p.client.name.toLowerCase().includes(search))
    )
  }

  return filtered
})

// Methods
const loadProjects = async () => {
  isLoading.value = true
  try {
    await projectsStore.fetchProjects()
    // TODO: Load clients from API
    clients.value = []
  } catch (error) {
    console.error('Error loading projects:', error)
  } finally {
    isLoading.value = false
  }
}

const search = () => {
  // La ricerca è reattiva tramite computed
}

const applyFilters = () => {
  // I filtri sono reattivi tramite computed
}

const resetFilters = () => {
  filters.value = {
    status: '',
    client: ''
  }
  searchQuery.value = ''
}

const createProject = () => {
  router.push('/app/projects/create')
}

const viewProject = (projectId) => {
  router.push(`/app/projects/${projectId}`)
}

const getStatusClass = (status) => {
  const classes = {
    planning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    active: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    completed: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    'on-hold': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
}

const getStatusLabel = (status) => {
  const labels = {
    planning: 'Pianificazione',
    active: 'Attivo',
    completed: 'Completato',
    'on-hold': 'In Pausa'
  }
  return labels[status] || status
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('it-IT')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

const getProjectProgress = (project) => {
  // TODO: Calculate real progress based on tasks
  // For now, return a simple calculation based on status
  const statusProgress = {
    planning: 10,
    active: 50,
    completed: 100,
    'on-hold': 25
  }
  return statusProgress[project.status] || 0
}

// KPI Status Functions
const getBudgetStatusClass = (usage) => {
  if (usage >= 90) return 'bg-red-500'
  if (usage >= 75) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getTimeStatusClass = (usage) => {
  if (usage >= 90) return 'bg-red-500'
  if (usage >= 80) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getMarginStatusClass = (margin) => {
  if (margin < 10) return 'bg-red-500'
  if (margin < 20) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getOverallKPIStatus = (kpis) => {
  const budgetRisk = kpis.budget_usage >= 90
  const timeRisk = kpis.time_usage >= 90
  const marginRisk = kpis.margin < 10

  if (budgetRisk || timeRisk || marginRisk) return 'bg-red-500'

  const budgetWarning = kpis.budget_usage >= 75
  const timeWarning = kpis.time_usage >= 80
  const marginWarning = kpis.margin < 20

  if (budgetWarning || timeWarning || marginWarning) return 'bg-yellow-500'

  return 'bg-green-500'
}

const getOverallKPITextClass = (kpis) => {
  const status = getOverallKPIStatus(kpis)
  if (status.includes('red')) return 'text-red-600 dark:text-red-400'
  if (status.includes('yellow')) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-green-600 dark:text-green-400'
}

const getOverallKPILabel = (kpis) => {
  const status = getOverallKPIStatus(kpis)
  if (status.includes('red')) return 'Critico'
  if (status.includes('yellow')) return 'Attenzione'
  return 'Buono'
}

// Lifecycle
onMounted(() => {
  loadProjects()
})
</script>