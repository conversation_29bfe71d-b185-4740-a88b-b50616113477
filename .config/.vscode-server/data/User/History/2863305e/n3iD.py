"""
API RESTful per la gestione delle risorse dei progetti.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import ProjectResource, Project, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_RESOURCES,
    user_has_permission
)
from extensions import db, csrf

# Crea il blueprint per le API delle risorse
api_resources = Blueprint('api_resources', __name__, url_prefix='/resources')

@api_resources.route('/', methods=['GET'])
@login_required
def get_resources():
    """
    Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.
    ---
    tags:
      - resources
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: project_id
        in: query
        description: Filtra per ID progetto
        schema:
          type: integer
      - name: user_id
        in: query
        description: Filtra per ID utente
        schema:
          type: integer
    responses:
      200:
        description: Lista di risorse
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    resources:
                      type: array
                      items:
                        $ref: '#/components/schemas/ProjectResource'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = ProjectResource.query

        # Applica filtri
        project_id = request.args.get('project_id', type=int)
        if project_id:
            query = query.filter(ProjectResource.project_id == project_id)

        user_id = request.args.get('user_id', type=int)
        if user_id:
            query = query.filter(ProjectResource.user_id == user_id)

        # Filtra per permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Se l'utente non ha il permesso di vedere tutti i progetti,
            # mostra solo le risorse dei progetti a cui è assegnato
            query = query.filter(
                (ProjectResource.user_id == current_user.id) |
                (ProjectResource.project_id.in_(
                    db.session.query(Project.id).join(
                        Project.team_members
                    ).filter(User.id == current_user.id)
                ))
            )

        # Applica ordinamento
        query = query.order_by(desc(ProjectResource.project_id))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati delle risorse
        resources_data = []
        for resource in pagination.items:
            resources_data.append({
                'id': resource.id,
                'project_id': resource.project_id,
                'user_id': resource.user_id,
                'allocation_percentage': resource.allocation_percentage,
                'role': resource.role,
                'project_name': resource.project.name if resource.project else None,
                'user_name': f"{resource.user.first_name} {resource.user.last_name}" if resource.user else None
            })

        # Restituisci risposta
        return api_response(
            data={'resources': resources_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_resources: {str(e)}")
        return handle_api_error(e)

@api_resources.route('/<int:resource_id>', methods=['GET'])
@login_required
def get_resource(resource_id):
    """
    Ottiene i dettagli di una risorsa specifica.
    ---
    tags:
      - resources
    parameters:
      - name: resource_id
        in: path
        required: true
        description: ID della risorsa
        schema:
          type: integer
    responses:
      200:
        description: Dettagli della risorsa
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    resource:
                      $ref: '#/components/schemas/ProjectResource'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        resource = ProjectResource.query.get_or_404(resource_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Verifica se l'utente è la risorsa o è assegnato al progetto
            is_resource = resource.user_id == current_user.id
            is_in_project = current_user in resource.project.team_members if resource.project else False

            if not (is_resource or is_in_project):
                return api_response(
                    message="Non hai i permessi per visualizzare questa risorsa",
                    status_code=403
                )

        # Prepara i dati della risorsa
        resource_data = {
            'id': resource.id,
            'project_id': resource.project_id,
            'user_id': resource.user_id,
            'allocation_percentage': resource.allocation_percentage,
            'role': resource.role,
            'project_name': resource.project.name if resource.project else None,
            'user_name': f"{resource.user.first_name} {resource.user.last_name}" if resource.user else None
        }

        return api_response(data={'resource': resource_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_resource: {str(e)}")
        return handle_api_error(e)

@api_resources.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_resource():
    """
    Assegna una nuova risorsa a un progetto.
    ---
    tags:
      - resources
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - project_id
              - user_id
            properties:
              project_id:
                type: integer
                description: ID del progetto
              user_id:
                type: integer
                description: ID dell'utente da assegnare
              allocation_percentage:
                type: integer
                description: Percentuale di allocazione (1-100)
                default: 100
              role:
                type: string
                description: Ruolo dell'utente nel progetto
    responses:
      201:
        description: Risorsa assegnata con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    resource:
                      $ref: '#/components/schemas/ProjectResource'
                    message:
                      type: string
                      example: Risorsa assegnata con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: ID progetto e ID utente obbligatori
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_MANAGE_PROJECT_RESOURCES):
            return api_response(
                message="Non hai i permessi necessari per gestire le risorse dei progetti",
                status_code=403
            )

        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or not data.get('project_id') or not data.get('user_id'):
            return api_response(
                message="ID progetto e ID utente obbligatori",
                status_code=400
            )

        # Verifica che il progetto esista
        project = Project.query.get(data.get('project_id'))
        if not project:
            return api_response(
                message="Progetto non trovato",
                status_code=404
            )

        # Verifica che l'utente esista
        user = User.query.get(data.get('user_id'))
        if not user:
            return api_response(
                message="Utente non trovato",
                status_code=404
            )

        # Verifica che l'utente non sia già assegnato al progetto con lo stesso ruolo
        existing_resource = ProjectResource.query.filter_by(
            project_id=data.get('project_id'),
            user_id=data.get('user_id'),
            role=data.get('role')
        ).first()

        if existing_resource:
            return api_response(
                message="L'utente è già assegnato a questo progetto con lo stesso ruolo",
                status_code=400
            )

        # Crea la nuova risorsa
        new_resource = ProjectResource(
            project_id=data.get('project_id'),
            user_id=data.get('user_id'),
            allocation_percentage=data.get('allocation_percentage', 100),
            role=data.get('role')
        )

        # Aggiungi la risorsa al database
        db.session.add(new_resource)

        # Aggiungi l'utente al team del progetto se non è già incluso
        if user not in project.team_members:
            project.team_members.append(user)

        # Commit delle modifiche
        db.session.commit()

        # Prepara i dati della risorsa per la risposta
        resource_data = {
            'id': new_resource.id,
            'project_id': new_resource.project_id,
            'user_id': new_resource.user_id,
            'allocation_percentage': new_resource.allocation_percentage,
            'role': new_resource.role,
            'project_name': project.name,
            'user_name': f"{user.first_name} {user.last_name}"
        }

        return api_response(
            data={'resource': resource_data},
            message="Risorsa assegnata con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_resource: {str(e)}")
        return handle_api_error(e)

@api_resources.route('/<int:resource_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_RESOURCES)
def update_resource(resource_id):
    """
    Aggiorna un'assegnazione di risorsa esistente.
    ---
    tags:
      - resources
    parameters:
      - name: resource_id
        in: path
        required: true
        description: ID della risorsa da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              allocation_percentage:
                type: integer
                description: Percentuale di allocazione (1-100)
              role:
                type: string
                description: Ruolo dell'utente nel progetto
    responses:
      200:
        description: Risorsa aggiornata con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    resource:
                      $ref: '#/components/schemas/ProjectResource'
                    message:
                      type: string
                      example: Risorsa aggiornata con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni la risorsa dal database
        resource = ProjectResource.query.get_or_404(resource_id)

        # Ottieni i dati dalla richiesta
        data = request.json
        if not data:
            return api_response(
                message="Nessun dato fornito per l'aggiornamento",
                status_code=400
            )

        # Aggiorna i campi della risorsa
        if 'allocation_percentage' in data:
            resource.allocation_percentage = data['allocation_percentage']
        if 'role' in data:
            resource.role = data['role']

        # Commit delle modifiche
        db.session.commit()

        # Prepara i dati della risorsa per la risposta
        resource_data = {
            'id': resource.id,
            'project_id': resource.project_id,
            'user_id': resource.user_id,
            'allocation_percentage': resource.allocation_percentage,
            'role': resource.role,
            'project_name': resource.project.name if resource.project else None,
            'user_name': f"{resource.user.first_name} {resource.user.last_name}" if resource.user else None
        }

        return api_response(
            data={'resource': resource_data},
            message="Risorsa aggiornata con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_resource: {str(e)}")
        return handle_api_error(e)

@api_resources.route('/<int:resource_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_RESOURCES)
def delete_resource(resource_id):
    """
    Rimuove un'assegnazione di risorsa.
    ---
    tags:
      - resources
    parameters:
      - name: resource_id
        in: path
        required: true
        description: ID della risorsa da rimuovere
        schema:
          type: integer
    responses:
      200:
        description: Risorsa rimossa con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Risorsa rimossa con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni la risorsa dal database
        resource = ProjectResource.query.get_or_404(resource_id)

        # Salva i dati della risorsa per il messaggio di risposta
        project_name = resource.project.name if resource.project else "Progetto sconosciuto"
        user_name = f"{resource.user.first_name} {resource.user.last_name}" if resource.user else "Utente sconosciuto"

        # Elimina la risorsa
        db.session.delete(resource)
        db.session.commit()

        return api_response(
            message=f"Risorsa rimossa con successo: {user_name} dal progetto '{project_name}'"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_resource: {str(e)}")
        return handle_api_error(e)