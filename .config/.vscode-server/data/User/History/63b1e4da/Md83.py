import os
import json
import requests
import logging
from openai import OpenAI

logger = logging.getLogger(__name__)

# API Keys
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
PERPLEXITY_API_KEY = os.environ.get("PERPLEXITY_API_KEY")

# Initialize OpenAI client
openai_client = OpenAI(api_key=OPENAI_API_KEY)

def analyze_text_with_openai(text, prompt="", model="gpt-4o"):
    """
    Analyze text using OpenAI GPT models
    """
    try:
        # the newest OpenAI model is "gpt-4o" which was released May 13, 2024.
        # do not change this unless explicitly requested by the user
        if not prompt:
            prompt = "Analyze the following text and provide insights:"

        complete_prompt = f"{prompt}\n\n{text}"

        response = openai_client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": complete_prompt}],
            temperature=0.2,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return f"Error analyzing text: {str(e)}"

def generate_summary_with_openai(text, max_length=200):
    """
    Generate a concise summary of the provided text
    """
    try:
        prompt = f"Summarize the following text in about {max_length} words or less:"

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": f"{prompt}\n\n{text}"}],
            temperature=0.3,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return f"Error generating summary: {str(e)}"

def extract_insights_with_openai(text, context="business"):
    """
    Extract key insights from text with a specific business context
    """
    try:
        prompt = f"Extract key {context} insights and actionable points from the following text:"

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": f"{prompt}\n\n{text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        return {"error": str(e), "insights": []}

def analyze_with_perplexity(text, question="", model="llama-3.1-sonar-small-128k-online"):
    """
    Analyze text using Perplexity API
    """
    if not PERPLEXITY_API_KEY:
        logger.error("Perplexity API key not found")
        return "Error: Perplexity API key not configured"

    try:
        if not question:
            question = "Analyze this text and provide insights:"

        headers = {
            "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an AI assistant for business analytics. Be precise and concise."
                },
                {
                    "role": "user",
                    "content": f"{question}\n\n{text}"
                }
            ],
            "temperature": 0.2,
            "max_tokens": 500,
            "stream": False
        }

        response = requests.post(
            "https://api.perplexity.ai/chat/completions",
            headers=headers,
            json=data
        )

        if response.status_code == 200:
            response_data = response.json()
            return {
                "content": response_data["choices"][0]["message"]["content"],
                "citations": response_data.get("citations", [])
            }
        else:
            logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
            return {"error": f"API Error: {response.status_code}", "content": ""}
    except Exception as e:
        logger.error(f"Perplexity API call error: {str(e)}")
        return {"error": str(e), "content": ""}

def analyze_project_requirements(requirements_text):
    """
    Analyze project requirements and extract key components
    """
    try:
        prompt = """
        Analyze these project requirements and extract the following information in JSON format:
        1. Key deliverables
        2. Potential risks
        3. Resources needed
        4. Estimated timeline
        5. Success criteria
        """

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": f"{prompt}\n\n{requirements_text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Project requirements analysis error: {str(e)}")
        return {
            "error": str(e),
            "key_deliverables": [],
            "potential_risks": [],
            "resources_needed": [],
            "estimated_timeline": "Unknown",
            "success_criteria": []
        }

def generate_funding_recommendations(company_profile):
    """
    Generate funding recommendations based on company profile
    """
    try:
        prompt = """
        Based on this company profile, provide recommendations for:
        1. Suitable funding opportunities
        2. Grant programs to consider
        3. Application strategy suggestions
        4. Key points to highlight

        Respond in JSON format.
        """

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": f"{prompt}\n\n{company_profile}"}],
            temperature=0.3,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Funding recommendations error: {str(e)}")
        return {
            "error": str(e),
            "funding_opportunities": [],
            "grant_programs": [],
            "application_strategy": [],
            "key_highlights": []
        }

def extract_skills_from_cv(cv_text):
    """
    Estrae competenze da un CV usando OpenAI
    """
    try:
        prompt = """
        Analizza questo CV e estrai tutte le competenze tecniche e professionali.
        Categorizza le competenze in:
        - Linguaggi di programmazione
        - Framework e librerie
        - Database
        - Cloud e DevOps
        - Soft skills
        - Certificazioni
        - Strumenti e software
        - Metodologie

        Restituisci il risultato in formato JSON con questa struttura:
        {
            "skills": [
                {
                    "name": "nome competenza",
                    "category": "categoria",
                    "level": "beginner|intermediate|advanced|expert"
                }
            ],
            "summary": "breve riassunto del profilo professionale",
            "experience_years": numero_totale_anni_esperienza
        }
        """

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": f"{prompt}\n\nCV:\n{cv_text}"}],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"OpenAI CV analysis error: {str(e)}")
        return {"error": str(e), "skills": [], "summary": "", "experience_years": 0}

def generate_cv_html(user_data, skills_data):
    """
    Genera HTML per CV usando OpenAI
    """
    try:
        prompt = f"""
        Genera un CV professionale in HTML per questo profilo:

        Dati utente: {json.dumps(user_data, indent=2)}
        Competenze: {json.dumps(skills_data, indent=2)}

        Crea un CV completo con:
        - Intestazione con dati personali
        - Profilo professionale (summary)
        - Competenze tecniche organizzate per categoria
        - Layout professionale con CSS inline
        - Stile moderno e pulito

        Restituisci solo l'HTML completo del CV.
        """

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"OpenAI CV generation error: {str(e)}")
        return f"<p>Errore nella generazione del CV: {str(e)}</p>"

# ============================================================================
# RESOURCE ALLOCATION AI SERVICES
# ============================================================================

def analyze_resource_allocation(project_data, available_resources, current_allocations=None):
    """
    Analizza l'allocazione delle risorse per un progetto e fornisce suggerimenti AI
    """
    try:
        # Prepara i dati per l'analisi
        context = {
            "project": {
                "name": project_data.get("name", ""),
                "description": project_data.get("description", ""),
                "project_type": project_data.get("project_type", ""),
                "start_date": project_data.get("start_date", ""),
                "end_date": project_data.get("end_date", ""),
                "budget": project_data.get("budget", 0),
                "estimated_hours": project_data.get("estimated_hours", 0),
                "required_skills": project_data.get("required_skills", [])
            },
            "available_resources": available_resources,
            "current_allocations": current_allocations or []
        }

        prompt = """
        Analizza questa situazione di allocazione risorse e fornisci suggerimenti intelligenti.

        Considera:
        1. Competenze richieste vs competenze disponibili
        2. Carico di lavoro attuale delle risorse
        3. Disponibilità temporale
        4. Costi e budget
        5. Efficienza del team

        Fornisci suggerimenti in formato JSON con:
        - recommended_allocations: array di allocazioni suggerite
        - optimization_insights: insights per ottimizzazione
        - potential_conflicts: conflitti potenziali identificati
        - efficiency_score: punteggio efficienza (0-100)
        - cost_analysis: analisi costi
        """

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Sei un esperto AI in resource management e project planning. Analizza i dati e fornisci suggerimenti pratici e attuabili."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.2,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Resource allocation analysis error: {str(e)}")
        return {
            "error": str(e),
            "recommended_allocations": [],
            "optimization_insights": [],
            "potential_conflicts": [],
            "efficiency_score": 0,
            "cost_analysis": {}
        }

def predict_resource_conflicts(allocations_data, timeline_data):
    """
    Predice conflitti di risorse basandosi su dati storici e allocazioni attuali
    """
    try:
        prompt = """
        Analizza queste allocazioni di risorse e predici potenziali conflitti.

        Identifica:
        1. Sovrallocazioni (>100% capacità)
        2. Conflitti temporali
        3. Competenze mancanti
        4. Rischi di burnout
        5. Dipendenze critiche

        Fornisci risultato in JSON con:
        - conflicts: array di conflitti identificati
        - risk_level: livello rischio (low/medium/high)
        - recommendations: raccomandazioni per risoluzione
        - timeline_impact: impatto su timeline progetto
        """

        context = {
            "allocations": allocations_data,
            "timeline": timeline_data
        }

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Sei un AI specialist in project risk management. Identifica proattivamente i rischi nelle allocazioni di risorse."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.1,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Resource conflict prediction error: {str(e)}")
        return {
            "error": str(e),
            "conflicts": [],
            "risk_level": "unknown",
            "recommendations": [],
            "timeline_impact": "unknown"
        }

def optimize_team_composition(project_requirements, candidate_resources):
    """
    Ottimizza la composizione del team per un progetto specifico
    """
    try:
        prompt = """
        Ottimizza la composizione del team per questo progetto.

        Considera:
        1. Skill match con requisiti progetto
        2. Sinergie tra membri del team
        3. Bilanciamento senior/junior
        4. Copertura competenze critiche
        5. Dinamiche di team

        Fornisci in JSON:
        - optimal_team: composizione team ottimale
        - skill_coverage: copertura competenze (%)
        - team_synergy_score: punteggio sinergia (0-100)
        - alternative_options: opzioni alternative
        - training_needs: necessità formazione
        """

        context = {
            "project_requirements": project_requirements,
            "candidate_resources": candidate_resources
        }

        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Sei un AI expert in team building e resource optimization. Crea team ad alta performance."},
                {"role": "user", "content": f"{prompt}\n\nDati:\n{json.dumps(context, indent=2)}"}
            ],
            temperature=0.3,
            response_format={"type": "json_object"},
        )

        return json.loads(response.choices[0].message.content)
    except Exception as e:
        logger.error(f"Team composition optimization error: {str(e)}")
        return {
            "error": str(e),
            "optimal_team": [],
            "skill_coverage": 0,
            "team_synergy_score": 0,
            "alternative_options": [],
            "training_needs": []
        }
