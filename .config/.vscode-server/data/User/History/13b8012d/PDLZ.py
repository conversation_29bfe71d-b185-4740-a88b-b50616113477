import pytest
import json
from models import User

def test_access_after_logout_api(client, db_session, test_user_id, new_user_data):
    """Test that access is denied after logout via API."""
    # Recupera l'utente dal DB
    user = db_session.get(User, test_user_id)
    assert user is not None, f"Test user with id {test_user_id} not found."

    # Login via API
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': user.username,
                                    'password': new_user_data['password']
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Verify we're logged in
    me_response = client.get('/api/auth/me')
    assert me_response.status_code == 200

    # Logout via API
    logout_response = client.post('/api/auth/logout',
                                 headers={'Content-Type': 'application/json'})
    assert logout_response.status_code == 200

    # Try to access protected endpoint after logout
    protected_response = client.get('/api/auth/me')
    assert protected_response.status_code == 401

def test_session_persistence_api(client, db_session, created_user, new_user_data):
    """Test that sessions persist across requests."""
    user = db_session.get(User, created_user)
    assert user is not None

    # Login
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': user.username,
                                    'password': new_user_data['password']
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Make multiple requests to verify session persists
    for _ in range(3):
        response = client.get('/api/auth/me')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['data']['user']['id'] == user.id

def test_session_timeout_handling_api(client, auth):
    """Test handling of session timeouts."""
    # Login
    auth.login()

    # Verify session is active
    response = client.get('/api/auth/me')
    assert response.status_code == 200

    # Logout to simulate session end
    auth.logout()

    # Verify session is ended
    response = client.get('/api/auth/me')
    assert response.status_code == 401

def test_invalid_session_data_api(client):
    """Test handling of invalid session data."""
    # Try to access protected endpoint without login
    response = client.get('/api/auth/me')
    assert response.status_code == 401

    data = json.loads(response.data)
    assert data['success'] is False

def test_multiple_concurrent_sessions_api(client, db_session, created_user, new_user_data):
    """Test handling of multiple concurrent sessions."""
    user = db_session.get(User, created_user)
    assert user is not None

    # Login with first session
    login_data = {
        'username': user.username,
        'password': new_user_data['password']
    }

    response1 = client.post('/api/auth/login',
                           json=login_data,
                           headers={'Content-Type': 'application/json'})
    assert response1.status_code == 200

    # Verify first session works
    me_response1 = client.get('/api/auth/me')
    assert me_response1.status_code == 200

    # Login again with same client (should work)
    response2 = client.post('/api/auth/login',
                           json=login_data,
                           headers={'Content-Type': 'application/json'})
    assert response2.status_code == 200

    # Verify session still works
    me_response2 = client.get('/api/auth/me')
    assert me_response2.status_code == 200