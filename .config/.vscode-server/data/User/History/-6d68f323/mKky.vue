<template>
  <div class="personnel-allocation">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Allocazione Risorse</h1>
          <p class="text-gray-600 dark:text-gray-400 mt-1">Analisi temporale e confronto pianificato vs effettivo</p>
        </div>
      </div>

      <!-- Controls -->
      <div class="flex flex-wrap items-center gap-4">
        <!-- Time Period Selector -->
        <select v-model="selectedPeriod" @change="loadAllocationData"
                class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
          <option value="current-month">Mese Corrente</option>
          <option value="current-quarter">Trimestre Corrente</option>
          <option value="current-year">Anno Corrente</option>
          <option value="next-quarter">Prossimo Trimestre</option>
        </select>

        <!-- Department Filter -->
        <select v-model="selectedDepartment" @change="loadAllocationData"
                class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
          <option value="">Tutti i Dipartimenti</option>
          <option v-for="dept in departments" :key="dept.id" :value="dept.id">
            {{ dept.name }} ({{ dept.user_count }})
          </option>
        </select>

        <!-- Project Filter -->
        <select v-model="selectedProject" @change="loadAllocationData"
                class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
          <option value="">Tutti i Progetti</option>
          <option v-for="project in projects" :key="project.id" :value="project.id">
            {{ project.name }} ({{ project.allocated_users }})
          </option>
        </select>

        <!-- Only Allocated Filter -->
        <label class="flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300">
          <input type="checkbox" v-model="onlyAllocated" @change="loadAllocationData"
                 class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
          <span>Solo allocati</span>
        </label>

        <!-- View Mode -->
        <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button @click="viewMode = 'summary'"
                  :class="viewMode === 'summary' ? 'bg-white dark:bg-gray-600 shadow' : ''"
                  class="px-3 py-1 text-sm rounded-md transition-colors">
            Riepilogo
          </button>
          <button @click="viewMode = 'detailed'"
                  :class="viewMode === 'detailed' ? 'bg-white dark:bg-gray-600 shadow' : ''"
                  class="px-3 py-1 text-sm rounded-md transition-colors">
            Dettagliato
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <p class="text-red-800 dark:text-red-200">{{ error }}</p>
      </div>
    </div>

    <!-- Summary Cards -->
    <div v-if="!loading && !error" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Capacità Totale</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ summary.totalCapacity }}h</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Allocato</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ summary.totalAllocated }}h</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Effettivo</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ summary.totalActual }}h</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2" :class="summary.utilizationClass">
            <svg class="w-6 h-6" :class="summary.utilizationIconClass" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Utilizzo</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ summary.utilizationPercentage }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div v-if="!loading && !error" class="space-y-6">
      <!-- Summary View -->
      <div v-if="viewMode === 'summary'" class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Riepilogo per Persona</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="person in allocationData" :key="person.user_id"
                 class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                      {{ person.user_name.split(' ').map(n => n[0]).join('') }}
                    </span>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ person.user_name }}</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ person.role || 'Nessun ruolo' }}</p>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm text-gray-500 dark:text-gray-400">Utilizzo</div>
                  <div class="text-lg font-semibold" :class="getUtilizationTextClass(person.utilization_percentage)">
                    {{ Math.round(person.utilization_percentage) }}%
                  </div>
                </div>
              </div>

              <!-- Progress Bars -->
              <div class="space-y-3">
                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600 dark:text-gray-400">Capacità</span>
                    <span class="text-gray-900 dark:text-white">{{ person.capacity_hours }}h</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-gray-400 h-2 rounded-full" style="width: 100%"></div>
                  </div>
                </div>

                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600 dark:text-gray-400">Allocato</span>
                    <span class="text-gray-900 dark:text-white">{{ person.allocated_hours }}h</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full"
                         :style="{ width: Math.min((person.allocated_hours / person.capacity_hours) * 100, 100) + '%' }"></div>
                  </div>
                </div>

                <div>
                  <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600 dark:text-gray-400">Effettivo</span>
                    <span class="text-gray-900 dark:text-white">{{ person.actual_hours }}h</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full"
                         :style="{ width: Math.min((person.actual_hours / person.capacity_hours) * 100, 100) + '%' }"></div>
                  </div>
                </div>
              </div>

              <!-- Projects List -->
              <div v-if="person.projects && person.projects.length > 0" class="mt-4">
                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Progetti Attivi</h5>
                <div class="space-y-2">
                  <div v-for="project in person.projects" :key="project.project_id"
                       class="flex justify-between items-center text-sm">
                    <span class="text-gray-600 dark:text-gray-400">{{ project.project_name }}</span>
                    <div class="flex items-center space-x-2">
                      <span class="text-gray-900 dark:text-white">{{ project.allocation_percentage }}%</span>
                      <span class="text-gray-500 dark:text-gray-400">({{ project.allocated_hours }}h)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed View -->
      <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Vista Dettagliata</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Persona
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Progetto
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ruolo
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Allocazione
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ore Pianificate
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Ore Effettive
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Varianza
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="allocation in detailedAllocations" :key="`${allocation.user_id}-${allocation.project_id}`">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {{ allocation.user_name.split(' ').map(n => n[0]).join('') }}
                      </span>
                    </div>
                    <div class="ml-3">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ allocation.user_name }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-white">{{ allocation.project_name }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">{{ allocation.project_period }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ allocation.role }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ allocation.allocation_percentage }}%</div>
                    <div class="ml-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div class="bg-blue-500 h-2 rounded-full"
                           :style="{ width: allocation.allocation_percentage + '%' }"></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ allocation.planned_hours }}h
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ allocation.actual_hours }}h
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-sm font-medium" :class="getVarianceClass(allocation.variance)">
                    {{ allocation.variance > 0 ? '+' : '' }}{{ allocation.variance }}h
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Stores
const authStore = useAuthStore()

// State
const loading = ref(false)
const error = ref(null)
const selectedPeriod = ref('current-month')
const selectedDepartment = ref('')
const selectedProject = ref('')
const onlyAllocated = ref(false)
const viewMode = ref('summary')
const allocationData = ref([])
const detailedAllocations = ref([])
const departments = ref([])
const projects = ref([])

// Computed
const summary = computed(() => {
  if (!allocationData.value.length) {
    return {
      totalCapacity: 0,
      totalAllocated: 0,
      totalActual: 0,
      utilizationPercentage: 0,
      utilizationClass: 'bg-gray-100 dark:bg-gray-700',
      utilizationIconClass: 'text-gray-600 dark:text-gray-400'
    }
  }

  const totalCapacity = allocationData.value.reduce((sum, person) => sum + person.capacity_hours, 0)
  const totalAllocated = allocationData.value.reduce((sum, person) => sum + person.allocated_hours, 0)
  const totalActual = allocationData.value.reduce((sum, person) => sum + person.actual_hours, 0)
  const utilizationPercentage = totalCapacity > 0 ? Math.round((totalActual / totalCapacity) * 100) : 0

  let utilizationClass = 'bg-gray-100 dark:bg-gray-700'
  let utilizationIconClass = 'text-gray-600 dark:text-gray-400'

  if (utilizationPercentage > 100) {
    utilizationClass = 'bg-red-100 dark:bg-red-900'
    utilizationIconClass = 'text-red-600 dark:text-red-400'
  } else if (utilizationPercentage >= 90) {
    utilizationClass = 'bg-yellow-100 dark:bg-yellow-900'
    utilizationIconClass = 'text-yellow-600 dark:text-yellow-400'
  } else if (utilizationPercentage >= 70) {
    utilizationClass = 'bg-green-100 dark:bg-green-900'
    utilizationIconClass = 'text-green-600 dark:text-green-400'
  }

  return {
    totalCapacity,
    totalAllocated,
    totalActual,
    utilizationPercentage,
    utilizationClass,
    utilizationIconClass
  }
})

// Methods
const loadFilters = async () => {
  try {
    const response = await fetch('/api/allocation/filters', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      departments.value = result.data?.departments || []
      projects.value = result.data?.projects || []
    }
  } catch (err) {
    console.error('Error loading filters:', err)
  }
}

const loadAllocationData = async () => {
  loading.value = true
  error.value = null

  try {
    // Costruisci URL con parametri
    const params = new URLSearchParams({
      period: selectedPeriod.value
    })

    if (selectedDepartment.value) {
      params.append('department_id', selectedDepartment.value)
    }

    if (selectedProject.value) {
      params.append('project_id', selectedProject.value)
    }

    if (onlyAllocated.value) {
      params.append('only_allocated', 'true')
    }

    const url = `/api/allocation-analysis?${params.toString()}`
    console.log('Calling API:', url)

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      throw new Error('Errore nel caricamento dei dati di allocazione')
    }

    const result = await response.json()
    allocationData.value = result.data?.summary || []
    detailedAllocations.value = result.data?.detailed || []
  } catch (err) {
    error.value = err.message
    console.error('Error loading allocation data:', err)
  } finally {
    loading.value = false
  }
}

const getUtilizationTextClass = (percentage) => {
  if (percentage > 100) return 'text-red-600 dark:text-red-400'
  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'
  if (percentage >= 70) return 'text-green-600 dark:text-green-400'
  return 'text-gray-600 dark:text-gray-400'
}

const getVarianceClass = (variance) => {
  if (variance > 0) return 'text-red-600 dark:text-red-400'
  if (variance < 0) return 'text-green-600 dark:text-green-400'
  return 'text-gray-600 dark:text-gray-400'
}

// Lifecycle
onMounted(async () => {
  await loadFilters()
  await loadAllocationData()
})
</script>
