"""
API endpoints for personnel management.
Provides REST API for users, departments, skills, and organization chart.
"""

import os
import json
from datetime import datetime
from flask import Blueprint, request, jsonify, make_response, current_app, send_file
from flask_login import current_user
from sqlalchemy import or_, and_, func
from sqlalchemy.orm import joinedload
from werkzeug.utils import secure_filename

from extensions import db
from models import User, Department, Skill, UserSkill, UserProfile
from utils.api_utils import (
    api_response, get_pagination_params, api_permission_required,
    handle_api_error, api_login_required
)
from utils.permissions import (
    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS
)
from utils.cv_parser import extract_text_from_cv, is_valid_cv_file, get_file_size_mb
from ai_services import extract_skills_from_cv

# Create blueprint
api_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')

@api_personnel.route('', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_personnel():
    """
    Get list of personnel (alias for /users endpoint).
    This is a convenience endpoint that redirects to the users endpoint.
    """
    return get_users()

@api_personnel.route('/users', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_users():
    """
    Get list of users with filtering, pagination, and search.

    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20)
    - search: Search in name, username, email
    - department_id: Filter by department
    - role: Filter by role
    - is_active: Filter by active status
    - skills: Filter by skills (comma-separated skill IDs)
    """
    try:
        # Get pagination parameters
        page, per_page = get_pagination_params()

        # Build base query
        query = User.query.options(
            joinedload(User.department_obj),
            joinedload(User.detailed_skills).joinedload(UserSkill.skill),
            joinedload(User.profile)
        )

        # Apply filters
        search = request.args.get('search', '').strip()
        if search:
            search_filter = or_(
                User.first_name.ilike(f'%{search}%'),
                User.last_name.ilike(f'%{search}%'),
                User.username.ilike(f'%{search}%'),
                User.email.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)

        # Department filter
        department_id = request.args.get('department_id', type=int)
        if department_id:
            query = query.filter(User.department_id == department_id)

        # Role filter
        role = request.args.get('role')
        if role:
            query = query.filter(User.role == role)

        # Active status filter
        is_active = request.args.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1', 'yes']
            query = query.filter(User.is_active == is_active_bool)

        # Skills filter
        skills = request.args.get('skills')
        if skills:
            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]
            if skill_ids:
                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))

        # Order by
        order_by = request.args.get('order_by', 'last_name')
        order_dir = request.args.get('order_dir', 'asc')

        if hasattr(User, order_by):
            order_column = getattr(User, order_by)
            if order_dir.lower() == 'desc':
                order_column = order_column.desc()
            query = query.order_by(order_column)
        else:
            query = query.order_by(User.last_name.asc())

        # Execute pagination
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Serialize users
        users_data = []
        for user in pagination.items:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': user.full_name,
                'role': user.role,
                'department_id': user.department_id,
                'department_name': user.department_obj.name if user.department_obj else None,
                'position': user.position,
                'hire_date': user.hire_date.isoformat() if user.hire_date else None,
                'phone': user.phone,
                'profile_image': user.profile_image,
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'skills': [
                    {
                        'id': us.skill.id,
                        'name': us.skill.name,
                        'category': us.skill.category,
                        'proficiency_level': us.proficiency_level,
                        'years_experience': us.years_experience
                    }
                    for us in user.detailed_skills
                ] if user.detailed_skills else [],
                'profile_completion': user.profile.profile_completion if user.profile else 0.0
            }
            users_data.append(user_data)

        return api_response(
            data={
                'users': users_data,
                'pagination': {
                    'page': pagination.page,
                    'pages': pagination.pages,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            },
            message=f"Retrieved {len(users_data)} users"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_user(user_id):
    """
    Get detailed information about a specific user.
    """
    try:
        user = User.query.options(
            joinedload(User.department_obj),
            joinedload(User.detailed_skills).joinedload(UserSkill.skill),
            joinedload(User.profile),
            joinedload(User.projects)
        ).get_or_404(user_id)

        # Ricalcola il completamento del profilo se esiste
        if user.profile:
            user.profile.calculate_completion()
            db.session.commit()

        # Serialize user with full details
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': user.full_name,
            'role': user.role,
            'department_id': user.department_id,
            'department': {
                'id': user.department_obj.id,
                'name': user.department_obj.name,
                'description': user.department_obj.description
            } if user.department_obj else None,
            'position': user.position,
            'hire_date': user.hire_date.isoformat() if user.hire_date else None,
            'phone': user.phone,
            'profile_image': user.profile_image,
            'bio': user.bio,
            'is_active': user.is_active,
            'dark_mode': user.dark_mode,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'skills': [
                {
                    'id': us.skill.id,
                    'name': us.skill.name,
                    'category': us.skill.category,
                    'description': us.skill.description,
                    'proficiency_level': us.proficiency_level,
                    'years_experience': us.years_experience,
                    'certified': us.is_certified,
                    'last_used': us.certification_date.isoformat() if us.certification_date else None
                }
                for us in user.detailed_skills
            ] if user.detailed_skills else [],
            'projects': [
                {
                    'id': project.id,
                    'name': project.name,
                    'status': project.status,
                    'role': 'team_member'  # Could be enhanced with actual role from project_team table
                }
                for project in user.projects
            ] if user.projects else [],
            'profile': {
                'employee_id': user.profile.employee_id,
                'job_title': user.profile.job_title,
                'birth_date': user.profile.birth_date.isoformat() if user.profile.birth_date else None,
                'address': user.profile.address,
                'emergency_contact_name': user.profile.emergency_contact_name,
                'emergency_contact_phone': user.profile.emergency_contact_phone,
                'emergency_contact_relationship': user.profile.emergency_contact_relationship,
                'employment_type': user.profile.employment_type,
                'work_location': user.profile.work_location,
                'weekly_hours': user.profile.weekly_hours,
                'daily_hours': user.profile.daily_hours,
                'current_cv_path': user.profile.current_cv_path,
                'cv_last_updated': user.profile.cv_last_updated.isoformat() if user.profile.cv_last_updated else None,
                'profile_completion': user.profile.profile_completion,
                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None,
                'created_at': user.profile.created_at.isoformat() if user.profile.created_at else None,
                'updated_at': user.profile.updated_at.isoformat() if user.profile.updated_at else None
            } if user.profile else None
        }

        return api_response(
            data={'user': user_data},
            message=f"Retrieved user {user.full_name}"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)
def update_user_profile(user_id):
    """
    Update user profile information.
    Allows updating both User and UserProfile fields.
    """
    try:
        # Check if user can edit this profile
        user = User.query.get_or_404(user_id)

        # Permission check: own profile or admin/HR
        if user.id != current_user.id and not current_user.role in ['admin', 'human_resources']:
            return api_response(
                success=False,
                message="Non hai il permesso di modificare questo profilo",
                status_code=403
            )

        data = request.get_json()
        if not data:
            return api_response(
                success=False,
                message="Nessun dato fornito",
                status_code=400
            )

        # Update User fields
        user_fields = ['first_name', 'last_name', 'phone', 'bio', 'position']
        for field in user_fields:
            if field in data:
                setattr(user, field, data[field])

        # Get or create UserProfile
        profile = user.profile
        if not profile:
            profile = UserProfile(user_id=user.id)
            db.session.add(profile)

        # Update UserProfile fields
        profile_fields = [
            'employee_id', 'job_title', 'birth_date', 'address',
            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
            'employment_type', 'work_location', 'weekly_hours', 'daily_hours'
        ]

        for field in profile_fields:
            if field in data:
                if field == 'birth_date' and data[field]:
                    # Handle date conversion
                    from datetime import datetime
                    profile.birth_date = datetime.strptime(data[field], '%Y-%m-%d').date()
                else:
                    setattr(profile, field, data[field])

        # Recalculate profile completion
        profile.calculate_completion()

        # Save changes
        db.session.commit()

        # Return updated user data
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': user.full_name,
            'role': user.role,
            'department_id': user.department_id,
            'department': {
                'id': user.department_obj.id,
                'name': user.department_obj.name,
                'description': user.department_obj.description
            } if user.department_obj else None,
            'position': user.position,
            'hire_date': user.hire_date.isoformat() if user.hire_date else None,
            'phone': user.phone,
            'profile_image': user.profile_image,
            'bio': user.bio,
            'is_active': user.is_active,
            'profile': {
                'employee_id': profile.employee_id,
                'job_title': profile.job_title,
                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,
                'address': profile.address,
                'emergency_contact_name': profile.emergency_contact_name,
                'emergency_contact_phone': profile.emergency_contact_phone,
                'emergency_contact_relationship': profile.emergency_contact_relationship,
                'employment_type': profile.employment_type,
                'work_location': profile.work_location,
                'weekly_hours': profile.weekly_hours,
                'daily_hours': profile.daily_hours,
                'profile_completion': profile.profile_completion
            }
        }

        return api_response(
            data={'user': user_data},
            message="Profilo aggiornato con successo"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments', methods=['GET'])
@api_login_required
def get_departments():
    """
    Get list of departments with organization chart data.
    """
    try:
        departments = Department.query.options(
            joinedload(Department.manager)
        ).all()

        departments_data = []
        for dept in departments:
            dept_data = {
                'id': dept.id,
                'name': dept.name,
                'description': dept.description,
                'manager_id': dept.manager_id,
                'manager': {
                    'id': dept.manager.id,
                    'full_name': dept.manager.full_name,
                    'email': dept.manager.email
                } if dept.manager else None,
                'user_count': dept.employees.count() if dept.employees else 0,
                'users': [
                    {
                        'id': user.id,
                        'full_name': user.full_name,
                        'position': user.position,
                        'email': user.email,
                        'is_active': user.is_active
                    }
                    for user in dept.employees
                ] if dept.employees else [],
                'created_at': dept.created_at.isoformat() if dept.created_at else None
            }
            departments_data.append(dept_data)

        return api_response(
            data={'departments': departments_data},
            message=f"Retrieved {len(departments_data)} departments"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/orgchart', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_orgchart():
    """
    Get organization chart data with hierarchical structure.
    """
    try:
        # Build hierarchical department tree
        def build_department_tree(parent_id=None):
            tree = []
            departments = Department.query.filter_by(
                parent_id=parent_id,
                is_active=True
            ).order_by(Department.name).all()

            for dept in departments:
                # Get active employees for this department
                employees = User.query.filter_by(
                    department_id=dept.id,
                    is_active=True
                ).order_by(User.first_name, User.last_name).all()

                # Build employee data
                employees_data = []
                for emp in employees:
                    emp_data = {
                        'id': emp.id,
                        'full_name': emp.full_name,
                        'first_name': emp.first_name,
                        'last_name': emp.last_name,
                        'email': emp.email,
                        'position': emp.position,
                        'role': emp.role,
                        'hire_date': emp.hire_date.isoformat() if emp.hire_date else None,
                        'profile_image': emp.profile_image,
                        'is_manager': emp.id == dept.manager_id
                    }
                    employees_data.append(emp_data)

                # Build department node
                dept_data = {
                    'id': dept.id,
                    'name': dept.name,
                    'description': dept.description,
                    'manager_id': dept.manager_id,
                    'manager': {
                        'id': dept.manager.id,
                        'full_name': dept.manager.full_name,
                        'email': dept.manager.email,
                        'position': dept.manager.position,
                        'profile_image': dept.manager.profile_image
                    } if dept.manager else None,
                    'employees': employees_data,
                    'employee_count': len(employees_data),
                    'budget': dept.budget,
                    'subdepartments': build_department_tree(dept.id)
                }
                tree.append(dept_data)

            return tree

        # Get root departments (no parent)
        orgchart_data = build_department_tree()

        # Calculate total statistics
        total_employees = User.query.filter_by(is_active=True).count()
        total_departments = Department.query.filter_by(is_active=True).count()
        total_managers = Department.query.filter(
            Department.manager_id.isnot(None),
            Department.is_active == True
        ).count()

        stats = {
            'total_employees': total_employees,
            'total_departments': total_departments,
            'total_managers': total_managers
        }

        return api_response(
            data={
                'orgchart': orgchart_data,
                'stats': stats
            },
            message=f"Retrieved organization chart with {total_departments} departments"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def create_department():
    """
    Create a new department.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data.get('name'):
            return api_response(
                success=False,
                message='Nome dipartimento richiesto',
                status_code=400
            )

        # Check if department name already exists
        existing_dept = Department.query.filter_by(name=data['name']).first()
        if existing_dept:
            return api_response(
                success=False,
                message='Un dipartimento con questo nome esiste già',
                status_code=400
            )

        # Create new department
        department = Department(
            name=data['name'],
            description=data.get('description', ''),
            manager_id=data.get('manager_id'),
            parent_id=data.get('parent_id'),
            budget=data.get('budget', 0.0)
        )

        db.session.add(department)
        db.session.commit()

        # Return created department data
        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'user_count': 0,
            'created_at': department.created_at.isoformat()
        }

        return api_response(
            data={'department': dept_data},
            message=f"Dipartimento '{department.name}' creato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_department(dept_id):
    """
    Get detailed information about a specific department.
    """
    try:
        department = Department.query.options(
            joinedload(Department.manager)
        ).get_or_404(dept_id)

        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'manager': {
                'id': department.manager.id,
                'full_name': department.manager.full_name,
                'email': department.manager.email,
                'position': department.manager.position
            } if department.manager else None,
            'parent': {
                'id': department.parent.id,
                'name': department.parent.name
            } if department.parent_id and hasattr(department, 'parent') and department.parent else None,
            'employees': [
                {
                    'id': emp.id,
                    'full_name': emp.full_name,
                    'email': emp.email,
                    'position': emp.position,
                    'is_active': emp.is_active,
                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None
                }
                for emp in department.employees if emp.is_active
            ],
            'subdepartments': [
                {
                    'id': sub.id,
                    'name': sub.name,
                    'employee_count': getattr(sub, 'employee_count', 0)
                }
                for sub in department.subdepartments if getattr(sub, 'is_active', True)
            ],
            'employee_count': len([emp for emp in department.employees if emp.is_active]),
            'created_at': department.created_at.isoformat() if department.created_at else None,
            'updated_at': department.updated_at.isoformat() if department.updated_at else None
        }

        return api_response(
            data={'department': dept_data},
            message=f"Retrieved department {department.name}"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def update_department(dept_id):
    """
    Update an existing department.
    """
    try:
        department = Department.query.get_or_404(dept_id)
        data = request.get_json()

        # Validate name if provided
        if 'name' in data and data['name']:
            # Check if name already exists (excluding current department)
            existing_dept = Department.query.filter(
                Department.name == data['name'],
                Department.id != dept_id
            ).first()
            if existing_dept:
                return api_response(
                    success=False,
                    message='Un dipartimento con questo nome esiste già',
                    status_code=400
                )
            department.name = data['name']

        # Update other fields
        if 'description' in data:
            department.description = data['description']
        if 'manager_id' in data:
            department.manager_id = data['manager_id']
        if 'parent_id' in data:
            department.parent_id = data['parent_id']
        if 'budget' in data:
            department.budget = data['budget']

        db.session.commit()

        # Return updated department data
        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'employee_count': len([emp for emp in department.employees if emp.is_active]),
            'updated_at': department.updated_at.isoformat() if department.updated_at else None
        }

        return api_response(
            data={'department': dept_data},
            message=f"Dipartimento '{department.name}' aggiornato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def delete_department(dept_id):
    """
    Delete a department (soft delete by setting is_active=False).
    """
    try:
        department = Department.query.get_or_404(dept_id)

        # Check if department has employees
        active_employees = len([emp for emp in department.employees if emp.is_active])
        if active_employees > 0:
            return api_response(
                success=False,
                message='Impossibile eliminare un dipartimento con dipendenti assegnati',
                status_code=400
            )

        # Check if department has subdepartments
        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])
        if active_subdepartments > 0:
            return api_response(
                success=False,
                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',
                status_code=400
            )

        # Soft delete
        department.is_active = False
        db.session.commit()

        return api_response(
            message=f"Dipartimento '{department.name}' eliminato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/skills', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_skills():
    """
    Get list of skills with usage statistics.
    """
    try:
        # Get skills with user count
        skills_query = db.session.query(
            Skill,
            func.count(UserSkill.user_id).label('user_count')
        ).outerjoin(UserSkill).group_by(Skill.id)

        category = request.args.get('category')
        if category:
            skills_query = skills_query.filter(Skill.category == category)

        search = request.args.get('search', '').strip()
        if search:
            skills_query = skills_query.filter(
                or_(
                    Skill.name.ilike(f'%{search}%'),
                    Skill.description.ilike(f'%{search}%')
                )
            )

        skills_data = []
        for skill, user_count in skills_query.all():
            skill_data = {
                'id': skill.id,
                'name': skill.name,
                'category': skill.category,
                'description': skill.description,
                'user_count': user_count,
                'users': [
                    {
                        'id': us.user.id,
                        'full_name': us.user.full_name,
                        'proficiency_level': us.proficiency_level,
                        'years_experience': us.years_experience
                    }
                    for us in skill.user_skills
                ] if hasattr(skill, 'user_skills') else []
            }
            skills_data.append(skill_data)

        # Get categories for filter
        categories = db.session.query(Skill.category).distinct().all()
        categories_list = [cat[0] for cat in categories if cat[0]]

        return api_response(
            data={
                'skills': skills_data,
                'categories': categories_list
            },
            message=f"Retrieved {len(skills_data)} skills"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/skills-matrix', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_skills_matrix():
    """
    Get skills matrix data - users vs skills with proficiency levels.
    """
    try:
        # Get filter parameters
        department_id = request.args.get('department_id')
        category = request.args.get('category')
        skill_ids = request.args.get('skill_ids', '').split(',') if request.args.get('skill_ids') else []
        min_level = request.args.get('min_level', type=int)
        max_level = request.args.get('max_level', type=int)

        # Build users query with filters
        users_query = User.query.filter(User.is_active == True)

        if department_id:
            users_query = users_query.filter(User.department_id == department_id)

        # Get users with their skills
        users = users_query.options(
            joinedload(User.detailed_skills).joinedload(UserSkill.skill),
            joinedload(User.department_obj)
        ).order_by(User.first_name, User.last_name).all()

        # Build skills query with filters
        skills_query = Skill.query

        if category:
            skills_query = skills_query.filter(Skill.category == category)

        if skill_ids and skill_ids != ['']:
            skills_query = skills_query.filter(Skill.id.in_(skill_ids))

        skills = skills_query.order_by(Skill.category, Skill.name).all()

        # Build matrix data
        matrix_data = []

        for user in users:
            # Create user skills lookup
            user_skills_dict = {
                us.skill_id: {
                    'proficiency_level': us.proficiency_level,
                    'years_experience': us.years_experience,
                    'is_certified': us.is_certified,
                    'certification_name': us.certification_name,
                    'self_assessed': us.self_assessed,
                    'manager_assessed': us.manager_assessed
                }
                for us in user.detailed_skills
            }

            # Build skills data for this user
            user_skills = []
            for skill in skills:
                skill_data = user_skills_dict.get(skill.id, {
                    'proficiency_level': 0,
                    'years_experience': 0,
                    'is_certified': False,
                    'certification_name': None,
                    'self_assessed': False,
                    'manager_assessed': False
                })

                # Apply level filters
                if min_level and skill_data['proficiency_level'] < min_level:
                    continue
                if max_level and skill_data['proficiency_level'] > max_level:
                    continue

                user_skills.append({
                    'skill_id': skill.id,
                    'skill_name': skill.name,
                    'skill_category': skill.category,
                    **skill_data
                })

            # Add user to matrix if they have skills matching filters
            if not min_level and not max_level or user_skills:
                matrix_data.append({
                    'user_id': user.id,
                    'full_name': user.full_name,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'email': user.email,
                    'position': user.position,
                    'department': user.department_obj.name if user.department_obj else None,
                    'department_id': user.department_id,
                    'profile_image': user.profile_image,
                    'skills': user_skills
                })

        # Build skills summary
        skills_summary = []
        for skill in skills:
            # Count users by proficiency level
            level_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            total_users = 0
            avg_level = 0

            for user_data in matrix_data:
                for user_skill in user_data['skills']:
                    if user_skill['skill_id'] == skill.id and user_skill['proficiency_level'] > 0:
                        level_counts[user_skill['proficiency_level']] += 1
                        total_users += 1
                        avg_level += user_skill['proficiency_level']

            avg_level = round(avg_level / total_users, 1) if total_users > 0 else 0

            skills_summary.append({
                'id': skill.id,
                'name': skill.name,
                'category': skill.category,
                'description': skill.description,
                'total_users': total_users,
                'avg_level': avg_level,
                'level_distribution': level_counts
            })

        # Calculate statistics
        stats = {
            'total_users': len(matrix_data),
            'total_skills': len(skills),
            'total_skill_assignments': sum(len(user['skills']) for user in matrix_data),
            'avg_skills_per_user': round(
                sum(len([s for s in user['skills'] if s['proficiency_level'] > 0]) for user in matrix_data) / len(matrix_data), 1
            ) if matrix_data else 0,
            'skill_coverage': {
                'beginner': sum(1 for skill in skills_summary if skill['avg_level'] >= 1 and skill['avg_level'] < 2),
                'intermediate': sum(1 for skill in skills_summary if skill['avg_level'] >= 2 and skill['avg_level'] < 4),
                'advanced': sum(1 for skill in skills_summary if skill['avg_level'] >= 4),
                'no_coverage': sum(1 for skill in skills_summary if skill['total_users'] == 0)
            }
        }

        # Get available departments and categories for filters
        departments = Department.query.filter(Department.is_active == True).order_by(Department.name).all()
        categories = db.session.query(Skill.category).distinct().filter(Skill.category.isnot(None)).all()

        return api_response(
            data={
                'matrix': matrix_data,
                'skills_summary': skills_summary,
                'stats': stats,
                'filters': {
                    'departments': [{'id': d.id, 'name': d.name} for d in departments],
                    'categories': [cat[0] for cat in categories if cat[0]]
                }
            },
            message=f"Retrieved skills matrix for {len(matrix_data)} users and {len(skills)} skills"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/skills', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def create_skill():
    """
    Create a new skill.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data.get('name'):
            return api_response(
                success=False,
                message='Nome competenza richiesto',
                status_code=400
            )

        # Check if skill name already exists
        existing_skill = Skill.query.filter_by(name=data['name']).first()
        if existing_skill:
            return api_response(
                success=False,
                message='Una competenza con questo nome esiste già',
                status_code=400
            )

        # Create new skill
        skill = Skill(
            name=data['name'],
            category=data.get('category', ''),
            description=data.get('description', '')
        )

        db.session.add(skill)
        db.session.commit()

        # Return created skill data
        skill_data = {
            'id': skill.id,
            'name': skill.name,
            'category': skill.category,
            'description': skill.description,
            'user_count': 0
        }

        return api_response(
            data={'skill': skill_data},
            message=f"Competenza '{skill.name}' creata con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/skills/<int:skill_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def update_skill(skill_id):
    """
    Update an existing skill.
    """
    try:
        skill = Skill.query.get_or_404(skill_id)
        data = request.get_json()

        # Validate name if provided
        if 'name' in data and data['name']:
            # Check if name already exists (excluding current skill)
            existing_skill = Skill.query.filter(
                Skill.name == data['name'],
                Skill.id != skill_id
            ).first()
            if existing_skill:
                return api_response(
                    success=False,
                    message='Una competenza con questo nome esiste già',
                    status_code=400
                )
            skill.name = data['name']

        # Update other fields
        if 'category' in data:
            skill.category = data['category']
        if 'description' in data:
            skill.description = data['description']

        db.session.commit()

        # Get user count for response
        user_count = UserSkill.query.filter_by(skill_id=skill.id).count()

        skill_data = {
            'id': skill.id,
            'name': skill.name,
            'category': skill.category,
            'description': skill.description,
            'user_count': user_count
        }

        return api_response(
            data={'skill': skill_data},
            message=f"Competenza '{skill.name}' aggiornata con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/skills/<int:skill_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def delete_skill(skill_id):
    """
    Delete a skill and all associated user skills.
    """
    try:
        skill = Skill.query.get_or_404(skill_id)

        # Check if skill is assigned to users
        user_skills_count = UserSkill.query.filter_by(skill_id=skill.id).count()
        if user_skills_count > 0:
            return api_response(
                success=False,
                message=f'Impossibile eliminare la competenza. È assegnata a {user_skills_count} dipendenti.',
                status_code=400
            )

        skill_name = skill.name
        db.session.delete(skill)
        db.session.commit()

        return api_response(
            message=f"Competenza '{skill_name}' eliminata con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

# ADMIN ENDPOINTS

@api_personnel.route('/admin/users', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def create_user():
    """
    Create a new user with full profile data.
    """
    try:
        from datetime import datetime
        data = request.get_json()

        # Validate required fields
        required_fields = ['username', 'email', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    success=False,
                    message=f'Campo {field} richiesto',
                    status_code=400
                )

        # Check if username or email already exists
        existing_user = User.query.filter(
            or_(User.username == data['username'], User.email == data['email'])
        ).first()
        if existing_user:
            return api_response(
                success=False,
                message='Username o email già esistenti',
                status_code=400
            )

        # Create new user
        user = User(
            username=data['username'],
            email=data['email'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            role=data.get('role', 'employee'),
            department_id=data.get('department_id'),
            position=data.get('position'),
            hire_date=datetime.strptime(data['hire_date'], '%Y-%m-%d').date() if data.get('hire_date') else None,
            phone=data.get('phone'),
            is_active=data.get('is_active', True)
        )

        # Set password if provided
        if data.get('password'):
            user.set_password(data['password'])
        else:
            # Generate temporary password
            import secrets
            temp_password = secrets.token_urlsafe(12)
            user.set_password(temp_password)

        db.session.add(user)
        db.session.flush()  # Get user ID

        # Create profile if profile data provided
        profile_fields = [
            'employee_id', 'job_title', 'birth_date', 'address', 'employment_type',
            'work_location', 'salary', 'salary_currency', 'probation_end_date',
            'contract_end_date', 'notice_period_days', 'weekly_hours', 'daily_hours',
            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship'
        ]

        if any(field in data for field in profile_fields):
            profile = UserProfile(
                user_id=user.id,
                employee_id=data.get('employee_id'),
                job_title=data.get('job_title'),
                birth_date=datetime.strptime(data['birth_date'], '%Y-%m-%d').date() if data.get('birth_date') else None,
                address=data.get('address'),
                employment_type=data.get('employment_type', 'full_time'),
                work_location=data.get('work_location'),
                salary=data.get('salary'),
                salary_currency=data.get('salary_currency', 'EUR'),
                probation_end_date=datetime.strptime(data['probation_end_date'], '%Y-%m-%d').date() if data.get('probation_end_date') else None,
                contract_end_date=datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date() if data.get('contract_end_date') else None,
                notice_period_days=data.get('notice_period_days', 30),
                weekly_hours=data.get('weekly_hours', 40.0),
                daily_hours=data.get('daily_hours', 8.0),
                emergency_contact_name=data.get('emergency_contact_name'),
                emergency_contact_phone=data.get('emergency_contact_phone'),
                emergency_contact_relationship=data.get('emergency_contact_relationship')
            )

            db.session.add(profile)

        db.session.commit()

        # Return created user data
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.role,
            'department_id': user.department_id,
            'position': user.position,
            'hire_date': user.hire_date.isoformat() if user.hire_date else None,
            'phone': user.phone,
            'is_active': user.is_active,
            'created_at': user.created_at.isoformat()
        }

        return api_response(
            data={'user': user_data},
            message=f"Utente '{user.full_name}' creato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/admin/users/<int:user_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def delete_user(user_id):
    """
    Delete a user (soft delete by setting is_active=False).
    """
    try:
        user = User.query.get_or_404(user_id)

        # Prevent self-deletion
        if user.id == current_user.id:
            return api_response(
                success=False,
                message='Non puoi eliminare il tuo stesso account',
                status_code=400
            )

        # Soft delete
        user.is_active = False
        db.session.commit()

        return api_response(
            message=f"Utente '{user.full_name}' disattivato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/admin/users/<int:user_id>/reset-password', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def reset_user_password(user_id):
    """
    Reset user password to a temporary one.
    """
    try:
        user = User.query.get_or_404(user_id)

        # Generate temporary password
        import secrets
        temp_password = secrets.token_urlsafe(12)
        user.set_password(temp_password)

        db.session.commit()

        return api_response(
            data={'temporary_password': temp_password},
            message=f"Password di '{user.full_name}' resettata con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/admin/analytics', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_admin_analytics():
    """
    Get comprehensive analytics for personnel administration.
    """
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import extract

        # Basic statistics
        total_users = User.query.filter_by(is_active=True).count()
        total_departments = Department.query.filter_by(is_active=True).count()

        # Users by role
        users_by_role = db.session.query(
            User.role, func.count(User.id)
        ).filter_by(is_active=True).group_by(User.role).all()

        # Users by department
        users_by_dept = db.session.query(
            Department.name, func.count(User.id)
        ).join(User, Department.id == User.department_id)\
         .filter(User.is_active == True, Department.is_active == True)\
         .group_by(Department.name).all()

        # Contract expiring soon (next 90 days)
        today = datetime.now().date()
        expiring_soon = today + timedelta(days=90)

        expiring_contracts = db.session.query(User, UserProfile)\
            .join(UserProfile, User.id == UserProfile.user_id)\
            .filter(
                User.is_active == True,
                UserProfile.contract_end_date.isnot(None),
                UserProfile.contract_end_date <= expiring_soon,
                UserProfile.contract_end_date >= today
            ).all()

        # Probation ending soon (next 30 days)
        probation_ending = today + timedelta(days=30)

        ending_probation = db.session.query(User, UserProfile)\
            .join(UserProfile, User.id == UserProfile.user_id)\
            .filter(
                User.is_active == True,
                UserProfile.probation_end_date.isnot(None),
                UserProfile.probation_end_date <= probation_ending,
                UserProfile.probation_end_date >= today
            ).all()

        # Employment types distribution
        employment_types = db.session.query(
            UserProfile.employment_type, func.count(UserProfile.id)
        ).join(User, UserProfile.user_id == User.id)\
         .filter(User.is_active == True)\
         .group_by(UserProfile.employment_type).all()

        # Average salary by department (if salary data exists)
        avg_salary_by_dept = db.session.query(
            Department.name, func.avg(UserProfile.salary)
        ).join(User, Department.id == User.department_id)\
         .join(UserProfile, User.id == UserProfile.user_id)\
         .filter(
             User.is_active == True,
             Department.is_active == True,
             UserProfile.salary.isnot(None)
         ).group_by(Department.name).all()

        # Recent hires (last 90 days)
        recent_hire_date = today - timedelta(days=90)
        recent_hires = User.query.filter(
            User.is_active == True,
            User.hire_date >= recent_hire_date
        ).count()

        analytics_data = {
            'overview': {
                'total_users': total_users,
                'total_departments': total_departments,
                'recent_hires': recent_hires
            },
            'users_by_role': [{'role': role, 'count': count} for role, count in users_by_role],
            'users_by_department': [{'department': dept, 'count': count} for dept, count in users_by_dept],
            'employment_types': [{'type': emp_type or 'Non specificato', 'count': count} for emp_type, count in employment_types],
            'avg_salary_by_department': [{'department': dept, 'avg_salary': float(avg_sal) if avg_sal else 0} for dept, avg_sal in avg_salary_by_dept],
            'alerts': {
                'expiring_contracts': [
                    {
                        'user_id': user.id,
                        'full_name': user.full_name,
                        'contract_end_date': profile.contract_end_date.isoformat(),
                        'days_remaining': (profile.contract_end_date - today).days
                    }
                    for user, profile in expiring_contracts
                ],
                'ending_probation': [
                    {
                        'user_id': user.id,
                        'full_name': user.full_name,
                        'probation_end_date': profile.probation_end_date.isoformat(),
                        'days_remaining': (profile.probation_end_date - today).days
                    }
                    for user, profile in ending_probation
                ]
            }
        }

        return api_response(
            data=analytics_data,
            message="Analytics data retrieved successfully"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/export', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def export_personnel_data():
    """
    Export personnel data to CSV format.
    """
    try:
        import csv
        import io
        from datetime import datetime

        # Get all active users with their profiles
        users_query = db.session.query(User, UserProfile, Department)\
            .outerjoin(UserProfile, User.id == UserProfile.user_id)\
            .outerjoin(Department, User.department_id == Department.id)\
            .filter(User.is_active == True)\
            .order_by(User.last_name, User.first_name)

        users_data = users_query.all()

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        headers = [
            'ID', 'Nome', 'Cognome', 'Email', 'Username', 'Telefono',
            'Ruolo', 'Dipartimento', 'Posizione', 'Data Assunzione',
            'Tipo Contratto', 'Modalità Lavoro', 'Ore Settimanali',
            'Stipendio', 'Valuta', 'Fine Periodo Prova', 'Scadenza Contratto',
            'Giorni Preavviso', 'Contatto Emergenza', 'Tel. Emergenza', 'Relazione Emergenza',
            'Indirizzo', 'Data Nascita', 'Creato il'
        ]
        writer.writerow(headers)

        # Write data rows
        for user, profile, department in users_data:
            row = [
                user.id,
                user.first_name,
                user.last_name,
                user.email,
                user.username,
                user.phone or '',
                user.role,
                department.name if department else '',
                user.position or '',
                user.hire_date.isoformat() if user.hire_date else '',
                profile.employment_type if profile else '',
                profile.work_location if profile else '',
                profile.weekly_hours if profile else '',
                profile.salary if profile else '',
                profile.salary_currency if profile else '',
                profile.probation_end_date.isoformat() if profile and profile.probation_end_date else '',
                profile.contract_end_date.isoformat() if profile and profile.contract_end_date else '',
                profile.notice_period_days if profile else '',
                profile.emergency_contact_name if profile else '',
                profile.emergency_contact_phone if profile else '',
                profile.emergency_contact_relationship if profile else '',
                profile.address if profile else '',
                profile.birth_date.isoformat() if profile and profile.birth_date else '',
                user.created_at.isoformat() if user.created_at else ''
            ]
            writer.writerow(row)

        # Prepare response
        output.seek(0)
        csv_data = output.getvalue()
        output.close()

        # Create response with CSV data
        response = make_response(csv_data)
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename=personnel-export-{datetime.now().strftime("%Y%m%d")}.csv'

        return response

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/export/contacts', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def export_contacts_data():
    """
    Export only contact information to CSV format.
    """
    try:
        import csv
        import io
        from datetime import datetime

        # Get all active users with basic contact info
        users = User.query.filter_by(is_active=True)\
            .order_by(User.last_name, User.first_name).all()

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        headers = ['Nome', 'Cognome', 'Email', 'Telefono', 'Dipartimento', 'Posizione']
        writer.writerow(headers)

        # Write data
        for user in users:
            row = [
                user.first_name or '',
                user.last_name or '',
                user.email or '',
                user.phone or '',
                user.department_obj.name if user.department_obj else '',
                user.position or ''
            ]
            writer.writerow(row)

        # Prepare response
        output.seek(0)
        csv_data = output.getvalue()
        output.close()

        # Create response with CSV data
        response = make_response(csv_data)
        response.headers['Content-Type'] = 'text/csv'
        response.headers['Content-Disposition'] = f'attachment; filename=contacts-export-{datetime.now().strftime("%Y%m%d")}.csv'

        return response

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/import', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def import_personnel_data():
    """
    Import personnel data from CSV file.
    """
    try:
        import csv
        import io
        from datetime import datetime

        # Check if file was uploaded
        if 'file' not in request.files:
            return api_response(
                success=False,
                message='Nessun file caricato',
                status_code=400
            )

        file = request.files['file']
        if file.filename == '':
            return api_response(
                success=False,
                message='Nessun file selezionato',
                status_code=400
            )

        if not file.filename.lower().endswith('.csv'):
            return api_response(
                success=False,
                message='Solo file CSV sono supportati',
                status_code=400
            )

        # Read CSV content
        stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
        csv_input = csv.DictReader(stream)

        imported_count = 0
        total_count = 0
        errors = []

        for row_num, row in enumerate(csv_input, start=2):  # Start from 2 (header is row 1)
            total_count += 1

            try:
                # Validate required fields
                if not row.get('email') or not row.get('first_name') or not row.get('last_name'):
                    errors.append(f"Riga {row_num}: Email, nome e cognome sono obbligatori")
                    continue

                # Check if user already exists
                existing_user = User.query.filter_by(email=row['email']).first()
                if existing_user:
                    errors.append(f"Riga {row_num}: Email {row['email']} già esistente")
                    continue

                # Create new user
                user = User(
                    username=row.get('email'),  # Use email as username if not provided
                    email=row['email'],
                    first_name=row['first_name'],
                    last_name=row['last_name'],
                    phone=row.get('phone', ''),
                    role=row.get('role', 'employee'),
                    is_active=row.get('is_active', 'true').lower() == 'true',
                    position=row.get('position', '')
                )

                # Set department if provided
                if row.get('department_id'):
                    try:
                        dept_id = int(row['department_id'])
                        dept = Department.query.get(dept_id)
                        if dept:
                            user.department_id = dept_id
                        else:
                            errors.append(f"Riga {row_num}: Dipartimento ID {dept_id} non trovato")
                    except ValueError:
                        errors.append(f"Riga {row_num}: ID dipartimento non valido")

                # Set password (temporary)
                user.set_password('TempPassword123!')

                db.session.add(user)
                db.session.flush()  # Get user ID

                # Create profile if additional data is provided
                if any(row.get(field) for field in ['hire_date', 'employment_type', 'salary']):
                    profile = UserProfile(user_id=user.id)

                    # Set hire date
                    if row.get('hire_date'):
                        try:
                            profile.hire_date = datetime.strptime(row['hire_date'], '%Y-%m-%d').date()
                        except ValueError:
                            errors.append(f"Riga {row_num}: Formato data assunzione non valido (usa YYYY-MM-DD)")

                    # Set employment type
                    if row.get('employment_type'):
                        profile.employment_type = row['employment_type']

                    # Set salary
                    if row.get('salary'):
                        try:
                            profile.salary = float(row['salary'])
                        except ValueError:
                            errors.append(f"Riga {row_num}: Stipendio non valido")

                    db.session.add(profile)

                imported_count += 1

            except Exception as e:
                errors.append(f"Riga {row_num}: Errore durante l'importazione - {str(e)}")
                continue

        # Commit all changes
        db.session.commit()

        return api_response(
            data={
                'imported': imported_count,
                'total': total_count,
                'errors': errors[:10]  # Limit to first 10 errors
            },
            message=f"Import completato: {imported_count}/{total_count} dipendenti importati"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/verify', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def verify_data_integrity():
    """
    Verify data integrity and return issues found.
    """
    try:
        issues = []

        # Check for users without email
        users_no_email = User.query.filter(
            or_(User.email == None, User.email == '')
        ).filter_by(is_active=True).count()

        if users_no_email > 0:
            issues.append(f"{users_no_email} dipendenti attivi senza email")

        # Check for users without names
        users_no_name = User.query.filter(
            or_(
                and_(User.first_name == None, User.last_name == None),
                and_(User.first_name == '', User.last_name == '')
            )
        ).filter_by(is_active=True).count()

        if users_no_name > 0:
            issues.append(f"{users_no_name} dipendenti attivi senza nome")

        # Check for users without department
        users_no_dept = User.query.filter_by(
            department_id=None,
            is_active=True
        ).count()

        if users_no_dept > 0:
            issues.append(f"{users_no_dept} dipendenti attivi senza dipartimento")

        # Check for departments without manager
        depts_no_manager = Department.query.filter_by(
            manager_id=None,
            is_active=True
        ).count()

        if depts_no_manager > 0:
            issues.append(f"{depts_no_manager} dipartimenti attivi senza manager")

        # Check for orphaned user profiles
        orphaned_profiles = db.session.query(UserProfile).outerjoin(User).filter(
            User.id == None
        ).count()

        if orphaned_profiles > 0:
            issues.append(f"{orphaned_profiles} profili utente orfani")

        # Check for duplicate emails
        duplicate_emails = db.session.query(User.email, func.count(User.id))\
            .filter(User.email != None, User.email != '')\
            .group_by(User.email)\
            .having(func.count(User.id) > 1).count()

        if duplicate_emails > 0:
            issues.append(f"{duplicate_emails} email duplicate")

        return api_response(
            data={'issues': issues},
            message=f"Verifica completata: {len(issues)} problemi trovati"
        )

    except Exception as e:
        return handle_api_error(e)

# ============================================================================
# CV AND DOCUMENTS MANAGEMENT API ENDPOINTS
# ============================================================================

@api_personnel.route('/users/<int:user_id>/cv/upload', methods=['POST'])
@api_login_required
def upload_cv(user_id):
    """
    Upload CV for a user with AI analysis.
    """
    try:
        user_to_edit = User.query.get_or_404(user_id)

        # Permission check: own profile or admin/manager/HR
        if (user_to_edit.id != current_user.id and
            not current_user.role in ['admin', 'manager', 'human_resources']):
            return api_response(
                success=False,
                message='Non hai il permesso di modificare il CV di questo utente',
                status_code=403
            )

        # Check if file was uploaded
        if 'cv_file' not in request.files:
            return api_response(
                success=False,
                message='Nessun file CV caricato',
                status_code=400
            )

        file = request.files['cv_file']
        if file.filename == '':
            return api_response(
                success=False,
                message='Nessun file selezionato',
                status_code=400
            )

        # Validate file
        if not is_valid_cv_file(file.filename):
            return api_response(
                success=False,
                message='Formato file non supportato. Usa PDF, DOCX, DOC o TXT',
                status_code=400
            )

        # Check file size
        if get_file_size_mb(file) > 10:
            return api_response(
                success=False,
                message='File troppo grande. Dimensione massima: 10MB',
                status_code=400
            )

        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'cv')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate secure filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"cv_{user_to_edit.id}_{timestamp}_{secure_filename(file.filename)}"
        file_path = os.path.join('cv', filename)
        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)

        # Save file
        file.save(full_path)

        # Get or create profile
        profile = user_to_edit.profile
        if not profile:
            profile = UserProfile(user_id=user_to_edit.id)
            db.session.add(profile)
            db.session.flush()

        # Remove old CV if exists
        if profile.current_cv_path:
            old_cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile.current_cv_path)
            if os.path.exists(old_cv_path):
                try:
                    os.remove(old_cv_path)
                except OSError:
                    pass  # Ignore if file can't be deleted

        # Update profile
        profile.current_cv_path = file_path
        profile.cv_last_updated = datetime.utcnow()

        # AI Analysis if requested
        analyze_skills = request.form.get('analyze_skills', 'true').lower() == 'true'
        analysis_result = None

        if analyze_skills:
            cv_text = extract_text_from_cv(full_path)
            if cv_text:
                try:
                    analysis_result = extract_skills_from_cv(cv_text)
                    if 'error' not in analysis_result:
                        profile.cv_analysis_data = json.dumps(analysis_result)

                        # Auto-add skills if requested
                        auto_add_skills = request.form.get('auto_add_skills', 'false').lower() == 'true'
                        if auto_add_skills and 'skills' in analysis_result:
                            added_skills = []
                            for skill_data in analysis_result['skills'][:10]:  # Limit to first 10
                                skill_name = skill_data.get('name', '').strip()
                                if not skill_name:
                                    continue

                                # Find or create skill
                                skill = Skill.query.filter_by(name=skill_name).first()
                                if not skill:
                                    skill = Skill(
                                        name=skill_name,
                                        category=skill_data.get('category', 'Generale')
                                    )
                                    db.session.add(skill)
                                    db.session.flush()

                                # Check if user already has this skill
                                existing_user_skill = UserSkill.query.filter_by(
                                    user_id=user_to_edit.id,
                                    skill_id=skill.id
                                ).first()

                                if not existing_user_skill:
                                    # Add skill to user
                                    user_skill = UserSkill(
                                        user_id=user_to_edit.id,
                                        skill_id=skill.id,
                                        proficiency_level=skill_data.get('level', 3),
                                        years_experience=skill_data.get('years_experience', 0),
                                        notes=f"Estratto automaticamente dal CV - {skill_data.get('context', '')}"
                                    )
                                    db.session.add(user_skill)
                                    added_skills.append(skill_name)

                            analysis_result['added_skills'] = added_skills

                except Exception as ai_error:
                    # Don't fail the upload if AI analysis fails
                    analysis_result = {'error': f'Errore analisi AI: {str(ai_error)}'}

        # Recalculate profile completion
        profile.calculate_completion()
        db.session.commit()

        return api_response(
            data={
                'cv_path': file_path,
                'cv_last_updated': profile.cv_last_updated.isoformat(),
                'analysis': analysis_result,
                'profile_completion': profile.profile_completion
            },
            message='CV caricato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>/cv/download', methods=['GET'])
@api_login_required
def download_cv(user_id):
    """
    Download CV file for a user.
    """
    try:
        user_to_view = User.query.get_or_404(user_id)

        # Permission check: own profile or view personnel data permission
        if (user_to_view.id != current_user.id and
            not current_user.role in ['admin', 'manager', 'human_resources']):
            return api_response(
                success=False,
                message='Non hai il permesso di scaricare questo CV',
                status_code=403
            )

        if not user_to_view.profile or not user_to_view.profile.current_cv_path:
            return api_response(
                success=False,
                message='Nessun CV disponibile per questo utente',
                status_code=404
            )

        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_view.profile.current_cv_path)

        if not os.path.exists(cv_path):
            return api_response(
                success=False,
                message='File CV non trovato sul server',
                status_code=404
            )

        # Get original filename from path
        original_filename = os.path.basename(user_to_view.profile.current_cv_path)
        # Clean filename for download
        download_filename = f"CV_{user_to_view.full_name}_{original_filename.split('_')[-1]}"

        return send_file(
            cv_path,
            as_attachment=True,
            download_name=download_filename
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>/cv', methods=['DELETE'])
@api_login_required
def delete_cv(user_id):
    """
    Delete CV file for a user.
    """
    try:
        user_to_edit = User.query.get_or_404(user_id)

        # Permission check: own profile or admin/manager/HR
        if (user_to_edit.id != current_user.id and
            not current_user.role in ['admin', 'manager', 'human_resources']):
            return api_response(
                success=False,
                message='Non hai il permesso di cancellare il CV di questo utente',
                status_code=403
            )

        if not user_to_edit.profile or not user_to_edit.profile.current_cv_path:
            return api_response(
                success=False,
                message='Nessun CV da cancellare',
                status_code=404
            )

        # Remove file from filesystem
        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_edit.profile.current_cv_path)
        if os.path.exists(cv_path):
            try:
                os.remove(cv_path)
            except OSError:
                pass  # Ignore if file can't be deleted

        # Clear CV data from profile
        profile = user_to_edit.profile
        profile.current_cv_path = None
        profile.cv_last_updated = None
        profile.cv_analysis_data = None

        # Recalculate profile completion
        profile.calculate_completion()
        db.session.commit()

        return api_response(
            data={'profile_completion': profile.profile_completion},
            message='CV cancellato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>/cv/analysis', methods=['GET'])
@api_login_required
def get_cv_analysis(user_id):
    """
    Get CV analysis data for a user.
    """
    try:
        user_to_view = User.query.get_or_404(user_id)

        # Permission check: own profile or view personnel data permission
        if (user_to_view.id != current_user.id and
            not current_user.role in ['admin', 'manager', 'human_resources']):
            return api_response(
                success=False,
                message='Non hai il permesso di visualizzare l\'analisi CV di questo utente',
                status_code=403
            )

        if not user_to_view.profile or not user_to_view.profile.cv_analysis_data:
            return api_response(
                success=False,
                message='Nessuna analisi CV disponibile per questo utente',
                status_code=404
            )

        try:
            analysis_data = json.loads(user_to_view.profile.cv_analysis_data)
        except json.JSONDecodeError:
            return api_response(
                success=False,
                message='Dati di analisi CV corrotti',
                status_code=500
            )

        return api_response(
            data={
                'analysis': analysis_data,
                'cv_last_updated': user_to_view.profile.cv_last_updated.isoformat() if user_to_view.profile.cv_last_updated else None
            },
            message='Analisi CV recuperata con successo'
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>/skills/from-cv', methods=['POST'])
@api_login_required
def add_skills_from_cv_analysis(user_id):
    """
    Add skills to user profile from CV analysis data.
    """
    try:
        user_to_edit = User.query.get_or_404(user_id)

        # Permission check: own profile or admin/manager/HR
        if (user_to_edit.id != current_user.id and
            not current_user.role in ['admin', 'manager', 'human_resources']):
            return api_response(
                success=False,
                message='Non hai il permesso di modificare le competenze di questo utente',
                status_code=403
            )

        if not user_to_edit.profile or not user_to_edit.profile.cv_analysis_data:
            return api_response(
                success=False,
                message='Nessuna analisi CV disponibile per questo utente',
                status_code=404
            )

        try:
            analysis_data = json.loads(user_to_edit.profile.cv_analysis_data)
        except json.JSONDecodeError:
            return api_response(
                success=False,
                message='Dati di analisi CV corrotti',
                status_code=500
            )

        if 'skills' not in analysis_data:
            return api_response(
                success=False,
                message='Nessuna competenza trovata nell\'analisi CV',
                status_code=404
            )

        # Get selected skills from request
        data = request.get_json()
        selected_skills = data.get('selected_skills', [])

        if not selected_skills:
            return api_response(
                success=False,
                message='Nessuna competenza selezionata',
                status_code=400
            )

        added_skills = []
        skipped_skills = []

        for skill_index in selected_skills:
            if skill_index >= len(analysis_data['skills']):
                continue

            skill_data = analysis_data['skills'][skill_index]
            skill_name = skill_data.get('name', '').strip()

            if not skill_name:
                continue

            # Find or create skill
            skill = Skill.query.filter_by(name=skill_name).first()
            if not skill:
                skill = Skill(
                    name=skill_name,
                    category=skill_data.get('category', 'Generale'),
                    description=skill_data.get('description', '')
                )
                db.session.add(skill)
                db.session.flush()

            # Check if user already has this skill
            existing_user_skill = UserSkill.query.filter_by(
                user_id=user_to_edit.id,
                skill_id=skill.id
            ).first()

            if existing_user_skill:
                skipped_skills.append(skill_name)
                continue

            # Add skill to user
            user_skill = UserSkill(
                user_id=user_to_edit.id,
                skill_id=skill.id,
                proficiency_level=skill_data.get('level', 3),
                years_experience=skill_data.get('years_experience', 0),
                notes=f"Estratto dal CV - {skill_data.get('context', '')}"
            )
            db.session.add(user_skill)
            added_skills.append(skill_name)

        db.session.commit()

        return api_response(
            data={
                'added_skills': added_skills,
                'skipped_skills': skipped_skills,
                'total_added': len(added_skills),
                'total_skipped': len(skipped_skills)
            },
            message=f'Aggiunte {len(added_skills)} competenze al profilo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)