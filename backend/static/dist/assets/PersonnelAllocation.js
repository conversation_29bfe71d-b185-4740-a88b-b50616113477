import{r as u,f as P,A as V,c as l,j as t,g as f,I as B,v as E,H as L,n as g,t as a,F as b,k,o as d,z as w}from"./vendor.js";import{u as S}from"./app.js";const T={class:"personnel-allocation"},D={class:"flex justify-between items-center mb-6"},$={class:"flex items-center space-x-4"},I={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},R={key:0,class:"flex justify-center items-center h-64"},H={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},N={class:"flex"},U={class:"text-red-800 dark:text-red-200"},F={key:2,class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},q={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},O={class:"flex items-center"},X={class:"ml-4"},G={class:"text-2xl font-semibold text-gray-900 dark:text-white"},J={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},K={class:"flex items-center"},Q={class:"ml-4"},W={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Y={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},Z={class:"flex items-center"},tt={class:"ml-4"},et={class:"text-2xl font-semibold text-gray-900 dark:text-white"},st={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},at={class:"flex items-center"},rt={class:"ml-4"},lt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},dt={key:3,class:"space-y-6"},ot={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow"},it={class:"p-6"},nt={class:"space-y-4"},ct={class:"flex items-center justify-between mb-4"},ut={class:"flex items-center"},gt={class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"},xt={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},yt={class:"ml-3"},mt={class:"text-lg font-medium text-gray-900 dark:text-white"},pt={class:"text-sm text-gray-500 dark:text-gray-400"},vt={class:"text-right"},ht={class:"space-y-3"},_t={class:"flex justify-between text-sm mb-1"},ft={class:"text-gray-900 dark:text-white"},bt={class:"flex justify-between text-sm mb-1"},kt={class:"text-gray-900 dark:text-white"},wt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},zt={class:"flex justify-between text-sm mb-1"},Ct={class:"text-gray-900 dark:text-white"},jt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},At={key:0,class:"mt-4"},Mt={class:"space-y-2"},Pt={class:"text-gray-600 dark:text-gray-400"},Vt={class:"flex items-center space-x-2"},Bt={class:"text-gray-900 dark:text-white"},Et={class:"text-gray-500 dark:text-gray-400"},Lt={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow"},St={class:"overflow-x-auto"},Tt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Dt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap"},It={class:"flex items-center"},Rt={class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"},Ht={class:"text-xs font-medium text-blue-600 dark:text-blue-400"},Nt={class:"ml-3"},Ut={class:"text-sm font-medium text-gray-900 dark:text-white"},Ft={class:"px-6 py-4 whitespace-nowrap"},qt={class:"text-sm text-gray-900 dark:text-white"},Ot={class:"text-sm text-gray-500 dark:text-gray-400"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Gt={class:"px-6 py-4 whitespace-nowrap"},Jt={class:"flex items-center"},Kt={class:"text-sm font-medium text-gray-900 dark:text-white"},Qt={class:"ml-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Yt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Zt={class:"px-6 py-4 whitespace-nowrap"},ae={__name:"PersonnelAllocation",setup(te){const j=S(),x=u(!1),i=u(null),v=u("current-month"),y=u("summary"),n=u([]),z=u([]),c=P(()=>{if(!n.value.length)return{totalCapacity:0,totalAllocated:0,totalActual:0,utilizationPercentage:0,utilizationClass:"bg-gray-100 dark:bg-gray-700",utilizationIconClass:"text-gray-600 dark:text-gray-400"};const o=n.value.reduce((m,p)=>m+p.capacity_hours,0),e=n.value.reduce((m,p)=>m+p.allocated_hours,0),s=n.value.reduce((m,p)=>m+p.actual_hours,0),r=o>0?Math.round(s/o*100):0;let h="bg-gray-100 dark:bg-gray-700",_="text-gray-600 dark:text-gray-400";return r>100?(h="bg-red-100 dark:bg-red-900",_="text-red-600 dark:text-red-400"):r>=90?(h="bg-yellow-100 dark:bg-yellow-900",_="text-yellow-600 dark:text-yellow-400"):r>=70&&(h="bg-green-100 dark:bg-green-900",_="text-green-600 dark:text-green-400"),{totalCapacity:o,totalAllocated:e,totalActual:s,utilizationPercentage:r,utilizationClass:h,utilizationIconClass:_}}),C=async()=>{var o,e;x.value=!0,i.value=null;try{console.log("Calling API:",`/api/allocation-analysis?period=${v.value}`);const s=await fetch(`/api/allocation-analysis?period=${v.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(!s.ok)throw new Error("Errore nel caricamento dei dati di allocazione");const r=await s.json();n.value=((o=r.data)==null?void 0:o.summary)||[],z.value=((e=r.data)==null?void 0:e.detailed)||[]}catch(s){i.value=s.message,console.error("Error loading allocation data:",s)}finally{x.value=!1}},A=o=>o>100?"text-red-600 dark:text-red-400":o>=90?"text-yellow-600 dark:text-yellow-400":o>=70?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400",M=o=>o>0?"text-red-600 dark:text-red-400":o<0?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400";return V(()=>{C()}),(o,e)=>(d(),l("div",T,[t("div",D,[e[4]||(e[4]=B('<div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Allocazione Risorse</h1><p class="text-gray-600 dark:text-gray-400 mt-1">Analisi temporale e confronto pianificato vs effettivo</p></div></div>',1)),t("div",$,[E(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>v.value=s),onChange:C,class:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},e[3]||(e[3]=[t("option",{value:"current-month"},"Mese Corrente",-1),t("option",{value:"current-quarter"},"Trimestre Corrente",-1),t("option",{value:"current-year"},"Anno Corrente",-1),t("option",{value:"next-quarter"},"Prossimo Trimestre",-1)]),544),[[L,v.value]]),t("div",I,[t("button",{onClick:e[1]||(e[1]=s=>y.value="summary"),class:g([y.value==="summary"?"bg-white dark:bg-gray-600 shadow":"","px-3 py-1 text-sm rounded-md transition-colors"])}," Riepilogo ",2),t("button",{onClick:e[2]||(e[2]=s=>y.value="detailed"),class:g([y.value==="detailed"?"bg-white dark:bg-gray-600 shadow":"","px-3 py-1 text-sm rounded-md transition-colors"])}," Dettagliato ",2)])])]),x.value?(d(),l("div",R,e[5]||(e[5]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):i.value?(d(),l("div",H,[t("div",N,[e[6]||(e[6]=t("svg",{class:"w-5 h-5 text-red-400 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),t("p",U,a(i.value),1)])])):f("",!0),!x.value&&!i.value?(d(),l("div",F,[t("div",q,[t("div",O,[e[8]||(e[8]=t("div",{class:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",X,[e[7]||(e[7]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Capacità Totale",-1)),t("p",G,a(c.value.totalCapacity)+"h",1)])])]),t("div",J,[t("div",K,[e[10]||(e[10]=t("div",{class:"p-2 bg-green-100 dark:bg-green-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),t("div",Q,[e[9]||(e[9]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Allocato",-1)),t("p",W,a(c.value.totalAllocated)+"h",1)])])]),t("div",Y,[t("div",Z,[e[12]||(e[12]=t("div",{class:"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])],-1)),t("div",tt,[e[11]||(e[11]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Effettivo",-1)),t("p",et,a(c.value.totalActual)+"h",1)])])]),t("div",st,[t("div",at,[t("div",{class:g(["p-2",c.value.utilizationClass])},[(d(),l("svg",{class:g(["w-6 h-6",c.value.utilizationIconClass]),fill:"currentColor",viewBox:"0 0 20 20"},e[13]||(e[13]=[t("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]),2))],2),t("div",rt,[e[14]||(e[14]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo",-1)),t("p",lt,a(c.value.utilizationPercentage)+"%",1)])])])])):f("",!0),!x.value&&!i.value?(d(),l("div",dt,[y.value==="summary"?(d(),l("div",ot,[e[21]||(e[21]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Riepilogo per Persona")],-1)),t("div",it,[t("div",nt,[(d(!0),l(b,null,k(n.value,s=>(d(),l("div",{key:s.user_id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[t("div",ct,[t("div",ut,[t("div",gt,[t("span",xt,a(s.user_name.split(" ").map(r=>r[0]).join("")),1)]),t("div",yt,[t("h4",mt,a(s.user_name),1),t("p",pt,a(s.role||"Nessun ruolo"),1)])]),t("div",vt,[e[15]||(e[15]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Utilizzo",-1)),t("div",{class:g(["text-lg font-semibold",A(s.utilization_percentage)])},a(Math.round(s.utilization_percentage))+"% ",3)])]),t("div",ht,[t("div",null,[t("div",_t,[e[16]||(e[16]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Capacità",-1)),t("span",ft,a(s.capacity_hours)+"h",1)]),e[17]||(e[17]=t("div",{class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},[t("div",{class:"bg-gray-400 h-2 rounded-full",style:{width:"100%"}})],-1))]),t("div",null,[t("div",bt,[e[18]||(e[18]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Allocato",-1)),t("span",kt,a(s.allocated_hours)+"h",1)]),t("div",wt,[t("div",{class:"bg-blue-500 h-2 rounded-full",style:w({width:Math.min(s.allocated_hours/s.capacity_hours*100,100)+"%"})},null,4)])]),t("div",null,[t("div",zt,[e[19]||(e[19]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Effettivo",-1)),t("span",Ct,a(s.actual_hours)+"h",1)]),t("div",jt,[t("div",{class:"bg-green-500 h-2 rounded-full",style:w({width:Math.min(s.actual_hours/s.capacity_hours*100,100)+"%"})},null,4)])])]),s.projects&&s.projects.length>0?(d(),l("div",At,[e[20]||(e[20]=t("h5",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Progetti Attivi",-1)),t("div",Mt,[(d(!0),l(b,null,k(s.projects,r=>(d(),l("div",{key:r.project_id,class:"flex justify-between items-center text-sm"},[t("span",Pt,a(r.project_name),1),t("div",Vt,[t("span",Bt,a(r.allocation_percentage)+"%",1),t("span",Et,"("+a(r.allocated_hours)+"h)",1)])]))),128))])])):f("",!0)]))),128))])])])):(d(),l("div",Lt,[e[23]||(e[23]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Vista Dettagliata")],-1)),t("div",St,[t("table",Tt,[e[22]||(e[22]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Persona "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Allocazione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Pianificate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Effettive "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Varianza ")])],-1)),t("tbody",Dt,[(d(!0),l(b,null,k(z.value,s=>(d(),l("tr",{key:`${s.user_id}-${s.project_id}`},[t("td",$t,[t("div",It,[t("div",Rt,[t("span",Ht,a(s.user_name.split(" ").map(r=>r[0]).join("")),1)]),t("div",Nt,[t("div",Ut,a(s.user_name),1)])])]),t("td",Ft,[t("div",qt,a(s.project_name),1),t("div",Ot,a(s.project_period),1)]),t("td",Xt,a(s.role),1),t("td",Gt,[t("div",Jt,[t("div",Kt,a(s.allocation_percentage)+"%",1),t("div",Qt,[t("div",{class:"bg-blue-500 h-2 rounded-full",style:w({width:s.allocation_percentage+"%"})},null,4)])])]),t("td",Wt,a(s.planned_hours)+"h ",1),t("td",Yt,a(s.actual_hours)+"h ",1),t("td",Zt,[t("span",{class:g(["text-sm font-medium",M(s.variance)])},a(s.variance>0?"+":"")+a(s.variance)+"h ",3)])]))),128))])])])]))])):f("",!0)]))}};export{ae as default};
