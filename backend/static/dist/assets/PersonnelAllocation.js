import{r as i,f as U,A as R,c as l,j as t,g as C,I as F,v as z,H as M,F as _,k as f,C as I,n as y,t as s,o,z as P}from"./vendor.js";import{u as $}from"./app.js";const H={class:"personnel-allocation"},N={class:"flex justify-between items-center mb-6"},q={class:"flex flex-wrap items-center gap-4"},O=["value"],X=["value"],G={class:"flex items-center space-x-2 text-sm text-gray-700 dark:text-gray-300"},J={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},K={key:0,class:"flex justify-center items-center h-64"},Q={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},W={class:"flex"},Y={class:"text-red-800 dark:text-red-200"},Z={key:2,class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},tt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},et={class:"flex items-center"},at={class:"ml-4"},st={class:"text-2xl font-semibold text-gray-900 dark:text-white"},rt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},lt={class:"flex items-center"},ot={class:"ml-4"},dt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},it={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},nt={class:"flex items-center"},ut={class:"ml-4"},ct={class:"text-2xl font-semibold text-gray-900 dark:text-white"},gt={class:"bg-white dark:bg-gray-800 rounded-lg shadow p-6"},xt={class:"flex items-center"},yt={class:"ml-4"},pt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},vt={key:3,class:"space-y-6"},mt={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow"},ht={class:"p-6"},bt={class:"space-y-4"},_t={class:"flex items-center justify-between mb-4"},ft={class:"flex items-center"},kt={class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"},wt={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},Ct={class:"ml-3"},zt={class:"text-lg font-medium text-gray-900 dark:text-white"},jt={class:"text-sm text-gray-500 dark:text-gray-400"},At={class:"text-right"},Mt={class:"space-y-3"},Pt={class:"flex justify-between text-sm mb-1"},Vt={class:"text-gray-900 dark:text-white"},Tt={class:"flex justify-between text-sm mb-1"},St={class:"text-gray-900 dark:text-white"},Bt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Dt={class:"flex justify-between text-sm mb-1"},Et={class:"text-gray-900 dark:text-white"},Lt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ut={key:0,class:"mt-4"},Rt={class:"space-y-2"},Ft={class:"text-gray-600 dark:text-gray-400"},It={class:"flex items-center space-x-2"},$t={class:"text-gray-900 dark:text-white"},Ht={class:"text-gray-500 dark:text-gray-400"},Nt={key:1,class:"bg-white dark:bg-gray-800 rounded-lg shadow"},qt={class:"overflow-x-auto"},Ot={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Xt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Gt={class:"px-6 py-4 whitespace-nowrap"},Jt={class:"flex items-center"},Kt={class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center"},Qt={class:"text-xs font-medium text-blue-600 dark:text-blue-400"},Wt={class:"ml-3"},Yt={class:"text-sm font-medium text-gray-900 dark:text-white"},Zt={class:"px-6 py-4 whitespace-nowrap"},te={class:"text-sm text-gray-900 dark:text-white"},ee={class:"text-sm text-gray-500 dark:text-gray-400"},ae={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},se={class:"px-6 py-4 whitespace-nowrap"},re={class:"flex items-center"},le={class:"text-sm font-medium text-gray-900 dark:text-white"},oe={class:"ml-2 w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},de={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ie={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ne={class:"px-6 py-4 whitespace-nowrap"},xe={__name:"PersonnelAllocation",setup(ue){const V=$(),p=i(!1),c=i(null),j=i("current-month"),k=i(""),w=i(""),A=i(!1),v=i("summary"),g=i([]),T=i([]),S=i([]),B=i([]),x=U(()=>{if(!g.value.length)return{totalCapacity:0,totalAllocated:0,totalActual:0,utilizationPercentage:0,utilizationClass:"bg-gray-100 dark:bg-gray-700",utilizationIconClass:"text-gray-600 dark:text-gray-400"};const d=g.value.reduce((h,b)=>h+b.capacity_hours,0),e=g.value.reduce((h,b)=>h+b.allocated_hours,0),a=g.value.reduce((h,b)=>h+b.actual_hours,0),r=d>0?Math.round(a/d*100):0;let n="bg-gray-100 dark:bg-gray-700",u="text-gray-600 dark:text-gray-400";return r>100?(n="bg-red-100 dark:bg-red-900",u="text-red-600 dark:text-red-400"):r>=90?(n="bg-yellow-100 dark:bg-yellow-900",u="text-yellow-600 dark:text-yellow-400"):r>=70&&(n="bg-green-100 dark:bg-green-900",u="text-green-600 dark:text-green-400"),{totalCapacity:d,totalAllocated:e,totalActual:a,utilizationPercentage:r,utilizationClass:n,utilizationIconClass:u}}),D=async()=>{var d,e;try{const a=await fetch("/api/allocation/filters",{headers:{"Content-Type":"application/json","X-CSRFToken":V.csrfToken}});if(a.ok){const r=await a.json();S.value=((d=r.data)==null?void 0:d.departments)||[],B.value=((e=r.data)==null?void 0:e.projects)||[]}}catch(a){console.error("Error loading filters:",a)}},m=async()=>{var d,e;p.value=!0,c.value=null;try{const a=new URLSearchParams({period:j.value});k.value&&a.append("department_id",k.value),w.value&&a.append("project_id",w.value),A.value&&a.append("only_allocated","true");const r=`/api/allocation-analysis?${a.toString()}`;console.log("Calling API:",r);const n=await fetch(r,{headers:{"Content-Type":"application/json","X-CSRFToken":V.csrfToken}});if(!n.ok)throw new Error("Errore nel caricamento dei dati di allocazione");const u=await n.json();g.value=((d=u.data)==null?void 0:d.summary)||[],T.value=((e=u.data)==null?void 0:e.detailed)||[]}catch(a){c.value=a.message,console.error("Error loading allocation data:",a)}finally{p.value=!1}},E=d=>d>100?"text-red-600 dark:text-red-400":d>=90?"text-yellow-600 dark:text-yellow-400":d>=70?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400",L=d=>d>0?"text-red-600 dark:text-red-400":d<0?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400";return R(async()=>{await D(),await m()}),(d,e)=>(o(),l("div",H,[t("div",N,[e[10]||(e[10]=F('<div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Allocazione Risorse</h1><p class="text-gray-600 dark:text-gray-400 mt-1">Analisi temporale e confronto pianificato vs effettivo</p></div></div>',1)),t("div",q,[z(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>j.value=a),onChange:m,class:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},e[6]||(e[6]=[t("option",{value:"current-month"},"Mese Corrente",-1),t("option",{value:"current-quarter"},"Trimestre Corrente",-1),t("option",{value:"current-year"},"Anno Corrente",-1),t("option",{value:"next-quarter"},"Prossimo Trimestre",-1)]),544),[[M,j.value]]),z(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>k.value=a),onChange:m,class:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[e[7]||(e[7]=t("option",{value:""},"Tutti i Dipartimenti",-1)),(o(!0),l(_,null,f(S.value,a=>(o(),l("option",{key:a.id,value:a.id},s(a.name)+" ("+s(a.user_count)+") ",9,O))),128))],544),[[M,k.value]]),z(t("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>w.value=a),onChange:m,class:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"},[e[8]||(e[8]=t("option",{value:""},"Tutti i Progetti",-1)),(o(!0),l(_,null,f(B.value,a=>(o(),l("option",{key:a.id,value:a.id},s(a.name)+" ("+s(a.allocated_users)+") ",9,X))),128))],544),[[M,w.value]]),t("label",G,[z(t("input",{type:"checkbox","onUpdate:modelValue":e[3]||(e[3]=a=>A.value=a),onChange:m,class:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"},null,544),[[I,A.value]]),e[9]||(e[9]=t("span",null,"Solo allocati",-1))]),t("div",J,[t("button",{onClick:e[4]||(e[4]=a=>v.value="summary"),class:y([v.value==="summary"?"bg-white dark:bg-gray-600 shadow":"","px-3 py-1 text-sm rounded-md transition-colors"])}," Riepilogo ",2),t("button",{onClick:e[5]||(e[5]=a=>v.value="detailed"),class:y([v.value==="detailed"?"bg-white dark:bg-gray-600 shadow":"","px-3 py-1 text-sm rounded-md transition-colors"])}," Dettagliato ",2)])])]),p.value?(o(),l("div",K,e[11]||(e[11]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):c.value?(o(),l("div",Q,[t("div",W,[e[12]||(e[12]=t("svg",{class:"w-5 h-5 text-red-400 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),t("p",Y,s(c.value),1)])])):C("",!0),!p.value&&!c.value?(o(),l("div",Z,[t("div",tt,[t("div",et,[e[14]||(e[14]=t("div",{class:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",at,[e[13]||(e[13]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Capacità Totale",-1)),t("p",st,s(x.value.totalCapacity)+"h",1)])])]),t("div",rt,[t("div",lt,[e[16]||(e[16]=t("div",{class:"p-2 bg-green-100 dark:bg-green-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])],-1)),t("div",ot,[e[15]||(e[15]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Allocato",-1)),t("p",dt,s(x.value.totalAllocated)+"h",1)])])]),t("div",it,[t("div",nt,[e[18]||(e[18]=t("div",{class:"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg"},[t("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])],-1)),t("div",ut,[e[17]||(e[17]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Effettivo",-1)),t("p",ct,s(x.value.totalActual)+"h",1)])])]),t("div",gt,[t("div",xt,[t("div",{class:y(["p-2",x.value.utilizationClass])},[(o(),l("svg",{class:y(["w-6 h-6",x.value.utilizationIconClass]),fill:"currentColor",viewBox:"0 0 20 20"},e[19]||(e[19]=[t("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]),2))],2),t("div",yt,[e[20]||(e[20]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo",-1)),t("p",pt,s(x.value.utilizationPercentage)+"%",1)])])])])):C("",!0),!p.value&&!c.value?(o(),l("div",vt,[v.value==="summary"?(o(),l("div",mt,[e[27]||(e[27]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Riepilogo per Persona")],-1)),t("div",ht,[t("div",bt,[(o(!0),l(_,null,f(g.value,a=>(o(),l("div",{key:a.user_id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[t("div",_t,[t("div",ft,[t("div",kt,[t("span",wt,s(a.user_name.split(" ").map(r=>r[0]).join("")),1)]),t("div",Ct,[t("h4",zt,s(a.user_name),1),t("p",jt,s(a.role||"Nessun ruolo"),1)])]),t("div",At,[e[21]||(e[21]=t("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Utilizzo",-1)),t("div",{class:y(["text-lg font-semibold",E(a.utilization_percentage)])},s(Math.round(a.utilization_percentage))+"% ",3)])]),t("div",Mt,[t("div",null,[t("div",Pt,[e[22]||(e[22]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Capacità",-1)),t("span",Vt,s(a.capacity_hours)+"h",1)]),e[23]||(e[23]=t("div",{class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},[t("div",{class:"bg-gray-400 h-2 rounded-full",style:{width:"100%"}})],-1))]),t("div",null,[t("div",Tt,[e[24]||(e[24]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Allocato",-1)),t("span",St,s(a.allocated_hours)+"h",1)]),t("div",Bt,[t("div",{class:"bg-blue-500 h-2 rounded-full",style:P({width:Math.min(a.allocated_hours/a.capacity_hours*100,100)+"%"})},null,4)])]),t("div",null,[t("div",Dt,[e[25]||(e[25]=t("span",{class:"text-gray-600 dark:text-gray-400"},"Effettivo",-1)),t("span",Et,s(a.actual_hours)+"h",1)]),t("div",Lt,[t("div",{class:"bg-green-500 h-2 rounded-full",style:P({width:Math.min(a.actual_hours/a.capacity_hours*100,100)+"%"})},null,4)])])]),a.projects&&a.projects.length>0?(o(),l("div",Ut,[e[26]||(e[26]=t("h5",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Progetti Attivi",-1)),t("div",Rt,[(o(!0),l(_,null,f(a.projects,r=>(o(),l("div",{key:r.project_id,class:"flex justify-between items-center text-sm"},[t("span",Ft,s(r.project_name),1),t("div",It,[t("span",$t,s(r.allocation_percentage)+"%",1),t("span",Ht,"("+s(r.allocated_hours)+"h)",1)])]))),128))])])):C("",!0)]))),128))])])])):(o(),l("div",Nt,[e[29]||(e[29]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Vista Dettagliata")],-1)),t("div",qt,[t("table",Ot,[e[28]||(e[28]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Persona "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Allocazione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Pianificate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Effettive "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Varianza ")])],-1)),t("tbody",Xt,[(o(!0),l(_,null,f(T.value,a=>(o(),l("tr",{key:`${a.user_id}-${a.project_id}`},[t("td",Gt,[t("div",Jt,[t("div",Kt,[t("span",Qt,s(a.user_name.split(" ").map(r=>r[0]).join("")),1)]),t("div",Wt,[t("div",Yt,s(a.user_name),1)])])]),t("td",Zt,[t("div",te,s(a.project_name),1),t("div",ee,s(a.project_period),1)]),t("td",ae,s(a.role),1),t("td",se,[t("div",re,[t("div",le,s(a.allocation_percentage)+"%",1),t("div",oe,[t("div",{class:"bg-blue-500 h-2 rounded-full",style:P({width:a.allocation_percentage+"%"})},null,4)])])]),t("td",de,s(a.planned_hours)+"h ",1),t("td",ie,s(a.actual_hours)+"h ",1),t("td",ne,[t("span",{class:y(["text-sm font-medium",L(a.variance)])},s(a.variance>0?"+":"")+s(a.variance)+"h ",3)])]))),128))])])])]))])):C("",!0)]))}};export{xe as default};
