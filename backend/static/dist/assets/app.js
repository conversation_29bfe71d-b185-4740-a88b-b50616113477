const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/PersonnelDirectory.js","assets/personnel.js","assets/PersonnelOrgChart.js","assets/PersonnelOrgChart.css","assets/SkillsMatrix.js","assets/SkillsMatrix.css","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAllocation.js","assets/PersonnelAdmin.js","assets/PersonnelAdmin.css","assets/PersonnelProfile.js","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as j,w as le,c as a,a as $,b as q,o as s,d as Ve,e as pe,f as x,g as k,n as P,h as B,i as S,t as c,u as ge,j as e,F as N,k as O,l as ee,m as I,p as R,q as Pe,s as ve,v as K,x as Z,y as ne,z as he,A as Q,T as De,B as Le,C as Te,D as Se,E as ue,G as Be,H as me,I as He,J as qe,K as Re,L as Ne,M as Oe}from"./vendor.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))t(n);new MutationObserver(n=>{for(const p of n)if(p.type==="childList")for(const m of p.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&t(m)}).observe(document,{childList:!0,subtree:!0});function l(n){const p={};return n.integrity&&(p.integrity=n.integrity),n.referrerPolicy&&(p.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?p.credentials="include":n.crossOrigin==="anonymous"?p.credentials="omit":p.credentials="same-origin",p}function t(n){if(n.ep)return;n.ep=!0;const p=l(n);fetch(n.href,p)}})();const W=j(!1);let we=!1;const Ae=r=>{r?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ue=()=>{we||(le(W,r=>{Ae(r)}),we=!0)};function fe(){return Ue(),{isDarkMode:W,toggleDarkMode:()=>{W.value=!W.value},setDarkMode:t=>{W.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),n=document.documentElement.classList.contains("dark");if(t==="true")W.value=!0;else if(t==="false")W.value=!1;else{const h=window.matchMedia("(prefers-color-scheme: dark)").matches;W.value=n||h}Ae(W.value);const p=window.matchMedia("(prefers-color-scheme: dark)"),m=h=>{const v=localStorage.getItem("darkMode");(!v||v==="null")&&(W.value=h.matches)};p.addEventListener("change",m)}}}const Fe={id:"app"},Ke={__name:"App",setup(r){const{initializeDarkMode:o}=fe();return o(),(l,t)=>{const n=q("router-view");return s(),a("div",Fe,[$(n)])}}},We="modulepreload",Ge=function(r){return"/"+r},$e={},H=function(o,l,t){let n=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),h=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));n=Promise.allSettled(l.map(v=>{if(v=Ge(v),v in $e)return;$e[v]=!0;const d=v.endsWith(".css"),i=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${i}`))return;const f=document.createElement("link");if(f.rel=d?"stylesheet":We,d||(f.as="script"),f.crossOrigin="",f.href=v,h&&f.setAttribute("nonce",h),document.head.appendChild(f),d)return new Promise((_,b)=>{f.addEventListener("load",_),f.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${v}`)))})}))}function p(m){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=m,window.dispatchEvent(h),!h.defaultPrevented)throw m}return n.then(m=>{for(const h of m||[])h.status==="rejected"&&p(h.reason);return o().catch(p)})},F=Ve.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});F.interceptors.request.use(r=>{var l,t;const o=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return o&&["post","put","patch","delete"].includes((t=r.method)==null?void 0:t.toLowerCase())&&(r.headers["X-CSRFToken"]=o),r},r=>Promise.reject(r));F.interceptors.response.use(r=>r,r=>{var o;return((o=r.response)==null?void 0:o.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(r)});const X=pe("auth",()=>{const r=localStorage.getItem("user"),o=j(r?JSON.parse(r):null),l=j(!1),t=j(null),n=j(!1),p=x(()=>!!o.value&&n.value),m={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},h=z=>!o.value||!o.value.role?!1:o.value.role==="admin"?!0:(m[o.value.role]||[]).includes(z),v=()=>{var z,A;console.log("Current user:",o.value),console.log("User role:",(z=o.value)==null?void 0:z.role),console.log("Has admin permission:",h("admin")),console.log("Available permissions for role:",m[(A=o.value)==null?void 0:A.role])};async function d(z){var A,M;l.value=!0,t.value=null;try{const w=await F.post("/api/auth/login",z);return w.data.success?(o.value=w.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),n.value=!0,{success:!0}):(t.value=w.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(w){return t.value=((M=(A=w.response)==null?void 0:A.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function i(z){var A,M;l.value=!0,t.value=null;try{const w=await F.post("/api/auth/register",z);return w.data.success?{success:!0,message:w.data.message}:(t.value=w.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(w){return t.value=((M=(A=w.response)==null?void 0:A.data)==null?void 0:M.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function f(){try{await F.post("/api/auth/logout")}catch(z){console.warn("Errore durante il logout:",z)}finally{o.value=null,n.value=!1,localStorage.removeItem("user")}}async function _(){if(n.value)return p.value;try{const z=await F.get("/api/auth/me");return z.data.success?(o.value=z.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),n.value=!0,!0):(await f(),!1)}catch{return await f(),!1}}async function b(){return o.value?await _():(n.value=!0,!1)}return{user:o,loading:l,error:t,sessionChecked:n,isAuthenticated:p,hasPermission:h,debugPermissions:v,login:d,register:i,logout:f,checkAuth:_,initializeAuth:b}}),te=pe("tenant",()=>{const r=j(null),o=j(!1),l=j(null),t=x(()=>{var i;return((i=r.value)==null?void 0:i.company)||{}}),n=x(()=>{var i;return((i=r.value)==null?void 0:i.contact)||{}}),p=x(()=>{var i;return((i=r.value)==null?void 0:i.pages)||{}}),m=x(()=>{var i;return((i=r.value)==null?void 0:i.navigation)||{}}),h=x(()=>{var i;return((i=r.value)==null?void 0:i.footer)||{}});async function v(){try{if(o.value=!0,window.TENANT_CONFIG){r.value=window.TENANT_CONFIG;return}const i=await fetch("/api/config/tenant");r.value=await i.json()}catch(i){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",i)}finally{o.value=!1}}function d(i,f={}){if(!i||typeof i!="string")return i;let _=i;const b={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":n.value.email||"","contact.phone":n.value.phone||"","contact.address":n.value.address||"",current_year:new Date().getFullYear().toString(),...f};for(const[z,A]of Object.entries(b)){const M=new RegExp(`\\{${z}\\}`,"g");_=_.replace(M,A||"")}return _}return{config:r,loading:o,error:l,company:t,contact:n,pages:p,navigation:m,footer:h,loadConfig:v,interpolateText:d}});function Qe(){const r=X(),o=x(()=>b=>r.hasPermission(b)),l=x(()=>{var b;return((b=r.user)==null?void 0:b.role)||null}),t=x(()=>l.value==="admin"),n=x(()=>l.value==="manager"),p=x(()=>l.value==="employee"),m=x(()=>l.value==="sales"),h=x(()=>l.value==="human_resources"),v=x(()=>o.value("create_project")||o.value("edit_project")||o.value("delete_project")),d=x(()=>o.value("manage_users")||o.value("assign_roles")),i=x(()=>o.value("view_all_projects")),f=x(()=>o.value("view_personnel_data")||o.value("edit_personnel_data")),_=x(()=>o.value("approve_timesheets"));return{hasPermission:o,userRole:l,isAdmin:t,isManager:n,isEmployee:p,isSales:m,isHR:h,canManageProjects:v,canManageUsers:d,canViewAllProjects:i,canManagePersonnel:f,canApproveTimesheets:_}}const Je={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ye={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Xe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},et={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},tt={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},st={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},rt={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},ot={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},at={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},nt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},it={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},lt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},dt={key:13,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},ct={key:14,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},ut={key:15,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},mt={key:16,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},pt={key:17,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"},gt={key:18,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},vt={key:19,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},ht={key:20,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},de={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(r){return(o,l)=>(s(),a("svg",{class:P(r.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r.icon==="dashboard"?(s(),a("path",Je)):r.icon==="projects"?(s(),a("path",Ye)):r.icon==="users"?(s(),a("path",Xe)):r.icon==="clients"?(s(),a("path",Ze)):r.icon==="products"?(s(),a("path",et)):r.icon==="reports"?(s(),a("path",tt)):r.icon==="settings"?(s(),a("path",st)):k("",!0),r.icon==="settings"?(s(),a("path",rt)):r.icon==="user-management"?(s(),a("path",ot)):r.icon==="communications"?(s(),a("path",at)):r.icon==="funding"?(s(),a("path",nt)):r.icon==="reporting"?(s(),a("path",it)):r.icon==="team"?(s(),a("path",lt)):r.icon==="directory"?(s(),a("path",dt)):r.icon==="orgchart"?(s(),a("path",ct)):r.icon==="skills"?(s(),a("path",ut)):r.icon==="departments"?(s(),a("path",mt)):r.icon==="admin"?(s(),a("path",pt)):r.icon==="allocation"?(s(),a("path",gt)):r.icon==="user-profile"?(s(),a("path",vt)):(s(),a("path",ht))],2))}},ft={key:0,class:"truncate"},xt={key:0,class:"truncate"},Y={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(r){const o=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const n=q("router-link");return s(),a("div",null,[r.item.path!=="#"?(s(),B(n,{key:0,to:r.item.path,class:P(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[o.value,{"justify-center":r.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=p=>l.$emit("click"))},{default:S(()=>[$(de,{icon:r.item.icon,class:P(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),a("span",ft,c(r.item.name),1))]),_:1},8,["to","class"])):(s(),a("div",{key:1,class:P(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":r.isCollapsed}]])},[$(de,{icon:r.item.icon,class:P(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),a("span",xt,c(r.item.name),1))],2))])}}},yt={key:0,class:"flex-1 text-left truncate"},_t={key:0,class:"ml-6 space-y-1 mt-1"},kt={class:"truncate"},Ce={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(r){const o=r,l=ge(),t=X(),n=j(!1),p=x(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":m.value}]),m=x(()=>o.item.children?o.item.children.some(i=>i.path!=="#"&&l.path.startsWith(i.path)):!1),h=x(()=>o.item.children?o.item.children.filter(i=>{var f;return i.admin?((f=t.user)==null?void 0:f.role)==="admin":!0}):[]);m.value&&(n.value=!0);function v(){o.isCollapsed||(n.value=!n.value)}function d(i){if(i.path==="#")return!1}return(i,f)=>{const _=q("router-link");return s(),a("div",null,[e("button",{onClick:v,class:P(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[p.value,{"justify-center":r.isCollapsed}]])},[$(de,{icon:r.item.icon,class:P(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),a("span",yt,c(r.item.name),1)),r.isCollapsed?k("",!0):(s(),a("svg",{key:1,class:P([{"rotate-90":n.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},f[0]||(f[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),n.value&&!r.isCollapsed?(s(),a("div",_t,[(s(!0),a(N,null,O(h.value,b=>(s(),B(_,{key:b.name,to:b.path,class:P(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",b.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:z=>d(b)},{default:S(()=>[b.icon?(s(),B(de,{key:0,icon:b.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):k("",!0),e("span",kt,c(b.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):k("",!0)])}}},bt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},wt={class:"flex-1 px-2 space-y-1"},je={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(r){const{hasPermission:o}=Qe(),l=x(()=>o.value("view_dashboard")),t=x(()=>o.value("view_personnel_data")),n=x(()=>o.value("view_all_projects")),p=x(()=>o.value("view_crm")),m=x(()=>o.value("view_products")),h=x(()=>o.value("view_performance")),v=x(()=>o.value("view_communications")),d=x(()=>o.value("view_funding")),i=x(()=>o.value("view_reports")),f=x(()=>o.value("admin_access"));return(_,b)=>(s(),a("div",bt,[e("nav",wt,[l.value?(s(),B(Y,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":r.isCollapsed,onClick:b[0]||(b[0]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),t.value?(s(),B(Ce,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Directory",path:"/app/personnel",icon:"directory"},{name:"Organigramma",path:"/app/personnel/orgchart",icon:"orgchart"},{name:"Competenze",path:"/app/personnel/skills",icon:"skills"},{name:"Allocazione Risorse",path:"/app/personnel/allocation",icon:"allocation"},{name:"Dipartimenti",path:"/app/personnel/departments",icon:"departments",admin:!0},{name:"Amministrazione",path:"/app/personnel/admin",icon:"admin",admin:!0}]},"is-collapsed":r.isCollapsed,onClick:b[1]||(b[1]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),n.value?(s(),B(Y,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":r.isCollapsed,onClick:b[2]||(b[2]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),p.value?(s(),B(Y,{key:3,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":r.isCollapsed,onClick:b[3]||(b[3]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),m.value?(s(),B(Y,{key:4,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":r.isCollapsed,onClick:b[4]||(b[4]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),h.value?(s(),B(Y,{key:5,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":r.isCollapsed,onClick:b[5]||(b[5]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),v.value?(s(),B(Y,{key:6,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":r.isCollapsed,onClick:b[6]||(b[6]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),d.value?(s(),B(Y,{key:7,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":r.isCollapsed,onClick:b[7]||(b[7]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),i.value?(s(),B(Y,{key:8,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":r.isCollapsed,onClick:b[8]||(b[8]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),f.value?(s(),B(Ce,{key:9,item:{name:"Amministrazione",icon:"settings",children:[{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":r.isCollapsed,onClick:b[9]||(b[9]=z=>_.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0)])]))}},$t={class:"flex-shrink-0 border-t border-gray-200 p-4"},Ct={class:"flex-shrink-0"},jt={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Mt={class:"text-sm font-medium text-primary-700"},zt={key:0,class:"ml-3 flex-1 min-w-0"},Pt={class:"text-sm font-medium text-gray-900 truncate"},St={class:"text-xs text-gray-500 truncate"},At={class:"py-1"},Et={key:0,class:"mt-3 text-xs text-gray-400 text-center"},Me={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(r){const o=ee(),l=X(),t=j(!1),n=x(()=>l.user&&(l.user.name||l.user.username)||"Utente"),p=x(()=>l.user?n.value.charAt(0).toUpperCase():"U"),m=x(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),h=x(()=>"1.0.0");async function v(){t.value=!1,await l.logout(),o.push("/auth/login")}return(d,i)=>{const f=q("router-link");return s(),a("div",$t,[e("div",{class:P(["flex items-center",{"justify-center":r.isCollapsed}])},[e("div",Ct,[e("div",jt,[e("span",Mt,c(p.value),1)])]),r.isCollapsed?k("",!0):(s(),a("div",zt,[e("p",Pt,c(n.value),1),e("p",St,c(m.value),1)])),e("div",{class:P(["relative",{"ml-3":!r.isCollapsed}])},[e("button",{onClick:i[0]||(i[0]=_=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},i[4]||(i[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),a("div",{key:0,onClick:i[3]||(i[3]=_=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",At,[$(f,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[1]||(i[1]=_=>t.value=!1)},{default:S(()=>i[5]||(i[5]=[I(" Il tuo profilo ")])),_:1,__:[5]}),$(f,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[2]||(i[2]=_=>t.value=!1)},{default:S(()=>i[6]||(i[6]=[I(" Impostazioni ")])),_:1,__:[6]}),i[7]||(i[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:v,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):k("",!0)],2)],2),h.value&&!r.isCollapsed?(s(),a("div",Et," v"+c(h.value),1)):k("",!0)])}}},It={class:"flex"},Vt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Dt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Lt={class:"flex items-center flex-shrink-0 px-4"},Tt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Bt={class:"text-white font-bold text-lg"},Ht={class:"text-xl font-semibold text-gray-900 dark:text-white"},qt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Rt={class:"text-white font-bold text-sm"},Nt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ot=["d"],Ut={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Ft={class:"flex items-center justify-between px-4 mb-4"},Kt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Wt={class:"text-white font-bold text-sm"},Gt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Qt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(r,{emit:o}){const l=o,t=te(),n=j(!1),p=x(()=>t.config||{}),m=x(()=>{var i;return((i=p.value.company)==null?void 0:i.name)||"DatPortal"}),h=x(()=>m.value.split(" ").map(f=>f[0]).join("").toUpperCase().slice(0,2));function v(){n.value=!n.value,l("toggle-collapsed",n.value)}function d(){n.value&&(n.value=!1)}return(i,f)=>{const _=q("router-link");return s(),a("div",It,[e("div",Vt,[e("div",{class:P(["flex flex-col transition-all duration-300",[n.value?"w-20":"w-64"]])},[e("div",Dt,[e("div",Lt,[e("div",{class:P(["flex items-center",{"justify-center":n.value}])},[$(_,{to:"/app/dashboard",class:P(["flex items-center",{hidden:n.value}])},{default:S(()=>[e("div",Tt,[e("span",Bt,c(h.value),1)]),e("h3",Ht,c(m.value),1)]),_:1},8,["class"]),$(_,{to:"/app/dashboard",class:P(["flex items-center justify-center",{hidden:!n.value}])},{default:S(()=>[e("div",qt,[e("span",Rt,c(h.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:v,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),a("svg",Nt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Ot)]))])]),$(je,{"is-collapsed":n.value,onItemClick:d},null,8,["is-collapsed"]),$(Me,{"is-collapsed":n.value},null,8,["is-collapsed"])])],2)]),e("div",{class:P(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",r.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Ut,[e("div",Ft,[$(_,{to:"/app/dashboard",class:"flex items-center"},{default:S(()=>[e("div",Kt,[e("span",Wt,c(h.value),1)]),e("h3",Gt,c(m.value),1)]),_:1}),e("button",{onClick:f[0]||(f[0]=b=>i.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},f[2]||(f[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),$(je,{"is-collapsed":!1,onItemClick:f[1]||(f[1]=b=>i.$emit("close"))}),$(Me,{"is-collapsed":!1})])],2)])}}},Jt={class:"flex","aria-label":"Breadcrumb"},Yt={class:"flex items-center space-x-2 text-sm text-gray-500"},Xt={key:0,class:"mr-2"},Zt={class:"flex items-center"},es={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ts=["d"],ss={key:2,class:"font-medium text-gray-900"},rs={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(r){return(o,l)=>{const t=q("router-link");return s(),a("nav",Jt,[e("ol",Yt,[(s(!0),a(N,null,O(r.breadcrumbs,(n,p)=>(s(),a("li",{key:p,class:"flex items-center"},[p>0?(s(),a("div",Xt,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):k("",!0),n.to&&p<r.breadcrumbs.length-1?(s(),B(t,{key:1,to:n.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:S(()=>[e("span",Zt,[n.icon?(s(),a("svg",es,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:n.icon},null,8,ts)])):k("",!0),I(" "+c(n.label),1)])]),_:2},1032,["to"])):(s(),a("span",ss,c(n.label),1))]))),128))])])}}},os={class:"flex items-center space-x-2"},as={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ns={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},is={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(r){const o=ge(),{isDarkMode:l,toggleDarkMode:t}=fe(),n=x(()=>{var p;return((p=o.name)==null?void 0:p.includes("projects"))||o.path.includes("/projects")});return(p,m)=>(s(),a("div",os,[n.value?(s(),a("button",{key:0,onClick:m[0]||(m[0]=h=>p.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[2]||(m[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),I(" Nuovo Progetto ")]))):k("",!0),e("button",{onClick:m[1]||(m[1]=(...h)=>R(t)&&R(t)(...h)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[R(l)?(s(),a("svg",ns,m[4]||(m[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),a("svg",as,m[3]||(m[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},ls={class:"relative"},ds={class:"relative"},cs={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},us={class:"py-1"},ms={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},ps={key:1,class:"max-h-64 overflow-y-auto"},gs=["onClick"],vs={class:"flex items-start"},hs={class:"flex-shrink-0"},fs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},xs=["d"],ys={class:"ml-3 flex-1"},_s={class:"text-sm font-medium text-gray-900"},ks={class:"text-xs text-gray-500 mt-1"},bs={class:"text-xs text-gray-400 mt-1"},ws={key:0,class:"flex-shrink-0"},$s={key:2,class:"px-4 py-2 border-t border-gray-100"},Cs={__name:"HeaderNotifications",setup(r){const o=j(!1),l=j([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=x(()=>l.value.filter(d=>!d.read).length);function n(d){const i={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return i[d]||i.system}function p(d){const i={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return i[d]||i.system}function m(d){const i=new Date(d),_=new Date-i;return _<6e4?"Adesso":_<36e5?`${Math.floor(_/6e4)}m fa`:_<864e5?`${Math.floor(_/36e5)}h fa`:i.toLocaleDateString("it-IT")}function h(d){d.read||(d.read=!0),o.value=!1}function v(){l.value.forEach(d=>d.read=!0)}return(d,i)=>(s(),a("div",ls,[e("button",{onClick:i[0]||(i[0]=f=>o.value=!o.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[i[3]||(i[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",ds,[i[2]||(i[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),a("span",cs,c(t.value>9?"9+":t.value),1)):k("",!0)])]),o.value?(s(),a("div",{key:0,onClick:i[1]||(i[1]=f=>o.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",us,[i[5]||(i[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),a("div",ms," Nessuna notifica ")):(s(),a("div",ps,[(s(!0),a(N,null,O(l.value,f=>(s(),a("div",{key:f.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:_=>h(f)},[e("div",vs,[e("div",hs,[e("div",{class:P(n(f.type))},[(s(),a("svg",fs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:p(f.type)},null,8,xs)]))],2)]),e("div",ys,[e("p",_s,c(f.title),1),e("p",ks,c(f.message),1),e("p",bs,c(m(f.created_at)),1)]),f.read?k("",!0):(s(),a("div",ws,i[4]||(i[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,gs))),128))])),l.value.length>0?(s(),a("div",$s,[e("button",{onClick:v,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):k("",!0)])])):k("",!0)]))}},js={class:"relative"},Ms={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},zs={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},Ps={class:"flex items-center"},Ss={class:"flex-1"},As={key:0,class:"mt-4 max-h-64 overflow-y-auto"},Es={class:"space-y-1"},Is=["onClick"],Vs={class:"flex-shrink-0"},Ds={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ls=["d"],Ts={class:"ml-3 flex-1 min-w-0"},Bs={class:"text-sm font-medium text-gray-900 truncate"},Hs={class:"text-xs text-gray-500 truncate"},qs={class:"ml-2 text-xs text-gray-400"},Rs={key:1,class:"mt-4 text-center py-4"},Ns={key:2,class:"mt-4 text-center py-4"},Os={__name:"HeaderSearch",setup(r){const o=ee(),l=j(!1),t=j(""),n=j([]),p=j(-1),m=j(!1),h=j(null),v=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];le(l,async M=>{var w;M?(await Pe(),(w=h.value)==null||w.focus()):(t.value="",n.value=[],p.value=-1)});function d(){if(!t.value.trim()){n.value=[];return}m.value=!0,setTimeout(()=>{n.value=v.filter(M=>M.title.toLowerCase().includes(t.value.toLowerCase())||M.description.toLowerCase().includes(t.value.toLowerCase())),p.value=-1,m.value=!1},200)}function i(M){if(n.value.length===0)return;const w=p.value+M;w>=0&&w<n.value.length&&(p.value=w)}function f(){p.value>=0&&n.value[p.value]&&_(n.value[p.value])}function _(M){l.value=!1,o.push(M.path)}function b(M){const w={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return w[M]||w.document}function z(M){const w={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return w[M]||w.document}function A(M){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[M]||"Elemento"}return(M,w)=>(s(),a("div",js,[e("button",{onClick:w[0]||(w[0]=T=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},w[7]||(w[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),a("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:w[6]||(w[6]=ve(T=>l.value=!1,["self"]))},[e("div",Ms,[w[11]||(w[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",zs,[e("div",null,[e("div",Ps,[e("div",Ss,[K(e("input",{ref_key:"searchInput",ref:h,"onUpdate:modelValue":w[1]||(w[1]=T=>t.value=T),onInput:d,onKeydown:[w[2]||(w[2]=ne(T=>l.value=!1,["escape"])),ne(f,["enter"]),w[3]||(w[3]=ne(T=>i(-1),["up"])),w[4]||(w[4]=ne(T=>i(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[Z,t.value]])]),e("button",{onClick:w[5]||(w[5]=T=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},w[8]||(w[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),n.value.length>0?(s(),a("div",As,[e("div",Es,[(s(!0),a(N,null,O(n.value,(T,G)=>(s(),a("div",{key:T.id,onClick:se=>_(T),class:P(["flex items-center px-3 py-2 rounded-md cursor-pointer",G===p.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Vs,[e("div",{class:P(b(T.type))},[(s(),a("svg",Ds,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:z(T.type)},null,8,Ls)]))],2)]),e("div",Ts,[e("p",Bs,c(T.title),1),e("p",Hs,c(T.description),1)]),e("div",qs,c(A(T.type)),1)],10,Is))),128))])])):t.value&&!m.value?(s(),a("div",Rs,w[9]||(w[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?k("",!0):(s(),a("div",Ns,w[10]||(w[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):k("",!0)]))}},Us={class:"relative"},Fs={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Ks={class:"text-sm font-medium text-primary-700"},Ws={class:"py-1"},Gs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Qs={class:"text-sm font-medium text-gray-900 dark:text-white"},Js={class:"text-xs text-gray-500 dark:text-gray-400"},Ys={__name:"HeaderUserMenu",setup(r){const o=ee(),l=X(),t=j(!1),{isDarkMode:n,toggleDarkMode:p}=fe(),m=x(()=>l.user&&(l.user.name||l.user.username)||"Utente"),h=x(()=>{var i;return((i=l.user)==null?void 0:i.email)||""}),v=x(()=>l.user?m.value.charAt(0).toUpperCase():"U");async function d(){t.value=!1,await l.logout(),o.push("/auth/login")}return(i,f)=>{const _=q("router-link");return s(),a("div",Us,[e("button",{onClick:f[0]||(f[0]=b=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[f[5]||(f[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Fs,[e("span",Ks,c(v.value),1)])]),t.value?(s(),a("div",{key:0,onClick:f[4]||(f[4]=b=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ws,[e("div",Gs,[e("p",Qs,c(m.value),1),e("p",Js,c(h.value),1)]),$(_,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[1]||(f[1]=b=>t.value=!1)},{default:S(()=>f[6]||(f[6]=[I(" Il tuo profilo ")])),_:1,__:[6]}),$(_,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:f[2]||(f[2]=b=>t.value=!1)},{default:S(()=>f[7]||(f[7]=[I(" Impostazioni ")])),_:1,__:[7]}),f[8]||(f[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:f[3]||(f[3]=(...b)=>R(p)&&R(p)(...b)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(R(n)?"Modalità chiara":"Modalità scura"),1),e("i",{class:P([R(n)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:d,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):k("",!0)])}}},Xs={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Zs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},er={class:"flex items-center space-x-4"},tr={class:"flex flex-col"},sr={class:"text-lg font-semibold text-gray-900 dark:text-white"},rr={class:"flex items-center space-x-4"},or={class:"hidden md:flex items-center space-x-2"},ar={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(r){return(o,l)=>(s(),a("header",Xs,[e("div",Zs,[e("div",er,[e("button",{onClick:l[0]||(l[0]=t=>o.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",tr,[e("h2",sr,c(r.pageTitle),1),r.breadcrumbs.length>0?(s(),B(rs,{key:0,breadcrumbs:r.breadcrumbs},null,8,["breadcrumbs"])):k("",!0)])]),e("div",rr,[e("div",or,[$(is,{onQuickCreateProject:l[1]||(l[1]=t=>o.$emit("quick-create-project"))})]),$(Cs),$(Os),$(Ys)])])]))}},nr={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:r=>["sm","md","lg","xl"].includes(r)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(r){const o=r,l=x(()=>{const m={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${m[o.size]}; height: ${m[o.size]};`}),t=x(()=>["flex",o.centered?"items-center justify-center":"","space-y-2"]),n=x(()=>["flex items-center justify-center"]),p=x(()=>["text-sm text-gray-600 text-center"]);return(m,h)=>(s(),a("div",{class:P(t.value)},[e("div",{class:P(n.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:he(l.value)},null,4)],2),r.message?(s(),a("p",{key:0,class:P(p.value)},c(r.message),3)):k("",!0)],2))}},Ee=(r,o)=>{const l=r.__vccOpts||r;for(const[t,n]of o)l[t]=n;return l},ir={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},lr={class:"p-4"},dr={class:"flex items-start"},cr={class:"flex-shrink-0"},ur={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},mr=["d"],pr={class:"ml-3 w-0 flex-1 pt-0.5"},gr={class:"text-sm font-medium text-gray-900"},vr={class:"mt-1 text-sm text-gray-500"},hr={class:"ml-4 flex-shrink-0 flex"},fr=["onClick"],xr={__name:"NotificationManager",setup(r){const o=j([]);function l(h){const v=Date.now(),d={id:v,type:h.type||"info",title:h.title,message:h.message,duration:h.duration||5e3};o.value.push(d),d.duration>0&&setTimeout(()=>{t(v)},d.duration)}function t(h){const v=o.value.findIndex(d=>d.id===h);v>-1&&o.value.splice(v,1)}function n(h){const v={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return v[h]||v.info}function p(h){const v={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return v[h]||v.info}function m(h){const v={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return v[h]||v.info}return window.showNotification=l,Q(()=>{}),(h,v)=>(s(),a("div",ir,[$(De,{name:"notification",tag:"div",class:"space-y-4"},{default:S(()=>[(s(!0),a(N,null,O(o.value,d=>(s(),a("div",{key:d.id,class:P([n(d.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",lr,[e("div",dr,[e("div",cr,[e("div",{class:P(p(d.type))},[(s(),a("svg",ur,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(d.type)},null,8,mr)]))],2)]),e("div",pr,[e("p",gr,c(d.title),1),e("p",vr,c(d.message),1)]),e("div",hr,[e("button",{onClick:i=>t(d.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},v[0]||(v[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,fr)])])])],2))),128))]),_:1})]))}},yr=Ee(xr,[["__scopeId","data-v-220f0827"]]),_r={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},kr={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},br={class:"py-6"},wr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},$r={key:0,class:"mb-6"},Cr={key:1,class:"flex items-center justify-center h-64"},jr={__name:"AppLayout",setup(r){const o=ge(),l=ee(),t=te(),n=j(!1),p=j(!1),m=j(!1);x(()=>t.config||{});const h=x(()=>t.config!==null),v=x(()=>{var M;return(M=o.meta)!=null&&M.title?o.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[o.name]||"DatPortal"}),d=x(()=>{var A;return(A=o.meta)!=null&&A.breadcrumbs?o.meta.breadcrumbs.map(M=>({label:M.label,to:M.to,icon:M.icon})):[]}),i=x(()=>{var A;return((A=o.meta)==null?void 0:A.hasActions)||!1});function f(){n.value=!n.value}function _(){n.value=!1}function b(A){p.value=A}function z(){l.push("/app/projects/create")}return le(o,()=>{m.value=!0,setTimeout(()=>{m.value=!1},300)}),le(o,()=>{_()}),Q(()=>{h.value||t.loadConfig()}),(A,M)=>{const w=q("router-view");return s(),a("div",_r,[n.value?(s(),a("div",{key:0,onClick:_,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):k("",!0),$(Qt,{"is-mobile-open":n.value,onClose:_,onToggleCollapsed:b},null,8,["is-mobile-open"]),e("div",{class:P(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[p.value?"lg:ml-20":"lg:ml-64"]])},[$(ar,{"page-title":v.value,breadcrumbs:d.value,onToggleMobileSidebar:f,onQuickCreateProject:z},null,8,["page-title","breadcrumbs"]),e("main",kr,[e("div",br,[e("div",wr,[i.value?(s(),a("div",$r,[Le(A.$slots,"page-actions")])):k("",!0),m.value?(s(),a("div",Cr,[$(nr)])):(s(),B(w,{key:2}))])])])],2),$(yr)])}}},Mr={class:"min-h-screen bg-gray-50"},zr={class:"bg-white shadow-sm border-b"},Pr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Sr={class:"flex justify-between h-16"},Ar={class:"flex items-center"},Er={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ir={class:"text-white font-bold text-sm"},Vr={class:"text-xl font-semibold text-gray-900"},Dr={class:"hidden md:flex items-center space-x-8"},Lr={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Tr={class:"md:hidden flex items-center"},Br={key:0,class:"md:hidden"},Hr={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},qr={class:"bg-gray-800 text-white"},Rr={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Nr={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Or={class:"col-span-1 md:col-span-2"},Ur={class:"flex items-center space-x-3 mb-4"},Fr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Kr={class:"text-white font-bold text-sm"},Wr={class:"text-xl font-semibold"},Gr={class:"text-gray-300 max-w-md"},Qr={class:"space-y-2"},Jr={class:"space-y-2 text-gray-300"},Yr={key:0},Xr={key:1},Zr={key:2},eo={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ze={__name:"PublicLayout",setup(r){const o=te(),l=j(!1),t=x(()=>o.config||{}),n=x(()=>{var v;return((v=t.value.company)==null?void 0:v.name)||"DatVinci"}),p=x(()=>n.value.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2)),m=x(()=>o.config!==null),h=new Date().getFullYear();return Q(()=>{m.value||o.loadConfig()}),(v,d)=>{var _,b,z,A,M,w;const i=q("router-link"),f=q("router-view");return s(),a("div",Mr,[e("nav",zr,[e("div",Pr,[e("div",Sr,[e("div",Ar,[$(i,{to:"/",class:"flex items-center space-x-3"},{default:S(()=>[e("div",Er,[e("span",Ir,c(p.value),1)]),e("span",Vr,c(n.value),1)]),_:1})]),e("div",Dr,[$(i,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:S(()=>d[1]||(d[1]=[I(" Home ")])),_:1,__:[1]}),$(i,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:S(()=>d[2]||(d[2]=[I(" Chi Siamo ")])),_:1,__:[2]}),$(i,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:S(()=>d[3]||(d[3]=[I(" Servizi ")])),_:1,__:[3]}),$(i,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:S(()=>d[4]||(d[4]=[I(" Contatti ")])),_:1,__:[4]}),e("div",Lr,[$(i,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:S(()=>d[5]||(d[5]=[I(" Accedi ")])),_:1,__:[5]}),$(i,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:S(()=>d[6]||(d[6]=[I(" Registrati ")])),_:1,__:[6]})])]),e("div",Tr,[e("button",{onClick:d[0]||(d[0]=T=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},d[7]||(d[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),a("div",Br,[e("div",Hr,[$(i,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:S(()=>d[8]||(d[8]=[I(" Home ")])),_:1,__:[8]}),$(i,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:S(()=>d[9]||(d[9]=[I(" Chi Siamo ")])),_:1,__:[9]}),$(i,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:S(()=>d[10]||(d[10]=[I(" Servizi ")])),_:1,__:[10]}),$(i,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:S(()=>d[11]||(d[11]=[I(" Contatti ")])),_:1,__:[11]}),d[14]||(d[14]=e("hr",{class:"my-2"},null,-1)),$(i,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:S(()=>d[12]||(d[12]=[I(" Accedi ")])),_:1,__:[12]}),$(i,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:S(()=>d[13]||(d[13]=[I(" Registrati ")])),_:1,__:[13]})])])):k("",!0)]),e("main",null,[$(f)]),e("footer",qr,[e("div",Rr,[e("div",Nr,[e("div",Or,[e("div",Ur,[e("div",Fr,[e("span",Kr,c(p.value),1)]),e("span",Wr,c(n.value),1)]),e("p",Gr,c(R(o).interpolateText((_=t.value.footer)==null?void 0:_.description)||((b=t.value.company)==null?void 0:b.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[d[19]||(d[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Qr,[e("li",null,[$(i,{to:"/",class:"text-gray-300 hover:text-white"},{default:S(()=>d[15]||(d[15]=[I("Home")])),_:1,__:[15]})]),e("li",null,[$(i,{to:"/about",class:"text-gray-300 hover:text-white"},{default:S(()=>d[16]||(d[16]=[I("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[$(i,{to:"/services",class:"text-gray-300 hover:text-white"},{default:S(()=>d[17]||(d[17]=[I("Servizi")])),_:1,__:[17]})]),e("li",null,[$(i,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:S(()=>d[18]||(d[18]=[I("Contatti")])),_:1,__:[18]})])])]),e("div",null,[d[20]||(d[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Jr,[(z=t.value.contact)!=null&&z.email?(s(),a("li",Yr,c(t.value.contact.email),1)):k("",!0),(A=t.value.contact)!=null&&A.phone?(s(),a("li",Xr,c(t.value.contact.phone),1)):k("",!0),(M=t.value.contact)!=null&&M.address?(s(),a("li",Zr,c(t.value.contact.address),1)):k("",!0)])])]),e("div",eo,[e("p",null,c(R(o).interpolateText((w=t.value.footer)==null?void 0:w.copyright)||`© ${R(h)} ${n.value}. Tutti i diritti riservati.`),1)])])])])}}},to={class:"bg-white"},so={class:"relative overflow-hidden"},ro={class:"max-w-7xl mx-auto"},oo={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},ao={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},no={class:"sm:text-center lg:text-left"},io={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},lo={class:"block xl:inline"},co={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},uo={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},mo={class:"rounded-md shadow"},po={class:"mt-3 sm:mt-0 sm:ml-3"},go={class:"py-12 bg-white"},vo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ho={class:"lg:text-center"},fo={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},xo={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},yo={key:0,class:"mt-10"},_o={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},ko={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},bo={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},wo={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},$o={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Co={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},jo={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},Mo={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},zo={class:"mt-2 ml-16 text-base text-gray-500"},Po={__name:"Home",setup(r){const o=te(),l=x(()=>o.config||{}),t=x(()=>{var p;return((p=l.value.pages)==null?void 0:p.home)||{}}),n=x(()=>l.value.company||{});return Q(()=>{o.config||o.loadConfig()}),(p,m)=>{var v,d,i,f;const h=q("router-link");return s(),a("div",to,[e("div",so,[e("div",ro,[e("div",oo,[e("main",ao,[e("div",no,[e("h1",io,[e("span",lo,c(((v=t.value.hero)==null?void 0:v.title)||"Innovazione per il futuro"),1)]),e("p",co,c(((d=t.value.hero)==null?void 0:d.subtitle)||R(o).interpolateText(n.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",uo,[e("div",mo,[$(h,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:S(()=>{var _;return[I(c(((_=t.value.hero)==null?void 0:_.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",po,[$(h,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:S(()=>{var _;return[I(c(((_=t.value.hero)==null?void 0:_.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),m[0]||(m[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",go,[e("div",vo,[e("div",ho,[e("h2",fo,c(((i=t.value.services_section)==null?void 0:i.title)||"I nostri servizi"),1),e("p",xo,c(((f=t.value.services_section)==null?void 0:f.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),n.value.platform_features?(s(),a("div",yo,[e("div",_o,[(s(!0),a(N,null,O(n.value.platform_features,_=>(s(),a("div",{key:_.title,class:"relative"},[e("div",ko,[(s(),a("svg",bo,[_.icon==="briefcase"?(s(),a("path",wo)):_.icon==="users"?(s(),a("path",$o)):_.icon==="chart"?(s(),a("path",Co)):(s(),a("path",jo))]))]),e("p",Mo,c(_.title),1),e("p",zo,c(_.description),1)]))),128))])])):k("",!0)])])])}}},So={class:"py-16 bg-white"},Ao={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Eo={class:"text-center"},Io={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Vo={class:"mt-4 text-xl text-gray-600"},Do={key:0,class:"mt-16"},Lo={class:"max-w-3xl mx-auto"},To={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Bo={class:"text-lg text-gray-700 leading-relaxed"},Ho={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},qo={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ro={class:"text-2xl font-bold text-gray-900 mb-4"},No={class:"text-gray-700"},Oo={key:1,class:"bg-gray-50 p-8 rounded-lg"},Uo={class:"text-2xl font-bold text-gray-900 mb-4"},Fo={class:"text-gray-700"},Ko={key:1,class:"mt-16"},Wo={class:"text-center mb-12"},Go={class:"text-3xl font-bold text-gray-900"},Qo={class:"mt-4 text-xl text-gray-600"},Jo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Yo={class:"text-lg font-semibold text-gray-900"},Xo={key:2,class:"mt-16"},Zo={class:"text-center"},ea={class:"text-3xl font-bold text-gray-900"},ta={class:"mt-4 text-xl text-gray-600"},sa={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},ra={class:"text-primary-900 font-medium"},oa={__name:"About",setup(r){const o=te(),l=x(()=>o.config||{}),t=x(()=>{var p;return((p=l.value.pages)==null?void 0:p.about)||{}}),n=x(()=>l.value.company||{});return Q(()=>{o.config||o.loadConfig()}),(p,m)=>{var h,v;return s(),a("div",So,[e("div",Ao,[e("div",Eo,[e("h1",Io,c(((h=t.value.hero)==null?void 0:h.title)||"Chi Siamo"),1),e("p",Vo,c(((v=t.value.hero)==null?void 0:v.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),a("div",Do,[e("div",Lo,[e("h2",To,c(t.value.story_section.title),1),e("p",Bo,c(R(o).interpolateText(t.value.story_section.content)),1)])])):k("",!0),e("div",Ho,[t.value.mission_section?(s(),a("div",qo,[e("h3",Ro,c(t.value.mission_section.title),1),e("p",No,c(R(o).interpolateText(t.value.mission_section.content)),1)])):k("",!0),t.value.vision_section?(s(),a("div",Oo,[e("h3",Uo,c(t.value.vision_section.title),1),e("p",Fo,c(R(o).interpolateText(t.value.vision_section.content)),1)])):k("",!0)]),t.value.expertise_section&&n.value.expertise?(s(),a("div",Ko,[e("div",Wo,[e("h2",Go,c(t.value.expertise_section.title),1),e("p",Qo,c(t.value.expertise_section.subtitle),1)]),e("div",Jo,[(s(!0),a(N,null,O(n.value.expertise,d=>(s(),a("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[m[0]||(m[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Yo,c(d),1)]))),128))])])):k("",!0),t.value.team_section?(s(),a("div",Xo,[e("div",Zo,[e("h2",ea,c(t.value.team_section.title),1),e("p",ta,c(t.value.team_section.subtitle),1),e("div",sa,[m[1]||(m[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",ra,c(n.value.team_size),1)])])])):k("",!0)])])}}},aa={class:"py-16 bg-white"},na={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ia={class:"text-center"},la={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},da={class:"mt-4 text-xl text-gray-600"},ca={key:0,class:"mt-8 text-center"},ua={class:"text-lg text-gray-700 max-w-3xl mx-auto"},ma={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},pa={key:0},ga={class:"text-2xl font-bold text-gray-900 mb-8"},va={class:"block text-sm font-medium text-gray-700 mb-2"},ha={class:"block text-sm font-medium text-gray-700 mb-2"},fa={class:"block text-sm font-medium text-gray-700 mb-2"},xa=["disabled"],ya={key:1},_a={class:"text-2xl font-bold text-gray-900 mb-8"},ka={class:"space-y-6"},ba={key:0,class:"flex items-start"},wa={class:"font-medium text-gray-900"},$a={class:"text-gray-600"},Ca={key:1,class:"flex items-start"},ja={class:"font-medium text-gray-900"},Ma={class:"text-gray-600"},za={key:2,class:"flex items-start"},Pa={class:"font-medium text-gray-900"},Sa={class:"text-gray-600"},Aa={key:3,class:"flex items-start"},Ea={class:"font-medium text-gray-900"},Ia={class:"text-gray-600"},Va={__name:"Contact",setup(r){const o=te(),l=x(()=>o.config||{}),t=x(()=>{var d;return((d=l.value.pages)==null?void 0:d.contact)||{}}),n=x(()=>l.value.contact||{}),p=j({name:"",email:"",message:""}),m=j(!1),h=j({text:"",type:""}),v=async()=>{var d,i;if(!p.value.name||!p.value.email||!p.value.message){h.value={text:((d=t.value.form)==null?void 0:d.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}m.value=!0,h.value={text:"",type:""};try{await new Promise(f=>setTimeout(f,1e3)),h.value={text:((i=t.value.form)==null?void 0:i.success_message)||"Messaggio inviato con successo!",type:"success"},p.value={name:"",email:"",message:""}}catch{h.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{m.value=!1}};return Q(()=>{o.config||o.loadConfig()}),(d,i)=>{var f,_;return s(),a("div",aa,[e("div",na,[e("div",ia,[e("h1",la,c(((f=t.value.hero)==null?void 0:f.title)||"Contattaci"),1),e("p",da,c(((_=t.value.hero)==null?void 0:_.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),a("div",ca,[e("p",ua,c(t.value.intro.content),1)])):k("",!0),e("div",ma,[t.value.form?(s(),a("div",pa,[e("h2",ga,c(t.value.form.title),1),e("form",{onSubmit:ve(v,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",va,c(t.value.form.name_label),1),K(e("input",{"onUpdate:modelValue":i[0]||(i[0]=b=>p.value.name=b),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,p.value.name]])]),e("div",null,[e("label",ha,c(t.value.form.email_label),1),K(e("input",{"onUpdate:modelValue":i[1]||(i[1]=b=>p.value.email=b),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,p.value.email]])]),e("div",null,[e("label",fa,c(t.value.form.message_label),1),K(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=b=>p.value.message=b),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[Z,p.value.message]])]),e("button",{type:"submit",disabled:m.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(m.value?"Invio in corso...":t.value.form.submit_button),9,xa),h.value.text?(s(),a("div",{key:0,class:P([h.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(h.value.text),3)):k("",!0)],32)])):k("",!0),t.value.info?(s(),a("div",ya,[e("h2",_a,c(t.value.info.title),1),e("div",ka,[n.value.address?(s(),a("div",ba,[i[3]||(i[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",wa,c(t.value.info.address_label),1),e("p",$a,c(n.value.address),1)])])):k("",!0),n.value.phone?(s(),a("div",Ca,[i[4]||(i[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",ja,c(t.value.info.phone_label),1),e("p",Ma,c(n.value.phone),1)])])):k("",!0),n.value.email?(s(),a("div",za,[i[5]||(i[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",Pa,c(t.value.info.email_label),1),e("p",Sa,c(n.value.email),1)])])):k("",!0),n.value.hours?(s(),a("div",Aa,[i[6]||(i[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",Ea,c(t.value.info.hours_label),1),e("p",Ia,c(n.value.hours),1)])])):k("",!0)])])):k("",!0)])])])}}},Da={class:"py-16 bg-white"},La={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ta={class:"text-center"},Ba={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ha={class:"mt-4 text-xl text-gray-600"},qa={key:0,class:"mt-8 text-center"},Ra={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Na={key:1,class:"mt-16"},Oa={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ua={class:"text-xl font-bold text-gray-900 text-center mb-4"},Fa={class:"text-gray-600 text-center"},Ka={key:2,class:"mt-20"},Wa={class:"bg-primary-50 rounded-2xl p-12 text-center"},Ga={class:"text-3xl font-bold text-gray-900 mb-4"},Qa={class:"text-xl text-gray-600 mb-8"},Ja={__name:"Services",setup(r){const o=te(),l=x(()=>o.config||{}),t=x(()=>{var m;return((m=l.value.pages)==null?void 0:m.services)||{}}),n=x(()=>l.value.company||{}),p=m=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[m]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return Q(()=>{o.config||o.loadConfig()}),(m,h)=>{var d,i;const v=q("router-link");return s(),a("div",Da,[e("div",La,[e("div",Ta,[e("h1",Ba,c(((d=t.value.hero)==null?void 0:d.title)||"I nostri servizi"),1),e("p",Ha,c(((i=t.value.hero)==null?void 0:i.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),a("div",qa,[e("p",Ra,c(t.value.intro.content),1)])):k("",!0),n.value.expertise?(s(),a("div",Na,[e("div",Oa,[(s(!0),a(N,null,O(n.value.expertise,f=>(s(),a("div",{key:f,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[h[0]||(h[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Ua,c(f),1),e("p",Fa,c(p(f)),1)]))),128))])])):k("",!0),t.value.cta?(s(),a("div",Ka,[e("div",Wa,[e("h2",Ga,c(t.value.cta.title),1),e("p",Qa,c(t.value.cta.subtitle),1),$(v,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:S(()=>[I(c(t.value.cta.button)+" ",1),h[1]||(h[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):k("",!0)])])}}},Ya={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Xa={class:"max-w-md w-full space-y-8"},Za={class:"mt-2 text-center text-sm text-gray-600"},en={key:0,class:"rounded-md bg-red-50 p-4"},tn={class:"text-sm text-red-700"},sn={class:"rounded-md shadow-sm -space-y-px"},rn={class:"flex items-center justify-between"},on={class:"flex items-center"},an=["disabled"],nn={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},ln={__name:"Login",setup(r){const o=ee(),l=X(),t=j({username:"",password:"",remember:!1}),n=x(()=>l.loading),p=x(()=>l.error);async function m(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&o.push("/app/dashboard")}return(h,v)=>{const d=q("router-link");return s(),a("div",Ya,[e("div",Xa,[e("div",null,[v[5]||(v[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),v[6]||(v[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Za,[v[4]||(v[4]=I(" Oppure ")),$(d,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:S(()=>v[3]||(v[3]=[I(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:ve(m,["prevent"]),class:"mt-8 space-y-6"},[p.value?(s(),a("div",en,[e("div",tn,c(p.value),1)])):k("",!0),e("div",sn,[e("div",null,[v[7]||(v[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),K(e("input",{id:"username","onUpdate:modelValue":v[0]||(v[0]=i=>t.value.username=i),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[Z,t.value.username]])]),e("div",null,[v[8]||(v[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),K(e("input",{id:"password","onUpdate:modelValue":v[1]||(v[1]=i=>t.value.password=i),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[Z,t.value.password]])])]),e("div",rn,[e("div",on,[K(e("input",{id:"remember-me","onUpdate:modelValue":v[2]||(v[2]=i=>t.value.remember=i),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Te,t.value.remember]]),v[9]||(v[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),v[10]||(v[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:n.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[n.value?(s(),a("span",nn,v[11]||(v[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):k("",!0),I(" "+c(n.value?"Accesso in corso...":"Accedi"),1)],8,an)])],32)])])}}},dn={},cn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function un(r,o){return s(),a("div",cn,o[0]||(o[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const mn=Ee(dn,[["render",un]]),pn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},gn={class:"p-5"},vn={class:"flex items-center"},hn={class:"ml-5 w-0 flex-1"},fn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},xn={class:"text-lg font-medium text-gray-900 dark:text-white"},yn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},_n={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},kn={class:"text-sm"},ie={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(r){const o=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const n={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return n[t]||n.primary};return(t,n)=>{const p=q("router-link");return s(),a("div",pn,[e("div",gn,[e("div",vn,[e("div",{class:P(["flex-shrink-0 rounded-md p-3",l(r.color)])},[(s(),B(Se(o(r.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",hn,[e("dl",null,[e("dt",fn,c(r.title),1),e("dd",null,[e("div",xn,c(r.value),1),r.subtitle?(s(),a("div",yn,c(r.subtitle),1)):k("",!0)])])])])]),r.link?(s(),a("div",_n,[e("div",kn,[$(p,{to:r.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:S(()=>n[0]||(n[0]=[I(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):k("",!0)])}}},bn={class:"py-6"},wn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},$n={class:"mt-4 md:mt-0 flex space-x-3"},Cn={class:"relative"},jn=["disabled"],Mn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},zn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},Pn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Sn={class:"relative h-64"},An={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},En={class:"relative h-64"},In={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Dn={class:"p-6"},Ln={key:0,class:"text-center py-8 text-gray-500"},Tn={key:1,class:"space-y-4"},Bn={class:"flex justify-between items-start"},Hn={class:"flex-1"},qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Rn={class:"text-xs text-gray-500 dark:text-gray-400"},Nn={class:"mt-2 flex justify-between items-center"},On={class:"text-xs text-gray-500 dark:text-gray-400"},Un={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Fn={class:"text-sm"},Kn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Wn={class:"p-6"},Gn={key:0,class:"text-center py-8 text-gray-500"},Qn={key:1,class:"space-y-4"},Jn={class:"flex-shrink-0"},Yn={class:"flex-1 min-w-0"},Xn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei={class:"text-xs text-gray-400 dark:text-gray-500"},ti={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},si={class:"p-6"},ri={key:0,class:"text-center py-8 text-gray-500"},oi={key:1,class:"space-y-4"},ai={class:"flex justify-between items-start"},ni={class:"flex-1"},ii={class:"text-sm font-medium text-gray-900 dark:text-white"},li={class:"text-xs text-gray-500 dark:text-gray-400"},di={class:"text-right"},ci={class:"text-sm font-bold text-gray-900 dark:text-white"},ui={class:"text-xs text-gray-500"},mi={class:"mt-2"},pi={class:"w-full bg-gray-200 rounded-full h-2"},gi={class:"text-xs text-gray-500 mt-1"},vi={__name:"Dashboard",setup(r){ue.register(...Be),ee(),X();const o=j(!1),l=j("7"),t=j({}),n=j([]),p=j([]),m=j([]),h=j(null),v=j(null);let d=null,i=null;const f=async()=>{try{const u=await fetch("/api/dashboard/stats");if(!u.ok)throw new Error("Failed to fetch stats");const g=await u.json();t.value=g.data}catch(u){console.error("Error fetching dashboard stats:",u),t.value={}}},_=async()=>{try{const u=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!u.ok)throw new Error("Failed to fetch upcoming tasks");const g=await u.json();n.value=g.data.tasks}catch(u){console.error("Error fetching upcoming tasks:",u),n.value=[]}},b=async()=>{try{const u=await fetch("/api/dashboard/recent-activities?limit=5");if(!u.ok)throw new Error("Failed to fetch recent activities");const g=await u.json();p.value=g.data.activities}catch(u){console.error("Error fetching recent activities:",u),p.value=[]}},z=async()=>{try{const u=await fetch("/api/dashboard/kpis?limit=3");if(!u.ok)throw new Error("Failed to fetch KPIs");const g=await u.json();m.value=g.data.kpis}catch(u){console.error("Error fetching KPIs:",u),m.value=[]}},A=async()=>{try{const u=await fetch("/api/dashboard/charts/project-status");if(!u.ok)throw new Error("Failed to fetch project chart data");const g=await u.json();w(g.data.chart)}catch(u){console.error("Error fetching project chart:",u)}},M=async()=>{try{const u=await fetch("/api/dashboard/charts/task-status");if(!u.ok)throw new Error("Failed to fetch task chart data");const g=await u.json();T(g.data.chart)}catch(u){console.error("Error fetching task chart:",u)}},w=u=>{if(!h.value)return;const g=h.value.getContext("2d");d&&d.destroy(),d=new ue(g,{type:"doughnut",data:{labels:u.labels,datasets:[{data:u.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},T=u=>{if(!v.value)return;const g=v.value.getContext("2d");i&&i.destroy(),i=new ue(g,{type:"bar",data:{labels:u.labels,datasets:[{label:"Tasks",data:u.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},G=async()=>{o.value=!0;try{await Promise.all([f(),_(),b(),z(),A(),M()])}finally{o.value=!1}},se=u=>new Date(u).toLocaleDateString("it-IT"),re=u=>{const g=new Date(u),U=Math.floor((new Date-g)/(1e3*60));return U<60?`${U} minuti fa`:U<1440?`${Math.floor(U/60)} ore fa`:`${Math.floor(U/1440)} giorni fa`},C=u=>{const g={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return g[u]||g.medium},E=u=>{const g={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return g[u]||g.todo},L=u=>{const g={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return g[u]||g.task},D=u=>{const g={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return g[u]||g.task},y=u=>u>=90?"bg-green-500":u>=70?"bg-yellow-500":"bg-red-500";return Q(async()=>{await G(),await Pe(),h.value&&v.value&&(await A(),await M())}),(u,g)=>{var U,oe,ae,xe,ye,_e,ke,be;const J=q("router-link");return s(),a("div",bn,[e("div",wn,[g[4]||(g[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",$n,[e("div",Cn,[K(e("select",{"onUpdate:modelValue":g[0]||(g[0]=V=>l.value=V),onChange:G,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},g[1]||(g[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[me,l.value]])]),e("button",{onClick:G,disabled:o.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),a("svg",{xmlns:"http://www.w3.org/2000/svg",class:P(["h-4 w-4 mr-2",{"animate-spin":o.value}]),viewBox:"0 0 20 20",fill:"currentColor"},g[2]||(g[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),g[3]||(g[3]=I(" Aggiorna "))],8,jn)])]),e("div",Mn,[$(ie,{title:"Progetti Attivi",value:((U=t.value.projects)==null?void 0:U.active)||0,subtitle:`di ${((oe=t.value.projects)==null?void 0:oe.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),$(ie,{title:"Clienti",value:((ae=t.value.team)==null?void 0:ae.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),$(ie,{title:"Task Pendenti",value:((xe=t.value.tasks)==null?void 0:xe.pending)||0,subtitle:`${((ye=t.value.tasks)==null?void 0:ye.overdue)||0} in ritardo`,icon:"clock",color:((_e=t.value.tasks)==null?void 0:_e.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),$(ie,{title:"Team Members",value:((ke=t.value.team)==null?void 0:ke.users)||0,subtitle:`${((be=t.value.team)==null?void 0:be.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",zn,[e("div",Pn,[g[5]||(g[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",Sn,[e("canvas",{ref_key:"projectChart",ref:h},null,512)])]),e("div",An,[g[6]||(g[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",En,[e("canvas",{ref_key:"taskChart",ref:v},null,512)])])]),e("div",In,[e("div",Vn,[e("div",Dn,[g[7]||(g[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),n.value.length===0?(s(),a("div",Ln," Nessuna attività in scadenza ")):(s(),a("div",Tn,[(s(!0),a(N,null,O(n.value,V=>(s(),a("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Bn,[e("div",Hn,[e("h3",qn,c(V.name),1),e("p",Rn,c(V.project_name),1)]),e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",C(V.priority)])},c(V.priority),3)]),e("div",Nn,[e("span",On," Scadenza: "+c(se(V.due_date)),1),e("span",{class:P(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",E(V.status)])},c(V.status),3)])]))),128))]))]),e("div",Un,[e("div",Fn,[$(J,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:S(()=>g[8]||(g[8]=[I(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Kn,[e("div",Wn,[g[9]||(g[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),p.value.length===0?(s(),a("div",Gn," Nessuna attività recente ")):(s(),a("div",Qn,[(s(!0),a(N,null,O(p.value,V=>(s(),a("div",{key:`${V.type}-${V.id}`,class:"flex items-start space-x-3"},[e("div",Jn,[e("div",{class:P(["w-8 h-8 rounded-full flex items-center justify-center",D(V.type)])},[(s(),B(Se(L(V.type)),{class:"w-4 h-4"}))],2)]),e("div",Yn,[e("p",Xn,c(V.title),1),e("p",Zn,c(V.description),1),e("p",ei,c(re(V.timestamp)),1)])]))),128))]))])]),e("div",ti,[e("div",si,[g[10]||(g[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),m.value.length===0?(s(),a("div",ri," Nessun KPI configurato ")):(s(),a("div",oi,[(s(!0),a(N,null,O(m.value,V=>(s(),a("div",{key:V.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",ai,[e("div",ni,[e("h3",ii,c(V.name),1),e("p",li,c(V.description),1)]),e("div",di,[e("p",ci,c(V.current_value)+c(V.unit),1),e("p",ui," Target: "+c(V.target_value)+c(V.unit),1)])]),e("div",mi,[e("div",pi,[e("div",{class:P(["h-2 rounded-full",y(V.performance_percentage)]),style:he({width:Math.min(V.performance_percentage,100)+"%"})},null,6)]),e("p",gi,c(Math.round(V.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},hi=pe("projects",()=>{const r=j([]),o=j(null),l=j(!1),t=j(null),n=j(new Map),p=j({page:1,perPage:20,total:0,totalPages:0}),m=j({search:"",status:"",client:"",type:""}),h=x(()=>{let C=r.value;if(m.value.search){const E=m.value.search.toLowerCase();C=C.filter(L=>{var D,y,u;return L.name.toLowerCase().includes(E)||((D=L.description)==null?void 0:D.toLowerCase().includes(E))||((u=(y=L.client)==null?void 0:y.name)==null?void 0:u.toLowerCase().includes(E))})}return m.value.status&&(C=C.filter(E=>E.status===m.value.status)),m.value.client&&(C=C.filter(E=>E.client_id===m.value.client)),m.value.type&&(C=C.filter(E=>E.project_type===m.value.type)),C}),v=x(()=>{const C={};return r.value.forEach(E=>{C[E.status]||(C[E.status]=[]),C[E.status].push(E)}),C}),d=async(C={})=>{var E,L;l.value=!0,t.value=null;try{const D=new URLSearchParams({page:C.page||p.value.page,per_page:C.perPage||p.value.perPage,search:C.search||m.value.search,status:C.status||m.value.status,client:C.client||m.value.client,type:C.type||m.value.type}),y=await F.get(`/api/projects?${D}`);y.data.success&&(r.value=y.data.data.projects,p.value=y.data.data.pagination)}catch(D){t.value=((L=(E=D.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",D)}finally{l.value=!1}},i=async(C,E=!1)=>{var L,D;if(!E&&n.value.has(C)){const y=n.value.get(C);return o.value=y,y}l.value=!0,t.value=null;try{const y=await F.get(`/api/projects/${C}`);if(y.data.success){const u=y.data.data.project;return o.value=u,n.value.set(C,u),u}}catch(y){throw t.value=((D=(L=y.response)==null?void 0:L.data)==null?void 0:D.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",y),y}finally{l.value=!1}};return{projects:r,currentProject:o,loading:l,error:t,pagination:p,filters:m,filteredProjects:h,projectsByStatus:v,fetchProjects:d,fetchProject:i,createProject:async C=>{var E,L;l.value=!0,t.value=null;try{const D=await F.post("/api/projects",C);if(D.data.success){const y=D.data.data.project;return r.value.unshift(y),y}}catch(D){throw t.value=((L=(E=D.response)==null?void 0:E.data)==null?void 0:L.message)||"Errore nella creazione progetto",console.error("Error creating project:",D),D}finally{l.value=!1}},updateProject:async(C,E)=>{var L,D,y;l.value=!0,t.value=null;try{const u=await F.put(`/api/projects/${C}`,E);if(u.data.success){const g=u.data.data.project,J=r.value.findIndex(U=>U.id===C);return J!==-1&&(r.value[J]=g),((L=o.value)==null?void 0:L.id)===C&&(o.value=g),n.value.set(C,g),g}}catch(u){throw t.value=((y=(D=u.response)==null?void 0:D.data)==null?void 0:y.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",u),u}finally{l.value=!1}},deleteProject:async C=>{var E,L,D;l.value=!0,t.value=null;try{(await F.delete(`/api/projects/${C}`)).data.success&&(r.value=r.value.filter(u=>u.id!==C),((E=o.value)==null?void 0:E.id)===C&&(o.value=null),n.value.delete(C))}catch(y){throw t.value=((D=(L=y.response)==null?void 0:L.data)==null?void 0:D.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",y),y}finally{l.value=!1}},setFilters:C=>{m.value={...m.value,...C}},clearFilters:()=>{m.value={search:"",status:"",client:"",type:""}},setCurrentProject:C=>{o.value=C},clearCurrentProject:()=>{o.value=null},clearCache:()=>{n.value.clear()},refreshProject:async C=>await i(C,!0),getCachedProject:C=>n.value.get(C),$reset:()=>{r.value=[],o.value=null,l.value=!1,t.value=null,n.value.clear(),p.value={page:1,perPage:20,total:0,totalPages:0},m.value={search:"",status:"",client:"",type:""}}}}),fi={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},xi={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},yi=["value"],_i={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ki={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},bi={class:"text-lg font-medium text-gray-900 dark:text-white"},wi={key:0,class:"p-6 text-center"},$i={key:1,class:"p-6 text-center"},Ci={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},ji=["onClick"],Mi={class:"flex items-center justify-between"},zi={class:"flex-1"},Pi={class:"flex items-center"},Si={class:"text-lg font-medium text-gray-900 dark:text-white"},Ai={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},Ei={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},Ii={key:0},Vi={key:1,class:"mx-2"},Di={key:2},Li={key:3,class:"mx-2"},Ti={key:4},Bi={key:0,class:"mt-3 flex items-center space-x-4"},Hi={key:0,class:"flex items-center space-x-1"},qi={class:"text-xs text-gray-600 dark:text-gray-400"},Ri={key:1,class:"flex items-center space-x-1"},Ni={class:"text-xs text-gray-600 dark:text-gray-400"},Oi={key:2,class:"flex items-center space-x-1"},Ui={class:"text-xs text-gray-600 dark:text-gray-400"},Fi={class:"ml-4 flex items-center space-x-4"},Ki={class:"text-right"},Wi={class:"text-sm font-medium text-gray-900 dark:text-white"},Gi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Qi={key:0,class:"text-right"},Ji={class:"flex items-center space-x-1 mt-1"},Yi={__name:"Projects",setup(r){const o=ee(),l=hi(),t=j(!0),n=j(""),p=j({status:"",client:""}),m=x(()=>l.projects),h=j([]),v=x(()=>{let y=m.value;if(p.value.status&&(y=y.filter(u=>u.status===p.value.status)),p.value.client&&(y=y.filter(u=>u.client_id==p.value.client)),n.value){const u=n.value.toLowerCase();y=y.filter(g=>g.name.toLowerCase().includes(u)||g.description&&g.description.toLowerCase().includes(u)||g.client&&g.client.name&&g.client.name.toLowerCase().includes(u))}return y}),d=async()=>{t.value=!0;try{await l.fetchProjects(),h.value=[]}catch(y){console.error("Error loading projects:",y)}finally{t.value=!1}},i=()=>{},f=()=>{},_=()=>{p.value={status:"",client:""},n.value=""},b=()=>{o.push("/app/projects/create")},z=y=>{o.push(`/app/projects/${y}`)},A=y=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[y]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",M=y=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[y]||y,w=y=>new Date(y).toLocaleDateString("it-IT"),T=y=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y),G=y=>({planning:10,active:50,completed:100,"on-hold":25})[y.status]||0,se=y=>y>=90?"bg-red-500":y>=75?"bg-yellow-500":"bg-green-500",re=y=>y>=90?"bg-red-500":y>=80?"bg-yellow-500":"bg-green-500",C=y=>y<10?"bg-red-500":y<20?"bg-yellow-500":"bg-green-500",E=y=>{const u=y.budget_usage>=90,g=y.time_usage>=90,J=y.margin<10;if(u||g||J)return"bg-red-500";const U=y.budget_usage>=75,oe=y.time_usage>=80,ae=y.margin<20;return U||oe||ae?"bg-yellow-500":"bg-green-500"},L=y=>{const u=E(y);return u.includes("red")?"text-red-600 dark:text-red-400":u.includes("yellow")?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"},D=y=>{const u=E(y);return u.includes("red")?"Critico":u.includes("yellow")?"Attenzione":"Buono"};return Q(()=>{d()}),(y,u)=>(s(),a("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[u[4]||(u[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:b,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[3]||(u[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),I(" Nuovo Progetto ")]))])])]),e("div",fi,[e("div",xi,[e("div",null,[u[6]||(u[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),K(e("select",{"onUpdate:modelValue":u[0]||(u[0]=g=>p.value.status=g),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},u[5]||(u[5]=[He('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[me,p.value.status]])]),e("div",null,[u[8]||(u[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),K(e("select",{"onUpdate:modelValue":u[1]||(u[1]=g=>p.value.client=g),onChange:f,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[u[7]||(u[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),a(N,null,O(h.value,g=>(s(),a("option",{key:g.id,value:g.id},c(g.name),9,yi))),128))],544),[[me,p.value.client]])]),e("div",null,[u[9]||(u[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),K(e("input",{"onUpdate:modelValue":u[2]||(u[2]=g=>n.value=g),onInput:i,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[Z,n.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:_,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",_i,[e("div",ki,[e("h3",bi," Progetti ("+c(v.value.length)+") ",1)]),t.value?(s(),a("div",wi,u[10]||(u[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):v.value.length===0?(s(),a("div",$i,u[11]||(u[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),a("div",Ci,[(s(!0),a(N,null,O(v.value,g=>(s(),a("div",{key:g.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:J=>z(g.id)},[e("div",Mi,[e("div",zi,[e("div",Pi,[e("h4",Si,c(g.name),1),e("span",{class:P([A(g.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(M(g.status)),3)]),e("p",Ai,c(g.description),1),e("div",Ei,[g.client?(s(),a("span",Ii,"Cliente: "+c(g.client.name),1)):k("",!0),g.client?(s(),a("span",Vi,"•")):k("",!0),g.end_date?(s(),a("span",Di,"Scadenza: "+c(w(g.end_date)),1)):k("",!0),g.end_date&&g.budget?(s(),a("span",Li,"•")):k("",!0),g.budget?(s(),a("span",Ti,"Budget: "+c(T(g.budget)),1)):k("",!0)]),g.kpis?(s(),a("div",Bi,[g.kpis.budget_usage!==void 0?(s(),a("div",Hi,[e("div",{class:P(["w-3 h-3 rounded-full",se(g.kpis.budget_usage)])},null,2),e("span",qi," Budget: "+c(g.kpis.budget_usage)+"% ",1)])):k("",!0),g.kpis.time_usage!==void 0?(s(),a("div",Ri,[e("div",{class:P(["w-3 h-3 rounded-full",re(g.kpis.time_usage)])},null,2),e("span",Ni," Tempo: "+c(g.kpis.time_usage)+"% ",1)])):k("",!0),g.kpis.margin!==void 0?(s(),a("div",Oi,[e("div",{class:P(["w-3 h-3 rounded-full",C(g.kpis.margin)])},null,2),e("span",Ui," Margine: "+c(g.kpis.margin)+"% ",1)])):k("",!0)])):k("",!0)]),e("div",Fi,[e("div",Ki,[e("div",Wi,c(G(g))+"% ",1),e("div",Gi,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:he({width:G(g)+"%"})},null,4)])]),g.kpis?(s(),a("div",Qi,[u[12]||(u[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"KPI Status",-1)),e("div",Ji,[e("div",{class:P(["w-2 h-2 rounded-full",E(g.kpis)])},null,2),e("span",{class:P(["text-xs font-medium",L(g.kpis)])},c(D(g.kpis)),3)])])):k("",!0),u[13]||(u[13]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,ji))),128))]))])]))}},Xi=[{path:"/",component:ze,children:[{path:"",name:"home",component:Po},{path:"about",name:"about",component:oa},{path:"contact",name:"contact",component:Va},{path:"services",name:"services",component:Ja}]},{path:"/auth",component:ze,children:[{path:"login",name:"login",component:ln},{path:"register",name:"register",component:mn}]},{path:"/app",component:jr,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:vi,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Yi,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>H(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>H(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>H(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"personnel",name:"personnel",component:()=>H(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([5,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>H(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([7,1,8])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>H(()=>import("./SkillsMatrix.js"),__vite__mapDeps([9,1,10])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>H(()=>import("./DepartmentList.js"),__vite__mapDeps([11,1,6])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>H(()=>import("./DepartmentCreate.js"),__vite__mapDeps([12,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>H(()=>import("./DepartmentView.js"),__vite__mapDeps([13,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>H(()=>import("./DepartmentEdit.js"),__vite__mapDeps([14,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/allocation",name:"personnel-allocation",component:()=>H(()=>import("./PersonnelAllocation.js"),__vite__mapDeps([15,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/admin",name:"personnel-admin",component:()=>H(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([16,1,17])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>H(()=>import("./PersonnelProfile.js"),__vite__mapDeps([18,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>H(()=>import("./Admin.js"),__vite__mapDeps([19,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>H(()=>import("./KPITemplates.js"),__vite__mapDeps([20,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>H(()=>import("./Profile.js"),__vite__mapDeps([21,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>H(()=>import("./Settings.js"),__vite__mapDeps([22,1])),meta:{requiresAuth:!0}}]}],Ie=qe({history:Re(),routes:Xi});Ie.beforeEach(async(r,o,l)=>{const t=X();if(r.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(r.meta.requiredPermission&&!t.hasPermission(r.meta.requiredPermission)){console.warn(`Accesso negato a ${r.path}: permesso '${r.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const ce=Ne(Ke),Zi=Oe();ce.use(Zi);ce.use(Ie);const el=X();el.initializeAuth().then(()=>{console.log("Auth initialized successfully"),ce.mount("#app")}).catch(r=>{console.error("Auth initialization failed:",r),ce.mount("#app")});export{Ee as _,Qe as a,hi as b,F as c,fe as d,X as u};
