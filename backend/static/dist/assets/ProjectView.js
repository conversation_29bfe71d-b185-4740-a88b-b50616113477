import{c as r,o as s,j as e,t as o,n as H,g as A,m as X,a as oe,i as ye,b as fe,F as O,k as F,h as de,D as ve,f as U,z as ee,r as S,w as Q,A as te,v as E,H as W,I as ae,x as J,s as re,N as be,p as le,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as xe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ze={key:0},Ae={key:1},De={key:2},Ie={key:3},Ve={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(b){const P=v=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[v]||"bg-gray-100 text-gray-800",_=v=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[v]||v,T=v=>v?new Date(v).toLocaleDateString("it-IT"):"",C=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"";return(v,x)=>{const y=fe("router-link");return s(),r("div",$e,[b.loading?(s(),r("div",je,x[1]||(x[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):b.project?(s(),r("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,o(b.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",P(b.project.status)])},o(_(b.project.status)),3)]),e("div",Pe,[b.project.client?(s(),r("span",ze,[x[2]||(x[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),X(" Cliente: "+o(b.project.client.name),1)])):A("",!0),b.project.start_date?(s(),r("span",Ae,[x[3]||(x[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Inizio: "+o(T(b.project.start_date)),1)])):A("",!0),b.project.end_date?(s(),r("span",De,[x[4]||(x[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Fine: "+o(T(b.project.end_date)),1)])):A("",!0),b.project.budget?(s(),r("span",Ie,[x[5]||(x[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),X(" Budget: "+o(C(b.project.budget)),1)])):A("",!0)])]),e("div",Ve,[oe(y,{to:`/app/projects/${b.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ye(()=>x[6]||(x[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),X(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:x[0]||(x[0]=f=>v.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},x[7]||(x[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),X(" Elimina ")]))])])):(s(),r("div",Ee,x[8]||(x[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:b=>b.every(P=>typeof P=="object"&&P.id&&P.label)}},emits:["update:modelValue"],setup(b,{emit:P}){const _=b,T=P,C=y=>_.modelValue===y,v=y=>{T("update:modelValue",y)},x=y=>{const f={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return f[y]||f["chart-bar"]};return(y,f)=>(s(),r("div",Re,[e("div",He,[e("nav",Oe,[(s(!0),r(O,null,F(b.tabs,a=>(s(),r("button",{key:a.id,onClick:M=>v(a.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",C(a.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":C(a.id)?"page":void 0},[a.icon?(s(),de(ve(x(a.icon)),{key:0,class:"w-4 h-4"})):A("",!0),e("span",null,o(a.label),1),a.count!==void 0?(s(),r("span",Le,o(a.count),1)):A("",!0)],10,Fe))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Je={key:1,class:"space-y-6"},We={class:"bg-white shadow rounded-lg p-6"},Ge={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"flex items-center"},tt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900"},rt={class:"bg-white shadow rounded-lg p-6"},ot={class:"flex items-center"},at={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},it={class:"bg-white shadow rounded-lg p-6"},lt={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},xt={class:"w-full bg-gray-200 rounded-full h-2.5"},yt={class:"text-sm text-gray-500 mt-2"},ft={class:"bg-white shadow rounded-lg p-6"},bt={class:"space-y-4"},ht={class:"flex justify-between items-center"},kt={class:"text-sm font-medium"},wt={class:"flex justify-between items-center"},_t={class:"text-sm font-medium"},$t={class:"w-full bg-gray-200 rounded-full h-3"},jt={class:"flex justify-between items-center text-sm"},Ct={class:"bg-white shadow rounded-lg p-6"},Mt={class:"space-y-3"},Tt={class:"flex-shrink-0"},St=["src","alt"],Pt={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},zt={class:"text-xs font-medium text-gray-600"},At={class:"flex-1"},Dt={class:"text-sm font-medium text-gray-900"},It={class:"text-xs text-gray-500"},Vt={class:"text-right"},Et={class:"text-xs text-gray-500"},Bt={key:0,class:"text-center py-4"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ot={class:"flex-1"},Ft={class:"text-sm text-gray-900"},Lt={class:"flex items-center space-x-2 mt-1"},Kt={class:"text-xs text-gray-500"},qt={class:"text-xs text-gray-500"},Nt={key:0,class:"text-center py-4"},Xt={key:2,class:"text-center py-8"},Jt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b){const P=b,_=U(()=>{if(!P.project||!P.project.task_count)return 0;const $=P.project.completed_tasks||0,u=P.project.task_count||1;return Math.round($/u*100)}),T=U(()=>{var $;return(($=P.project)==null?void 0:$.team_members)||[]}),C=U(()=>{var D,L,j;if((D=P.project)!=null&&D.expenses)return P.project.expenses;const $=((L=P.project)==null?void 0:L.total_hours)||0,u=(j=P.project)!=null&&j.client_daily_rate?P.project.client_daily_rate/8:50;return $*u}),v=U(()=>{var u;return(((u=P.project)==null?void 0:u.budget)||0)-C.value}),x=U(()=>{var u;const $=((u=P.project)==null?void 0:u.budget)||1;return Math.min(Math.round(C.value/$*100),100)}),y=U(()=>{const $=x.value;return $>=90?"bg-red-600":$>=75?"bg-yellow-600":"bg-green-600"}),f=U(()=>{var u;const $=v.value;return $<0?"text-red-600":$<(((u=P.project)==null?void 0:u.budget)||0)*.1?"text-yellow-600":"text-green-600"}),a=U(()=>{var $;return($=P.project)!=null&&$.tasks?[...P.project.tasks].sort((u,D)=>new Date(D.updated_at)-new Date(u.updated_at)).slice(0,5).map(u=>{var D;return{id:u.id,description:`Task "${u.name}" ${M(u.status)}`,created_at:u.updated_at,user_name:((D=u.assignee)==null?void 0:D.full_name)||"Non assegnato",type:B(u.status)}}):[]}),M=$=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[$]||$,B=$=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[$]||"task_updated",V=$=>$?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format($):"Non specificato",I=$=>$?new Date($).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",k=$=>$?$.split(" ").map(u=>u.charAt(0).toUpperCase()).slice(0,2).join(""):"??",N=$=>{const u={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return u[$]||u.default};return($,u)=>(s(),r("div",Ne,[b.loading?(s(),r("div",Xe,u[0]||(u[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):b.project?(s(),r("div",Je,[e("div",We,[u[1]||(u[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),b.project.description?(s(),r("p",Ge,o(b.project.description),1)):(s(),r("p",Ye,"Nessuna descrizione disponibile"))]),e("div",Qe,[e("div",Ze,[e("div",et,[u[3]||(u[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",tt,[e("dl",null,[u[2]||(u[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",st,o(b.project.task_count||0),1)])])])]),e("div",rt,[e("div",ot,[u[5]||(u[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",at,[e("dl",null,[u[4]||(u[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",nt,o(b.project.completed_tasks||0),1)])])])]),e("div",it,[e("div",lt,[u[7]||(u[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",dt,[e("dl",null,[u[6]||(u[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ut,o(b.project.team_count||0),1)])])])]),e("div",ct,[e("div",gt,[u[9]||(u[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",mt,[e("dl",null,[u[8]||(u[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",pt,o(V(b.project.budget)),1)])])])])]),e("div",vt,[u[10]||(u[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",xt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${_.value}%`})},null,4)]),e("p",yt,o(_.value)+"% completato",1)]),e("div",ft,[u[15]||(u[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",bt,[e("div",ht,[u[11]||(u[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",kt,o(V(b.project.budget)),1)]),u[14]||(u[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",wt,[u[12]||(u[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",_t,o(V(C.value)),1)]),e("div",$t,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",y.value]),style:ee({width:x.value+"%"})},null,6)]),e("div",jt,[u[13]||(u[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",f.value])},o(V(v.value)),3)])])]),e("div",Ct,[u[17]||(u[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Mt,[(s(!0),r(O,null,F(T.value,D=>(s(),r("div",{key:D.id,class:"flex items-center space-x-3"},[e("div",Tt,[D.profile_image?(s(),r("img",{key:0,src:D.profile_image,alt:D.full_name,class:"w-8 h-8 rounded-full"},null,8,St)):(s(),r("div",Pt,[e("span",zt,o(k(D.full_name)),1)]))]),e("div",At,[e("p",Dt,o(D.full_name),1),e("p",It,o(D.role||"Team Member"),1)]),e("div",Vt,[e("p",Et,o(D.hours_worked||0)+"h",1)])]))),128)),T.value.length===0?(s(),r("div",Bt,u[16]||(u[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):A("",!0)])]),e("div",Ut,[u[20]||(u[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Rt,[(s(!0),r(O,null,F(a.value,D=>(s(),r("div",{key:D.id,class:"flex items-start space-x-3"},[e("div",Ht,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",N(D.type)])},null,2)]),e("div",Ot,[e("p",Ft,o(D.description),1),e("div",Lt,[e("p",Kt,o(I(D.created_at)),1),u[18]||(u[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",qt,o(D.user_name),1)])])]))),128)),a.value.length===0?(s(),r("div",Nt,u[19]||(u[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):A("",!0)])])])):(s(),r("div",Xt,u[21]||(u[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Jt,[["__scopeId","data-v-16274846"]]),Wt={class:"space-y-6"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Yt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qt={class:"flex items-center justify-between"},Zt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},es=["value"],ts={class:"mt-4 flex items-center justify-between"},ss={class:"flex items-center space-x-4"},rs={class:"text-sm text-gray-500 dark:text-gray-400"},os={key:0,class:"flex justify-center py-8"},as={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ns={class:"text-red-600"},is={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ls={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ds={class:"col-span-4"},us={class:"text-sm font-medium text-gray-900 dark:text-white"},cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},gs={class:"col-span-2"},ms={key:0,class:"flex items-center"},ps={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},vs={class:"ml-2"},xs={class:"text-sm font-medium text-gray-900 dark:text-white"},ys={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},fs={class:"col-span-1"},bs={class:"col-span-1"},hs={class:"col-span-2"},ks={key:0,class:"text-sm text-gray-900 dark:text-white"},ws={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},_s={class:"col-span-1"},$s={class:"text-sm text-gray-900 dark:text-white"},js={key:0,class:"text-gray-500"},Cs={class:"col-span-1"},Ms={class:"flex items-center space-x-2"},Ts=["onClick"],Ss={key:0,class:"px-6 py-12 text-center"},Ps={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},zs={class:"flex items-center justify-between mb-4"},As={class:"font-medium text-gray-900 dark:text-white"},Ds={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Is={class:"space-y-3"},Vs=["onClick"],Es={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Bs={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Us={class:"flex items-center justify-between"},Rs={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Hs={class:"mt-3"},Os={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Fs={class:"grid grid-cols-1 gap-4"},Ls={class:"grid grid-cols-2 gap-4"},Ks=["value"],qs={class:"grid grid-cols-2 gap-4"},Ns={class:"flex justify-end space-x-3 mt-6"},Xs=["disabled"],Js={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:P}){const _=b,T=ne(),{hasPermission:C}=_e(),v=S([]),x=S(!1),y=S(""),f=S("list"),a=S(!1),M=S({status:"",priority:"",assignee_id:"",search:""}),B=S(!1),V=S(!1),I=S(null),k=S({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),N=U(()=>C.value("manage_project_tasks")),$=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],u=async()=>{var z,c;if((z=_.project)!=null&&z.id){x.value=!0,y.value="";try{const Z=new URLSearchParams({project_id:_.project.id,...M.value}),se=await fetch(`/api/tasks?${Z}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!se.ok)throw new Error("Errore nel caricamento dei task");const h=await se.json();v.value=((c=h.data)==null?void 0:c.tasks)||h.tasks||[]}catch(Z){y.value=Z.message}finally{x.value=!1}}},D=async()=>{a.value=!0;try{const z=V.value?`/api/tasks/${I.value.id}`:"/api/tasks",c=V.value?"PUT":"POST",Z={...k.value,project_id:_.project.id};if(!(await fetch(z,{method:c,headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(Z)})).ok)throw new Error("Errore nel salvataggio del task");await u(),j()}catch(z){y.value=z.message}finally{a.value=!1}},L=z=>{I.value=z,k.value={name:z.name,description:z.description||"",status:z.status,priority:z.priority,assignee_id:z.assignee_id||"",due_date:z.due_date?z.due_date.split("T")[0]:"",estimated_hours:z.estimated_hours},V.value=!0},j=()=>{B.value=!1,V.value=!1,I.value=null,k.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},m=z=>v.value.filter(c=>c.status===z),g=z=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[z]||"bg-gray-100 text-gray-800",R=z=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[z]||z,n=z=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[z]||"bg-gray-100 text-gray-800",t=z=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[z]||z,i=(z,c)=>`${(z==null?void 0:z.charAt(0))||""}${(c==null?void 0:c.charAt(0))||""}`.toUpperCase(),l=z=>new Date(z).toLocaleDateString("it-IT");let w;const q=()=>{clearTimeout(w),w=setTimeout(()=>{u()},300)};return Q(()=>{var z;return(z=_.project)==null?void 0:z.id},z=>{z&&u()}),te(()=>{var z;(z=_.project)!=null&&z.id&&u()}),P({refresh:u}),(z,c)=>{var Z,se;return s(),r("div",Wt,[e("div",Gt,[e("div",Yt,[e("div",Qt,[c[16]||(c[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),N.value?(s(),r("button",{key:0,onClick:c[0]||(c[0]=h=>B.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},c[15]||(c[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Nuovo Task ")]))):A("",!0)]),e("div",Zt,[e("div",null,[c[18]||(c[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":c[1]||(c[1]=h=>M.value.status=h),onChange:u,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},c[17]||(c[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[W,M.value.status]])]),e("div",null,[c[20]||(c[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":c[2]||(c[2]=h=>M.value.priority=h),onChange:u,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},c[19]||(c[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[W,M.value.priority]])]),e("div",null,[c[22]||(c[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":c[3]||(c[3]=h=>M.value.assignee_id=h),onChange:u,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[c[21]||(c[21]=e("option",{value:""},"Tutti",-1)),(s(!0),r(O,null,F(((Z=b.project)==null?void 0:Z.team_members)||[],h=>(s(),r("option",{key:h.id,value:h.id},o(h.first_name)+" "+o(h.last_name),9,es))),128))],544),[[W,M.value.assignee_id]])]),e("div",null,[c[23]||(c[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":c[4]||(c[4]=h=>M.value.search=h),onInput:q,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[J,M.value.search]])])]),e("div",ts,[e("div",ss,[c[24]||(c[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:c[5]||(c[5]=h=>f.value="list"),class:H([f.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:c[6]||(c[6]=h=>f.value="kanban"),class:H([f.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",rs,o(v.value.length)+" task trovati ",1)])])]),x.value?(s(),r("div",os,c[25]||(c[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):A("",!0),y.value?(s(),r("div",as,[e("p",ns,o(y.value),1)])):A("",!0),!x.value&&f.value==="list"?(s(),r("div",is,[e("div",ls,[c[27]||(c[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(s(!0),r(O,null,F(v.value,h=>(s(),r("div",{key:h.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ds,[e("div",us,o(h.name),1),h.description?(s(),r("div",cs,o(h.description),1)):A("",!0)]),e("div",gs,[h.assignee?(s(),r("div",ms,[e("div",ps,o(i(h.assignee.first_name,h.assignee.last_name)),1),e("div",vs,[e("div",xs,o(h.assignee.first_name)+" "+o(h.assignee.last_name),1)])])):(s(),r("span",ys,"Non assegnato"))]),e("div",fs,[e("span",{class:H([g(h.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(R(h.status)),3)]),e("div",bs,[e("span",{class:H([n(h.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(t(h.priority)),3)]),e("div",hs,[h.due_date?(s(),r("div",ks,o(l(h.due_date)),1)):(s(),r("span",ws,"-"))]),e("div",_s,[e("div",$s,[X(o(h.actual_hours||0)+"h ",1),h.estimated_hours?(s(),r("span",js,"/ "+o(h.estimated_hours)+"h",1)):A("",!0)])]),e("div",Cs,[e("div",Ms,[e("button",{onClick:d=>L(h),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Ts)])])]))),128)),v.value.length===0?(s(),r("div",Ss,c[26]||(c[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):A("",!0)])])):A("",!0),!x.value&&f.value==="kanban"?(s(),r("div",Ps,[(s(),r(O,null,F($,h=>e("div",{key:h.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",zs,[e("h4",As,o(h.label),1),e("span",Ds,o(m(h.value).length),1)]),e("div",Is,[(s(!0),r(O,null,F(m(h.value),d=>(s(),r("div",{key:d.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:p=>L(d)},[e("div",Es,o(d.name),1),d.description?(s(),r("div",Bs,o(d.description),1)):A("",!0),e("div",Us,[e("span",{class:H([n(d.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(t(d.priority)),3),d.assignee?(s(),r("div",Rs,o(i(d.assignee.first_name,d.assignee.last_name)),1)):A("",!0)])],8,Vs))),128))])])),64))])):A("",!0),B.value||V.value?(s(),r("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:j},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:c[14]||(c[14]=re(()=>{},["stop"]))},[e("div",Hs,[e("h3",Os,o(V.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:re(D,["prevent"])},[e("div",Fs,[e("div",null,[c[28]||(c[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":c[7]||(c[7]=h=>k.value.name=h),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.name]])]),e("div",null,[c[29]||(c[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":c[8]||(c[8]=h=>k.value.description=h),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.description]])]),e("div",Ls,[e("div",null,[c[31]||(c[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":c[9]||(c[9]=h=>k.value.status=h),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},c[30]||(c[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[W,k.value.status]])]),e("div",null,[c[33]||(c[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":c[10]||(c[10]=h=>k.value.priority=h),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},c[32]||(c[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[W,k.value.priority]])])]),e("div",null,[c[35]||(c[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":c[11]||(c[11]=h=>k.value.assignee_id=h),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[c[34]||(c[34]=e("option",{value:""},"Non assegnato",-1)),(s(!0),r(O,null,F(((se=b.project)==null?void 0:se.team_members)||[],h=>(s(),r("option",{key:h.id,value:h.id},o(h.first_name)+" "+o(h.last_name),9,Ks))),128))],512),[[W,k.value.assignee_id]])]),e("div",qs,[e("div",null,[c[36]||(c[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":c[12]||(c[12]=h=>k.value.due_date=h),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.due_date]])]),e("div",null,[c[37]||(c[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":c[13]||(c[13]=h=>k.value.estimated_hours=h),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.estimated_hours]])])])]),e("div",Ns,[e("button",{type:"button",onClick:j,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:a.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(a.value?"Salvataggio...":V.value?"Aggiorna":"Crea"),9,Xs)])],32)])])])):A("",!0)])}}},Ws={class:"space-y-6"},Gs={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ys={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qs={class:"flex items-center justify-between"},Zs={class:"p-6 border-b border-gray-200 dark:border-gray-700"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},rr={class:"text-center"},or={class:"text-2xl font-bold text-green-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-blue-600"},ir={class:"text-center"},lr={class:"text-2xl font-bold text-purple-600"},dr={class:"p-6"},ur={class:"space-y-4"},cr={class:"flex items-center justify-between"},gr={class:"flex items-center space-x-4"},mr={class:"flex-shrink-0"},pr=["src","alt"],vr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},xr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},yr={class:"flex-1"},fr={class:"flex items-center space-x-2"},br={class:"text-lg font-medium text-gray-900 dark:text-white"},hr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},kr={class:"text-sm text-gray-600 dark:text-gray-400"},wr={class:"text-xs text-gray-500 dark:text-gray-500"},_r={class:"flex items-center space-x-4"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"text-right"},Sr={class:"text-sm font-medium text-gray-900 dark:text-white"},Pr={class:"flex items-center space-x-2"},zr=["onClick"],Ar=["onClick"],Dr={class:"mt-4"},Ir={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Vr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Er={key:0,class:"text-center py-8"},Br={class:"mt-6"},Ur={class:"mt-3"},Rr={class:"space-y-4"},Hr=["value"],Or={class:"flex justify-end space-x-3 mt-6"},Fr=["disabled"],Lr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(b,{expose:P,emit:_}){const T=b,C=ne(),v=S(!1),x=S([]),y=S(!1),f=S({user_id:"",role:""}),a=U(()=>{var t;return((t=T.project)==null?void 0:t.team_members)||[]}),M=U(()=>a.value.reduce((t,i)=>t+(i.hours_worked||0),0)),B=U(()=>a.value.length===0?0:Math.round(M.value/a.value.length)),V=U(()=>a.value.filter(t=>(t.hours_worked||0)>0).length),I=t=>t?t.split(" ").map(i=>i.charAt(0).toUpperCase()).slice(0,2).join(""):"??",k=t=>{var l;return(((l=T.project)==null?void 0:l.tasks)||[]).filter(w=>w.assignee_id===t).length},N=t=>{var l;return(((l=T.project)==null?void 0:l.tasks)||[]).filter(w=>w.assignee_id===t&&w.status==="done").length},$=t=>{const i=k(t),l=N(t);return i===0?0:Math.round(l/i*100)},u=t=>{const i=$(t);return i>=80?"bg-green-600":i>=60?"bg-yellow-600":i>=40?"bg-orange-600":"bg-red-600"},D=t=>!t||t===0?"0.00":parseFloat(t).toFixed(2),L=async()=>{var t;try{const i=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(i.ok){const l=await i.json(),w=a.value.map(q=>q.id);x.value=(t=l.data)!=null&&t.users?l.data.users.filter(q=>!w.includes(q.id)):[]}}catch(i){console.error("Errore nel caricamento utenti:",i),x.value=[]}},j=async()=>{y.value=!0;try{const t=await fetch(`/api/projects/${T.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken},body:JSON.stringify(f.value)});if(t.ok)n("refresh"),R();else{const i=await t.json();alert(i.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{y.value=!1}},m=t=>{console.log("Edit member:",t)},g=async t=>{if(confirm(`Rimuovere ${t.full_name} dal progetto?`))try{const i=await fetch(`/api/projects/${T.project.id}/team/${t.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(i.ok)n("refresh");else{const l=await i.json();alert(l.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},R=()=>{v.value=!1,f.value={user_id:"",role:""}},n=_;return te(()=>{L()}),Q(()=>v.value,t=>{t&&L()}),Q(()=>{var t;return(t=T.project)==null?void 0:t.team_members},()=>{v.value&&L()}),P({refresh:L}),(t,i)=>(s(),r("div",Ws,[e("div",Gs,[e("div",Ys,[e("div",Qs,[i[6]||(i[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:i[0]||(i[0]=l=>v.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[5]||(i[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Membro ")]))])]),e("div",Zs,[e("div",er,[e("div",tr,[e("div",sr,o(a.value.length),1),i[7]||(i[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",rr,[e("div",or,o(M.value)+"h",1),i[8]||(i[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",ar,[e("div",nr,o(B.value)+"h",1),i[9]||(i[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",ir,[e("div",lr,o(V.value),1),i[10]||(i[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",dr,[e("div",ur,[(s(!0),r(O,null,F(a.value,l=>{var w,q;return s(),r("div",{key:l.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",cr,[e("div",gr,[e("div",mr,[l.profile_image?(s(),r("img",{key:0,src:l.profile_image,alt:l.full_name,class:"w-12 h-12 rounded-full"},null,8,pr)):(s(),r("div",vr,[e("span",xr,o(I(l.full_name)),1)]))]),e("div",yr,[e("div",fr,[e("h4",br,o(l.full_name),1),l.id===((w=b.project)==null?void 0:w.manager_id)?(s(),r("span",hr," Project Manager ")):A("",!0)]),e("p",kr,o(l.role||"Team Member"),1),e("p",wr,o(l.email),1)])]),e("div",_r,[e("div",$r,[e("div",jr,o(D(l.hours_worked||0))+"h",1),i[11]||(i[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Cr,[e("div",Mr,o(k(l.id)),1),i[12]||(i[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Tr,[e("div",Sr,o(N(l.id)),1),i[13]||(i[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Pr,[e("button",{onClick:z=>m(l),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,zr),l.id!==((q=b.project)==null?void 0:q.manager_id)?(s(),r("button",{key:0,onClick:z=>g(l),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Ar)):A("",!0)])])]),e("div",Dr,[e("div",Ir,[i[16]||(i[16]=e("span",null,"Produttività",-1)),e("span",null,o($(l.id))+"%",1)]),e("div",Vr,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",u(l.id)]),style:ee({width:$(l.id)+"%"})},null,6)])])])}),128)),a.value.length===0?(s(),r("div",Er,[i[18]||(i[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),i[19]||(i[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),i[20]||(i[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Br,[e("button",{onClick:i[1]||(i[1]=l=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},i[17]||(i[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi primo membro ")]))])])):A("",!0)])])]),v.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:R},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[4]||(i[4]=re(()=>{},["stop"]))},[e("div",Ur,[i[25]||(i[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:re(j,["prevent"])},[e("div",Rr,[e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=l=>f.value.user_id=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Seleziona utente",-1)),(s(!0),r(O,null,F(x.value,l=>(s(),r("option",{key:l.id,value:l.id},o(l.full_name)+" ("+o(l.email)+") ",9,Hr))),128))],512),[[W,f.value.user_id]])]),e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":i[3]||(i[3]=l=>f.value.role=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[23]||(i[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[W,f.value.role]])])]),e("div",Or,[e("button",{type:"button",onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Aggiungendo...":"Aggiungi"),9,Fr)])],32)])])])):A("",!0)]))}};function Kr(b,P){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(b,P){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function qr(b,P){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Nr={class:"fixed inset-0 z-50 overflow-y-auto"},Xr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Jr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Wr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Gr={class:"mb-4"},Yr={class:"text-lg font-medium text-gray-900 dark:text-white"},Qr={class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},eo=["disabled"],to={key:0},so={key:1},ro={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(b,{emit:P}){const _=b,T=P,C=S(!1),v=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),x=async()=>{C.value=!0;try{const f=_.expense?`/api/expenses/${_.expense.id}`:`/api/projects/${_.projectId}/expenses`,a=_.expense?"PUT":"POST";(await fetch(f,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(v)})).ok?T("saved"):console.error("Error saving expense")}catch(f){console.error("Error saving expense:",f)}finally{C.value=!1}},y=f=>{const a=f.target.files[0];if(a){if(a.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),f.target.value="";return}v.receipt_file=a}};return te(()=>{_.expense&&Object.assign(v,{description:_.expense.description,amount:_.expense.amount,category:_.expense.category,billing_type:_.expense.billing_type||"billable",status:_.expense.status||"pending",date:_.expense.date.split("T")[0],notes:_.expense.notes||""})}),(f,a)=>(s(),r("div",Nr,[e("div",Xr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:a[0]||(a[0]=M=>f.$emit("close"))}),e("div",Jr,[e("form",{onSubmit:re(x,["prevent"])},[e("div",Wr,[e("div",Gr,[e("h3",Yr,o(b.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Qr,[e("div",null,[a[9]||(a[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":a[1]||(a[1]=M=>v.description=M),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[J,v.description]])]),e("div",null,[a[10]||(a[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":a[2]||(a[2]=M=>v.amount=M),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[J,v.amount,void 0,{number:!0}]])]),e("div",null,[a[12]||(a[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":a[3]||(a[3]=M=>v.category=M),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[11]||(a[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[W,v.category]])]),e("div",null,[a[14]||(a[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":a[4]||(a[4]=M=>v.billing_type=M),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[13]||(a[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[W,v.billing_type]])]),e("div",null,[a[16]||(a[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":a[5]||(a[5]=M=>v.status=M),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[15]||(a[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[W,v.status]])]),e("div",null,[a[17]||(a[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":a[6]||(a[6]=M=>v.date=M),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[J,v.date]])]),e("div",null,[a[18]||(a[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":a[7]||(a[7]=M=>v.notes=M),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[J,v.notes]])]),e("div",null,[a[19]||(a[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:y,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),a[20]||(a[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",Zr,[e("button",{type:"submit",disabled:C.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[C.value?(s(),r("span",to,"Salvando...")):(s(),r("span",so,o(b.expense?"Aggiorna":"Salva"),1))],8,eo),e("button",{type:"button",onClick:a[8]||(a[8]=M=>f.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},oo={class:"project-expenses"},ao={class:"space-y-6"},no={class:"flex justify-between items-center"},io={key:0,class:"text-center py-8"},lo={key:1,class:"text-center py-12"},uo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},co={class:"divide-y divide-gray-200 dark:divide-gray-700"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},po={class:"flex items-center"},vo={class:"flex-shrink-0"},xo={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},yo={class:"ml-4 flex-1"},fo={class:"flex items-center justify-between"},bo={class:"text-sm font-medium text-gray-900 dark:text-white"},ho={class:"ml-2 flex-shrink-0"},ko={class:"text-sm font-medium text-gray-900 dark:text-white"},wo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},_o={class:"capitalize"},$o={key:0,class:"mx-2"},jo={key:1},Co={class:"mt-2 flex items-center space-x-4 text-xs"},Mo={key:0,class:"inline-flex items-center text-green-600 dark:text-green-400"},To={key:0,class:"flex items-center space-x-2"},So=["onClick"],Po=["onClick"],zo={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Ao={class:"flex justify-between items-center"},Do={class:"text-lg font-bold text-gray-900 dark:text-white"},Io={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(b){const P=b;xe();const _=ne(),T=S(!1),C=S([]),v=S(!1),x=S(null),y=U(()=>_.hasPermission("manage_expenses")),f=U(()=>C.value.reduce((m,g)=>m+g.amount,0)),a=async()=>{var m;if((m=P.project)!=null&&m.id){T.value=!0;try{const g=await fetch(`/api/projects/${P.project.id}/expenses`);g.ok&&(C.value=await g.json())}catch(g){console.error("Error loading expenses:",g)}finally{T.value=!1}}},M=m=>{x.value=m,v.value=!0},B=async m=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${m}`,{method:"DELETE"})).ok&&(C.value=C.value.filter(R=>R.id!==m))}catch(g){console.error("Error deleting expense:",g)}},V=()=>{v.value=!1,x.value=null},I=()=>{V(),a()},k=m=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(m),N=m=>new Date(m).toLocaleDateString("it-IT"),$=m=>({licenses:"📄 Licenze",travel:"✈️ Viaggi",meals:"🍽️ Pasti",equipment:"🖥️ Attrezzature",external:"🏢 Servizi Esterni",other:"📦 Altro"})[m]||m,u=m=>({billable:"💰 Fatturabile","non-billable":"🏢 Non Fatturabile",reimbursable:"💳 Rimborsabile"})[m]||m,D=m=>({billable:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400","non-billable":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",reimbursable:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"})[m]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",L=m=>({pending:"⏳ In Attesa",approved:"✅ Approvata",rejected:"❌ Rifiutata"})[m]||m,j=m=>({pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",approved:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[m]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";return Q(()=>{var m;return(m=P.project)==null?void 0:m.id},(m,g)=>{m&&m!==g&&a()},{immediate:!0}),te(()=>{a()}),(m,g)=>{var R;return s(),r("div",oo,[e("div",ao,[e("div",no,[g[2]||(g[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),y.value?(s(),r("button",{key:0,onClick:g[0]||(g[0]=n=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(le(qr),{class:"w-4 h-4 mr-2"}),g[1]||(g[1]=X(" Aggiungi Spesa "))])):A("",!0)]),T.value?(s(),r("div",io,g[3]||(g[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):C.value.length===0?(s(),r("div",lo,[oe(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),g[4]||(g[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),g[5]||(g[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(s(),r("div",uo,[e("ul",co,[(s(!0),r(O,null,F(C.value,n=>(s(),r("li",{key:n.id,class:"px-6 py-4"},[e("div",go,[e("div",mo,[e("div",po,[e("div",vo,[e("div",xo,[oe(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",yo,[e("div",fo,[e("p",bo,o(n.description),1),e("div",ho,[e("p",ko," €"+o(k(n.amount)),1)])]),e("div",wo,[oe(le(Kr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),X(" "+o(N(n.date))+" ",1),g[6]||(g[6]=e("span",{class:"mx-2"},"•",-1)),e("span",_o,o($(n.category)),1),n.user?(s(),r("span",$o,"•")):A("",!0),n.user?(s(),r("span",jo,o(n.user.name),1)):A("",!0)]),e("div",Co,[e("span",{class:H([D(n.billing_type),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(u(n.billing_type)),3),e("span",{class:H([j(n.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(L(n.status)),3),n.receipt_path?(s(),r("span",Mo,g[7]||(g[7]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),X(" Ricevuta ")]))):A("",!0)])])])]),y.value?(s(),r("div",To,[e("button",{onClick:t=>M(n),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,So),e("button",{onClick:t=>B(n.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Po)])):A("",!0)])]))),128))])])),C.value.length>0?(s(),r("div",zo,[e("div",Ao,[g[8]||(g[8]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Do,"€"+o(k(f.value)),1)])])):A("",!0)]),v.value?(s(),de(ro,{key:0,"project-id":(R=b.project)==null?void 0:R.id,expense:x.value,onClose:V,onSaved:I},null,8,["project-id","expense"])):A("",!0)])}}},Vo={class:"project-kpi"},Eo={key:0,class:"animate-pulse space-y-4"},Bo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Uo={key:1,class:"space-y-6"},Ro={class:"bg-white shadow rounded-lg p-6"},Ho={class:"flex items-center justify-between"},Oo=["disabled"],Fo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Lo={class:"bg-white shadow rounded-lg p-6"},Ko={class:"flex items-center"},qo={class:"ml-5 w-0 flex-1"},No={class:"text-lg font-medium text-gray-900"},Xo={class:"text-xs text-gray-500"},Jo={class:"bg-white shadow rounded-lg p-6"},Wo={class:"flex items-center"},Go={class:"ml-5 w-0 flex-1"},Yo={class:"text-lg font-medium text-gray-900"},Qo={class:"bg-white shadow rounded-lg p-6"},Zo={class:"flex items-center"},ea={class:"ml-5 w-0 flex-1"},ta={class:"text-lg font-medium text-gray-900"},sa={class:"text-xs text-gray-500"},ra={class:"bg-white shadow rounded-lg p-6"},oa={class:"flex items-center"},aa={class:"ml-5 w-0 flex-1"},na={class:"text-lg font-medium text-gray-900"},ia={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},la={class:"bg-white shadow rounded-lg p-6"},da={class:"space-y-4"},ua={class:"flex justify-between text-sm"},ca={class:"font-medium"},ga={class:"w-full bg-gray-200 rounded-full h-3"},ma={class:"flex justify-between text-sm"},pa={class:"text-gray-600"},va={class:"font-medium"},xa={class:"bg-white shadow rounded-lg p-6"},ya={class:"space-y-4"},fa={class:"flex justify-between text-sm"},ba={class:"font-medium"},ha={class:"w-full bg-gray-200 rounded-full h-3"},ka={class:"flex justify-between text-sm"},wa={class:"text-gray-600"},_a={class:"font-medium"},$a={class:"bg-white shadow rounded-lg p-6"},ja={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={class:"text-center p-4 border rounded-lg"},Sa={class:"text-xs text-gray-500"},Pa={class:"text-center p-4 border rounded-lg"},za={class:"text-xs text-gray-500"},Aa={key:2,class:"text-center py-8"},Da={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ia={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Va={class:"mt-3"},Ea={class:"mt-6 space-y-6"},Ba={class:"bg-gray-50 p-4 rounded-lg"},Ua={class:"font-medium text-gray-900"},Ra={class:"text-sm text-gray-600"},Ha={class:"space-y-6"},Oa={class:"flex items-center justify-between mb-4"},Fa={class:"font-medium text-gray-900"},La={class:"text-sm text-gray-600"},Ka={class:"flex items-center space-x-2"},qa={class:"text-xs text-gray-500"},Na=["onClick"],Xa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ja=["onUpdate:modelValue","onInput"],Wa=["onUpdate:modelValue","onInput"],Ga=["onUpdate:modelValue","onInput"],Ya={class:"mt-4"},Qa=["onUpdate:modelValue","onInput"],Za={class:"mt-4 flex justify-end"},en=["onClick","disabled"],tn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},sn={key:1,class:"text-sm text-green-600"},rn={class:"mt-6 pt-4 border-t flex justify-between"},on={class:"flex space-x-3"},an=["disabled"],nn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(b,{emit:P}){const _=b,T=P,C=S(!1),v=S(!1),x=S(null),y=S({}),f=S({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),a=S({budget:80,time:85,margin:15}),M=U(()=>{var d;return!((d=_.project)!=null&&d.budget)||f.value.totalCosts===0?0:Math.round(f.value.totalCosts/_.project.budget*100)}),B=U(()=>{var d;return!((d=_.project)!=null&&d.estimated_hours)||f.value.totalHours===0?0:Math.round(f.value.totalHours/_.project.estimated_hours*100)}),V=U(()=>{const d=f.value.costVariance;return d>0?"text-red-600":d<0?"text-green-600":"text-gray-600"}),I=U(()=>{const d=f.value.marginPercentage;return d>=a.value.margin?"text-green-600":d>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),k=U(()=>{const d=f.value.marginPercentage;return d>=a.value.margin?"Ottimo":d>=a.value.margin*.7?"Accettabile":"Critico"}),N=U(()=>{const d=M.value;return d>=a.value.budget?"text-red-600":d>=a.value.budget*.8?"text-yellow-600":"text-green-600"}),$=U(()=>{const d=B.value;return d>=a.value.time?"text-red-600":d>=a.value.time*.8?"text-yellow-600":"text-green-600"}),u=U(()=>{const d=f.value.marginPercentage;return d>=a.value.margin?"text-green-600":d>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),D=d=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(d||0),L=d=>!d||d===0?"0h":`${parseFloat(d).toFixed(2)}h`,j=d=>`${(d||0).toFixed(1)}%`,m=async()=>{var d;(d=_.project)!=null&&d.id&&g()},g=()=>{const d=_.project;d&&(f.value={totalHours:d.total_hours||0,workDays:Math.ceil((d.total_hours||0)/8),totalCosts:(d.total_hours||0)*50,costVariance:(d.total_hours||0)*50-(d.budget||0),potentialRevenue:d.budget||0,actualRevenue:d.invoiced_amount||0,marginPercentage:d.budget?(d.budget-(d.total_hours||0)*50)/d.budget*100:0})},R=async()=>{C.value=!0;try{await m(),T("refresh")}catch(d){console.error("Error refreshing KPIs:",d)}finally{C.value=!1}},n=U(()=>{var p;const d=((p=_.project)==null?void 0:p.project_type)||"service";return t(d)}),t=d=>{const p={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return p[d]||p.service},i=d=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[d]||"Sconosciuto",l=()=>{n.value.forEach(p=>{y.value[p.name]||(y.value[p.name]={target_min:p.target_min,target_max:p.target_max,warning_threshold:p.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),v.value=!0},w=()=>{v.value=!1},q=d=>{y.value[d]&&(y.value[d].isDirty=!0,y.value[d].isSaved=!1)},z=d=>{const p=n.value.find(G=>G.name===d);p&&y.value[d]&&(y.value[d].target_min=p.target_min,y.value[d].target_max=p.target_max,y.value[d].warning_threshold=p.warning_threshold,y.value[d].custom_description="",y.value[d].isDirty=!0,y.value[d].isSaved=!1)},c=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&n.value.forEach(d=>{z(d.name)})},Z=async d=>{var p;if(y.value[d]){x.value=d;try{const G=y.value[d];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(p=_.project)==null?void 0:p.id,kpi_name:d,target_min:G.target_min,target_max:G.target_max,warning_threshold:G.warning_threshold,custom_description:G.custom_description}),y.value[d].isDirty=!1,y.value[d].isSaved=!0,setTimeout(()=>{y.value[d]&&(y.value[d].isSaved=!1)},3e3)}catch(G){console.error("Error saving KPI config:",G),alert("Errore nel salvataggio della configurazione KPI")}finally{x.value=null}}},se=async()=>{const d=n.value.filter(p=>{var G;return(G=y.value[p.name])==null?void 0:G.isDirty});for(const p of d)await Z(p.name)},h=U(()=>n.value.some(d=>{var p;return(p=y.value[d.name])==null?void 0:p.isDirty}));return Q(()=>_.project,d=>{d&&m()},{immediate:!0}),te(()=>{_.project&&m()}),(d,p)=>{var G,ie;return s(),r("div",Vo,[b.loading?(s(),r("div",Eo,[e("div",Bo,[(s(),r(O,null,F(4,K=>e("div",{key:K,class:"bg-gray-200 rounded-lg h-24"})),64))]),p[0]||(p[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):b.project?(s(),r("div",Uo,[e("div",Ro,[e("div",Ho,[p[3]||(p[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:R,disabled:C.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),r("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":C.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},p[1]||(p[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),p[2]||(p[2]=X(" Aggiorna "))],8,Oo)])]),e("div",Fo,[e("div",Lo,[e("div",Ko,[p[5]||(p[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",qo,[e("dl",null,[p[4]||(p[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",No,o(L(f.value.totalHours)),1),e("dd",Xo,o(f.value.workDays)+" giorni lavorati",1)])])])]),e("div",Jo,[e("div",Wo,[p[7]||(p[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Go,[e("dl",null,[p[6]||(p[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Yo,o(D(f.value.totalCosts)),1),e("dd",{class:H(["text-xs",V.value])},o(D(f.value.costVariance))+" vs budget",3)])])])]),e("div",Qo,[e("div",Zo,[p[9]||(p[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ea,[e("dl",null,[p[8]||(p[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ta,o(D(f.value.potentialRevenue)),1),e("dd",sa,o(D(f.value.actualRevenue))+" fatturati",1)])])])]),e("div",ra,[e("div",oa,[p[11]||(p[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",aa,[e("dl",null,[p[10]||(p[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",na,o(j(f.value.marginPercentage)),1),e("dd",{class:H(["text-xs",I.value])},o(k.value),3)])])])])]),e("div",ia,[e("div",la,[p[13]||(p[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",da,[e("div",ua,[p[12]||(p[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ca,o(D(b.project.budget||0)),1)]),e("div",ga,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:M.value+"%"})},null,4)]),e("div",ma,[e("span",pa,"Utilizzato: "+o(D(f.value.totalCosts)),1),e("span",va,o(M.value)+"%",1)])])]),e("div",xa,[p[15]||(p[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",ya,[e("div",fa,[p[14]||(p[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",ba,o(L(b.project.estimated_hours||0)),1)]),e("div",ha,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:B.value+"%"})},null,4)]),e("div",ka,[e("span",wa,"Lavorate: "+o(L(f.value.totalHours)),1),e("span",_a,o(B.value)+"%",1)])])])]),e("div",$a,[e("div",{class:"flex items-center justify-between mb-4"},[p[17]||(p[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:l,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},p[16]||(p[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),X(" Configura KPI ")]))]),e("div",ja,[e("div",Ca,[e("div",{class:H(["text-2xl font-bold",N.value])},o(M.value)+"% ",3),p[18]||(p[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ma,"Soglia: "+o(a.value.budget)+"%",1)]),e("div",Ta,[e("div",{class:H(["text-2xl font-bold",$.value])},o(B.value)+"% ",3),p[19]||(p[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Sa,"Soglia: "+o(a.value.time)+"%",1)]),e("div",Pa,[e("div",{class:H(["text-2xl font-bold",u.value])},o(j(f.value.marginPercentage)),3),p[20]||(p[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",za,"Soglia: "+o(a.value.margin)+"%",1)])])])])):(s(),r("div",Aa,p[21]||(p[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),v.value?(s(),r("div",Da,[e("div",Ia,[e("div",Va,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[p[23]||(p[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:w,class:"text-gray-400 hover:text-gray-600"},p[22]||(p[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ea,[e("div",Ba,[e("h4",Ua,o((G=b.project)==null?void 0:G.name),1),e("p",Ra,"Tipo: "+o(i((ie=b.project)==null?void 0:ie.project_type)),1)]),e("div",Ha,[(s(!0),r(O,null,F(n.value,K=>{var ce,ge;return s(),r("div",{key:K.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Oa,[e("div",null,[e("h5",Fa,o(K.display_name),1),e("p",La,o(K.description),1)]),e("div",Ka,[e("span",qa,o(K.unit),1),e("button",{onClick:Y=>z(K.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Na)])]),e("div",Xa,[e("div",null,[p[24]||(p[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Y=>y.value[K.name].target_min=Y,onInput:Y=>q(K.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ja),[[J,y.value[K.name].target_min]])]),e("div",null,[p[25]||(p[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Y=>y.value[K.name].target_max=Y,onInput:Y=>q(K.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Wa),[[J,y.value[K.name].target_max]])]),e("div",null,[p[26]||(p[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Y=>y.value[K.name].warning_threshold=Y,onInput:Y=>q(K.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ga),[[J,y.value[K.name].warning_threshold]])])]),e("div",Ya,[p[27]||(p[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Y=>y.value[K.name].custom_description=Y,onInput:Y=>q(K.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Qa),[[J,y.value[K.name].custom_description]])]),e("div",Za,[(ce=y.value[K.name])!=null&&ce.isDirty?(s(),r("button",{key:0,onClick:Y=>Z(K.name),disabled:x.value===K.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[x.value===K.name?(s(),r("svg",tn,p[28]||(p[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):A("",!0),X(" "+o(x.value===K.name?"Salvataggio...":"Salva KPI"),1)],8,en)):(ge=y.value[K.name])!=null&&ge.isSaved?(s(),r("span",sn,"✓ Salvato")):A("",!0)])])}),128))])]),e("div",rn,[e("button",{onClick:c,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",on,[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:se,disabled:!h.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,an)])])])])])):A("",!0)])}}},ln={class:"space-y-6"},dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},un={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},cn={class:"flex items-center justify-between"},gn={class:"flex items-center space-x-4"},mn={class:"flex items-center space-x-2"},pn={key:0,class:"p-6"},vn={class:"overflow-x-auto"},xn={class:"min-w-[1000px]"},yn={class:"flex mb-4"},fn={class:"flex-1 flex"},bn={class:"space-y-1"},hn={class:"w-80 flex-shrink-0 px-4 py-3"},kn={class:"flex items-center space-x-2"},wn={class:"flex-1 min-w-0"},_n={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},$n={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},jn={key:0},Cn={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Mn={class:"flex-1 relative h-12 flex items-center"},Tn=["title"],Sn={class:"truncate"},Pn={class:"ml-2"},zn={key:1,class:"text-center py-12"},An={key:2,class:"flex justify-center py-12"},Dn={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:P}){const _=b,T=S("weeks"),C=S(new Date),v=S([]),x=S(0),y=U(()=>{var j;return((j=_.project)==null?void 0:j.tasks)||[]}),f=U(()=>y.value.filter(j=>j.start_date&&j.due_date).map(j=>{const m=M(j);return{...j,timeline:m}})),a=()=>{const j=new Date,m=new Date(C.value),g=[],R=12;for(let n=0;n<R;n++){const t=new Date(m);T.value==="weeks"?t.setDate(m.getDate()+n*7):T.value==="months"&&t.setMonth(m.getMonth()+n),g.push(t)}if(v.value=g,g.length>1){const n=g[0],t=new Date(g[g.length-1]);T.value==="weeks"?t.setDate(t.getDate()+7):T.value==="months"&&t.setMonth(t.getMonth()+1);const i=t-n,l=j-n;x.value=Math.max(0,Math.min(100,l/i*100))}else x.value=0},M=j=>{if(!v.value.length)return null;const m=new Date(j.start_date),g=new Date(j.due_date),R=v.value[0],t=v.value[v.value.length-1]-R,i=m-R,l=g-m,w=Math.max(0,i/t*100),q=Math.min(100-w,l/t*100);return{leftPercent:w,widthPercent:Math.max(5,q)}},B=j=>T.value==="weeks"?`${j.getDate()}/${j.getMonth()+1}`:T.value==="months"?j.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",V=j=>{const m=new Date,g=new Date(j);if(T.value==="weeks"){const R=new Date(g),n=new Date(g);return n.setDate(n.getDate()+6),m>=R&&m<=n}else if(T.value==="months")return g.getMonth()===m.getMonth()&&g.getFullYear()===m.getFullYear();return!1},I=()=>{const j=new Date;if(T.value==="weeks"){const m=new Date(j);m.setDate(j.getDate()-j.getDay()),C.value=m}else{const m=new Date(j.getFullYear(),j.getMonth(),1);C.value=m}a()},k=j=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[j]||"bg-gray-400",N=j=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[j]||"bg-gray-500",$=j=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[j]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",u=j=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[j]||"Non specificata",D=j=>({todo:0,"in-progress":50,review:75,done:100})[j.status]||0,L=j=>j?new Date(j).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Q(()=>_.project,()=>{a()},{immediate:!0}),te(()=>{I()}),P({refresh:a}),(j,m)=>(s(),r("div",ln,[e("div",dn,[e("div",un,[e("div",cn,[m[3]||(m[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",gn,[e("div",mn,[m[2]||(m[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":m[0]||(m[0]=g=>T.value=g),onChange:a,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},m[1]||(m[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[W,T.value]])]),e("button",{onClick:I,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!b.loading&&f.value.length>0?(s(),r("div",pn,[e("div",vn,[e("div",xn,[e("div",yn,[m[4]||(m[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",fn,[(s(!0),r(O,null,F(v.value,(g,R)=>(s(),r("div",{key:R,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":V(g)}])},o(B(g)),3))),128))])]),e("div",bn,[(s(!0),r(O,null,F(f.value,g=>(s(),r("div",{key:g.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",hn,[e("div",kn,[e("div",{class:H(["w-3 h-3 rounded-full",k(g.status)])},null,2),e("div",wn,[e("p",_n,o(g.name),1),e("div",$n,[g.assignee?(s(),r("span",jn,o(g.assignee.full_name),1)):A("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",$(g.priority)])},o(u(g.priority)),3)]),e("div",Cn,o(L(g.start_date))+" - "+o(L(g.due_date)),1)])])]),e("div",Mn,[g.timeline?(s(),r("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",N(g.status)]),style:ee({left:g.timeline.leftPercent+"%",width:g.timeline.widthPercent+"%",minWidth:"60px"}),title:`${g.name} - ${D(g)}% completato`},[e("span",Sn,o(g.name.length>15?g.name.substring(0,15)+"...":g.name),1),e("span",Pn,o(D(g))+"%",1)],14,Tn)):A("",!0),g.timeline&&D(g)>0&&D(g)<100?(s(),r("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:g.timeline.leftPercent+"%",width:g.timeline.widthPercent*D(g)/100+"%",minWidth:"2px"})},null,4)):A("",!0),(s(!0),r(O,null,F(v.value,(R,n)=>(s(),r("div",{key:n,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:n/v.value.length*100+"%"})},null,4))),128)),x.value>=0&&x.value<=100?(s(),r("div",{key:2,class:"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10",style:ee({left:x.value+"%"})},null,4)):A("",!0)])]))),128))])])]),m[5]||(m[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):b.loading?A("",!0):(s(),r("div",zn,m[6]||(m[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),b.loading?(s(),r("div",An,m[7]||(m[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):A("",!0)])]))}},In={class:"space-y-6"},Vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},En={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Bn={class:"flex items-center justify-between"},Un={class:"flex items-center space-x-4"},Rn={class:"flex items-center space-x-2"},Hn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},On={class:"flex items-center space-x-2"},Fn=["value"],Ln={key:0,class:"flex justify-center py-8"},Kn={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},qn={class:"text-red-600"},Nn={key:2,class:"p-6"},Xn={class:"overflow-x-auto"},Jn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Wn={class:"bg-gray-50 dark:bg-gray-700"},Gn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Yn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei=["onClick"],ti={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},si={key:1,class:"text-gray-300 dark:text-gray-600"},ri={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},oi={class:"text-sm font-medium text-gray-900 dark:text-white"},ai={class:"bg-gray-100 dark:bg-gray-600 font-medium"},ni={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ii={key:0,class:"text-center py-8"},li={class:"mt-3"},di={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ui={class:"grid grid-cols-1 gap-4"},ci=["value"],gi={class:"flex justify-end space-x-3 mt-6"},mi=["disabled"],pi={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:P}){const _=b,T=ne(),C=S(null),v=S(!1),x=S(""),y=S(!1),f=S(new Date().getFullYear()),a=S(new Date().getMonth()+1),M=S(""),B=S(!1),V=S(!1),I=S(null),k=S({task_id:"",date:"",hours:0,description:""}),N=U(()=>C.value?Array.from({length:C.value.days_in_month},(n,t)=>t+1):[]),$=async()=>{var n;if((n=_.project)!=null&&n.id){v.value=!0,x.value="";try{const t=new URLSearchParams({year:f.value.toString(),month:a.value.toString()});M.value&&t.append("member_id",M.value.toString());const i=await fetch(`/api/timesheets/project/${_.project.id}/monthly?${t}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento del timesheet");const l=await i.json();C.value=l.data}catch(t){x.value=t.message}finally{v.value=!1}}},u=async()=>{y.value=!0;try{const n={...k.value,project_id:_.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(n)})).ok)throw new Error("Errore nel salvataggio del timesheet");await $(),L()}catch(n){x.value=n.message}finally{y.value=!1}},D=(n,t)=>{const i=C.value.tasks.find(l=>l.id===n);i&&(I.value={taskId:n,day:t},k.value={task_id:n,date:`${f.value}-${String(a.value).padStart(2,"0")}-${String(t).padStart(2,"0")}`,hours:i.daily_hours[t]||0,description:""},i.daily_hours[t]>0?V.value=!0:B.value=!0)},L=()=>{B.value=!1,V.value=!1,I.value=null,k.value={task_id:"",date:"",hours:0,description:""}},j=()=>{a.value===1?(a.value=12,f.value--):a.value--,$()},m=()=>{a.value===12?(a.value=1,f.value++):a.value++,$()},g=n=>{const t=new Date;return t.getFullYear()===f.value&&t.getMonth()+1===a.value&&t.getDate()===n},R=n=>!n||n===0?"0":n%1===0?n.toString():n.toFixed(2);return Q(()=>{var n;return(n=_.project)==null?void 0:n.id},n=>{n&&$()}),Q(M,()=>{$()}),te(()=>{var n;(n=_.project)!=null&&n.id&&$()}),P({refresh:$}),(n,t)=>{var i,l;return s(),r("div",In,[e("div",Vn,[e("div",En,[e("div",Bn,[e("div",Un,[t[11]||(t[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Rn,[e("button",{onClick:j,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[7]||(t[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Hn,o(a.value)+"/"+o(f.value),1),e("button",{onClick:m,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[8]||(t[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",On,[t[10]||(t[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":t[0]||(t[0]=w=>M.value=w),onChange:$,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[9]||(t[9]=e("option",{value:""},"Tutti i membri",-1)),(s(!0),r(O,null,F(((i=b.project)==null?void 0:i.team_members)||[],w=>(s(),r("option",{key:w.id,value:w.id},o(w.first_name)+" "+o(w.last_name),9,Fn))),128))],544),[[W,M.value]])])]),e("button",{onClick:t[1]||(t[1]=w=>B.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[12]||(t[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Ore ")]))])]),v.value?(s(),r("div",Ln,t[13]||(t[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):A("",!0),x.value?(s(),r("div",Kn,[e("p",qn,o(x.value),1)])):A("",!0),!v.value&&C.value?(s(),r("div",Nn,[e("div",Xn,[e("table",Jn,[e("thead",Wn,[e("tr",null,[t[14]||(t[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(s(!0),r(O,null,F(N.value,w=>(s(),r("th",{key:w,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":g(w)}])},o(w),3))),128)),t[15]||(t[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Gn,[(s(!0),r(O,null,F(C.value.tasks,w=>(s(),r("tr",{key:w.id},[e("td",Yn,[e("div",Qn,o(w.name),1),e("div",Zn,o(w.workers.length?w.workers.join(", "):"Nessuno ha lavorato"),1)]),(s(!0),r(O,null,F(N.value,q=>(s(),r("td",{key:q,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":g(q)}]),onClick:z=>D(w.id,q)},[w.daily_hours[q]>0?(s(),r("span",ti,o(R(w.daily_hours[q])),1)):(s(),r("span",si,"-"))],10,ei))),128)),e("td",ri,[e("span",oi,o(R(w.total_hours)),1)])]))),128)),e("tr",ai,[t[16]||(t[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(s(!0),r(O,null,F(N.value,w=>(s(),r("td",{key:w,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":g(w)}])},o(R(C.value.daily_totals[w]||0)),3))),128)),e("td",ni,o(R(C.value.grand_total)),1)])])])]),C.value.tasks.length===0?(s(),r("div",ii,t[17]||(t[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):A("",!0)])):A("",!0)]),B.value||V.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:L},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[6]||(t[6]=re(()=>{},["stop"]))},[e("div",li,[e("h3",di,o(V.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:re(u,["prevent"])},[e("div",ui,[e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=w=>k.value.task_id=w),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[18]||(t[18]=e("option",{value:""},"Seleziona task",-1)),(s(!0),r(O,null,F(((l=C.value)==null?void 0:l.tasks)||[],w=>(s(),r("option",{key:w.id,value:w.id},o(w.name),9,ci))),128))],512),[[W,k.value.task_id]])]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":t[3]||(t[3]=w=>k.value.date=w),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.date]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":t[4]||(t[4]=w=>k.value.hours=w),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.hours]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=w=>k.value.description=w),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[J,k.value.description]])])]),e("div",gi,[e("button",{type:"button",onClick:L,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Salvataggio...":V.value?"Aggiorna":"Aggiungi"),9,mi)])],32)])])])):A("",!0)])}}},vi={class:"space-y-6"},xi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},yi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},fi={class:"flex items-center justify-between"},bi={class:"flex items-center space-x-3"},hi=["disabled"],ki={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},wi={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},_i={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},$i={class:"flex items-start space-x-3"},ji={class:"flex-1"},Ci={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},Mi={class:"mt-2 space-y-2"},Ti={key:0,class:"mt-3"},Si={class:"space-y-2"},Pi={class:"flex items-center space-x-3"},zi={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},Ai={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},Di={class:"text-sm font-medium text-gray-900 dark:text-white"},Ii={class:"text-xs text-gray-500 dark:text-gray-400"},Vi=["onClick"],Ei={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Bi={key:0,class:"p-6"},Ui={class:"animate-pulse space-y-4"},Ri={key:1,class:"p-6 text-center"},Hi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Oi={class:"flex items-center justify-between"},Fi={class:"flex items-center space-x-4"},Li={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},Ki={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},qi={class:"text-sm font-medium text-gray-900 dark:text-white"},Ni={class:"text-sm text-gray-500 dark:text-gray-400"},Xi={class:"flex items-center space-x-4"},Ji={class:"text-right"},Wi={class:"text-sm font-medium text-gray-900 dark:text-white"},Gi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Yi={class:"flex items-center space-x-2"},Qi=["onClick"],Zi=["onClick"],el={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},tl={class:"p-6"},sl={class:"space-y-4"},rl={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},ol={class:"flex-1 mx-4"},al={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},nl={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},il={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},ll={class:"mt-3"},dl={class:"space-y-4"},ul=["value"],cl={class:"flex justify-end space-x-3 mt-6"},gl=["disabled"],ml={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(b){const P=b,_=ne(),T=S(!0),C=S(!1),v=S(!1),x=S([]),y=S([]),f=S([]),a=S(null),M=S(!1),B=S({user_id:"",role:"",allocation_percentage:100}),V=U(()=>{var n;return(n=P.project)==null?void 0:n.id}),I=async()=>{var n;if(V.value){T.value=!0;try{const t=await fetch(`/api/resources?project_id=${V.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!t.ok)throw new Error("Errore nel caricamento allocazioni");const i=await t.json();x.value=((n=i.data)==null?void 0:n.resources)||[],await N()}catch(t){console.error("Error loading allocations:",t)}finally{T.value=!1}}},k=async()=>{var n;try{const t=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!t.ok)throw new Error("Errore nel caricamento utenti");const i=await t.json();y.value=((n=i.data)==null?void 0:n.users)||[]}catch(t){console.error("Error loading users:",t)}},N=async()=>{f.value=x.value.map(n=>({user_id:n.user_id,user_name:n.user_name,total_allocation:n.allocation_percentage+Math.floor(Math.random()*30)}))},$=async()=>{var n;if(V.value){v.value=!0;try{const t=await fetch(`/api/ai-resources/analyze-allocation/${V.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!t.ok)throw new Error("Errore nell'analisi AI");const i=await t.json();a.value=((n=i.data)==null?void 0:n.analysis)||null}catch(t){console.error("Error in AI analysis:",t),alert("Errore nell'analisi AI: "+t.message)}finally{v.value=!1}}},u=async()=>{C.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:V.value,...B.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await I(),M.value=!1,B.value={user_id:"",role:"",allocation_percentage:100}}catch(n){console.error("Error adding resource:",n),alert("Errore nell'aggiunta risorsa: "+n.message)}finally{C.value=!1}},D=n=>{console.log("Edit allocation:",n)},L=async n=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${n.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}})).ok)throw new Error("Errore nella rimozione");await I()}catch(t){console.error("Error removing allocation:",t),alert("Errore nella rimozione: "+t.message)}},j=async n=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:V.value,user_id:n.user_id,role:n.role,allocation_percentage:n.allocation})}),await I()}catch(t){console.error("Error applying AI recommendation:",t)}},m=n=>n>=80?"bg-red-500":n>=60?"bg-yellow-500":"bg-green-500",g=n=>n>100?"bg-red-500":n>=90?"bg-yellow-500":"bg-green-500",R=n=>n>100?"text-red-600 dark:text-red-400":n>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return Q(()=>P.project,n=>{n&&I()},{immediate:!0}),te(()=>{k()}),(n,t)=>{var i;return s(),r("div",vi,[e("div",xi,[e("div",yi,[e("div",fi,[t[9]||(t[9]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",bi,[e("button",{onClick:$,disabled:v.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[v.value?(s(),r("svg",wi,t[7]||(t[7]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(s(),r("svg",ki,t[6]||(t[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),X(" "+o(v.value?"Analizzando...":"Analisi AI"),1)],8,hi),e("button",{onClick:t[0]||(t[0]=l=>M.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[8]||(t[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Risorsa ")]))])])]),a.value?(s(),r("div",_i,[e("div",$i,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",ji,[e("h4",Ci," Insights AI - Efficienza: "+o(a.value.efficiency_score)+"% ",1),e("div",Mi,[(s(!0),r(O,null,F(a.value.optimization_insights,l=>(s(),r("div",{key:l,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+o(l),1))),128))]),(i=a.value.recommended_allocations)!=null&&i.length?(s(),r("div",Ti,[t[10]||(t[10]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Si,[(s(!0),r(O,null,F(a.value.recommended_allocations,l=>{var w;return s(),r("div",{key:l.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Pi,[e("div",zi,[e("span",Ai,o((w=l.user_name)==null?void 0:w.charAt(0)),1)]),e("div",null,[e("p",Di,o(l.user_name),1),e("p",Ii,o(l.role)+" - "+o(l.allocation)+"%",1)])]),e("button",{onClick:q=>j(l),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,Vi)])}),128))])])):A("",!0)]),e("button",{onClick:t[1]||(t[1]=l=>a.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},t[11]||(t[11]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):A("",!0)]),e("div",Ei,[t[17]||(t[17]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),T.value?(s(),r("div",Bi,[e("div",Ui,[(s(),r(O,null,F(3,l=>e("div",{key:l,class:"flex items-center space-x-4"},t[13]||(t[13]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):x.value.length?(s(),r("div",Hi,[(s(!0),r(O,null,F(x.value,l=>{var w;return s(),r("div",{key:l.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",Oi,[e("div",Fi,[e("div",Li,[e("span",Ki,o((w=l.user_name)==null?void 0:w.charAt(0)),1)]),e("div",null,[e("h4",qi,o(l.user_name),1),e("p",Ni,o(l.role||"Team Member"),1)])]),e("div",Xi,[e("div",Ji,[e("div",Wi,o(l.allocation_percentage)+"% ",1),e("div",Gi,[e("div",{class:H(["h-2 rounded-full",m(l.allocation_percentage)]),style:ee({width:l.allocation_percentage+"%"})},null,6)])]),e("div",Yi,[e("button",{onClick:q=>D(l),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[15]||(t[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Qi),e("button",{onClick:q=>L(l),class:"text-red-400 hover:text-red-600"},t[16]||(t[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Zi)])])])])}),128))])):(s(),r("div",Ri,t[14]||(t[14]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",el,[t[18]||(t[18]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",tl,[e("div",sl,[(s(!0),r(O,null,F(f.value,l=>(s(),r("div",{key:l.user_id,class:"flex items-center"},[e("div",rl,o(l.user_name),1),e("div",ol,[e("div",al,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",g(l.total_allocation)]),style:ee({width:Math.min(l.total_allocation,100)+"%"})},null,6)])]),e("div",{class:H(["w-16 text-sm text-right font-medium",R(l.total_allocation)])},o(l.total_allocation)+"% ",3)]))),128))])])]),M.value?(s(),r("div",nl,[e("div",il,[e("div",ll,[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:re(u,["prevent"])},[e("div",dl,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=l=>B.value.user_id=l),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[t[19]||(t[19]=e("option",{value:""},"Seleziona utente...",-1)),(s(!0),r(O,null,F(y.value,l=>(s(),r("option",{key:l.id,value:l.id},o(l.full_name)+" ("+o(l.role)+") ",9,ul))),128))],512),[[W,B.value.user_id]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":t[3]||(t[3]=l=>B.value.role=l),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[J,B.value.role]])]),e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":t[4]||(t[4]=l=>B.value.allocation_percentage=l),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[J,B.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",cl,[e("button",{type:"button",onClick:t[5]||(t[5]=l=>M.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:C.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o(C.value?"Salvando...":"Aggiungi"),9,gl)])],32)])])])):A("",!0)])}}},pl={class:"project-view"},vl={class:"tab-content"},xl={__name:"ProjectView",setup(b){const P=xe(),_=ne(),T=he(),C=we(),v=S(!0),x=S("overview"),y=U(()=>P.currentProject),f=U(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(k=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(k.id)||k.id==="kpi"&&_.hasPermission("view_reports")||k.id==="expenses"&&_.hasPermission("manage_expenses")))),a=U(()=>({overview:me,tasks:Js,team:Lr,resources:ml,expenses:Io,kpi:nn,gantt:Dn,timesheet:pi})[x.value]||me),M=async()=>{v.value=!0;try{const I=T.params.id;await P.fetchProject(I)}catch(I){console.error("Error loading project:",I)}finally{v.value=!1}},B=()=>{C.push(`/projects/${T.params.id}/edit`)},V=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await P.deleteProject(T.params.id),C.push("/projects")}catch(I){console.error("Error deleting project:",I)}};return Q(()=>T.params.id,(I,k)=>{I&&I!==k&&M()}),Q(()=>T.hash,I=>{if(I){const k=I.replace("#","");f.value.find(N=>N.id===k)&&x.value!==k&&(x.value=k)}},{immediate:!0}),Q(x,I=>{const k=`#${I}`;T.hash!==k&&C.replace({...T,hash:k})}),te(()=>{if(T.hash){const I=T.hash.replace("#","");f.value.find(k=>k.id===I)&&(x.value=I)}M()}),(I,k)=>(s(),r("div",pl,[oe(Ue,{project:y.value,loading:v.value,onEdit:B,onDelete:V},null,8,["project","loading"]),oe(qe,{modelValue:x.value,"onUpdate:modelValue":k[0]||(k[0]=N=>x.value=N),tabs:f.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",vl,[(s(),de(ke,null,[(s(),de(ve(a.value),{project:y.value,loading:v.value},null,8,["project","loading"]))],1024))])]))}},bl=ue(xl,[["__scopeId","data-v-de1f32e3"]]);export{bl as default};
