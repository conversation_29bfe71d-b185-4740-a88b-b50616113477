import{c as s,o as t,j as e,t as r,n as H,g as z,m as J,a as oe,i as xe,b as fe,F,k as L,h as de,D as ve,f as B,z as ee,r as P,w as Z,A as se,v as E,H as W,I as ae,x as X,s as te,N as be,p as ie,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as ye}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},Ae={key:0},ze={key:1},De={key:2},Ve={key:3},Ie={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(k){const A=y=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[y]||"bg-gray-100 text-gray-800",$=y=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[y]||y,S=y=>y?new Date(y).toLocaleDateString("it-IT"):"",j=y=>y?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y):"";return(y,f)=>{const b=fe("router-link");return t(),s("div",$e,[k.loading?(t(),s("div",je,f[1]||(f[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):k.project?(t(),s("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,r(k.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",A(k.project.status)])},r($(k.project.status)),3)]),e("div",Pe,[k.project.client?(t(),s("span",Ae,[f[2]||(f[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),J(" Cliente: "+r(k.project.client.name),1)])):z("",!0),k.project.start_date?(t(),s("span",ze,[f[3]||(f[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),J(" Inizio: "+r(S(k.project.start_date)),1)])):z("",!0),k.project.end_date?(t(),s("span",De,[f[4]||(f[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),J(" Fine: "+r(S(k.project.end_date)),1)])):z("",!0),k.project.budget?(t(),s("span",Ve,[f[5]||(f[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),J(" Budget: "+r(j(k.project.budget)),1)])):z("",!0)])]),e("div",Ie,[oe(b,{to:`/app/projects/${k.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:xe(()=>f[6]||(f[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),J(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:f[0]||(f[0]=h=>y.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},f[7]||(f[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),J(" Elimina ")]))])])):(t(),s("div",Ee,f[8]||(f[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},qe={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:k=>k.every(A=>typeof A=="object"&&A.id&&A.label)}},emits:["update:modelValue"],setup(k,{emit:A}){const $=k,S=A,j=b=>$.modelValue===b,y=b=>{S("update:modelValue",b)},f=b=>{const h={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return h[b]||h["chart-bar"]};return(b,h)=>(t(),s("div",Re,[e("div",He,[e("nav",Oe,[(t(!0),s(F,null,L(k.tabs,n=>(t(),s("button",{key:n.id,onClick:T=>y(n.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",j(n.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":j(n.id)?"page":void 0},[n.icon?(t(),de(ve(f(n.icon)),{key:0,class:"w-4 h-4"})):z("",!0),e("span",null,r(n.label),1),n.count!==void 0?(t(),s("span",Le,r(n.count),1)):z("",!0)],10,Fe))),128))])])]))}},Ke=ue(qe,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Je={key:1,class:"space-y-6"},We={class:"bg-white shadow rounded-lg p-6"},Ge={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"flex items-center"},tt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900"},rt={class:"bg-white shadow rounded-lg p-6"},ot={class:"flex items-center"},at={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},lt={class:"bg-white shadow rounded-lg p-6"},it={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},yt={class:"w-full bg-gray-200 rounded-full h-2.5"},xt={class:"text-sm text-gray-500 mt-2"},ft={class:"bg-white shadow rounded-lg p-6"},bt={class:"space-y-4"},ht={class:"flex justify-between items-center"},kt={class:"text-sm font-medium"},wt={class:"flex justify-between items-center"},_t={class:"text-sm font-medium"},$t={class:"w-full bg-gray-200 rounded-full h-3"},jt={class:"flex justify-between items-center text-sm"},Ct={class:"bg-white shadow rounded-lg p-6"},Mt={class:"space-y-3"},Tt={class:"flex-shrink-0"},St=["src","alt"],Pt={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},At={class:"text-xs font-medium text-gray-600"},zt={class:"flex-1"},Dt={class:"text-sm font-medium text-gray-900"},Vt={class:"text-xs text-gray-500"},It={class:"text-right"},Et={class:"text-xs text-gray-500"},Bt={key:0,class:"text-center py-4"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ot={class:"flex-1"},Ft={class:"text-sm text-gray-900"},Lt={class:"flex items-center space-x-2 mt-1"},qt={class:"text-xs text-gray-500"},Kt={class:"text-xs text-gray-500"},Nt={key:0,class:"text-center py-4"},Xt={key:2,class:"text-center py-8"},Jt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(k){const A=k,$=B(()=>{if(!A.project||!A.project.task_count)return 0;const C=A.project.completed_tasks||0,g=A.project.task_count||1;return Math.round(C/g*100)}),S=B(()=>{var C;return((C=A.project)==null?void 0:C.team_members)||[]}),j=B(()=>{var V,K,M;if((V=A.project)!=null&&V.expenses)return A.project.expenses;const C=((K=A.project)==null?void 0:K.total_hours)||0,g=(M=A.project)!=null&&M.client_daily_rate?A.project.client_daily_rate/8:50;return C*g}),y=B(()=>{var g;return(((g=A.project)==null?void 0:g.budget)||0)-j.value}),f=B(()=>{var g;const C=((g=A.project)==null?void 0:g.budget)||1;return Math.min(Math.round(j.value/C*100),100)}),b=B(()=>{const C=f.value;return C>=90?"bg-red-600":C>=75?"bg-yellow-600":"bg-green-600"}),h=B(()=>{var g;const C=y.value;return C<0?"text-red-600":C<(((g=A.project)==null?void 0:g.budget)||0)*.1?"text-yellow-600":"text-green-600"}),n=B(()=>{var C;return(C=A.project)!=null&&C.tasks?[...A.project.tasks].sort((g,V)=>new Date(V.updated_at)-new Date(g.updated_at)).slice(0,5).map(g=>{var V;return{id:g.id,description:`Task "${g.name}" ${T(g.status)}`,created_at:g.updated_at,user_name:((V=g.assignee)==null?void 0:V.full_name)||"Non assegnato",type:O(g.status)}}):[]}),T=C=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[C]||C,O=C=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[C]||"task_updated",I=C=>C?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(C):"Non specificato",D=C=>C?new Date(C).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",w=C=>C?C.split(" ").map(g=>g.charAt(0).toUpperCase()).slice(0,2).join(""):"??",q=C=>{const g={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return g[C]||g.default};return(C,g)=>(t(),s("div",Ne,[k.loading?(t(),s("div",Xe,g[0]||(g[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):k.project?(t(),s("div",Je,[e("div",We,[g[1]||(g[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),k.project.description?(t(),s("p",Ge,r(k.project.description),1)):(t(),s("p",Ye,"Nessuna descrizione disponibile"))]),e("div",Qe,[e("div",Ze,[e("div",et,[g[3]||(g[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",tt,[e("dl",null,[g[2]||(g[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",st,r(k.project.task_count||0),1)])])])]),e("div",rt,[e("div",ot,[g[5]||(g[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",at,[e("dl",null,[g[4]||(g[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",nt,r(k.project.completed_tasks||0),1)])])])]),e("div",lt,[e("div",it,[g[7]||(g[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",dt,[e("dl",null,[g[6]||(g[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ut,r(k.project.team_count||0),1)])])])]),e("div",ct,[e("div",gt,[g[9]||(g[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",mt,[e("dl",null,[g[8]||(g[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",pt,r(I(k.project.budget)),1)])])])])]),e("div",vt,[g[10]||(g[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",yt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${$.value}%`})},null,4)]),e("p",xt,r($.value)+"% completato",1)]),e("div",ft,[g[15]||(g[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",bt,[e("div",ht,[g[11]||(g[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",kt,r(I(k.project.budget)),1)]),g[14]||(g[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",wt,[g[12]||(g[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",_t,r(I(j.value)),1)]),e("div",$t,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",b.value]),style:ee({width:f.value+"%"})},null,6)]),e("div",jt,[g[13]||(g[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",h.value])},r(I(y.value)),3)])])]),e("div",Ct,[g[17]||(g[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Mt,[(t(!0),s(F,null,L(S.value,V=>(t(),s("div",{key:V.id,class:"flex items-center space-x-3"},[e("div",Tt,[V.profile_image?(t(),s("img",{key:0,src:V.profile_image,alt:V.full_name,class:"w-8 h-8 rounded-full"},null,8,St)):(t(),s("div",Pt,[e("span",At,r(w(V.full_name)),1)]))]),e("div",zt,[e("p",Dt,r(V.full_name),1),e("p",Vt,r(V.role||"Team Member"),1)]),e("div",It,[e("p",Et,r(V.hours_worked||0)+"h",1)])]))),128)),S.value.length===0?(t(),s("div",Bt,g[16]||(g[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):z("",!0)])]),e("div",Ut,[g[20]||(g[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Rt,[(t(!0),s(F,null,L(n.value,V=>(t(),s("div",{key:V.id,class:"flex items-start space-x-3"},[e("div",Ht,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",q(V.type)])},null,2)]),e("div",Ot,[e("p",Ft,r(V.description),1),e("div",Lt,[e("p",qt,r(D(V.created_at)),1),g[18]||(g[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",Kt,r(V.user_name),1)])])]))),128)),n.value.length===0?(t(),s("div",Nt,g[19]||(g[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):z("",!0)])])])):(t(),s("div",Xt,g[21]||(g[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Jt,[["__scopeId","data-v-16274846"]]),Wt={class:"space-y-6"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Yt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qt={class:"flex items-center justify-between"},Zt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},es=["value"],ts={class:"mt-4 flex items-center justify-between"},ss={class:"flex items-center space-x-4"},rs={class:"text-sm text-gray-500 dark:text-gray-400"},os={key:0,class:"flex justify-center py-8"},as={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ns={class:"text-red-600"},ls={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},is={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ds={class:"col-span-4"},us={class:"text-sm font-medium text-gray-900 dark:text-white"},cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},gs={class:"col-span-2"},ms={key:0,class:"flex items-center"},ps={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},vs={class:"ml-2"},ys={class:"text-sm font-medium text-gray-900 dark:text-white"},xs={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},fs={class:"col-span-1"},bs={class:"col-span-1"},hs={class:"col-span-2"},ks={key:0,class:"text-sm text-gray-900 dark:text-white"},ws={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},_s={class:"col-span-1"},$s={class:"text-sm text-gray-900 dark:text-white"},js={key:0,class:"text-gray-500"},Cs={class:"col-span-1"},Ms={class:"flex items-center space-x-2"},Ts=["onClick"],Ss={key:0,class:"px-6 py-12 text-center"},Ps={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},As={class:"flex items-center justify-between mb-4"},zs={class:"font-medium text-gray-900 dark:text-white"},Ds={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Vs={class:"space-y-3"},Is=["onClick"],Es={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Bs={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Us={class:"flex items-center justify-between"},Rs={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Hs={class:"mt-3"},Os={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Fs={class:"grid grid-cols-1 gap-4"},Ls={class:"grid grid-cols-2 gap-4"},qs=["value"],Ks={class:"grid grid-cols-2 gap-4"},Ns={class:"flex justify-end space-x-3 mt-6"},Xs=["disabled"],Js={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(k,{expose:A}){const $=k,S=ne(),{hasPermission:j}=_e(),y=P([]),f=P(!1),b=P(""),h=P("list"),n=P(!1),T=P({status:"",priority:"",assignee_id:"",search:""}),O=P(!1),I=P(!1),D=P(null),w=P({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),q=B(()=>j.value("manage_project_tasks")),C=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],g=async()=>{var c,i;if((c=$.project)!=null&&c.id){f.value=!0,b.value="";try{const G=new URLSearchParams({project_id:$.project.id,...T.value}),re=await fetch(`/api/tasks?${G}`,{headers:{"Content-Type":"application/json","X-CSRFToken":S.csrfToken}});if(!re.ok)throw new Error("Errore nel caricamento dei task");const _=await re.json();y.value=((i=_.data)==null?void 0:i.tasks)||_.tasks||[]}catch(G){b.value=G.message}finally{f.value=!1}}},V=async()=>{n.value=!0;try{const c=I.value?`/api/tasks/${D.value.id}`:"/api/tasks",i=I.value?"PUT":"POST",G={...w.value,project_id:$.project.id};if(!(await fetch(c,{method:i,headers:{"Content-Type":"application/json","X-CSRFToken":S.csrfToken},body:JSON.stringify(G)})).ok)throw new Error("Errore nel salvataggio del task");await g(),M()}catch(c){b.value=c.message}finally{n.value=!1}},K=c=>{D.value=c,w.value={name:c.name,description:c.description||"",status:c.status,priority:c.priority,assignee_id:c.assignee_id||"",due_date:c.due_date?c.due_date.split("T")[0]:"",estimated_hours:c.estimated_hours},I.value=!0},M=()=>{O.value=!1,I.value=!1,D.value=null,w.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},p=c=>y.value.filter(i=>i.status===c),m=c=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[c]||"bg-gray-100 text-gray-800",R=c=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[c]||c,x=c=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[c]||"bg-gray-100 text-gray-800",a=c=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[c]||c,u=(c,i)=>`${(c==null?void 0:c.charAt(0))||""}${(i==null?void 0:i.charAt(0))||""}`.toUpperCase(),l=c=>new Date(c).toLocaleDateString("it-IT");let o;const U=()=>{clearTimeout(o),o=setTimeout(()=>{g()},300)};return Z(()=>{var c;return(c=$.project)==null?void 0:c.id},c=>{c&&g()}),se(()=>{var c;(c=$.project)!=null&&c.id&&g()}),A({refresh:g}),(c,i)=>{var G,re;return t(),s("div",Wt,[e("div",Gt,[e("div",Yt,[e("div",Qt,[i[16]||(i[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),q.value?(t(),s("button",{key:0,onClick:i[0]||(i[0]=_=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Nuovo Task ")]))):z("",!0)]),e("div",Zt,[e("div",null,[i[18]||(i[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":i[1]||(i[1]=_=>T.value.status=_),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[17]||(i[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[W,T.value.status]])]),e("div",null,[i[20]||(i[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=_=>T.value.priority=_),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[19]||(i[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[W,T.value.priority]])]),e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":i[3]||(i[3]=_=>T.value.assignee_id=_),onChange:g,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(F,null,L(((G=k.project)==null?void 0:G.team_members)||[],_=>(t(),s("option",{key:_.id,value:_.id},r(_.first_name)+" "+r(_.last_name),9,es))),128))],544),[[W,T.value.assignee_id]])]),e("div",null,[i[23]||(i[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":i[4]||(i[4]=_=>T.value.search=_),onInput:U,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[X,T.value.search]])])]),e("div",ts,[e("div",ss,[i[24]||(i[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:i[5]||(i[5]=_=>h.value="list"),class:H([h.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:i[6]||(i[6]=_=>h.value="kanban"),class:H([h.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",rs,r(y.value.length)+" task trovati ",1)])])]),f.value?(t(),s("div",os,i[25]||(i[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):z("",!0),b.value?(t(),s("div",as,[e("p",ns,r(b.value),1)])):z("",!0),!f.value&&h.value==="list"?(t(),s("div",ls,[e("div",is,[i[27]||(i[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(F,null,L(y.value,_=>(t(),s("div",{key:_.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ds,[e("div",us,r(_.name),1),_.description?(t(),s("div",cs,r(_.description),1)):z("",!0)]),e("div",gs,[_.assignee?(t(),s("div",ms,[e("div",ps,r(u(_.assignee.first_name,_.assignee.last_name)),1),e("div",vs,[e("div",ys,r(_.assignee.first_name)+" "+r(_.assignee.last_name),1)])])):(t(),s("span",xs,"Non assegnato"))]),e("div",fs,[e("span",{class:H([m(_.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(R(_.status)),3)]),e("div",bs,[e("span",{class:H([x(_.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(a(_.priority)),3)]),e("div",hs,[_.due_date?(t(),s("div",ks,r(l(_.due_date)),1)):(t(),s("span",ws,"-"))]),e("div",_s,[e("div",$s,[J(r(_.actual_hours||0)+"h ",1),_.estimated_hours?(t(),s("span",js,"/ "+r(_.estimated_hours)+"h",1)):z("",!0)])]),e("div",Cs,[e("div",Ms,[e("button",{onClick:d=>K(_),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Ts)])])]))),128)),y.value.length===0?(t(),s("div",Ss,i[26]||(i[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):z("",!0)])])):z("",!0),!f.value&&h.value==="kanban"?(t(),s("div",Ps,[(t(),s(F,null,L(C,_=>e("div",{key:_.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",As,[e("h4",zs,r(_.label),1),e("span",Ds,r(p(_.value).length),1)]),e("div",Vs,[(t(!0),s(F,null,L(p(_.value),d=>(t(),s("div",{key:d.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:v=>K(d)},[e("div",Es,r(d.name),1),d.description?(t(),s("div",Bs,r(d.description),1)):z("",!0),e("div",Us,[e("span",{class:H([x(d.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},r(a(d.priority)),3),d.assignee?(t(),s("div",Rs,r(u(d.assignee.first_name,d.assignee.last_name)),1)):z("",!0)])],8,Is))),128))])])),64))])):z("",!0),O.value||I.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:M},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[14]||(i[14]=te(()=>{},["stop"]))},[e("div",Hs,[e("h3",Os,r(I.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:te(V,["prevent"])},[e("div",Fs,[e("div",null,[i[28]||(i[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":i[7]||(i[7]=_=>w.value.name=_),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.name]])]),e("div",null,[i[29]||(i[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":i[8]||(i[8]=_=>w.value.description=_),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.description]])]),e("div",Ls,[e("div",null,[i[31]||(i[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":i[9]||(i[9]=_=>w.value.status=_),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[30]||(i[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[W,w.value.status]])]),e("div",null,[i[33]||(i[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":i[10]||(i[10]=_=>w.value.priority=_),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[32]||(i[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[W,w.value.priority]])])]),e("div",null,[i[35]||(i[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":i[11]||(i[11]=_=>w.value.assignee_id=_),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[34]||(i[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(F,null,L(((re=k.project)==null?void 0:re.team_members)||[],_=>(t(),s("option",{key:_.id,value:_.id},r(_.first_name)+" "+r(_.last_name),9,qs))),128))],512),[[W,w.value.assignee_id]])]),e("div",Ks,[e("div",null,[i[36]||(i[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":i[12]||(i[12]=_=>w.value.due_date=_),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.due_date]])]),e("div",null,[i[37]||(i[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":i[13]||(i[13]=_=>w.value.estimated_hours=_),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.estimated_hours]])])])]),e("div",Ns,[e("button",{type:"button",onClick:M,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},r(n.value?"Salvataggio...":I.value?"Aggiorna":"Crea"),9,Xs)])],32)])])])):z("",!0)])}}},Ws={class:"space-y-6"},Gs={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ys={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qs={class:"flex items-center justify-between"},Zs={class:"p-6 border-b border-gray-200 dark:border-gray-700"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},rr={class:"text-center"},or={class:"text-2xl font-bold text-green-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-blue-600"},lr={class:"text-center"},ir={class:"text-2xl font-bold text-purple-600"},dr={class:"p-6"},ur={class:"space-y-4"},cr={class:"flex items-center justify-between"},gr={class:"flex items-center space-x-4"},mr={class:"flex-shrink-0"},pr=["src","alt"],vr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},yr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},xr={class:"flex-1"},fr={class:"flex items-center space-x-2"},br={class:"text-lg font-medium text-gray-900 dark:text-white"},hr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},kr={class:"text-sm text-gray-600 dark:text-gray-400"},wr={class:"text-xs text-gray-500 dark:text-gray-500"},_r={class:"flex items-center space-x-4"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"text-right"},Sr={class:"text-sm font-medium text-gray-900 dark:text-white"},Pr={class:"flex items-center space-x-2"},Ar=["onClick"],zr=["onClick"],Dr={class:"mt-4"},Vr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Ir={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Er={key:0,class:"text-center py-8"},Br={class:"mt-6"},Ur={class:"mt-3"},Rr={class:"space-y-4"},Hr=["value"],Or={class:"flex justify-end space-x-3 mt-6"},Fr=["disabled"],Lr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(k,{expose:A,emit:$}){const S=k,j=ne(),y=P(!1),f=P([]),b=P(!1),h=P({user_id:"",role:""}),n=B(()=>{var a;return((a=S.project)==null?void 0:a.team_members)||[]}),T=B(()=>n.value.reduce((a,u)=>a+(u.hours_worked||0),0)),O=B(()=>n.value.length===0?0:Math.round(T.value/n.value.length)),I=B(()=>n.value.filter(a=>(a.hours_worked||0)>0).length),D=a=>a?a.split(" ").map(u=>u.charAt(0).toUpperCase()).slice(0,2).join(""):"??",w=a=>{var l;return(((l=S.project)==null?void 0:l.tasks)||[]).filter(o=>o.assignee_id===a).length},q=a=>{var l;return(((l=S.project)==null?void 0:l.tasks)||[]).filter(o=>o.assignee_id===a&&o.status==="done").length},C=a=>{const u=w(a),l=q(a);return u===0?0:Math.round(l/u*100)},g=a=>{const u=C(a);return u>=80?"bg-green-600":u>=60?"bg-yellow-600":u>=40?"bg-orange-600":"bg-red-600"},V=a=>!a||a===0?"0.00":parseFloat(a).toFixed(2),K=async()=>{var a;try{const u=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(u.ok){const l=await u.json(),o=n.value.map(U=>U.id);f.value=(a=l.data)!=null&&a.users?l.data.users.filter(U=>!o.includes(U.id)):[]}}catch(u){console.error("Errore nel caricamento utenti:",u),f.value=[]}},M=async()=>{b.value=!0;try{const a=await fetch(`/api/projects/${S.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken},body:JSON.stringify(h.value)});if(a.ok)x("refresh"),R();else{const u=await a.json();alert(u.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{b.value=!1}},p=a=>{console.log("Edit member:",a)},m=async a=>{if(confirm(`Rimuovere ${a.full_name} dal progetto?`))try{const u=await fetch(`/api/projects/${S.project.id}/team/${a.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(u.ok)x("refresh");else{const l=await u.json();alert(l.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},R=()=>{y.value=!1,h.value={user_id:"",role:""}},x=$;return se(()=>{K()}),Z(()=>y.value,a=>{a&&K()}),Z(()=>{var a;return(a=S.project)==null?void 0:a.team_members},()=>{y.value&&K()}),A({refresh:K}),(a,u)=>(t(),s("div",Ws,[e("div",Gs,[e("div",Ys,[e("div",Qs,[u[6]||(u[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:u[0]||(u[0]=l=>y.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[5]||(u[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Membro ")]))])]),e("div",Zs,[e("div",er,[e("div",tr,[e("div",sr,r(n.value.length),1),u[7]||(u[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",rr,[e("div",or,r(T.value)+"h",1),u[8]||(u[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",ar,[e("div",nr,r(O.value)+"h",1),u[9]||(u[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",lr,[e("div",ir,r(I.value),1),u[10]||(u[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",dr,[e("div",ur,[(t(!0),s(F,null,L(n.value,l=>{var o,U;return t(),s("div",{key:l.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",cr,[e("div",gr,[e("div",mr,[l.profile_image?(t(),s("img",{key:0,src:l.profile_image,alt:l.full_name,class:"w-12 h-12 rounded-full"},null,8,pr)):(t(),s("div",vr,[e("span",yr,r(D(l.full_name)),1)]))]),e("div",xr,[e("div",fr,[e("h4",br,r(l.full_name),1),l.id===((o=k.project)==null?void 0:o.manager_id)?(t(),s("span",hr," Project Manager ")):z("",!0)]),e("p",kr,r(l.role||"Team Member"),1),e("p",wr,r(l.email),1)])]),e("div",_r,[e("div",$r,[e("div",jr,r(V(l.hours_worked||0))+"h",1),u[11]||(u[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Cr,[e("div",Mr,r(w(l.id)),1),u[12]||(u[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Tr,[e("div",Sr,r(q(l.id)),1),u[13]||(u[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Pr,[e("button",{onClick:c=>p(l),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},u[14]||(u[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Ar),l.id!==((U=k.project)==null?void 0:U.manager_id)?(t(),s("button",{key:0,onClick:c=>m(l),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},u[15]||(u[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,zr)):z("",!0)])])]),e("div",Dr,[e("div",Vr,[u[16]||(u[16]=e("span",null,"Produttività",-1)),e("span",null,r(C(l.id))+"%",1)]),e("div",Ir,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",g(l.id)]),style:ee({width:C(l.id)+"%"})},null,6)])])])}),128)),n.value.length===0?(t(),s("div",Er,[u[18]||(u[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),u[19]||(u[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),u[20]||(u[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Br,[e("button",{onClick:u[1]||(u[1]=l=>y.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},u[17]||(u[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi primo membro ")]))])])):z("",!0)])])]),y.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:R},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:u[4]||(u[4]=te(()=>{},["stop"]))},[e("div",Ur,[u[25]||(u[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:te(M,["prevent"])},[e("div",Rr,[e("div",null,[u[22]||(u[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":u[2]||(u[2]=l=>h.value.user_id=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[u[21]||(u[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(F,null,L(f.value,l=>(t(),s("option",{key:l.id,value:l.id},r(l.full_name)+" ("+r(l.email)+") ",9,Hr))),128))],512),[[W,h.value.user_id]])]),e("div",null,[u[24]||(u[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":u[3]||(u[3]=l=>h.value.role=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},u[23]||(u[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[W,h.value.role]])])]),e("div",Or,[e("button",{type:"button",onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:b.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},r(b.value?"Aggiungendo...":"Aggiungi"),9,Fr)])],32)])])])):z("",!0)]))}};function qr(k,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(k,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function Kr(k,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Nr={class:"fixed inset-0 z-50 overflow-y-auto"},Xr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Jr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Wr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Gr={class:"mb-4"},Yr={class:"text-lg font-medium text-gray-900 dark:text-white"},Qr={class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},eo=["disabled"],to={key:0},so={key:1},ro={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(k,{emit:A}){const $=k,S=A,j=P(!1),y=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),f=async()=>{j.value=!0;try{const h=$.expense?`/api/expenses/${$.expense.id}`:`/api/projects/${$.projectId}/expenses`,n=$.expense?"PUT":"POST";(await fetch(h,{method:n,headers:{"Content-Type":"application/json"},body:JSON.stringify(y)})).ok?S("saved"):console.error("Error saving expense")}catch(h){console.error("Error saving expense:",h)}finally{j.value=!1}},b=h=>{const n=h.target.files[0];if(n){if(n.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),h.target.value="";return}y.receipt_file=n}};return se(()=>{$.expense&&Object.assign(y,{description:$.expense.description,amount:$.expense.amount,category:$.expense.category,billing_type:$.expense.billing_type||"billable",status:$.expense.status||"pending",date:$.expense.date.split("T")[0],notes:$.expense.notes||""})}),(h,n)=>(t(),s("div",Nr,[e("div",Xr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:n[0]||(n[0]=T=>h.$emit("close"))}),e("div",Jr,[e("form",{onSubmit:te(f,["prevent"])},[e("div",Wr,[e("div",Gr,[e("h3",Yr,r(k.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Qr,[e("div",null,[n[9]||(n[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":n[1]||(n[1]=T=>y.description=T),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[X,y.description]])]),e("div",null,[n[10]||(n[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":n[2]||(n[2]=T=>y.amount=T),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[X,y.amount,void 0,{number:!0}]])]),e("div",null,[n[12]||(n[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":n[3]||(n[3]=T=>y.category=T),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[11]||(n[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[W,y.category]])]),e("div",null,[n[14]||(n[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":n[4]||(n[4]=T=>y.billing_type=T),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[13]||(n[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[W,y.billing_type]])]),e("div",null,[n[16]||(n[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":n[5]||(n[5]=T=>y.status=T),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[15]||(n[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[W,y.status]])]),e("div",null,[n[17]||(n[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":n[6]||(n[6]=T=>y.date=T),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,y.date]])]),e("div",null,[n[18]||(n[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":n[7]||(n[7]=T=>y.notes=T),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[X,y.notes]])]),e("div",null,[n[19]||(n[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:b,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),n[20]||(n[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",Zr,[e("button",{type:"submit",disabled:j.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[j.value?(t(),s("span",to,"Salvando...")):(t(),s("span",so,r(k.expense?"Aggiorna":"Salva"),1))],8,eo),e("button",{type:"button",onClick:n[8]||(n[8]=T=>h.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},oo={class:"project-expenses"},ao={class:"space-y-6"},no={class:"flex justify-between items-center"},lo={key:0,class:"text-center py-8"},io={key:1,class:"text-center py-12"},uo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},co={class:"divide-y divide-gray-200 dark:divide-gray-700"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},po={class:"flex items-center"},vo={class:"flex-shrink-0"},yo={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},xo={class:"ml-4 flex-1"},fo={class:"flex items-center justify-between"},bo={class:"text-sm font-medium text-gray-900 dark:text-white"},ho={class:"ml-2 flex-shrink-0"},ko={class:"text-sm font-medium text-gray-900 dark:text-white"},wo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},_o={class:"capitalize"},$o={key:0,class:"mx-2"},jo={key:1},Co={class:"mt-2 flex items-center space-x-4 text-xs"},Mo={key:0,class:"inline-flex items-center text-green-600 dark:text-green-400"},To={key:0,class:"flex items-center space-x-2"},So=["onClick"],Po=["onClick"],Ao={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},zo={class:"flex justify-between items-center"},Do={class:"text-lg font-bold text-gray-900 dark:text-white"},Vo={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(k){const A=k;ye();const $=ne(),S=P(!1),j=P([]),y=P(!1),f=P(null),b=B(()=>$.hasPermission("manage_expenses")),h=B(()=>j.value.reduce((p,m)=>p+m.amount,0)),n=async()=>{var p;if((p=A.project)!=null&&p.id){S.value=!0;try{const m=await fetch(`/api/projects/${A.project.id}/expenses`);m.ok&&(j.value=await m.json())}catch(m){console.error("Error loading expenses:",m)}finally{S.value=!1}}},T=p=>{f.value=p,y.value=!0},O=async p=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${p}`,{method:"DELETE"})).ok&&(j.value=j.value.filter(R=>R.id!==p))}catch(m){console.error("Error deleting expense:",m)}},I=()=>{y.value=!1,f.value=null},D=()=>{I(),n()},w=p=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(p),q=p=>new Date(p).toLocaleDateString("it-IT"),C=p=>({licenses:"📄 Licenze",travel:"✈️ Viaggi",meals:"🍽️ Pasti",equipment:"🖥️ Attrezzature",external:"🏢 Servizi Esterni",other:"📦 Altro"})[p]||p,g=p=>({billable:"💰 Fatturabile","non-billable":"🏢 Non Fatturabile",reimbursable:"💳 Rimborsabile"})[p]||p,V=p=>({billable:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400","non-billable":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",reimbursable:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"})[p]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",K=p=>({pending:"⏳ In Attesa",approved:"✅ Approvata",rejected:"❌ Rifiutata"})[p]||p,M=p=>({pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",approved:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[p]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";return Z(()=>{var p;return(p=A.project)==null?void 0:p.id},(p,m)=>{p&&p!==m&&n()},{immediate:!0}),se(()=>{n()}),(p,m)=>{var R;return t(),s("div",oo,[e("div",ao,[e("div",no,[m[2]||(m[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),b.value?(t(),s("button",{key:0,onClick:m[0]||(m[0]=x=>y.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(ie(Kr),{class:"w-4 h-4 mr-2"}),m[1]||(m[1]=J(" Aggiungi Spesa "))])):z("",!0)]),S.value?(t(),s("div",lo,m[3]||(m[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):j.value.length===0?(t(),s("div",io,[oe(ie(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),m[4]||(m[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),m[5]||(m[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",uo,[e("ul",co,[(t(!0),s(F,null,L(j.value,x=>(t(),s("li",{key:x.id,class:"px-6 py-4"},[e("div",go,[e("div",mo,[e("div",po,[e("div",vo,[e("div",yo,[oe(ie(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",xo,[e("div",fo,[e("p",bo,r(x.description),1),e("div",ho,[e("p",ko," €"+r(w(x.amount)),1)])]),e("div",wo,[oe(ie(qr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),J(" "+r(q(x.date))+" ",1),m[6]||(m[6]=e("span",{class:"mx-2"},"•",-1)),e("span",_o,r(C(x.category)),1),x.user?(t(),s("span",$o,"•")):z("",!0),x.user?(t(),s("span",jo,r(x.user.name),1)):z("",!0)]),e("div",Co,[e("span",{class:H([V(x.billing_type),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(g(x.billing_type)),3),e("span",{class:H([M(x.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(K(x.status)),3),x.receipt_path?(t(),s("span",Mo,m[7]||(m[7]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),J(" Ricevuta ")]))):z("",!0)])])])]),b.value?(t(),s("div",To,[e("button",{onClick:a=>T(x),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,So),e("button",{onClick:a=>O(x.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Po)])):z("",!0)])]))),128))])])),j.value.length>0?(t(),s("div",Ao,[e("div",zo,[m[8]||(m[8]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Do,"€"+r(w(h.value)),1)])])):z("",!0)]),y.value?(t(),de(ro,{key:0,"project-id":(R=k.project)==null?void 0:R.id,expense:f.value,onClose:I,onSaved:D},null,8,["project-id","expense"])):z("",!0)])}}},Io={class:"project-kpi"},Eo={key:0,class:"animate-pulse space-y-4"},Bo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Uo={key:1,class:"space-y-6"},Ro={class:"bg-white shadow rounded-lg p-6"},Ho={class:"flex items-center justify-between"},Oo=["disabled"],Fo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Lo={class:"bg-white shadow rounded-lg p-6"},qo={class:"flex items-center"},Ko={class:"ml-5 w-0 flex-1"},No={class:"text-lg font-medium text-gray-900"},Xo={class:"text-xs text-gray-500"},Jo={class:"bg-white shadow rounded-lg p-6"},Wo={class:"flex items-center"},Go={class:"ml-5 w-0 flex-1"},Yo={class:"text-lg font-medium text-gray-900"},Qo={class:"bg-white shadow rounded-lg p-6"},Zo={class:"flex items-center"},ea={class:"ml-5 w-0 flex-1"},ta={class:"text-lg font-medium text-gray-900"},sa={class:"text-xs text-gray-500"},ra={class:"bg-white shadow rounded-lg p-6"},oa={class:"flex items-center"},aa={class:"ml-5 w-0 flex-1"},na={class:"text-lg font-medium text-gray-900"},la={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ia={class:"bg-white shadow rounded-lg p-6"},da={class:"space-y-4"},ua={class:"flex justify-between text-sm"},ca={class:"font-medium"},ga={class:"w-full bg-gray-200 rounded-full h-3"},ma={class:"flex justify-between text-sm"},pa={class:"text-gray-600"},va={class:"font-medium"},ya={class:"bg-white shadow rounded-lg p-6"},xa={class:"space-y-4"},fa={class:"flex justify-between text-sm"},ba={class:"font-medium"},ha={class:"w-full bg-gray-200 rounded-full h-3"},ka={class:"flex justify-between text-sm"},wa={class:"text-gray-600"},_a={class:"font-medium"},$a={class:"bg-white shadow rounded-lg p-6"},ja={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={class:"text-center p-4 border rounded-lg"},Sa={class:"text-xs text-gray-500"},Pa={class:"text-center p-4 border rounded-lg"},Aa={class:"text-xs text-gray-500"},za={key:2,class:"text-center py-8"},Da={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Va={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Ia={class:"mt-3"},Ea={class:"mt-6 space-y-6"},Ba={class:"bg-gray-50 p-4 rounded-lg"},Ua={class:"font-medium text-gray-900"},Ra={class:"text-sm text-gray-600"},Ha={class:"space-y-6"},Oa={class:"flex items-center justify-between mb-4"},Fa={class:"font-medium text-gray-900"},La={class:"text-sm text-gray-600"},qa={class:"flex items-center space-x-2"},Ka={class:"text-xs text-gray-500"},Na=["onClick"],Xa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ja=["onUpdate:modelValue","onInput"],Wa=["onUpdate:modelValue","onInput"],Ga=["onUpdate:modelValue","onInput"],Ya={class:"mt-4"},Qa=["onUpdate:modelValue","onInput"],Za={class:"mt-4 flex justify-end"},en=["onClick","disabled"],tn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},sn={key:1,class:"text-sm text-green-600"},rn={class:"mt-6 pt-4 border-t flex justify-between"},on={class:"flex space-x-3"},an=["disabled"],nn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(k,{emit:A}){const $=k,S=A,j=P(!1),y=P(!1),f=P(null),b=P({}),h=P({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),n=P({budget:80,time:85,margin:15}),T=B(()=>{var d;return!((d=$.project)!=null&&d.budget)||h.value.totalCosts===0?0:Math.round(h.value.totalCosts/$.project.budget*100)}),O=B(()=>{var d;return!((d=$.project)!=null&&d.estimated_hours)||h.value.totalHours===0?0:Math.round(h.value.totalHours/$.project.estimated_hours*100)}),I=B(()=>{const d=h.value.costVariance;return d>0?"text-red-600":d<0?"text-green-600":"text-gray-600"}),D=B(()=>{const d=h.value.marginPercentage;return d>=n.value.margin?"text-green-600":d>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),w=B(()=>{const d=h.value.marginPercentage;return d>=n.value.margin?"Ottimo":d>=n.value.margin*.7?"Accettabile":"Critico"}),q=B(()=>{const d=T.value;return d>=n.value.budget?"text-red-600":d>=n.value.budget*.8?"text-yellow-600":"text-green-600"}),C=B(()=>{const d=O.value;return d>=n.value.time?"text-red-600":d>=n.value.time*.8?"text-yellow-600":"text-green-600"}),g=B(()=>{const d=h.value.marginPercentage;return d>=n.value.margin?"text-green-600":d>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),V=d=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(d||0),K=d=>!d||d===0?"0h":`${parseFloat(d).toFixed(2)}h`,M=d=>`${(d||0).toFixed(1)}%`,p=async()=>{var d;(d=$.project)!=null&&d.id&&m()},m=()=>{const d=$.project;d&&(h.value={totalHours:d.total_hours||0,workDays:Math.ceil((d.total_hours||0)/8),totalCosts:(d.total_hours||0)*50,costVariance:(d.total_hours||0)*50-(d.budget||0),potentialRevenue:d.budget||0,actualRevenue:d.invoiced_amount||0,marginPercentage:d.budget?(d.budget-(d.total_hours||0)*50)/d.budget*100:0})},R=async()=>{j.value=!0;try{await p(),S("refresh")}catch(d){console.error("Error refreshing KPIs:",d)}finally{j.value=!1}},x=B(()=>{var v;const d=((v=$.project)==null?void 0:v.project_type)||"service";return a(d)}),a=d=>{const v={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return v[d]||v.service},u=d=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[d]||"Sconosciuto",l=()=>{x.value.forEach(v=>{b.value[v.name]||(b.value[v.name]={target_min:v.target_min,target_max:v.target_max,warning_threshold:v.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),y.value=!0},o=()=>{y.value=!1},U=d=>{b.value[d]&&(b.value[d].isDirty=!0,b.value[d].isSaved=!1)},c=d=>{const v=x.value.find(Y=>Y.name===d);v&&b.value[d]&&(b.value[d].target_min=v.target_min,b.value[d].target_max=v.target_max,b.value[d].warning_threshold=v.warning_threshold,b.value[d].custom_description="",b.value[d].isDirty=!0,b.value[d].isSaved=!1)},i=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&x.value.forEach(d=>{c(d.name)})},G=async d=>{var v;if(b.value[d]){f.value=d;try{const Y=b.value[d];await new Promise(le=>setTimeout(le,1e3)),console.log("Saving KPI config:",{project_id:(v=$.project)==null?void 0:v.id,kpi_name:d,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),b.value[d].isDirty=!1,b.value[d].isSaved=!0,setTimeout(()=>{b.value[d]&&(b.value[d].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{f.value=null}}},re=async()=>{const d=x.value.filter(v=>{var Y;return(Y=b.value[v.name])==null?void 0:Y.isDirty});for(const v of d)await G(v.name)},_=B(()=>x.value.some(d=>{var v;return(v=b.value[d.name])==null?void 0:v.isDirty}));return Z(()=>$.project,d=>{d&&p()},{immediate:!0}),se(()=>{$.project&&p()}),(d,v)=>{var Y,le;return t(),s("div",Io,[k.loading?(t(),s("div",Eo,[e("div",Bo,[(t(),s(F,null,L(4,N=>e("div",{key:N,class:"bg-gray-200 rounded-lg h-24"})),64))]),v[0]||(v[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):k.project?(t(),s("div",Uo,[e("div",Ro,[e("div",Ho,[v[3]||(v[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:R,disabled:j.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":j.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},v[1]||(v[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),v[2]||(v[2]=J(" Aggiorna "))],8,Oo)])]),e("div",Fo,[e("div",Lo,[e("div",qo,[v[5]||(v[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Ko,[e("dl",null,[v[4]||(v[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",No,r(K(h.value.totalHours)),1),e("dd",Xo,r(h.value.workDays)+" giorni lavorati",1)])])])]),e("div",Jo,[e("div",Wo,[v[7]||(v[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Go,[e("dl",null,[v[6]||(v[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Yo,r(V(h.value.totalCosts)),1),e("dd",{class:H(["text-xs",I.value])},r(V(h.value.costVariance))+" vs budget",3)])])])]),e("div",Qo,[e("div",Zo,[v[9]||(v[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ea,[e("dl",null,[v[8]||(v[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ta,r(V(h.value.potentialRevenue)),1),e("dd",sa,r(V(h.value.actualRevenue))+" fatturati",1)])])])]),e("div",ra,[e("div",oa,[v[11]||(v[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",aa,[e("dl",null,[v[10]||(v[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",na,r(M(h.value.marginPercentage)),1),e("dd",{class:H(["text-xs",D.value])},r(w.value),3)])])])])]),e("div",la,[e("div",ia,[v[13]||(v[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",da,[e("div",ua,[v[12]||(v[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ca,r(V(k.project.budget||0)),1)]),e("div",ga,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:T.value+"%"})},null,4)]),e("div",ma,[e("span",pa,"Utilizzato: "+r(V(h.value.totalCosts)),1),e("span",va,r(T.value)+"%",1)])])]),e("div",ya,[v[15]||(v[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",xa,[e("div",fa,[v[14]||(v[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",ba,r(K(k.project.estimated_hours||0)),1)]),e("div",ha,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:O.value+"%"})},null,4)]),e("div",ka,[e("span",wa,"Lavorate: "+r(K(h.value.totalHours)),1),e("span",_a,r(O.value)+"%",1)])])])]),e("div",$a,[e("div",{class:"flex items-center justify-between mb-4"},[v[17]||(v[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:l,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},v[16]||(v[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),J(" Configura KPI ")]))]),e("div",ja,[e("div",Ca,[e("div",{class:H(["text-2xl font-bold",q.value])},r(T.value)+"% ",3),v[18]||(v[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ma,"Soglia: "+r(n.value.budget)+"%",1)]),e("div",Ta,[e("div",{class:H(["text-2xl font-bold",C.value])},r(O.value)+"% ",3),v[19]||(v[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Sa,"Soglia: "+r(n.value.time)+"%",1)]),e("div",Pa,[e("div",{class:H(["text-2xl font-bold",g.value])},r(M(h.value.marginPercentage)),3),v[20]||(v[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Aa,"Soglia: "+r(n.value.margin)+"%",1)])])])])):(t(),s("div",za,v[21]||(v[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),y.value?(t(),s("div",Da,[e("div",Va,[e("div",Ia,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[v[23]||(v[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:o,class:"text-gray-400 hover:text-gray-600"},v[22]||(v[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ea,[e("div",Ba,[e("h4",Ua,r((Y=k.project)==null?void 0:Y.name),1),e("p",Ra,"Tipo: "+r(u((le=k.project)==null?void 0:le.project_type)),1)]),e("div",Ha,[(t(!0),s(F,null,L(x.value,N=>{var ce,ge;return t(),s("div",{key:N.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Oa,[e("div",null,[e("h5",Fa,r(N.display_name),1),e("p",La,r(N.description),1)]),e("div",qa,[e("span",Ka,r(N.unit),1),e("button",{onClick:Q=>c(N.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Na)])]),e("div",Xa,[e("div",null,[v[24]||(v[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>b.value[N.name].target_min=Q,onInput:Q=>U(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ja),[[X,b.value[N.name].target_min]])]),e("div",null,[v[25]||(v[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>b.value[N.name].target_max=Q,onInput:Q=>U(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Wa),[[X,b.value[N.name].target_max]])]),e("div",null,[v[26]||(v[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>b.value[N.name].warning_threshold=Q,onInput:Q=>U(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ga),[[X,b.value[N.name].warning_threshold]])])]),e("div",Ya,[v[27]||(v[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Q=>b.value[N.name].custom_description=Q,onInput:Q=>U(N.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Qa),[[X,b.value[N.name].custom_description]])]),e("div",Za,[(ce=b.value[N.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:Q=>G(N.name),disabled:f.value===N.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[f.value===N.name?(t(),s("svg",tn,v[28]||(v[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):z("",!0),J(" "+r(f.value===N.name?"Salvataggio...":"Salva KPI"),1)],8,en)):(ge=b.value[N.name])!=null&&ge.isSaved?(t(),s("span",sn,"✓ Salvato")):z("",!0)])])}),128))])]),e("div",rn,[e("button",{onClick:i,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",on,[e("button",{onClick:o,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:re,disabled:!_.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,an)])])])])])):z("",!0)])}}},ln={class:"space-y-6"},dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},un={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},cn={class:"flex items-center justify-between"},gn={class:"flex items-center space-x-4"},mn={class:"flex items-center space-x-2"},pn={key:0,class:"p-6"},vn={class:"overflow-x-auto"},yn={class:"min-w-[1000px]"},xn={class:"flex mb-4"},fn={class:"flex-1 flex"},bn={class:"space-y-1"},hn={class:"w-80 flex-shrink-0 px-4 py-3"},kn={class:"flex items-center space-x-2"},wn={class:"flex-1 min-w-0"},_n={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},$n={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},jn={key:0},Cn={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Mn={class:"flex-1 relative h-12 flex items-center"},Tn=["title"],Sn={class:"truncate"},Pn={class:"ml-2"},An={key:1,class:"text-center py-12"},zn={key:2,class:"flex justify-center py-12"},Dn={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(k,{expose:A}){const $=k,S=P("weeks"),j=P(new Date),y=P([]),f=P(0),b=B(()=>{var M;return((M=$.project)==null?void 0:M.tasks)||[]}),h=B(()=>b.value.filter(M=>M.start_date&&M.due_date).map(M=>{const p=T(M);return{...M,timeline:p}})),n=()=>{const M=new Date,p=new Date(j.value),m=[],R=12;for(let x=0;x<R;x++){const a=new Date(p);S.value==="weeks"?a.setDate(p.getDate()+x*7):S.value==="months"&&a.setMonth(p.getMonth()+x),m.push(a)}if(y.value=m,m.length>1){const x=m[0],a=new Date(m[m.length-1]);S.value==="weeks"?a.setDate(a.getDate()+7):S.value==="months"&&a.setMonth(a.getMonth()+1);const u=a-x,l=M-x;f.value=Math.max(0,Math.min(100,l/u*100))}else f.value=0},T=M=>{if(!y.value.length)return null;const p=new Date(M.start_date),m=new Date(M.due_date),R=y.value[0],a=y.value[y.value.length-1]-R,u=p-R,l=m-p,o=Math.max(0,u/a*100),U=Math.min(100-o,l/a*100);return{leftPercent:o,widthPercent:Math.max(5,U)}},O=M=>S.value==="weeks"?`${M.getDate()}/${M.getMonth()+1}`:S.value==="months"?M.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",I=M=>{const p=new Date,m=new Date(M);if(S.value==="weeks"){const R=new Date(m),x=new Date(m);return x.setDate(x.getDate()+6),p>=R&&p<=x}else if(S.value==="months")return m.getMonth()===p.getMonth()&&m.getFullYear()===p.getFullYear();return!1},D=()=>{const M=new Date;if(S.value==="weeks"){const p=new Date(M);p.setDate(M.getDate()-M.getDay()),j.value=p}else{const p=new Date(M.getFullYear(),M.getMonth(),1);j.value=p}n()},w=M=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[M]||"bg-gray-400",q=M=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[M]||"bg-gray-500",C=M=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[M]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",g=M=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[M]||"Non specificata",V=M=>({todo:0,"in-progress":50,review:75,done:100})[M.status]||0,K=M=>M?new Date(M).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>$.project,()=>{n()},{immediate:!0}),se(()=>{D()}),A({refresh:n}),(M,p)=>(t(),s("div",ln,[e("div",dn,[e("div",un,[e("div",cn,[p[3]||(p[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",gn,[e("div",mn,[p[2]||(p[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":p[0]||(p[0]=m=>S.value=m),onChange:n,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},p[1]||(p[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[W,S.value]])]),e("button",{onClick:D,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!k.loading&&h.value.length>0?(t(),s("div",pn,[e("div",vn,[e("div",yn,[e("div",xn,[p[4]||(p[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",fn,[(t(!0),s(F,null,L(y.value,(m,R)=>(t(),s("div",{key:R,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":I(m)}])},r(O(m)),3))),128))])]),e("div",bn,[(t(!0),s(F,null,L(h.value,m=>(t(),s("div",{key:m.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",hn,[e("div",kn,[e("div",{class:H(["w-3 h-3 rounded-full",w(m.status)])},null,2),e("div",wn,[e("p",_n,r(m.name),1),e("div",$n,[m.assignee?(t(),s("span",jn,r(m.assignee.full_name),1)):z("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",C(m.priority)])},r(g(m.priority)),3)]),e("div",Cn,r(K(m.start_date))+" - "+r(K(m.due_date)),1)])])]),e("div",Mn,[m.timeline?(t(),s("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",q(m.status)]),style:ee({left:m.timeline.leftPercent+"%",width:m.timeline.widthPercent+"%",minWidth:"60px"}),title:`${m.name} - ${V(m)}% completato`},[e("span",Sn,r(m.name.length>15?m.name.substring(0,15)+"...":m.name),1),e("span",Pn,r(V(m))+"%",1)],14,Tn)):z("",!0),m.timeline&&V(m)>0&&V(m)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:m.timeline.leftPercent+"%",width:m.timeline.widthPercent*V(m)/100+"%",minWidth:"2px"})},null,4)):z("",!0),(t(!0),s(F,null,L(y.value,(R,x)=>(t(),s("div",{key:x,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:x/y.value.length*100+"%"})},null,4))),128)),f.value>=0&&f.value<=100?(t(),s("div",{key:2,class:"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10",style:ee({left:f.value+"%"})},null,4)):z("",!0)])]))),128))])])]),p[5]||(p[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):k.loading?z("",!0):(t(),s("div",An,p[6]||(p[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),k.loading?(t(),s("div",zn,p[7]||(p[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):z("",!0)])]))}},Vn={class:"space-y-6"},In={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},En={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Bn={class:"flex items-center justify-between"},Un={class:"flex items-center space-x-4"},Rn={class:"flex items-center space-x-2"},Hn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},On={class:"flex items-center space-x-2"},Fn=["value"],Ln={key:0,class:"flex justify-center py-8"},qn={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},Kn={class:"text-red-600"},Nn={key:2,class:"p-6"},Xn={class:"overflow-x-auto"},Jn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Wn={class:"bg-gray-50 dark:bg-gray-700"},Gn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Yn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},el=["onClick"],tl={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},sl={key:1,class:"text-gray-300 dark:text-gray-600"},rl={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},ol={class:"text-sm font-medium text-gray-900 dark:text-white"},al={class:"bg-gray-100 dark:bg-gray-600 font-medium"},nl={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ll={key:0,class:"text-center py-8"},il={class:"mt-3"},dl={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ul={class:"grid grid-cols-1 gap-4"},cl=["value"],gl={class:"flex justify-end space-x-3 mt-6"},ml=["disabled"],pl={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(k,{expose:A}){const $=k,S=ne(),j=P(null),y=P(!1),f=P(""),b=P(!1),h=P(new Date().getFullYear()),n=P(new Date().getMonth()+1),T=P(""),O=P(!1),I=P(!1),D=P(null),w=P({task_id:"",date:"",hours:0,description:""}),q=B(()=>j.value?Array.from({length:j.value.days_in_month},(x,a)=>a+1):[]),C=async()=>{var x;if((x=$.project)!=null&&x.id){y.value=!0,f.value="";try{const a=new URLSearchParams({year:h.value.toString(),month:n.value.toString()});T.value&&a.append("member_id",T.value.toString());const u=await fetch(`/api/timesheets/project/${$.project.id}/monthly?${a}`,{headers:{"Content-Type":"application/json","X-CSRFToken":S.csrfToken}});if(!u.ok)throw new Error("Errore nel caricamento del timesheet");const l=await u.json();j.value=l.data}catch(a){f.value=a.message}finally{y.value=!1}}},g=async()=>{b.value=!0;try{const x={...w.value,project_id:$.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":S.csrfToken},body:JSON.stringify(x)})).ok)throw new Error("Errore nel salvataggio del timesheet");await C(),K()}catch(x){f.value=x.message}finally{b.value=!1}},V=(x,a)=>{const u=j.value.tasks.find(l=>l.id===x);u&&(D.value={taskId:x,day:a},w.value={task_id:x,date:`${h.value}-${String(n.value).padStart(2,"0")}-${String(a).padStart(2,"0")}`,hours:u.daily_hours[a]||0,description:""},u.daily_hours[a]>0?I.value=!0:O.value=!0)},K=()=>{O.value=!1,I.value=!1,D.value=null,w.value={task_id:"",date:"",hours:0,description:""}},M=()=>{n.value===1?(n.value=12,h.value--):n.value--,C()},p=()=>{n.value===12?(n.value=1,h.value++):n.value++,C()},m=x=>{const a=new Date;return a.getFullYear()===h.value&&a.getMonth()+1===n.value&&a.getDate()===x},R=x=>!x||x===0?"0":x%1===0?x.toString():x.toFixed(2);return Z(()=>{var x;return(x=$.project)==null?void 0:x.id},x=>{x&&C()}),Z(T,()=>{C()}),se(()=>{var x;(x=$.project)!=null&&x.id&&C()}),A({refresh:C}),(x,a)=>{var u,l;return t(),s("div",Vn,[e("div",In,[e("div",En,[e("div",Bn,[e("div",Un,[a[11]||(a[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Rn,[e("button",{onClick:M,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},a[7]||(a[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Hn,r(n.value)+"/"+r(h.value),1),e("button",{onClick:p,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},a[8]||(a[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",On,[a[10]||(a[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":a[0]||(a[0]=o=>T.value=o),onChange:C,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[a[9]||(a[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(F,null,L(((u=k.project)==null?void 0:u.team_members)||[],o=>(t(),s("option",{key:o.id,value:o.id},r(o.first_name)+" "+r(o.last_name),9,Fn))),128))],544),[[W,T.value]])])]),e("button",{onClick:a[1]||(a[1]=o=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},a[12]||(a[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Ore ")]))])]),y.value?(t(),s("div",Ln,a[13]||(a[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):z("",!0),f.value?(t(),s("div",qn,[e("p",Kn,r(f.value),1)])):z("",!0),!y.value&&j.value?(t(),s("div",Nn,[e("div",Xn,[e("table",Jn,[e("thead",Wn,[e("tr",null,[a[14]||(a[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(F,null,L(q.value,o=>(t(),s("th",{key:o,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":m(o)}])},r(o),3))),128)),a[15]||(a[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Gn,[(t(!0),s(F,null,L(j.value.tasks,o=>(t(),s("tr",{key:o.id},[e("td",Yn,[e("div",Qn,r(o.name),1),e("div",Zn,r(o.workers.length?o.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(F,null,L(q.value,U=>(t(),s("td",{key:U,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":m(U)}]),onClick:c=>V(o.id,U)},[o.daily_hours[U]>0?(t(),s("span",tl,r(R(o.daily_hours[U])),1)):(t(),s("span",sl,"-"))],10,el))),128)),e("td",rl,[e("span",ol,r(R(o.total_hours)),1)])]))),128)),e("tr",al,[a[16]||(a[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(F,null,L(q.value,o=>(t(),s("td",{key:o,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":m(o)}])},r(R(j.value.daily_totals[o]||0)),3))),128)),e("td",nl,r(R(j.value.grand_total)),1)])])])]),j.value.tasks.length===0?(t(),s("div",ll,a[17]||(a[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):z("",!0)])):z("",!0)]),O.value||I.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:K},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:a[6]||(a[6]=te(()=>{},["stop"]))},[e("div",il,[e("h3",dl,r(I.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:te(g,["prevent"])},[e("div",ul,[e("div",null,[a[19]||(a[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":a[2]||(a[2]=o=>w.value.task_id=o),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[a[18]||(a[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(F,null,L(((l=j.value)==null?void 0:l.tasks)||[],o=>(t(),s("option",{key:o.id,value:o.id},r(o.name),9,cl))),128))],512),[[W,w.value.task_id]])]),e("div",null,[a[20]||(a[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":a[3]||(a[3]=o=>w.value.date=o),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.date]])]),e("div",null,[a[21]||(a[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":a[4]||(a[4]=o=>w.value.hours=o),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.hours]])]),e("div",null,[a[22]||(a[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":a[5]||(a[5]=o=>w.value.description=o),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,w.value.description]])])]),e("div",gl,[e("button",{type:"button",onClick:K,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:b.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},r(b.value?"Salvataggio...":I.value?"Aggiorna":"Aggiungi"),9,ml)])],32)])])])):z("",!0)])}}},vl={class:"space-y-6"},yl={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},xl={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},fl={class:"flex items-center justify-between"},bl={class:"flex items-center space-x-3"},hl=["disabled"],kl={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},wl={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},_l={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},$l={class:"flex items-start space-x-3"},jl={class:"flex-1"},Cl={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},Ml={class:"mt-2 space-y-2"},Tl={key:0,class:"mt-3"},Sl={class:"space-y-2"},Pl={class:"flex items-center space-x-3"},Al={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},zl={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},Dl={class:"text-sm font-medium text-gray-900 dark:text-white"},Vl={class:"text-xs text-gray-500 dark:text-gray-400"},Il=["onClick"],El={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Bl={key:0,class:"p-6"},Ul={class:"animate-pulse space-y-4"},Rl={key:1,class:"p-6 text-center"},Hl={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Ol={class:"flex items-center justify-between"},Fl={class:"flex items-center space-x-4"},Ll={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},ql={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},Kl={class:"text-sm font-medium text-gray-900 dark:text-white"},Nl={class:"text-sm text-gray-500 dark:text-gray-400"},Xl={class:"flex items-center space-x-4"},Jl={class:"text-right"},Wl={class:"text-sm font-medium text-gray-900 dark:text-white"},Gl={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Yl={class:"flex items-center space-x-2"},Ql=["onClick"],Zl=["onClick"],ei={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ti={class:"p-6"},si={class:"space-y-4"},ri={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},oi={class:"flex-1 mx-4"},ai={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},ni={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},li={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},ii={class:"mt-3"},di={class:"space-y-4"},ui=["value"],ci={class:"flex justify-end space-x-3 mt-6"},gi=["disabled"],mi={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},pi={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},vi={class:"mt-3"},yi={class:"space-y-4"},xi={class:"flex justify-end space-x-3 mt-6"},fi=["disabled"],bi={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(k){const A=k,$=ne(),S=P(!0),j=P(!1),y=P(!1),f=P([]),b=P([]),h=P([]),n=P(null),T=P(!1),O=P(!1),I=P({user_id:"",role:"",allocation_percentage:100}),D=P({id:null,role:"",allocation_percentage:100}),w=B(()=>{var l;return(l=A.project)==null?void 0:l.id}),q=async()=>{var l;if(w.value){S.value=!0;try{const o=await fetch(`/api/resources?project_id=${w.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(!o.ok)throw new Error("Errore nel caricamento allocazioni");const U=await o.json();f.value=((l=U.data)==null?void 0:l.resources)||[],await g()}catch(o){console.error("Error loading allocations:",o)}finally{S.value=!1}}},C=async()=>{var l;try{const o=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(!o.ok)throw new Error("Errore nel caricamento utenti");const U=await o.json();b.value=((l=U.data)==null?void 0:l.users)||[]}catch(o){console.error("Error loading users:",o)}},g=async()=>{h.value=f.value.map(l=>({user_id:l.user_id,user_name:l.user_name,total_allocation:l.allocation_percentage+Math.floor(Math.random()*30)}))},V=async()=>{var l;if(w.value){y.value=!0;try{const o=await fetch(`/api/ai-resources/analyze-allocation/${w.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!o.ok)throw new Error("Errore nell'analisi AI");const U=await o.json();n.value=((l=U.data)==null?void 0:l.analysis)||null}catch(o){console.error("Error in AI analysis:",o),alert("Errore nell'analisi AI: "+o.message)}finally{y.value=!1}}},K=async()=>{j.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify({project_id:w.value,...I.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await q(),T.value=!1,I.value={user_id:"",role:"",allocation_percentage:100}}catch(l){console.error("Error adding resource:",l),alert("Errore nell'aggiunta risorsa: "+l.message)}finally{j.value=!1}},M=l=>{D.value={id:l.id,role:l.role,allocation_percentage:l.allocation_percentage},O.value=!0},p=async()=>{j.value=!0;try{if(!(await fetch(`/api/resources/${D.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify({role:D.value.role,allocation_percentage:D.value.allocation_percentage})})).ok)throw new Error("Errore nell'aggiornamento allocazione");await q(),O.value=!1,D.value={id:null,role:"",allocation_percentage:100}}catch(l){console.error("Error updating allocation:",l),alert("Errore nell'aggiornamento: "+l.message)}finally{j.value=!1}},m=async l=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${l.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}})).ok)throw new Error("Errore nella rimozione");await q()}catch(o){console.error("Error removing allocation:",o),alert("Errore nella rimozione: "+o.message)}},R=async l=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify({project_id:w.value,user_id:l.user_id,role:l.role,allocation_percentage:l.allocation})}),await q()}catch(o){console.error("Error applying AI recommendation:",o)}},x=l=>l>=80?"bg-red-500":l>=60?"bg-yellow-500":"bg-green-500",a=l=>l>100?"bg-red-500":l>=90?"bg-yellow-500":"bg-green-500",u=l=>l>100?"text-red-600 dark:text-red-400":l>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return Z(()=>A.project,l=>{l&&q()},{immediate:!0}),se(()=>{C()}),(l,o)=>{var U;return t(),s("div",vl,[e("div",yl,[e("div",xl,[e("div",fl,[o[12]||(o[12]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",bl,[e("button",{onClick:V,disabled:y.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[y.value?(t(),s("svg",wl,o[10]||(o[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(t(),s("svg",kl,o[9]||(o[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),J(" "+r(y.value?"Analizzando...":"Analisi AI"),1)],8,hl),e("button",{onClick:o[0]||(o[0]=c=>T.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},o[11]||(o[11]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Risorsa ")]))])])]),n.value?(t(),s("div",_l,[e("div",$l,[o[15]||(o[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",jl,[e("h4",Cl," Insights AI - Efficienza: "+r(n.value.efficiency_score)+"% ",1),e("div",Ml,[(t(!0),s(F,null,L(n.value.optimization_insights,c=>(t(),s("div",{key:c,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+r(c),1))),128))]),(U=n.value.recommended_allocations)!=null&&U.length?(t(),s("div",Tl,[o[13]||(o[13]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Sl,[(t(!0),s(F,null,L(n.value.recommended_allocations,c=>{var i;return t(),s("div",{key:c.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Pl,[e("div",Al,[e("span",zl,r((i=c.user_name)==null?void 0:i.charAt(0)),1)]),e("div",null,[e("p",Dl,r(c.user_name),1),e("p",Vl,r(c.role)+" - "+r(c.allocation)+"%",1)])]),e("button",{onClick:G=>R(c),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,Il)])}),128))])])):z("",!0)]),e("button",{onClick:o[1]||(o[1]=c=>n.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},o[14]||(o[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):z("",!0)]),e("div",El,[o[20]||(o[20]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),S.value?(t(),s("div",Bl,[e("div",Ul,[(t(),s(F,null,L(3,c=>e("div",{key:c,class:"flex items-center space-x-4"},o[16]||(o[16]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):f.value.length?(t(),s("div",Hl,[(t(!0),s(F,null,L(f.value,c=>{var i;return t(),s("div",{key:c.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",Ol,[e("div",Fl,[e("div",Ll,[e("span",ql,r((i=c.user_name)==null?void 0:i.charAt(0)),1)]),e("div",null,[e("h4",Kl,r(c.user_name),1),e("p",Nl,r(c.role||"Team Member"),1)])]),e("div",Xl,[e("div",Jl,[e("div",Wl,r(c.allocation_percentage)+"% ",1),e("div",Gl,[e("div",{class:H(["h-2 rounded-full",x(c.allocation_percentage)]),style:ee({width:c.allocation_percentage+"%"})},null,6)])]),e("div",Yl,[e("button",{onClick:G=>M(c),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},o[18]||(o[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Ql),e("button",{onClick:G=>m(c),class:"text-red-400 hover:text-red-600"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Zl)])])])])}),128))])):(t(),s("div",Rl,o[17]||(o[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",ei,[o[21]||(o[21]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",ti,[e("div",si,[(t(!0),s(F,null,L(h.value,c=>(t(),s("div",{key:c.user_id,class:"flex items-center"},[e("div",ri,r(c.user_name),1),e("div",oi,[e("div",ai,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",a(c.total_allocation)]),style:ee({width:Math.min(c.total_allocation,100)+"%"})},null,6)])]),e("div",{class:H(["w-16 text-sm text-right font-medium",u(c.total_allocation)])},r(c.total_allocation)+"% ",3)]))),128))])])]),T.value?(t(),s("div",ni,[e("div",li,[e("div",ii,[o[26]||(o[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:te(K,["prevent"])},[e("div",di,[e("div",null,[o[23]||(o[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":o[2]||(o[2]=c=>I.value.user_id=c),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[o[22]||(o[22]=e("option",{value:""},"Seleziona utente...",-1)),(t(!0),s(F,null,L(b.value,c=>(t(),s("option",{key:c.id,value:c.id},r(c.full_name)+" ("+r(c.role)+") ",9,ui))),128))],512),[[W,I.value.user_id]])]),e("div",null,[o[24]||(o[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":o[3]||(o[3]=c=>I.value.role=c),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,I.value.role]])]),e("div",null,[o[25]||(o[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":o[4]||(o[4]=c=>I.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,I.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",ci,[e("button",{type:"button",onClick:o[5]||(o[5]=c=>T.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:j.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},r(j.value?"Salvando...":"Aggiungi"),9,gi)])],32)])])])):z("",!0),O.value?(t(),s("div",mi,[e("div",pi,[e("div",vi,[o[29]||(o[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Allocazione ",-1)),e("form",{onSubmit:te(p,["prevent"])},[e("div",yi,[e("div",null,[o[27]||(o[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":o[6]||(o[6]=c=>D.value.role=c),type:"text",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,D.value.role]])]),e("div",null,[o[28]||(o[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":o[7]||(o[7]=c=>D.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,D.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",xi,[e("button",{type:"button",onClick:o[8]||(o[8]=c=>O.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:j.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},r(j.value?"Salvando...":"Aggiorna"),9,fi)])],32)])])])):z("",!0)])}}},hi={class:"project-view"},ki={class:"tab-content"},wi={__name:"ProjectView",setup(k){const A=ye(),$=ne(),S=he(),j=we(),y=P(!0),f=P("overview"),b=B(()=>A.currentProject),h=B(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(w=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(w.id)||w.id==="kpi"&&$.hasPermission("view_reports")||w.id==="expenses"&&$.hasPermission("manage_expenses")))),n=B(()=>({overview:me,tasks:Js,team:Lr,resources:bi,expenses:Vo,kpi:nn,gantt:Dn,timesheet:pl})[f.value]||me),T=async()=>{y.value=!0;try{const D=S.params.id;await A.fetchProject(D)}catch(D){console.error("Error loading project:",D)}finally{y.value=!1}},O=()=>{j.push(`/projects/${S.params.id}/edit`)},I=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await A.deleteProject(S.params.id),j.push("/projects")}catch(D){console.error("Error deleting project:",D)}};return Z(()=>S.params.id,(D,w)=>{D&&D!==w&&T()}),Z(()=>S.hash,D=>{if(D){const w=D.replace("#","");h.value.find(q=>q.id===w)&&f.value!==w&&(f.value=w)}},{immediate:!0}),Z(f,D=>{const w=`#${D}`;S.hash!==w&&j.replace({...S,hash:w})}),se(()=>{if(S.hash){const D=S.hash.replace("#","");h.value.find(w=>w.id===D)&&(f.value=D)}T()}),(D,w)=>(t(),s("div",hi,[oe(Ue,{project:b.value,loading:y.value,onEdit:O,onDelete:I},null,8,["project","loading"]),oe(Ke,{modelValue:f.value,"onUpdate:modelValue":w[0]||(w[0]=q=>f.value=q),tabs:h.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",ki,[(t(),de(ke,null,[(t(),de(ve(n.value),{project:b.value,loading:y.value},null,8,["project","loading"]))],1024))])]))}},ji=ue(wi,[["__scopeId","data-v-de1f32e3"]]);export{ji as default};
