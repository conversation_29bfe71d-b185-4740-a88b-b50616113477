import{c as s,o as t,j as e,t as o,n as H,g as D,m as X,a as re,i as ye,b as fe,F as L,k as K,h as de,D as ve,f as I,z as ee,r as z,w as Z,A as se,v as B,H as G,I as ae,x as W,s as oe,N as be,p as le,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as xe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Pe={class:"text-2xl font-bold text-gray-900"},Se={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},De={key:0},Ve={key:1},ze={key:2},Ae={key:3},Ie={class:"mt-4 sm:mt-0 flex space-x-3"},Be={key:2,class:"text-center py-8"},Ee={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(f){const S=m=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[m]||"bg-gray-100 text-gray-800",j=m=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[m]||m,M=m=>m?new Date(m).toLocaleDateString("it-IT"):"",C=m=>m?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(m):"";return(m,v)=>{const x=fe("router-link");return t(),s("div",$e,[f.loading?(t(),s("div",je,v[1]||(v[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):f.project?(t(),s("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Pe,o(f.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",S(f.project.status)])},o(j(f.project.status)),3)]),e("div",Se,[f.project.client?(t(),s("span",De,[v[2]||(v[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),X(" Cliente: "+o(f.project.client.name),1)])):D("",!0),f.project.start_date?(t(),s("span",Ve,[v[3]||(v[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Inizio: "+o(M(f.project.start_date)),1)])):D("",!0),f.project.end_date?(t(),s("span",ze,[v[4]||(v[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),X(" Fine: "+o(M(f.project.end_date)),1)])):D("",!0),f.project.budget?(t(),s("span",Ae,[v[5]||(v[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),X(" Budget: "+o(C(f.project.budget)),1)])):D("",!0)])]),e("div",Ie,[re(x,{to:`/app/projects/${f.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ye(()=>v[6]||(v[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),X(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:v[0]||(v[0]=y=>m.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},v[7]||(v[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),X(" Elimina ")]))])])):(t(),s("div",Be,v[8]||(v[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Ee,[["__scopeId","data-v-6f1b5cc9"]]),He={class:"tab-navigation"},Re={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:f=>f.every(S=>typeof S=="object"&&S.id&&S.label)}},emits:["update:modelValue"],setup(f,{emit:S}){const j=f,M=S,C=x=>j.modelValue===x,m=x=>{M("update:modelValue",x)},v=x=>{const y={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return y[x]||y["chart-bar"]};return(x,y)=>(t(),s("div",He,[e("div",Re,[e("nav",Oe,[(t(!0),s(L,null,K(f.tabs,a=>(t(),s("button",{key:a.id,onClick:T=>m(a.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",C(a.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":C(a.id)?"page":void 0},[a.icon?(t(),de(ve(v(a.icon)),{key:0,class:"w-4 h-4"})):D("",!0),e("span",null,o(a.label),1),a.count!==void 0?(t(),s("span",Le,o(a.count),1)):D("",!0)],10,Fe))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},We={key:1,class:"space-y-6"},Ye={class:"bg-white shadow rounded-lg p-6"},Ge={key:0,class:"text-gray-600"},Je={key:1,class:"text-gray-400 italic"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"flex items-center"},tt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900"},rt={class:"bg-white shadow rounded-lg p-6"},ot={class:"flex items-center"},at={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},it={class:"bg-white shadow rounded-lg p-6"},lt={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},xt={class:"w-full bg-gray-200 rounded-full h-2.5"},yt={class:"text-sm text-gray-500 mt-2"},ft={class:"bg-white shadow rounded-lg p-6"},bt={class:"space-y-4"},ht={class:"flex justify-between items-center"},kt={class:"text-sm font-medium"},wt={class:"flex justify-between items-center"},_t={class:"text-sm font-medium"},$t={class:"w-full bg-gray-200 rounded-full h-3"},jt={class:"flex justify-between items-center text-sm"},Ct={class:"bg-white shadow rounded-lg p-6"},Mt={class:"space-y-3"},Tt={class:"flex-shrink-0"},Pt=["src","alt"],St={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Dt={class:"text-xs font-medium text-gray-600"},Vt={class:"flex-1"},zt={class:"text-sm font-medium text-gray-900"},At={class:"text-xs text-gray-500"},It={class:"text-right"},Bt={class:"text-xs text-gray-500"},Et={key:0,class:"text-center py-4"},Ut={class:"bg-white shadow rounded-lg p-6"},Ht={class:"space-y-3"},Rt={class:"flex-shrink-0"},Ot={class:"flex-1"},Ft={class:"text-sm text-gray-900"},Lt={class:"flex items-center space-x-2 mt-1"},Kt={class:"text-xs text-gray-500"},qt={class:"text-xs text-gray-500"},Nt={key:0,class:"text-center py-4"},Xt={key:2,class:"text-center py-8"},Wt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(f){const S=f,j=I(()=>{if(!S.project||!S.project.task_count)return 0;const w=S.project.completed_tasks||0,d=S.project.task_count||1;return Math.round(w/d*100)}),M=I(()=>{var w;return((w=S.project)==null?void 0:w.team_members)||[]}),C=I(()=>{var V,F,_;if((V=S.project)!=null&&V.expenses)return S.project.expenses;const w=((F=S.project)==null?void 0:F.total_hours)||0,d=(_=S.project)!=null&&_.client_daily_rate?S.project.client_daily_rate/8:50;return w*d}),m=I(()=>{var d;return(((d=S.project)==null?void 0:d.budget)||0)-C.value}),v=I(()=>{var d;const w=((d=S.project)==null?void 0:d.budget)||1;return Math.min(Math.round(C.value/w*100),100)}),x=I(()=>{const w=v.value;return w>=90?"bg-red-600":w>=75?"bg-yellow-600":"bg-green-600"}),y=I(()=>{var d;const w=m.value;return w<0?"text-red-600":w<(((d=S.project)==null?void 0:d.budget)||0)*.1?"text-yellow-600":"text-green-600"}),a=I(()=>{var w;return(w=S.project)!=null&&w.tasks?[...S.project.tasks].sort((d,V)=>new Date(V.updated_at)-new Date(d.updated_at)).slice(0,5).map(d=>{var V;return{id:d.id,description:`Task "${d.name}" ${T(d.status)}`,created_at:d.updated_at,user_name:((V=d.assignee)==null?void 0:V.full_name)||"Non assegnato",type:O(d.status)}}):[]}),T=w=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[w]||w,O=w=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[w]||"task_updated",E=w=>w?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(w):"Non specificato",A=w=>w?new Date(w).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",h=w=>w?w.split(" ").map(d=>d.charAt(0).toUpperCase()).slice(0,2).join(""):"??",N=w=>{const d={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return d[w]||d.default};return(w,d)=>(t(),s("div",Ne,[f.loading?(t(),s("div",Xe,d[0]||(d[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):f.project?(t(),s("div",We,[e("div",Ye,[d[1]||(d[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),f.project.description?(t(),s("p",Ge,o(f.project.description),1)):(t(),s("p",Je,"Nessuna descrizione disponibile"))]),e("div",Qe,[e("div",Ze,[e("div",et,[d[3]||(d[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",tt,[e("dl",null,[d[2]||(d[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",st,o(f.project.task_count||0),1)])])])]),e("div",rt,[e("div",ot,[d[5]||(d[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",at,[e("dl",null,[d[4]||(d[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",nt,o(f.project.completed_tasks||0),1)])])])]),e("div",it,[e("div",lt,[d[7]||(d[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",dt,[e("dl",null,[d[6]||(d[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ut,o(f.project.team_count||0),1)])])])]),e("div",ct,[e("div",gt,[d[9]||(d[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",mt,[e("dl",null,[d[8]||(d[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",pt,o(E(f.project.budget)),1)])])])])]),e("div",vt,[d[10]||(d[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",xt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${j.value}%`})},null,4)]),e("p",yt,o(j.value)+"% completato",1)]),e("div",ft,[d[15]||(d[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",bt,[e("div",ht,[d[11]||(d[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",kt,o(E(f.project.budget)),1)]),d[14]||(d[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",wt,[d[12]||(d[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",_t,o(E(C.value)),1)]),e("div",$t,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",x.value]),style:ee({width:v.value+"%"})},null,6)]),e("div",jt,[d[13]||(d[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",y.value])},o(E(m.value)),3)])])]),e("div",Ct,[d[17]||(d[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Mt,[(t(!0),s(L,null,K(M.value,V=>(t(),s("div",{key:V.id,class:"flex items-center space-x-3"},[e("div",Tt,[V.profile_image?(t(),s("img",{key:0,src:V.profile_image,alt:V.full_name,class:"w-8 h-8 rounded-full"},null,8,Pt)):(t(),s("div",St,[e("span",Dt,o(h(V.full_name)),1)]))]),e("div",Vt,[e("p",zt,o(V.full_name),1),e("p",At,o(V.role||"Team Member"),1)]),e("div",It,[e("p",Bt,o(V.hours_worked||0)+"h",1)])]))),128)),M.value.length===0?(t(),s("div",Et,d[16]||(d[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):D("",!0)])]),e("div",Ut,[d[20]||(d[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Ht,[(t(!0),s(L,null,K(a.value,V=>(t(),s("div",{key:V.id,class:"flex items-start space-x-3"},[e("div",Rt,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",N(V.type)])},null,2)]),e("div",Ot,[e("p",Ft,o(V.description),1),e("div",Lt,[e("p",Kt,o(A(V.created_at)),1),d[18]||(d[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",qt,o(V.user_name),1)])])]))),128)),a.value.length===0?(t(),s("div",Nt,d[19]||(d[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):D("",!0)])])])):(t(),s("div",Xt,d[21]||(d[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Wt,[["__scopeId","data-v-16274846"]]),Yt={class:"space-y-6"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Jt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qt={class:"flex items-center justify-between"},Zt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},es=["value"],ts={class:"mt-4 flex items-center justify-between"},ss={class:"flex items-center space-x-4"},rs={class:"text-sm text-gray-500 dark:text-gray-400"},os={key:0,class:"flex justify-center py-8"},as={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ns={class:"text-red-600"},is={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ls={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ds={class:"col-span-4"},us={class:"text-sm font-medium text-gray-900 dark:text-white"},cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},gs={class:"col-span-2"},ms={key:0,class:"flex items-center"},ps={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},vs={class:"ml-2"},xs={class:"text-sm font-medium text-gray-900 dark:text-white"},ys={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},fs={class:"col-span-1"},bs={class:"col-span-1"},hs={class:"col-span-2"},ks={key:0,class:"text-sm text-gray-900 dark:text-white"},ws={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},_s={class:"col-span-1"},$s={class:"text-sm text-gray-900 dark:text-white"},js={key:0,class:"text-gray-500"},Cs={class:"col-span-1"},Ms={class:"flex items-center space-x-2"},Ts=["onClick"],Ps={key:0,class:"px-6 py-12 text-center"},Ss={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ds={class:"flex items-center justify-between mb-4"},Vs={class:"font-medium text-gray-900 dark:text-white"},zs={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},As={class:"space-y-3"},Is=["onClick"],Bs={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Es={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Us={class:"flex items-center justify-between"},Hs={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Rs={class:"mt-3"},Os={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Fs={class:"grid grid-cols-1 gap-4"},Ls={class:"grid grid-cols-2 gap-4"},Ks=["value"],qs={class:"grid grid-cols-2 gap-4"},Ns={class:"flex justify-end space-x-3 mt-6"},Xs=["disabled"],Ws={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(f,{expose:S}){const j=f,M=ne(),{hasPermission:C}=_e(),m=z([]),v=z(!1),x=z(""),y=z("list"),a=z(!1),T=z({status:"",priority:"",assignee_id:"",search:""}),O=z(!1),E=z(!1),A=z(null),h=z({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),N=I(()=>C.value("manage_project_tasks")),w=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],d=async()=>{var P,l;if((P=j.project)!=null&&P.id){v.value=!0,x.value="";try{const Q=new URLSearchParams({project_id:j.project.id,...T.value}),te=await fetch(`/api/tasks?${Q}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!te.ok)throw new Error("Errore nel caricamento dei task");const b=await te.json();m.value=((l=b.data)==null?void 0:l.tasks)||b.tasks||[]}catch(Q){x.value=Q.message}finally{v.value=!1}}},V=async()=>{a.value=!0;try{const P=E.value?`/api/tasks/${A.value.id}`:"/api/tasks",l=E.value?"PUT":"POST",Q={...h.value,project_id:j.project.id};if(!(await fetch(P,{method:l,headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(Q)})).ok)throw new Error("Errore nel salvataggio del task");await d(),_()}catch(P){x.value=P.message}finally{a.value=!1}},F=P=>{A.value=P,h.value={name:P.name,description:P.description||"",status:P.status,priority:P.priority,assignee_id:P.assignee_id||"",due_date:P.due_date?P.due_date.split("T")[0]:"",estimated_hours:P.estimated_hours},E.value=!0},_=()=>{O.value=!1,E.value=!1,A.value=null,h.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},g=P=>m.value.filter(l=>l.status===P),u=P=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[P]||"bg-gray-100 text-gray-800",U=P=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[P]||P,p=P=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[P]||"bg-gray-100 text-gray-800",r=P=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[P]||P,i=(P,l)=>`${(P==null?void 0:P.charAt(0))||""}${(l==null?void 0:l.charAt(0))||""}`.toUpperCase(),k=P=>new Date(P).toLocaleDateString("it-IT");let $;const q=()=>{clearTimeout($),$=setTimeout(()=>{d()},300)};return Z(()=>{var P;return(P=j.project)==null?void 0:P.id},P=>{P&&d()}),se(()=>{var P;(P=j.project)!=null&&P.id&&d()}),S({refresh:d}),(P,l)=>{var Q,te;return t(),s("div",Yt,[e("div",Gt,[e("div",Jt,[e("div",Qt,[l[16]||(l[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),N.value?(t(),s("button",{key:0,onClick:l[0]||(l[0]=b=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},l[15]||(l[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Nuovo Task ")]))):D("",!0)]),e("div",Zt,[e("div",null,[l[18]||(l[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),B(e("select",{"onUpdate:modelValue":l[1]||(l[1]=b=>T.value.status=b),onChange:d,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[17]||(l[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[G,T.value.status]])]),e("div",null,[l[20]||(l[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),B(e("select",{"onUpdate:modelValue":l[2]||(l[2]=b=>T.value.priority=b),onChange:d,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[19]||(l[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[G,T.value.priority]])]),e("div",null,[l[22]||(l[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),B(e("select",{"onUpdate:modelValue":l[3]||(l[3]=b=>T.value.assignee_id=b),onChange:d,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[21]||(l[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(L,null,K(((Q=f.project)==null?void 0:Q.team_members)||[],b=>(t(),s("option",{key:b.id,value:b.id},o(b.first_name)+" "+o(b.last_name),9,es))),128))],544),[[G,T.value.assignee_id]])]),e("div",null,[l[23]||(l[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),B(e("input",{"onUpdate:modelValue":l[4]||(l[4]=b=>T.value.search=b),onInput:q,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[W,T.value.search]])])]),e("div",ts,[e("div",ss,[l[24]||(l[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:l[5]||(l[5]=b=>y.value="list"),class:H([y.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:l[6]||(l[6]=b=>y.value="kanban"),class:H([y.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",rs,o(m.value.length)+" task trovati ",1)])])]),v.value?(t(),s("div",os,l[25]||(l[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),x.value?(t(),s("div",as,[e("p",ns,o(x.value),1)])):D("",!0),!v.value&&y.value==="list"?(t(),s("div",is,[e("div",ls,[l[27]||(l[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(L,null,K(m.value,b=>(t(),s("div",{key:b.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ds,[e("div",us,o(b.name),1),b.description?(t(),s("div",cs,o(b.description),1)):D("",!0)]),e("div",gs,[b.assignee?(t(),s("div",ms,[e("div",ps,o(i(b.assignee.first_name,b.assignee.last_name)),1),e("div",vs,[e("div",xs,o(b.assignee.first_name)+" "+o(b.assignee.last_name),1)])])):(t(),s("span",ys,"Non assegnato"))]),e("div",fs,[e("span",{class:H([u(b.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(U(b.status)),3)]),e("div",bs,[e("span",{class:H([p(b.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(r(b.priority)),3)]),e("div",hs,[b.due_date?(t(),s("div",ks,o(k(b.due_date)),1)):(t(),s("span",ws,"-"))]),e("div",_s,[e("div",$s,[X(o(b.actual_hours||0)+"h ",1),b.estimated_hours?(t(),s("span",js,"/ "+o(b.estimated_hours)+"h",1)):D("",!0)])]),e("div",Cs,[e("div",Ms,[e("button",{onClick:n=>F(b),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Ts)])])]))),128)),m.value.length===0?(t(),s("div",Ps,l[26]||(l[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):D("",!0)])])):D("",!0),!v.value&&y.value==="kanban"?(t(),s("div",Ss,[(t(),s(L,null,K(w,b=>e("div",{key:b.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ds,[e("h4",Vs,o(b.label),1),e("span",zs,o(g(b.value).length),1)]),e("div",As,[(t(!0),s(L,null,K(g(b.value),n=>(t(),s("div",{key:n.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:c=>F(n)},[e("div",Bs,o(n.name),1),n.description?(t(),s("div",Es,o(n.description),1)):D("",!0),e("div",Us,[e("span",{class:H([p(n.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(r(n.priority)),3),n.assignee?(t(),s("div",Hs,o(i(n.assignee.first_name,n.assignee.last_name)),1)):D("",!0)])],8,Is))),128))])])),64))])):D("",!0),O.value||E.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:_},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:l[14]||(l[14]=oe(()=>{},["stop"]))},[e("div",Rs,[e("h3",Os,o(E.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:oe(V,["prevent"])},[e("div",Fs,[e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),B(e("input",{"onUpdate:modelValue":l[7]||(l[7]=b=>h.value.name=b),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.name]])]),e("div",null,[l[29]||(l[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),B(e("textarea",{"onUpdate:modelValue":l[8]||(l[8]=b=>h.value.description=b),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.description]])]),e("div",Ls,[e("div",null,[l[31]||(l[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),B(e("select",{"onUpdate:modelValue":l[9]||(l[9]=b=>h.value.status=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[30]||(l[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[G,h.value.status]])]),e("div",null,[l[33]||(l[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),B(e("select",{"onUpdate:modelValue":l[10]||(l[10]=b=>h.value.priority=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},l[32]||(l[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[G,h.value.priority]])])]),e("div",null,[l[35]||(l[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),B(e("select",{"onUpdate:modelValue":l[11]||(l[11]=b=>h.value.assignee_id=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[l[34]||(l[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(L,null,K(((te=f.project)==null?void 0:te.team_members)||[],b=>(t(),s("option",{key:b.id,value:b.id},o(b.first_name)+" "+o(b.last_name),9,Ks))),128))],512),[[G,h.value.assignee_id]])]),e("div",qs,[e("div",null,[l[36]||(l[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),B(e("input",{"onUpdate:modelValue":l[12]||(l[12]=b=>h.value.due_date=b),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.due_date]])]),e("div",null,[l[37]||(l[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),B(e("input",{"onUpdate:modelValue":l[13]||(l[13]=b=>h.value.estimated_hours=b),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.estimated_hours]])])])]),e("div",Ns,[e("button",{type:"button",onClick:_,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:a.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(a.value?"Salvataggio...":E.value?"Aggiorna":"Crea"),9,Xs)])],32)])])])):D("",!0)])}}},Ys={class:"space-y-6"},Gs={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Js={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qs={class:"flex items-center justify-between"},Zs={class:"p-6 border-b border-gray-200 dark:border-gray-700"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},rr={class:"text-center"},or={class:"text-2xl font-bold text-green-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-blue-600"},ir={class:"text-center"},lr={class:"text-2xl font-bold text-purple-600"},dr={class:"p-6"},ur={class:"space-y-4"},cr={class:"flex items-center justify-between"},gr={class:"flex items-center space-x-4"},mr={class:"flex-shrink-0"},pr=["src","alt"],vr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},xr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},yr={class:"flex-1"},fr={class:"flex items-center space-x-2"},br={class:"text-lg font-medium text-gray-900 dark:text-white"},hr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},kr={class:"text-sm text-gray-600 dark:text-gray-400"},wr={class:"text-xs text-gray-500 dark:text-gray-500"},_r={class:"flex items-center space-x-4"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"text-right"},Pr={class:"text-sm font-medium text-gray-900 dark:text-white"},Sr={class:"flex items-center space-x-2"},Dr=["onClick"],Vr=["onClick"],zr={class:"mt-4"},Ar={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Ir={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Br={key:0,class:"text-center py-8"},Er={class:"mt-6"},Ur={class:"mt-3"},Hr={class:"space-y-4"},Rr=["value"],Or={class:"flex justify-end space-x-3 mt-6"},Fr=["disabled"],Lr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(f,{expose:S,emit:j}){const M=f,C=ne(),m=z(!1),v=z([]),x=z(!1),y=z({user_id:"",role:""}),a=I(()=>{var r;return((r=M.project)==null?void 0:r.team_members)||[]}),T=I(()=>a.value.reduce((r,i)=>r+(i.hours_worked||0),0)),O=I(()=>a.value.length===0?0:Math.round(T.value/a.value.length)),E=I(()=>a.value.filter(r=>(r.hours_worked||0)>0).length),A=r=>r?r.split(" ").map(i=>i.charAt(0).toUpperCase()).slice(0,2).join(""):"??",h=r=>{var k;return(((k=M.project)==null?void 0:k.tasks)||[]).filter($=>$.assignee_id===r).length},N=r=>{var k;return(((k=M.project)==null?void 0:k.tasks)||[]).filter($=>$.assignee_id===r&&$.status==="done").length},w=r=>{const i=h(r),k=N(r);return i===0?0:Math.round(k/i*100)},d=r=>{const i=w(r);return i>=80?"bg-green-600":i>=60?"bg-yellow-600":i>=40?"bg-orange-600":"bg-red-600"},V=r=>!r||r===0?"0.00":parseFloat(r).toFixed(2),F=async()=>{var r;try{const i=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(i.ok){const k=await i.json(),$=a.value.map(q=>q.id);v.value=(r=k.data)!=null&&r.users?k.data.users.filter(q=>!$.includes(q.id)):[]}}catch(i){console.error("Errore nel caricamento utenti:",i),v.value=[]}},_=async()=>{x.value=!0;try{const r=await fetch(`/api/projects/${M.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken},body:JSON.stringify(y.value)});if(r.ok)p("refresh"),U();else{const i=await r.json();alert(i.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{x.value=!1}},g=r=>{console.log("Edit member:",r)},u=async r=>{if(confirm(`Rimuovere ${r.full_name} dal progetto?`))try{const i=await fetch(`/api/projects/${M.project.id}/team/${r.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(i.ok)p("refresh");else{const k=await i.json();alert(k.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},U=()=>{m.value=!1,y.value={user_id:"",role:""}},p=j;return se(()=>{F()}),Z(()=>m.value,r=>{r&&F()}),Z(()=>{var r;return(r=M.project)==null?void 0:r.team_members},()=>{m.value&&F()}),S({refresh:F}),(r,i)=>(t(),s("div",Ys,[e("div",Gs,[e("div",Js,[e("div",Qs,[i[6]||(i[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:i[0]||(i[0]=k=>m.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[5]||(i[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Membro ")]))])]),e("div",Zs,[e("div",er,[e("div",tr,[e("div",sr,o(a.value.length),1),i[7]||(i[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",rr,[e("div",or,o(T.value)+"h",1),i[8]||(i[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",ar,[e("div",nr,o(O.value)+"h",1),i[9]||(i[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",ir,[e("div",lr,o(E.value),1),i[10]||(i[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",dr,[e("div",ur,[(t(!0),s(L,null,K(a.value,k=>{var $,q;return t(),s("div",{key:k.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",cr,[e("div",gr,[e("div",mr,[k.profile_image?(t(),s("img",{key:0,src:k.profile_image,alt:k.full_name,class:"w-12 h-12 rounded-full"},null,8,pr)):(t(),s("div",vr,[e("span",xr,o(A(k.full_name)),1)]))]),e("div",yr,[e("div",fr,[e("h4",br,o(k.full_name),1),k.id===(($=f.project)==null?void 0:$.manager_id)?(t(),s("span",hr," Project Manager ")):D("",!0)]),e("p",kr,o(k.role||"Team Member"),1),e("p",wr,o(k.email),1)])]),e("div",_r,[e("div",$r,[e("div",jr,o(V(k.hours_worked||0))+"h",1),i[11]||(i[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Cr,[e("div",Mr,o(h(k.id)),1),i[12]||(i[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Tr,[e("div",Pr,o(N(k.id)),1),i[13]||(i[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Sr,[e("button",{onClick:P=>g(k),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Dr),k.id!==((q=f.project)==null?void 0:q.manager_id)?(t(),s("button",{key:0,onClick:P=>u(k),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Vr)):D("",!0)])])]),e("div",zr,[e("div",Ar,[i[16]||(i[16]=e("span",null,"Produttività",-1)),e("span",null,o(w(k.id))+"%",1)]),e("div",Ir,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",d(k.id)]),style:ee({width:w(k.id)+"%"})},null,6)])])])}),128)),a.value.length===0?(t(),s("div",Br,[i[18]||(i[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),i[19]||(i[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),i[20]||(i[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Er,[e("button",{onClick:i[1]||(i[1]=k=>m.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},i[17]||(i[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi primo membro ")]))])])):D("",!0)])])]),m.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:U},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[4]||(i[4]=oe(()=>{},["stop"]))},[e("div",Ur,[i[25]||(i[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:oe(_,["prevent"])},[e("div",Hr,[e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),B(e("select",{"onUpdate:modelValue":i[2]||(i[2]=k=>y.value.user_id=k),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(L,null,K(v.value,k=>(t(),s("option",{key:k.id,value:k.id},o(k.full_name)+" ("+o(k.email)+") ",9,Rr))),128))],512),[[G,y.value.user_id]])]),e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),B(e("select",{"onUpdate:modelValue":i[3]||(i[3]=k=>y.value.role=k),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[23]||(i[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[G,y.value.role]])])]),e("div",Or,[e("button",{type:"button",onClick:U,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:x.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(x.value?"Aggiungendo...":"Aggiungi"),9,Fr)])],32)])])])):D("",!0)]))}};function Kr(f,S){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(f,S){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function qr(f,S){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Nr={class:"fixed inset-0 z-50 overflow-y-auto"},Xr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Wr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Yr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Gr={class:"mb-4"},Jr={class:"text-lg font-medium text-gray-900 dark:text-white"},Qr={class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},eo=["disabled"],to={key:0},so={key:1},ro={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(f,{emit:S}){const j=f,M=S,C=z(!1),m=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),v=async()=>{C.value=!0;try{const y=j.expense?`/api/expenses/${j.expense.id}`:`/api/projects/${j.projectId}/expenses`,a=j.expense?"PUT":"POST";(await fetch(y,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).ok?M("saved"):console.error("Error saving expense")}catch(y){console.error("Error saving expense:",y)}finally{C.value=!1}},x=y=>{const a=y.target.files[0];if(a){if(a.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),y.target.value="";return}m.receipt_file=a}};return se(()=>{j.expense&&Object.assign(m,{description:j.expense.description,amount:j.expense.amount,category:j.expense.category,billing_type:j.expense.billing_type||"billable",status:j.expense.status||"pending",date:j.expense.date.split("T")[0],notes:j.expense.notes||""})}),(y,a)=>(t(),s("div",Nr,[e("div",Xr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:a[0]||(a[0]=T=>y.$emit("close"))}),e("div",Wr,[e("form",{onSubmit:oe(v,["prevent"])},[e("div",Yr,[e("div",Gr,[e("h3",Jr,o(f.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Qr,[e("div",null,[a[9]||(a[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),B(e("input",{"onUpdate:modelValue":a[1]||(a[1]=T=>m.description=T),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[W,m.description]])]),e("div",null,[a[10]||(a[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),B(e("input",{"onUpdate:modelValue":a[2]||(a[2]=T=>m.amount=T),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[W,m.amount,void 0,{number:!0}]])]),e("div",null,[a[12]||(a[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),B(e("select",{"onUpdate:modelValue":a[3]||(a[3]=T=>m.category=T),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[11]||(a[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[G,m.category]])]),e("div",null,[a[14]||(a[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),B(e("select",{"onUpdate:modelValue":a[4]||(a[4]=T=>m.billing_type=T),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[13]||(a[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[G,m.billing_type]])]),e("div",null,[a[16]||(a[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),B(e("select",{"onUpdate:modelValue":a[5]||(a[5]=T=>m.status=T),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},a[15]||(a[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[G,m.status]])]),e("div",null,[a[17]||(a[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),B(e("input",{"onUpdate:modelValue":a[6]||(a[6]=T=>m.date=T),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[W,m.date]])]),e("div",null,[a[18]||(a[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),B(e("textarea",{"onUpdate:modelValue":a[7]||(a[7]=T=>m.notes=T),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[W,m.notes]])]),e("div",null,[a[19]||(a[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:x,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),a[20]||(a[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",Zr,[e("button",{type:"submit",disabled:C.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[C.value?(t(),s("span",to,"Salvando...")):(t(),s("span",so,o(f.expense?"Aggiorna":"Salva"),1))],8,eo),e("button",{type:"button",onClick:a[8]||(a[8]=T=>y.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},oo={class:"project-expenses"},ao={class:"space-y-6"},no={class:"flex justify-between items-center"},io={key:0,class:"text-center py-8"},lo={key:1,class:"text-center py-12"},uo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},co={class:"divide-y divide-gray-200 dark:divide-gray-700"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},po={class:"flex items-center"},vo={class:"flex-shrink-0"},xo={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},yo={class:"ml-4 flex-1"},fo={class:"flex items-center justify-between"},bo={class:"text-sm font-medium text-gray-900 dark:text-white"},ho={class:"ml-2 flex-shrink-0"},ko={class:"text-sm font-medium text-gray-900 dark:text-white"},wo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},_o={class:"capitalize"},$o={key:0,class:"mx-2"},jo={key:1},Co={class:"mt-2 flex items-center space-x-4 text-xs"},Mo={key:0,class:"inline-flex items-center text-green-600 dark:text-green-400"},To={key:0,class:"flex items-center space-x-2"},Po=["onClick"],So=["onClick"],Do={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Vo={class:"flex justify-between items-center"},zo={class:"text-lg font-bold text-gray-900 dark:text-white"},Ao={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(f){const S=f;xe();const j=ne(),M=z(!1),C=z([]),m=z(!1),v=z(null),x=I(()=>j.hasPermission("manage_expenses")),y=I(()=>C.value.reduce((g,u)=>g+u.amount,0)),a=async()=>{var g;if((g=S.project)!=null&&g.id){M.value=!0;try{const u=await fetch(`/api/projects/${S.project.id}/expenses`);u.ok&&(C.value=await u.json())}catch(u){console.error("Error loading expenses:",u)}finally{M.value=!1}}},T=g=>{v.value=g,m.value=!0},O=async g=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${g}`,{method:"DELETE"})).ok&&(C.value=C.value.filter(U=>U.id!==g))}catch(u){console.error("Error deleting expense:",u)}},E=()=>{m.value=!1,v.value=null},A=()=>{E(),a()},h=g=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(g),N=g=>new Date(g).toLocaleDateString("it-IT"),w=g=>({licenses:"📄 Licenze",travel:"✈️ Viaggi",meals:"🍽️ Pasti",equipment:"🖥️ Attrezzature",external:"🏢 Servizi Esterni",other:"📦 Altro"})[g]||g,d=g=>({billable:"💰 Fatturabile","non-billable":"🏢 Non Fatturabile",reimbursable:"💳 Rimborsabile"})[g]||g,V=g=>({billable:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400","non-billable":"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",reimbursable:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"})[g]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",F=g=>({pending:"⏳ In Attesa",approved:"✅ Approvata",rejected:"❌ Rifiutata"})[g]||g,_=g=>({pending:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",approved:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",rejected:"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[g]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";return Z(()=>{var g;return(g=S.project)==null?void 0:g.id},(g,u)=>{g&&g!==u&&a()},{immediate:!0}),se(()=>{a()}),(g,u)=>{var U;return t(),s("div",oo,[e("div",ao,[e("div",no,[u[2]||(u[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),x.value?(t(),s("button",{key:0,onClick:u[0]||(u[0]=p=>m.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[re(le(qr),{class:"w-4 h-4 mr-2"}),u[1]||(u[1]=X(" Aggiungi Spesa "))])):D("",!0)]),M.value?(t(),s("div",io,u[3]||(u[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):C.value.length===0?(t(),s("div",lo,[re(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),u[4]||(u[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),u[5]||(u[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",uo,[e("ul",co,[(t(!0),s(L,null,K(C.value,p=>(t(),s("li",{key:p.id,class:"px-6 py-4"},[e("div",go,[e("div",mo,[e("div",po,[e("div",vo,[e("div",xo,[re(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",yo,[e("div",fo,[e("p",bo,o(p.description),1),e("div",ho,[e("p",ko," €"+o(h(p.amount)),1)])]),e("div",wo,[re(le(Kr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),X(" "+o(N(p.date))+" ",1),u[6]||(u[6]=e("span",{class:"mx-2"},"•",-1)),e("span",_o,o(w(p.category)),1),p.user?(t(),s("span",$o,"•")):D("",!0),p.user?(t(),s("span",jo,o(p.user.name),1)):D("",!0)]),e("div",Co,[e("span",{class:H([V(p.billing_type),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(d(p.billing_type)),3),e("span",{class:H([_(p.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(F(p.status)),3),p.receipt_path?(t(),s("span",Mo,u[7]||(u[7]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),X(" Ricevuta ")]))):D("",!0)])])])]),x.value?(t(),s("div",To,[e("button",{onClick:r=>T(p),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Po),e("button",{onClick:r=>O(p.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,So)])):D("",!0)])]))),128))])])),C.value.length>0?(t(),s("div",Do,[e("div",Vo,[u[8]||(u[8]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",zo,"€"+o(h(y.value)),1)])])):D("",!0)]),m.value?(t(),de(ro,{key:0,"project-id":(U=f.project)==null?void 0:U.id,expense:v.value,onClose:E,onSaved:A},null,8,["project-id","expense"])):D("",!0)])}}},Io={class:"project-kpi"},Bo={key:0,class:"animate-pulse space-y-4"},Eo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Uo={key:1,class:"space-y-6"},Ho={class:"bg-white shadow rounded-lg p-6"},Ro={class:"flex items-center justify-between"},Oo=["disabled"],Fo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Lo={class:"bg-white shadow rounded-lg p-6"},Ko={class:"flex items-center"},qo={class:"ml-5 w-0 flex-1"},No={class:"text-lg font-medium text-gray-900"},Xo={class:"text-xs text-gray-500"},Wo={class:"bg-white shadow rounded-lg p-6"},Yo={class:"flex items-center"},Go={class:"ml-5 w-0 flex-1"},Jo={class:"text-lg font-medium text-gray-900"},Qo={class:"bg-white shadow rounded-lg p-6"},Zo={class:"flex items-center"},ea={class:"ml-5 w-0 flex-1"},ta={class:"text-lg font-medium text-gray-900"},sa={class:"text-xs text-gray-500"},ra={class:"bg-white shadow rounded-lg p-6"},oa={class:"flex items-center"},aa={class:"ml-5 w-0 flex-1"},na={class:"text-lg font-medium text-gray-900"},ia={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},la={class:"bg-white shadow rounded-lg p-6"},da={class:"space-y-4"},ua={class:"flex justify-between text-sm"},ca={class:"font-medium"},ga={class:"w-full bg-gray-200 rounded-full h-3"},ma={class:"flex justify-between text-sm"},pa={class:"text-gray-600"},va={class:"font-medium"},xa={class:"bg-white shadow rounded-lg p-6"},ya={class:"space-y-4"},fa={class:"flex justify-between text-sm"},ba={class:"font-medium"},ha={class:"w-full bg-gray-200 rounded-full h-3"},ka={class:"flex justify-between text-sm"},wa={class:"text-gray-600"},_a={class:"font-medium"},$a={class:"bg-white shadow rounded-lg p-6"},ja={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={class:"text-center p-4 border rounded-lg"},Pa={class:"text-xs text-gray-500"},Sa={class:"text-center p-4 border rounded-lg"},Da={class:"text-xs text-gray-500"},Va={key:2,class:"text-center py-8"},za={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Aa={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Ia={class:"mt-3"},Ba={class:"mt-6 space-y-6"},Ea={class:"bg-gray-50 p-4 rounded-lg"},Ua={class:"font-medium text-gray-900"},Ha={class:"text-sm text-gray-600"},Ra={class:"space-y-6"},Oa={class:"flex items-center justify-between mb-4"},Fa={class:"font-medium text-gray-900"},La={class:"text-sm text-gray-600"},Ka={class:"flex items-center space-x-2"},qa={class:"text-xs text-gray-500"},Na=["onClick"],Xa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Wa=["onUpdate:modelValue","onInput"],Ya=["onUpdate:modelValue","onInput"],Ga=["onUpdate:modelValue","onInput"],Ja={class:"mt-4"},Qa=["onUpdate:modelValue","onInput"],Za={class:"mt-4 flex justify-end"},en=["onClick","disabled"],tn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},sn={key:1,class:"text-sm text-green-600"},rn={class:"mt-6 pt-4 border-t flex justify-between"},on={class:"flex space-x-3"},an=["disabled"],nn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(f,{emit:S}){const j=f,M=S,C=z(!1),m=z(!1),v=z(null),x=z({}),y=z({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),a=z({budget:80,time:85,margin:15}),T=I(()=>{var n;return!((n=j.project)!=null&&n.budget)||y.value.totalCosts===0?0:Math.round(y.value.totalCosts/j.project.budget*100)}),O=I(()=>{var n;return!((n=j.project)!=null&&n.estimated_hours)||y.value.totalHours===0?0:Math.round(y.value.totalHours/j.project.estimated_hours*100)}),E=I(()=>{const n=y.value.costVariance;return n>0?"text-red-600":n<0?"text-green-600":"text-gray-600"}),A=I(()=>{const n=y.value.marginPercentage;return n>=a.value.margin?"text-green-600":n>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),h=I(()=>{const n=y.value.marginPercentage;return n>=a.value.margin?"Ottimo":n>=a.value.margin*.7?"Accettabile":"Critico"}),N=I(()=>{const n=T.value;return n>=a.value.budget?"text-red-600":n>=a.value.budget*.8?"text-yellow-600":"text-green-600"}),w=I(()=>{const n=O.value;return n>=a.value.time?"text-red-600":n>=a.value.time*.8?"text-yellow-600":"text-green-600"}),d=I(()=>{const n=y.value.marginPercentage;return n>=a.value.margin?"text-green-600":n>=a.value.margin*.7?"text-yellow-600":"text-red-600"}),V=n=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n||0),F=n=>!n||n===0?"0h":`${parseFloat(n).toFixed(2)}h`,_=n=>`${(n||0).toFixed(1)}%`,g=async()=>{var n;(n=j.project)!=null&&n.id&&u()},u=()=>{const n=j.project;n&&(y.value={totalHours:n.total_hours||0,workDays:Math.ceil((n.total_hours||0)/8),totalCosts:(n.total_hours||0)*50,costVariance:(n.total_hours||0)*50-(n.budget||0),potentialRevenue:n.budget||0,actualRevenue:n.invoiced_amount||0,marginPercentage:n.budget?(n.budget-(n.total_hours||0)*50)/n.budget*100:0})},U=async()=>{C.value=!0;try{await g(),M("refresh")}catch(n){console.error("Error refreshing KPIs:",n)}finally{C.value=!1}},p=I(()=>{var c;const n=((c=j.project)==null?void 0:c.project_type)||"service";return r(n)}),r=n=>{const c={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return c[n]||c.service},i=n=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[n]||"Sconosciuto",k=()=>{p.value.forEach(c=>{x.value[c.name]||(x.value[c.name]={target_min:c.target_min,target_max:c.target_max,warning_threshold:c.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),m.value=!0},$=()=>{m.value=!1},q=n=>{x.value[n]&&(x.value[n].isDirty=!0,x.value[n].isSaved=!1)},P=n=>{const c=p.value.find(Y=>Y.name===n);c&&x.value[n]&&(x.value[n].target_min=c.target_min,x.value[n].target_max=c.target_max,x.value[n].warning_threshold=c.warning_threshold,x.value[n].custom_description="",x.value[n].isDirty=!0,x.value[n].isSaved=!1)},l=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&p.value.forEach(n=>{P(n.name)})},Q=async n=>{var c;if(x.value[n]){v.value=n;try{const Y=x.value[n];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(c=j.project)==null?void 0:c.id,kpi_name:n,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),x.value[n].isDirty=!1,x.value[n].isSaved=!0,setTimeout(()=>{x.value[n]&&(x.value[n].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{v.value=null}}},te=async()=>{const n=p.value.filter(c=>{var Y;return(Y=x.value[c.name])==null?void 0:Y.isDirty});for(const c of n)await Q(c.name)},b=I(()=>p.value.some(n=>{var c;return(c=x.value[n.name])==null?void 0:c.isDirty}));return Z(()=>j.project,n=>{n&&g()},{immediate:!0}),se(()=>{j.project&&g()}),(n,c)=>{var Y,ie;return t(),s("div",Io,[f.loading?(t(),s("div",Bo,[e("div",Eo,[(t(),s(L,null,K(4,R=>e("div",{key:R,class:"bg-gray-200 rounded-lg h-24"})),64))]),c[0]||(c[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):f.project?(t(),s("div",Uo,[e("div",Ho,[e("div",Ro,[c[3]||(c[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:U,disabled:C.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":C.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},c[1]||(c[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),c[2]||(c[2]=X(" Aggiorna "))],8,Oo)])]),e("div",Fo,[e("div",Lo,[e("div",Ko,[c[5]||(c[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",qo,[e("dl",null,[c[4]||(c[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",No,o(F(y.value.totalHours)),1),e("dd",Xo,o(y.value.workDays)+" giorni lavorati",1)])])])]),e("div",Wo,[e("div",Yo,[c[7]||(c[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Go,[e("dl",null,[c[6]||(c[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Jo,o(V(y.value.totalCosts)),1),e("dd",{class:H(["text-xs",E.value])},o(V(y.value.costVariance))+" vs budget",3)])])])]),e("div",Qo,[e("div",Zo,[c[9]||(c[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ea,[e("dl",null,[c[8]||(c[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ta,o(V(y.value.potentialRevenue)),1),e("dd",sa,o(V(y.value.actualRevenue))+" fatturati",1)])])])]),e("div",ra,[e("div",oa,[c[11]||(c[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",aa,[e("dl",null,[c[10]||(c[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",na,o(_(y.value.marginPercentage)),1),e("dd",{class:H(["text-xs",A.value])},o(h.value),3)])])])])]),e("div",ia,[e("div",la,[c[13]||(c[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",da,[e("div",ua,[c[12]||(c[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ca,o(V(f.project.budget||0)),1)]),e("div",ga,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:T.value+"%"})},null,4)]),e("div",ma,[e("span",pa,"Utilizzato: "+o(V(y.value.totalCosts)),1),e("span",va,o(T.value)+"%",1)])])]),e("div",xa,[c[15]||(c[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",ya,[e("div",fa,[c[14]||(c[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",ba,o(F(f.project.estimated_hours||0)),1)]),e("div",ha,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:O.value+"%"})},null,4)]),e("div",ka,[e("span",wa,"Lavorate: "+o(F(y.value.totalHours)),1),e("span",_a,o(O.value)+"%",1)])])])]),e("div",$a,[e("div",{class:"flex items-center justify-between mb-4"},[c[17]||(c[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:k,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},c[16]||(c[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),X(" Configura KPI ")]))]),e("div",ja,[e("div",Ca,[e("div",{class:H(["text-2xl font-bold",N.value])},o(T.value)+"% ",3),c[18]||(c[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ma,"Soglia: "+o(a.value.budget)+"%",1)]),e("div",Ta,[e("div",{class:H(["text-2xl font-bold",w.value])},o(O.value)+"% ",3),c[19]||(c[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Pa,"Soglia: "+o(a.value.time)+"%",1)]),e("div",Sa,[e("div",{class:H(["text-2xl font-bold",d.value])},o(_(y.value.marginPercentage)),3),c[20]||(c[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Da,"Soglia: "+o(a.value.margin)+"%",1)])])])])):(t(),s("div",Va,c[21]||(c[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),m.value?(t(),s("div",za,[e("div",Aa,[e("div",Ia,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[c[23]||(c[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:$,class:"text-gray-400 hover:text-gray-600"},c[22]||(c[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ba,[e("div",Ea,[e("h4",Ua,o((Y=f.project)==null?void 0:Y.name),1),e("p",Ha,"Tipo: "+o(i((ie=f.project)==null?void 0:ie.project_type)),1)]),e("div",Ra,[(t(!0),s(L,null,K(p.value,R=>{var ce,ge;return t(),s("div",{key:R.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Oa,[e("div",null,[e("h5",Fa,o(R.display_name),1),e("p",La,o(R.description),1)]),e("div",Ka,[e("span",qa,o(R.unit),1),e("button",{onClick:J=>P(R.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Na)])]),e("div",Xa,[e("div",null,[c[24]||(c[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),B(e("input",{type:"number",step:"0.1","onUpdate:modelValue":J=>x.value[R.name].target_min=J,onInput:J=>q(R.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Wa),[[W,x.value[R.name].target_min]])]),e("div",null,[c[25]||(c[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),B(e("input",{type:"number",step:"0.1","onUpdate:modelValue":J=>x.value[R.name].target_max=J,onInput:J=>q(R.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ya),[[W,x.value[R.name].target_max]])]),e("div",null,[c[26]||(c[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),B(e("input",{type:"number",step:"0.1","onUpdate:modelValue":J=>x.value[R.name].warning_threshold=J,onInput:J=>q(R.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ga),[[W,x.value[R.name].warning_threshold]])])]),e("div",Ja,[c[27]||(c[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),B(e("textarea",{"onUpdate:modelValue":J=>x.value[R.name].custom_description=J,onInput:J=>q(R.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Qa),[[W,x.value[R.name].custom_description]])]),e("div",Za,[(ce=x.value[R.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:J=>Q(R.name),disabled:v.value===R.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[v.value===R.name?(t(),s("svg",tn,c[28]||(c[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):D("",!0),X(" "+o(v.value===R.name?"Salvataggio...":"Salva KPI"),1)],8,en)):(ge=x.value[R.name])!=null&&ge.isSaved?(t(),s("span",sn,"✓ Salvato")):D("",!0)])])}),128))])]),e("div",rn,[e("button",{onClick:l,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",on,[e("button",{onClick:$,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:te,disabled:!b.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,an)])])])])])):D("",!0)])}}},ln={class:"space-y-6"},dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},un={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},cn={class:"flex items-center justify-between"},gn={class:"flex items-center space-x-4"},mn={class:"flex items-center space-x-2"},pn={key:0,class:"p-6"},vn={class:"overflow-x-auto"},xn={class:"min-w-[1000px]"},yn={class:"flex mb-4"},fn={class:"flex-1 flex"},bn={class:"space-y-1"},hn={class:"w-80 flex-shrink-0 px-4 py-3"},kn={class:"flex items-center space-x-2"},wn={class:"flex-1 min-w-0"},_n={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},$n={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},jn={key:0},Cn={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Mn={class:"flex-1 relative h-12 flex items-center"},Tn=["title"],Pn={class:"truncate"},Sn={class:"ml-2"},Dn={key:1,class:"text-center py-12"},Vn={key:2,class:"flex justify-center py-12"},zn={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(f,{expose:S}){const j=f,M=z("weeks"),C=z(new Date),m=z([]),v=z(0),x=I(()=>{var _;return((_=j.project)==null?void 0:_.tasks)||[]}),y=I(()=>x.value.filter(_=>_.start_date&&_.due_date).map(_=>{const g=T(_);return{..._,timeline:g}})),a=()=>{const _=new Date,g=new Date(C.value),u=[],U=12;for(let p=0;p<U;p++){const r=new Date(g);M.value==="weeks"?r.setDate(g.getDate()+p*7):M.value==="months"&&r.setMonth(g.getMonth()+p),u.push(r)}if(m.value=u,u.length>1){const p=u[0],r=new Date(u[u.length-1]);M.value==="weeks"?r.setDate(r.getDate()+7):M.value==="months"&&r.setMonth(r.getMonth()+1);const i=r-p,k=_-p;v.value=Math.max(0,Math.min(100,k/i*100))}else v.value=0},T=_=>{if(!m.value.length)return null;const g=new Date(_.start_date),u=new Date(_.due_date),U=m.value[0],r=m.value[m.value.length-1]-U,i=g-U,k=u-g,$=Math.max(0,i/r*100),q=Math.min(100-$,k/r*100);return{leftPercent:$,widthPercent:Math.max(5,q)}},O=_=>M.value==="weeks"?`${_.getDate()}/${_.getMonth()+1}`:M.value==="months"?_.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",E=_=>{const g=new Date,u=new Date(_);if(M.value==="weeks"){const U=new Date(u),p=new Date(u);return p.setDate(p.getDate()+6),g>=U&&g<=p}else if(M.value==="months")return u.getMonth()===g.getMonth()&&u.getFullYear()===g.getFullYear();return!1},A=()=>{const _=new Date;if(M.value==="weeks"){const g=new Date(_);g.setDate(_.getDate()-_.getDay()),C.value=g}else{const g=new Date(_.getFullYear(),_.getMonth(),1);C.value=g}a()},h=_=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[_]||"bg-gray-400",N=_=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[_]||"bg-gray-500",w=_=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[_]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",d=_=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[_]||"Non specificata",V=_=>({todo:0,"in-progress":50,review:75,done:100})[_.status]||0,F=_=>_?new Date(_).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>j.project,()=>{a()},{immediate:!0}),se(()=>{A()}),S({refresh:a}),(_,g)=>(t(),s("div",ln,[e("div",dn,[e("div",un,[e("div",cn,[g[3]||(g[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",gn,[e("div",mn,[g[2]||(g[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),B(e("select",{"onUpdate:modelValue":g[0]||(g[0]=u=>M.value=u),onChange:a,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},g[1]||(g[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[G,M.value]])]),e("button",{onClick:A,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!f.loading&&y.value.length>0?(t(),s("div",pn,[e("div",vn,[e("div",xn,[e("div",yn,[g[4]||(g[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",fn,[(t(!0),s(L,null,K(m.value,(u,U)=>(t(),s("div",{key:U,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":E(u)}])},o(O(u)),3))),128))])]),e("div",bn,[(t(!0),s(L,null,K(y.value,u=>(t(),s("div",{key:u.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",hn,[e("div",kn,[e("div",{class:H(["w-3 h-3 rounded-full",h(u.status)])},null,2),e("div",wn,[e("p",_n,o(u.name),1),e("div",$n,[u.assignee?(t(),s("span",jn,o(u.assignee.full_name),1)):D("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",w(u.priority)])},o(d(u.priority)),3)]),e("div",Cn,o(F(u.start_date))+" - "+o(F(u.due_date)),1)])])]),e("div",Mn,[u.timeline?(t(),s("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",N(u.status)]),style:ee({left:u.timeline.leftPercent+"%",width:u.timeline.widthPercent+"%",minWidth:"60px"}),title:`${u.name} - ${V(u)}% completato`},[e("span",Pn,o(u.name.length>15?u.name.substring(0,15)+"...":u.name),1),e("span",Sn,o(V(u))+"%",1)],14,Tn)):D("",!0),u.timeline&&V(u)>0&&V(u)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:u.timeline.leftPercent+"%",width:u.timeline.widthPercent*V(u)/100+"%",minWidth:"2px"})},null,4)):D("",!0),(t(!0),s(L,null,K(m.value,(U,p)=>(t(),s("div",{key:p,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:p/m.value.length*100+"%"})},null,4))),128)),v.value>=0&&v.value<=100?(t(),s("div",{key:2,class:"absolute top-0 bottom-0 w-0.5 bg-red-500 z-10",style:ee({left:v.value+"%"})},null,4)):D("",!0)])]))),128))])])]),g[5]||(g[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):f.loading?D("",!0):(t(),s("div",Dn,g[6]||(g[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),f.loading?(t(),s("div",Vn,g[7]||(g[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0)])]))}},An={class:"space-y-6"},In={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Bn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},En={class:"flex items-center justify-between"},Un={class:"flex items-center space-x-4"},Hn={class:"flex items-center space-x-2"},Rn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},On={class:"flex items-center space-x-2"},Fn=["value"],Ln={key:0,class:"flex justify-center py-8"},Kn={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},qn={class:"text-red-600"},Nn={key:2,class:"p-6"},Xn={class:"overflow-x-auto"},Wn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Yn={class:"bg-gray-50 dark:bg-gray-700"},Gn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Jn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Qn={class:"text-sm font-medium text-gray-900 dark:text-white"},Zn={class:"text-xs text-gray-500 dark:text-gray-400"},ei=["onClick"],ti={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},si={key:1,class:"text-gray-300 dark:text-gray-600"},ri={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},oi={class:"text-sm font-medium text-gray-900 dark:text-white"},ai={class:"bg-gray-100 dark:bg-gray-600 font-medium"},ni={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ii={key:0,class:"text-center py-8"},li={class:"mt-3"},di={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},ui={class:"grid grid-cols-1 gap-4"},ci=["value"],gi={class:"flex justify-end space-x-3 mt-6"},mi=["disabled"],pi={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(f,{expose:S}){const j=f,M=ne(),C=z(null),m=z(!1),v=z(""),x=z(!1),y=z(new Date().getFullYear()),a=z(new Date().getMonth()+1),T=z(""),O=z(!1),E=z(!1),A=z(null),h=z({task_id:"",date:"",hours:0,description:""}),N=I(()=>C.value?Array.from({length:C.value.days_in_month},(p,r)=>r+1):[]),w=async()=>{var p;if((p=j.project)!=null&&p.id){m.value=!0,v.value="";try{const r=new URLSearchParams({year:y.value.toString(),month:a.value.toString()});T.value&&r.append("member_id",T.value.toString());const i=await fetch(`/api/timesheets/project/${j.project.id}/monthly?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento del timesheet");const k=await i.json();C.value=k.data}catch(r){v.value=r.message}finally{m.value=!1}}},d=async()=>{x.value=!0;try{const p={...h.value,project_id:j.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":M.csrfToken},body:JSON.stringify(p)})).ok)throw new Error("Errore nel salvataggio del timesheet");await w(),F()}catch(p){v.value=p.message}finally{x.value=!1}},V=(p,r)=>{const i=C.value.tasks.find(k=>k.id===p);i&&(A.value={taskId:p,day:r},h.value={task_id:p,date:`${y.value}-${String(a.value).padStart(2,"0")}-${String(r).padStart(2,"0")}`,hours:i.daily_hours[r]||0,description:""},i.daily_hours[r]>0?E.value=!0:O.value=!0)},F=()=>{O.value=!1,E.value=!1,A.value=null,h.value={task_id:"",date:"",hours:0,description:""}},_=()=>{a.value===1?(a.value=12,y.value--):a.value--,w()},g=()=>{a.value===12?(a.value=1,y.value++):a.value++,w()},u=p=>{const r=new Date;return r.getFullYear()===y.value&&r.getMonth()+1===a.value&&r.getDate()===p},U=p=>!p||p===0?"0":p%1===0?p.toString():p.toFixed(2);return Z(()=>{var p;return(p=j.project)==null?void 0:p.id},p=>{p&&w()}),Z(T,()=>{w()}),se(()=>{var p;(p=j.project)!=null&&p.id&&w()}),S({refresh:w}),(p,r)=>{var i,k;return t(),s("div",An,[e("div",In,[e("div",Bn,[e("div",En,[e("div",Un,[r[11]||(r[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Hn,[e("button",{onClick:_,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[7]||(r[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Rn,o(a.value)+"/"+o(y.value),1),e("button",{onClick:g,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},r[8]||(r[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",On,[r[10]||(r[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),B(e("select",{"onUpdate:modelValue":r[0]||(r[0]=$=>T.value=$),onChange:w,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[9]||(r[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(L,null,K(((i=f.project)==null?void 0:i.team_members)||[],$=>(t(),s("option",{key:$.id,value:$.id},o($.first_name)+" "+o($.last_name),9,Fn))),128))],544),[[G,T.value]])])]),e("button",{onClick:r[1]||(r[1]=$=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},r[12]||(r[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),X(" Aggiungi Ore ")]))])]),m.value?(t(),s("div",Ln,r[13]||(r[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):D("",!0),v.value?(t(),s("div",Kn,[e("p",qn,o(v.value),1)])):D("",!0),!m.value&&C.value?(t(),s("div",Nn,[e("div",Xn,[e("table",Wn,[e("thead",Yn,[e("tr",null,[r[14]||(r[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(L,null,K(N.value,$=>(t(),s("th",{key:$,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":u($)}])},o($),3))),128)),r[15]||(r[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Gn,[(t(!0),s(L,null,K(C.value.tasks,$=>(t(),s("tr",{key:$.id},[e("td",Jn,[e("div",Qn,o($.name),1),e("div",Zn,o($.workers.length?$.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(L,null,K(N.value,q=>(t(),s("td",{key:q,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":u(q)}]),onClick:P=>V($.id,q)},[$.daily_hours[q]>0?(t(),s("span",ti,o(U($.daily_hours[q])),1)):(t(),s("span",si,"-"))],10,ei))),128)),e("td",ri,[e("span",oi,o(U($.total_hours)),1)])]))),128)),e("tr",ai,[r[16]||(r[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(L,null,K(N.value,$=>(t(),s("td",{key:$,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":u($)}])},o(U(C.value.daily_totals[$]||0)),3))),128)),e("td",ni,o(U(C.value.grand_total)),1)])])])]),C.value.tasks.length===0?(t(),s("div",ii,r[17]||(r[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):D("",!0)])):D("",!0)]),O.value||E.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:F},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[6]||(r[6]=oe(()=>{},["stop"]))},[e("div",li,[e("h3",di,o(E.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:oe(d,["prevent"])},[e("div",ui,[e("div",null,[r[19]||(r[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),B(e("select",{"onUpdate:modelValue":r[2]||(r[2]=$=>h.value.task_id=$),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[r[18]||(r[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(L,null,K(((k=C.value)==null?void 0:k.tasks)||[],$=>(t(),s("option",{key:$.id,value:$.id},o($.name),9,ci))),128))],512),[[G,h.value.task_id]])]),e("div",null,[r[20]||(r[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),B(e("input",{"onUpdate:modelValue":r[3]||(r[3]=$=>h.value.date=$),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.date]])]),e("div",null,[r[21]||(r[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),B(e("input",{"onUpdate:modelValue":r[4]||(r[4]=$=>h.value.hours=$),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.hours]])]),e("div",null,[r[22]||(r[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),B(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=$=>h.value.description=$),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[W,h.value.description]])])]),e("div",gi,[e("button",{type:"button",onClick:F,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:x.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(x.value?"Salvataggio...":E.value?"Aggiorna":"Aggiungi"),9,mi)])],32)])])])):D("",!0)])}}},vi={class:"project-view"},xi={class:"tab-content"},yi={__name:"ProjectView",setup(f){const S=xe(),j=ne(),M=he(),C=we(),m=z(!0),v=z("overview"),x=I(()=>S.currentProject),y=I(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(h=>!!(["overview","tasks","gantt","team","timesheet"].includes(h.id)||h.id==="kpi"&&j.hasPermission("view_reports")||h.id==="expenses"&&j.hasPermission("manage_expenses")))),a=I(()=>({overview:me,tasks:Ws,team:Lr,expenses:Ao,kpi:nn,gantt:zn,timesheet:pi})[v.value]||me),T=async()=>{m.value=!0;try{const A=M.params.id;await S.fetchProject(A)}catch(A){console.error("Error loading project:",A)}finally{m.value=!1}},O=()=>{C.push(`/projects/${M.params.id}/edit`)},E=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await S.deleteProject(M.params.id),C.push("/projects")}catch(A){console.error("Error deleting project:",A)}};return Z(()=>M.params.id,(A,h)=>{A&&A!==h&&T()}),Z(()=>M.hash,A=>{if(A){const h=A.replace("#","");y.value.find(N=>N.id===h)&&v.value!==h&&(v.value=h)}},{immediate:!0}),Z(v,A=>{const h=`#${A}`;M.hash!==h&&C.replace({...M,hash:h})}),se(()=>{if(M.hash){const A=M.hash.replace("#","");y.value.find(h=>h.id===A)&&(v.value=A)}T()}),(A,h)=>(t(),s("div",vi,[re(Ue,{project:x.value,loading:m.value,onEdit:O,onDelete:E},null,8,["project","loading"]),re(qe,{modelValue:v.value,"onUpdate:modelValue":h[0]||(h[0]=N=>v.value=N),tabs:y.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",xi,[(t(),de(ke,null,[(t(),de(ve(a.value),{project:x.value,loading:m.value},null,8,["project","loading"]))],1024))])]))}},hi=ue(yi,[["__scopeId","data-v-5842e667"]]);export{hi as default};
