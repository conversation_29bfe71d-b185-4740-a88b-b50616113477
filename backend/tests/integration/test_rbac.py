import pytest
import json
from models import User

def test_admin_api_forbidden_for_non_admin(client, db_session, test_user_id, new_user_data):
    """Test that non-admin users cannot access admin-only API endpoints."""
    # Recupera l'utente normale dal DB
    user = db_session.get(User, test_user_id)
    assert user is not None, f"Test user with id {test_user_id} not found."

    # Assicura che l'utente non sia admin
    user.role = 'employee'
    db_session.commit()
    assert user.role != 'admin', "Test user should not have admin role for this test."

    # Login come utente normale via API
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': user.username,
                                    'password': new_user_data['password']
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Test accesso negato a API admin-only
    admin_only_endpoints = [
        '/api/personnel/users',
        '/api/personnel/export'
    ]

    for endpoint in admin_only_endpoints:
        response = client.get(endpoint)
        # Should return 401 (Unauthorized) or 403 (Forbidden)
        assert response.status_code in [401, 403], f"Endpoint {endpoint} should deny access to non-admin"

    # Test endpoints that employees can access
    employee_allowed_endpoints = [
        '/api/dashboard/stats'  # Employees can view dashboard stats
    ]

    for endpoint in employee_allowed_endpoints:
        response = client.get(endpoint)
        # Should return 200 (OK) for employees
        assert response.status_code == 200, f"Endpoint {endpoint} should allow access to employees"

def test_role_based_permissions_api(client, auth, sample_users):
    """Test that different roles have appropriate permissions via API."""
    # Login as admin
    auth.login()

    # Get current user info
    response = client.get('/api/auth/me')
    assert response.status_code == 200

    data = json.loads(response.data)
    user = data['data']['user']
    permissions = user['permissions']

    # Test role-based permissions
    if user['role'] == 'admin':
        # Admin should have extensive permissions
        assert len(permissions) > 5
        assert any('admin' in perm or 'all' in perm for perm in permissions)
    elif user['role'] == 'manager':
        # Manager should have moderate permissions
        assert len(permissions) > 2
        assert 'view_projects' in permissions or 'view_dashboard' in permissions
    elif user['role'] == 'employee':
        # Employee should have basic permissions
        assert len(permissions) >= 1
        assert 'view_dashboard' in permissions or 'view_own_timesheet' in permissions

def test_permission_escalation_prevention_api(client, db_session, created_user, new_user_data):
    """Test that users cannot escalate their own permissions."""
    # Get regular user
    user = db_session.get(User, created_user)
    assert user is not None

    # Make sure user is employee
    user.role = 'employee'
    db_session.commit()

    # Login as employee
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': user.username,
                                    'password': new_user_data['password']
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Try to access user management (should fail)
    response = client.get('/api/personnel/users')
    assert response.status_code in [401, 403]

    # Try to access personnel export (should fail)
    response = client.get('/api/personnel/export')
    assert response.status_code in [401, 403]

    # Dashboard stats should be accessible to employees
    response = client.get('/api/dashboard/stats')
    assert response.status_code == 200

def test_session_role_consistency_api(client, db_session, admin_user_id):
    """Test that user role in session matches database."""
    # Get admin user
    admin = db_session.get(User, admin_user_id)
    assert admin is not None
    assert admin.role == 'admin'

    # Login as admin
    login_response = client.post('/api/auth/login',
                                json={
                                    'username': admin.username,
                                    'password': 'adminpassword'
                                },
                                headers={'Content-Type': 'application/json'})
    assert login_response.status_code == 200

    # Check that API returns correct role
    me_response = client.get('/api/auth/me')
    assert me_response.status_code == 200

    data = json.loads(me_response.data)
    assert data['data']['user']['role'] == 'admin'
    assert data['data']['user']['id'] == admin.id