<template>
  <div class="space-y-6">
    <!-- Header con AI Assistant -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Allocazione Risorse
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Gestisci l'allocazione delle risorse con assistenza AI
            </p>
          </div>
          <div class="flex items-center space-x-3">
            <!-- AI Analysis Button -->
            <button
              @click="runAIAnalysis"
              :disabled="analyzingWithAI"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              <svg v-if="!analyzingWithAI" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <svg v-else class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ analyzingWithAI ? 'Analizzando...' : 'Analisi AI' }}
            </button>
            
            <!-- Add Resource Button -->
            <button
              @click="showAddResourceModal = true"
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Aggiungi Risorsa
            </button>
          </div>
        </div>
      </div>

      <!-- AI Insights Panel -->
      <div v-if="aiInsights" class="px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <div class="flex-1">
            <h4 class="text-sm font-medium text-purple-900 dark:text-purple-100">
              Insights AI - Efficienza: {{ aiInsights.efficiency_score }}%
            </h4>
            <div class="mt-2 space-y-2">
              <div v-for="insight in aiInsights.optimization_insights" :key="insight" class="text-sm text-purple-700 dark:text-purple-300">
                • {{ insight }}
              </div>
            </div>
            
            <!-- AI Recommendations -->
            <div v-if="aiInsights.recommended_allocations?.length" class="mt-3">
              <h5 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-2">
                Raccomandazioni AI:
              </h5>
              <div class="space-y-2">
                <div v-for="rec in aiInsights.recommended_allocations" :key="rec.user_id" 
                     class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                      <span class="text-xs font-medium text-purple-600 dark:text-purple-300">
                        {{ rec.user_name?.charAt(0) }}
                      </span>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-gray-900 dark:text-white">{{ rec.user_name }}</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">{{ rec.role }} - {{ rec.allocation }}%</p>
                    </div>
                  </div>
                  <button
                    @click="applyAIRecommendation(rec)"
                    class="text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"
                  >
                    Applica
                  </button>
                </div>
              </div>
            </div>
          </div>
          <button @click="aiInsights = null" class="flex-shrink-0 text-purple-400 hover:text-purple-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Current Allocations -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white">
          Allocazioni Attuali
        </h4>
      </div>
      
      <div v-if="loading" class="p-6">
        <div class="animate-pulse space-y-4">
          <div v-for="i in 3" :key="i" class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
              <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
            <div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      <div v-else-if="!allocations.length" class="p-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna risorsa allocata</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti.
        </p>
      </div>

      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="allocation in allocations" :key="allocation.id" 
             class="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600 dark:text-primary-300">
                  {{ allocation.user_name?.charAt(0) }}
                </span>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ allocation.user_name }}
                </h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ allocation.role || 'Team Member' }}
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <!-- Allocation Percentage -->
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ allocation.allocation_percentage }}%
                </div>
                <div class="w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                  <div
                    class="h-2 rounded-full"
                    :class="getAllocationClass(allocation.allocation_percentage)"
                    :style="{ width: allocation.allocation_percentage + '%' }"
                  ></div>
                </div>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center space-x-2">
                <button
                  @click="editAllocation(allocation)"
                  class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button
                  @click="removeAllocation(allocation)"
                  class="text-red-400 hover:text-red-600"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Resource Utilization Chart -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white">
          Utilizzo Risorse
        </h4>
      </div>
      <div class="p-6">
        <div class="space-y-4">
          <div v-for="resource in resourceUtilization" :key="resource.user_id" class="flex items-center">
            <div class="w-32 text-sm text-gray-600 dark:text-gray-400">
              {{ resource.user_name }}
            </div>
            <div class="flex-1 mx-4">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  class="h-3 rounded-full transition-all duration-300"
                  :class="getUtilizationClass(resource.total_allocation)"
                  :style="{ width: Math.min(resource.total_allocation, 100) + '%' }"
                ></div>
              </div>
            </div>
            <div class="w-16 text-sm text-right font-medium"
                 :class="getUtilizationTextClass(resource.total_allocation)">
              {{ resource.total_allocation }}%
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Resource Modal -->
    <div v-if="showAddResourceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Aggiungi Risorsa
          </h3>
          
          <form @submit.prevent="addResource">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Utente
                </label>
                <select v-model="newAllocation.user_id" required
                        class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                  <option value="">Seleziona utente...</option>
                  <option v-for="user in availableUsers" :key="user.id" :value="user.id">
                    {{ user.full_name }} ({{ user.role }})
                  </option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ruolo nel Progetto
                </label>
                <input v-model="newAllocation.role" type="text"
                       class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                       placeholder="es. Developer, Designer, PM">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Allocazione (%)
                </label>
                <input v-model.number="newAllocation.allocation_percentage" type="number" min="1" max="100" required
                       class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
              </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
              <button type="button" @click="showAddResourceModal = false"
                      class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500">
                Annulla
              </button>
              <button type="submit" :disabled="saving"
                      class="px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50">
                {{ saving ? 'Salvando...' : 'Aggiungi' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps({
  project: { type: Object, required: true }
})

// Stores
const authStore = useAuthStore()

// State
const loading = ref(true)
const saving = ref(false)
const analyzingWithAI = ref(false)
const allocations = ref([])
const availableUsers = ref([])
const resourceUtilization = ref([])
const aiInsights = ref(null)
const showAddResourceModal = ref(false)

const newAllocation = ref({
  user_id: '',
  role: '',
  allocation_percentage: 100
})

// Computed
const projectId = computed(() => props.project?.id)

// Methods
const loadAllocations = async () => {
  if (!projectId.value) return
  
  loading.value = true
  try {
    const response = await fetch(`/api/resources?project_id=${projectId.value}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) throw new Error('Errore nel caricamento allocazioni')

    const result = await response.json()
    allocations.value = result.data?.resources || []
    
    // Load resource utilization
    await loadResourceUtilization()
  } catch (error) {
    console.error('Error loading allocations:', error)
  } finally {
    loading.value = false
  }
}

const loadAvailableUsers = async () => {
  try {
    const response = await fetch('/api/personnel', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) throw new Error('Errore nel caricamento utenti')

    const result = await response.json()
    availableUsers.value = result.data?.users || []
  } catch (error) {
    console.error('Error loading users:', error)
  }
}

const loadResourceUtilization = async () => {
  // Mock data for now - in real implementation, this would come from API
  resourceUtilization.value = allocations.value.map(allocation => ({
    user_id: allocation.user_id,
    user_name: allocation.user_name,
    total_allocation: allocation.allocation_percentage + Math.floor(Math.random() * 30) // Mock other project allocations
  }))
}

const runAIAnalysis = async () => {
  if (!projectId.value) return
  
  analyzingWithAI.value = true
  try {
    const response = await fetch(`/api/ai-resources/analyze-allocation/${projectId.value}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        include_suggestions: true,
        analysis_depth: 'detailed'
      })
    })

    if (!response.ok) throw new Error('Errore nell\'analisi AI')

    const result = await response.json()
    aiInsights.value = result.data?.analysis || null
  } catch (error) {
    console.error('Error in AI analysis:', error)
    alert('Errore nell\'analisi AI: ' + error.message)
  } finally {
    analyzingWithAI.value = false
  }
}

const addResource = async () => {
  saving.value = true
  try {
    const response = await fetch('/api/resources', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        project_id: projectId.value,
        ...newAllocation.value
      })
    })

    if (!response.ok) throw new Error('Errore nell\'aggiunta risorsa')

    await loadAllocations()
    showAddResourceModal.value = false
    newAllocation.value = { user_id: '', role: '', allocation_percentage: 100 }
  } catch (error) {
    console.error('Error adding resource:', error)
    alert('Errore nell\'aggiunta risorsa: ' + error.message)
  } finally {
    saving.value = false
  }
}

const editAllocation = (allocation) => {
  // TODO: Implement edit modal
  console.log('Edit allocation:', allocation)
}

const removeAllocation = async (allocation) => {
  if (!confirm('Sei sicuro di voler rimuovere questa allocazione?')) return
  
  try {
    const response = await fetch(`/api/resources/${allocation.id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) throw new Error('Errore nella rimozione')

    await loadAllocations()
  } catch (error) {
    console.error('Error removing allocation:', error)
    alert('Errore nella rimozione: ' + error.message)
  }
}

const applyAIRecommendation = async (recommendation) => {
  try {
    await fetch('/api/resources', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        project_id: projectId.value,
        user_id: recommendation.user_id,
        role: recommendation.role,
        allocation_percentage: recommendation.allocation
      })
    })

    await loadAllocations()
  } catch (error) {
    console.error('Error applying AI recommendation:', error)
  }
}

// Utility functions
const getAllocationClass = (percentage) => {
  if (percentage >= 80) return 'bg-red-500'
  if (percentage >= 60) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getUtilizationClass = (percentage) => {
  if (percentage > 100) return 'bg-red-500'
  if (percentage >= 90) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getUtilizationTextClass = (percentage) => {
  if (percentage > 100) return 'text-red-600 dark:text-red-400'
  if (percentage >= 90) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-green-600 dark:text-green-400'
}

// Watchers
watch(() => props.project, (newProject) => {
  if (newProject) {
    loadAllocations()
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  loadAvailableUsers()
})
</script>
